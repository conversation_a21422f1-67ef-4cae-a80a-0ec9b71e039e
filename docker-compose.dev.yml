version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: cvleap-postgres-dev
    environment:
      POSTGRES_DB: cvleap_dev
      POSTGRES_USER: cvleap_dev
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cvleap_dev -d cvleap_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-dev-network

  # Redis for Development
  redis-dev:
    image: redis:7-alpine
    container_name: cvleap-redis-dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-dev-network

  # Development Server with Hot Reload
  server-dev:
    build:
      context: ./server
      dockerfile: Dockerfile.dev
    container_name: cvleap-server-dev
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: ******************************************************/cvleap_dev
      REDIS_URL: redis://redis-dev:6379
      JWT_SECRET: dev-jwt-secret-key
      CLIENT_URL: http://localhost:5173
    ports:
      - "3000:3000"
    volumes:
      - ./server:/app
      - /app/node_modules
      - uploads_dev_data:/app/uploads
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    command: npm run dev
    networks:
      - cvleap-dev-network

  # Development Client with Hot Reload
  client-dev:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    container_name: cvleap-client-dev
    environment:
      VITE_API_URL: http://localhost:3000
    ports:
      - "5173:5173"
    volumes:
      - ./client:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - cvleap-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:
  uploads_dev_data:

networks:
  cvleap-dev-network:
    driver: bridge