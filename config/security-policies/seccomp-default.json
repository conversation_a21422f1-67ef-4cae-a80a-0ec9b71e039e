{"defaultAction": "SCMP_ACT_ERRNO", "architectures": ["SCMP_ARCH_X86_64", "SCMP_ARCH_X86", "SCMP_ARCH_X32"], "syscalls": [{"names": ["accept", "accept4", "access", "arch_prctl", "bind", "brk", "chdir", "chmod", "chown", "clock_getres", "clock_gettime", "clock_nanosleep", "clone", "close", "connect", "dup", "dup2", "dup3", "epoll_create", "epoll_create1", "epoll_ctl", "epoll_wait", "eventfd", "eventfd2", "execve", "exit", "exit_group", "fchdir", "fchmod", "fchown", "fcntl", "fdatasync", "flock", "fork", "fstat", "fstatfs", "fsync", "ftrun<PERSON>", "futex", "getcwd", "getdents", "getdents64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getgid", "getpeername", "getpgrp", "getpid", "<PERSON><PERSON><PERSON>", "getpriority", "getrandom", "get<PERSON><PERSON>d", "getresuid", "getrlimit", "getrusage", "getsid", "getsockname", "getsockopt", "gettid", "gettimeofday", "getuid", "inotify_add_watch", "inotify_init", "inotify_init1", "inotify_rm_watch", "ioctl", "kill", "lchown", "listen", "lseek", "lstat", "madvise", "mkdir", "mmap", "mprotect", "mun<PERSON>p", "nanosleep", "open", "openat", "pipe", "pipe2", "poll", "ppoll", "prctl", "pread64", "prlimit64", "pselect6", "pwrite64", "read", "readlink", "readv", "recv", "recvfrom", "recvmsg", "rename", "rmdir", "rt_sigaction", "rt_sigpending", "rt_sigprocmask", "rt_sigqueueinfo", "rt_sigreturn", "rt_sigsuspend", "rt_sigtimedwait", "sched_getaffinity", "sched_yield", "select", "send", "sendfile", "sendmsg", "sendto", "set_robust_list", "set_tid_address", "<PERSON><PERSON>d", "setgroups", "setpgid", "setpriority", "set<PERSON>gi<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setresuid", "set<PERSON><PERSON>", "setsid", "setsockopt", "setuid", "shutdown", "sigaltsta<PERSON>", "socket", "socketpair", "splice", "stat", "statfs", "symlink", "sync", "sysinfo", "tee", "tgkill", "time", "timer_create", "timer_delete", "timer_getoverrun", "timer_gettime", "timer_settime", "timerfd_create", "timerfd_gettime", "timerfd_settime", "times", "tkill", "truncate", "umask", "uname", "unlink", "utimensat", "vfork", "wait4", "waitid", "write", "writev"], "action": "SCMP_ACT_ALLOW"}]}