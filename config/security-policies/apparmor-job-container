# AppArmor profile for CVLeap job containers
#include <tunables/global>

profile cvleap-job-container flags=(attach_disconnected,mediate_deleted) {
  #include <abstractions/base>
  #include <abstractions/nameservice>

  # Capabilities
  capability chown,
  capability dac_override,
  capability fowner,
  capability fsetid,
  capability kill,
  capability setgid,
  capability setuid,
  capability setpcap,
  capability net_bind_service,
  capability net_raw,
  capability sys_chroot,
  capability mknod,
  capability audit_write,
  capability setfcap,

  # Network access - restricted to specific domains
  network inet tcp,
  network inet udp,
  network inet6 tcp,
  network inet6 udp,

  # File system access restrictions
  / r,
  /app/ r,
  /app/** r,
  /app/job.js r,
  /tmp/ rw,
  /tmp/** rw,
  /usr/bin/node ix,
  /lib/x86_64-linux-gnu/lib*.so* mr,
  /lib64/ld-linux-x86-64.so.2 mr,
  /etc/passwd r,
  /etc/group r,
  /etc/nsswitch.conf r,
  /etc/hosts r,
  /etc/resolv.conf r,
  /etc/ssl/certs/ r,
  /etc/ssl/certs/** r,
  /usr/share/ca-certificates/ r,
  /usr/share/ca-certificates/** r,
  /proc/cpuinfo r,
  /proc/meminfo r,
  /proc/stat r,
  /proc/self/stat r,
  /proc/self/status r,
  /proc/self/environ r,
  /proc/sys/kernel/random/uuid r,
  /sys/devices/system/cpu/ r,
  /sys/devices/system/cpu/** r,
  /dev/null rw,
  /dev/zero r,
  /dev/full r,
  /dev/random r,
  /dev/urandom r,
  /dev/tty rw,

  # Deny dangerous operations
  deny /proc/sys/** w,
  deny /sys/** w,
  deny mount,
  deny umount,
  deny pivotroot,
  deny capability sys_admin,
  deny capability sys_module,
  deny capability sys_time,
  deny capability sys_boot,
  deny capability sys_ptrace,
  deny capability mac_admin,
  deny capability mac_override,
  deny capability sys_rawio,

  # Process control
  deny signal (receive) set=(kill,term) peer=unconfined,
  deny ptrace,
  deny @{PROC}/sys/kernel/cap_last_cap r,
  deny @{PROC}/sysrq-trigger rwklx,
  deny @{PROC}/mem rwklx,
  deny @{PROC}/kmem rwklx,

  # Network restrictions - only allow specific job platforms
  deny network packet,
  deny network raw,
}