# Kubernetes RuntimeClass for gVisor
apiVersion: node.k8s.io/v1
kind: RuntimeClass
metadata:
  name: gvisor
  labels:
    app: cvleap
    security: enhanced
handler: runsc
overhead:
  podFixed:
    memory: "75Mi"
    cpu: "100m"
scheduling:
  nodeClassification:
    - name: "security-tier"
      value: "high"
  tolerations:
  - key: "gvisor"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

---
# RuntimeClass for job execution with stricter isolation
apiVersion: node.k8s.io/v1
kind: RuntimeClass
metadata:
  name: gvisor-strict
  labels:
    app: cvleap
    security: maximum
handler: runsc
overhead:
  podFixed:
    memory: "100Mi"
    cpu: "150m"
scheduling:
  nodeClassification:
    - name: "security-tier"
      value: "maximum"
  tolerations:
  - key: "gvisor-strict"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

---
# ConfigMap for gVisor runtime configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gvisor-config
  namespace: cvleap
  labels:
    app: cvleap
    component: runtime-config
data:
  runsc.toml: |
    # gVisor runtime configuration for CVLeap
    [runsc]
      # Platform configuration
      platform = "kvm"
      
      # Network configuration
      network = "host"
      
      # File system configuration
      file-access = "exclusive"
      overlay = true
      
      # Security configuration
      rootless = true
      
      # Performance tuning
      num-network-channels = 4
      
      # Logging
      debug = false
      debug-log = "/var/log/runsc/"
      
      # Resource limits
      memory-limit = "2Gi"
      cpu-limit = "1000m"

  monitoring.yaml: |
    # Monitoring configuration for gVisor containers
    metrics:
      enabled: true
      port: 9090
      path: "/metrics"
      
    alerts:
      syscall_latency_threshold: "50ms"
      memory_overhead_threshold: "200Mi"
      startup_time_threshold: "5s"
      
    dashboards:
      - name: "gvisor-performance"
        panels:
          - syscall_latency
          - memory_usage
          - cpu_overhead
          - network_performance

---
# ServiceMonitor for Prometheus monitoring
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gvisor-metrics
  namespace: cvleap
  labels:
    app: cvleap
    component: monitoring
spec:
  selector:
    matchLabels:
      app: cvleap
      runtime: gvisor
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true

---
# PodSecurityPolicy for gVisor containers
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: gvisor-psp
  labels:
    app: cvleap
    security: enhanced
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1001
        max: 1001
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
  runtimeClass:
    allowedRuntimeClassNames:
      - gvisor
      - gvisor-strict

---
# NetworkPolicy for gVisor containers
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gvisor-network-policy
  namespace: cvleap
  labels:
    app: cvleap
    security: enhanced
spec:
  podSelector:
    matchLabels:
      runtime: gvisor
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: cvleap
    ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: cvleap
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []  # Allow external API calls
    ports:
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 80   # HTTP

---
# ResourceQuota for gVisor namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: gvisor-quota
  namespace: cvleap
  labels:
    app: cvleap
    component: resource-management
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "20"
    services: "10"
    secrets: "20"
    configmaps: "20"

---
# LimitRange for gVisor containers
apiVersion: v1
kind: LimitRange
metadata:
  name: gvisor-limits
  namespace: cvleap
  labels:
    app: cvleap
    component: resource-management
spec:
  limits:
  - type: Container
    default:
      cpu: "500m"
      memory: "1Gi"
    defaultRequest:
      cpu: "250m"
      memory: "512Mi"
    max:
      cpu: "2"
      memory: "4Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
  - type: Pod
    max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "200m"
      memory: "256Mi"
