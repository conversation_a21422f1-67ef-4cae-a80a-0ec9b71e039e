# CVLeap Sandbox Environment - Cost-Optimized gVisor Deployment
apiVersion: v1
kind: Namespace
metadata:
  name: cvleap-sandbox
  labels:
    security-tier: maximum
    workload-type: sandbox
    cost-optimization: enabled

---
# Sandbox-specific RuntimeClass
apiVersion: node.k8s.io/v1
kind: RuntimeClass
metadata:
  name: gvisor-sandbox
  labels:
    security-tier: maximum
handler: runsc
overhead:
  podFixed:
    memory: "100Mi"  # Optimized for sandbox workloads
    cpu: "50m"
scheduling:
  nodeClassification:
    - name: "workload-type"
      value: "sandbox"
  tolerations:
  - key: "sandbox"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

---
# Resume Processing Sandbox
apiVersion: apps/v1
kind: Deployment
metadata:
  name: resume-processor-sandbox
  namespace: cvleap-sandbox
  labels:
    app: cvleap-sandbox
    component: resume-processor
    cost-tier: optimized
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cvleap-sandbox
      component: resume-processor
  template:
    metadata:
      labels:
        app: cvleap-sandbox
        component: resume-processor
        security-tier: maximum
        workload-type: sandbox
      annotations:
        gvisor.dev/runtime: "runsc"
        cost-optimization.cvleap.com/tier: "spot-eligible"
    spec:
      runtimeClassName: gvisor-sandbox
      serviceAccountName: sandbox-runner
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: Unconfined
      containers:
      - name: resume-processor
        image: cvleap/resume-processor:sandbox-latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SANDBOX_MODE
          value: "true"
        - name: EXECUTION_TIMEOUT
          value: "120"
        - name: MAX_FILE_SIZE
          value: "10MB"
        - name: SECURITY_LEVEL
          value: "strict"
        - name: NODE_OPTIONS
          value: "--max-old-space-size=128"
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
            ephemeral-storage: 50Mi
          limits:
            cpu: 200m
            memory: 256Mi
            ephemeral-storage: 500Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: workspace
          mountPath: /workspace
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      - name: workspace
        emptyDir:
          sizeLimit: 200Mi
      nodeSelector:
        workload-type: sandbox
        cost-tier: spot-eligible
      tolerations:
      - key: "sandbox"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      - key: "spot"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: component
                  operator: In
                  values:
                  - resume-processor
              topologyKey: kubernetes.io/hostname

---
# AI Analysis Sandbox
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-analyzer-sandbox
  namespace: cvleap-sandbox
  labels:
    app: cvleap-sandbox
    component: ai-analyzer
    cost-tier: optimized
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cvleap-sandbox
      component: ai-analyzer
  template:
    metadata:
      labels:
        app: cvleap-sandbox
        component: ai-analyzer
        security-tier: maximum
        workload-type: sandbox
      annotations:
        gvisor.dev/runtime: "runsc"
    spec:
      runtimeClassName: gvisor-sandbox
      serviceAccountName: sandbox-runner
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: Unconfined
      containers:
      - name: ai-analyzer
        image: cvleap/ai-analyzer:sandbox-latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SANDBOX_MODE
          value: "true"
        - name: AI_TIMEOUT
          value: "180"
        - name: MAX_TOKENS
          value: "4000"
        - name: RATE_LIMIT
          value: "10/minute"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
          medium: Memory
      nodeSelector:
        workload-type: sandbox
      tolerations:
      - key: "sandbox"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
# Document Generator Sandbox
apiVersion: apps/v1
kind: Deployment
metadata:
  name: doc-generator-sandbox
  namespace: cvleap-sandbox
  labels:
    app: cvleap-sandbox
    component: doc-generator
    cost-tier: optimized
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cvleap-sandbox
      component: doc-generator
  template:
    metadata:
      labels:
        app: cvleap-sandbox
        component: doc-generator
        security-tier: maximum
        workload-type: sandbox
      annotations:
        gvisor.dev/runtime: "runsc"
    spec:
      runtimeClassName: gvisor-sandbox
      serviceAccountName: sandbox-runner
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: Unconfined
      containers:
      - name: doc-generator
        image: cvleap/doc-generator:sandbox-latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SANDBOX_MODE
          value: "true"
        - name: DOC_TIMEOUT
          value: "60"
        - name: MAX_FILE_SIZE
          value: "5MB"
        - name: ALLOWED_FORMATS
          value: "pdf,docx"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 300m
            memory: 384Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: output
          mountPath: /output
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
          medium: Memory
      - name: output
        emptyDir:
          sizeLimit: 200Mi
      nodeSelector:
        workload-type: sandbox
      tolerations:
      - key: "sandbox"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
# Sandbox Auto-Scaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sandbox-hpa
  namespace: cvleap-sandbox
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: resume-processor-sandbox
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 30
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60

---
# Sandbox Service
apiVersion: v1
kind: Service
metadata:
  name: sandbox-service
  namespace: cvleap-sandbox
  labels:
    app: cvleap-sandbox
spec:
  selector:
    app: cvleap-sandbox
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
# Sandbox Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: sandbox-network-policy
  namespace: cvleap-sandbox
spec:
  podSelector:
    matchLabels:
      workload-type: sandbox
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: cvleap
    - podSelector:
        matchLabels:
          app: cvleap-api
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # HTTPS for external APIs (OpenAI, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Internal services
  - to:
    - namespaceSelector:
        matchLabels:
          name: cvleap
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis

---
# Resource Quota for Sandbox
apiVersion: v1
kind: ResourceQuota
metadata:
  name: sandbox-quota
  namespace: cvleap-sandbox
spec:
  hard:
    requests.cpu: "5"
    requests.memory: 10Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    pods: "50"
    persistentvolumeclaims: "0"
    services: "5"
    secrets: "10"
    configmaps: "10"

---
# Limit Range for Sandbox Containers
apiVersion: v1
kind: LimitRange
metadata:
  name: sandbox-limits
  namespace: cvleap-sandbox
spec:
  limits:
  - type: Container
    default:
      cpu: 200m
      memory: 256Mi
      ephemeral-storage: 500Mi
    defaultRequest:
      cpu: 50m
      memory: 64Mi
      ephemeral-storage: 50Mi
    max:
      cpu: 1000m
      memory: 1Gi
      ephemeral-storage: 2Gi
    min:
      cpu: 25m
      memory: 32Mi
      ephemeral-storage: 10Mi
  - type: Pod
    max:
      cpu: 2000m
      memory: 2Gi
      ephemeral-storage: 4Gi
