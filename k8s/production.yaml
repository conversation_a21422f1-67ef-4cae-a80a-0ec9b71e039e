apiVersion: v1
kind: Namespace
metadata:
  name: cvleap
  labels:
    name: cvleap
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-config
  namespace: cvleap
data:
  NODE_ENV: "production"
  PORT: "3000"
  CLIENT_URL: "https://cvleap.example.com"
  LOG_LEVEL: "info"
  CACHE_TTL: "3600"
  RATE_LIMIT_WINDOW: "900000"
  RATE_LIMIT_MAX: "100"
---
apiVersion: v1
kind: Secret
metadata:
  name: cvleap-secrets
  namespace: cvleap
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  JWT_SECRET: eW91ci1zZWN1cmUtand0LXNlY3JldA==
  JWT_REFRESH_SECRET: eW91ci1yZWZyZXNoLXNlY3JldA==
  POSTGRES_PASSWORD: Y3ZsZWFwX3Bhc3N3b3Jk
  DATABASE_URL: ********************************************************************************************
  REDIS_URL: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=
  OPENAI_API_KEY: ""
  ANTHROPIC_API_KEY: ""
  GOOGLE_AI_API_KEY: ""
  ENCRYPTION_MASTER_KEY: ""
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: cvleap
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: cvleap
        - name: POSTGRES_USER
          value: cvleap_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            cpu: 250m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - cvleap_user
            - -d
            - cvleap
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - cvleap_user
            - -d
            - cvleap
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: cvleap
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: cvleap
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: cvleap
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: cvleap
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: cvleap
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-server
  namespace: cvleap
  labels:
    app: cvleap-server
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: cvleap-server
  template:
    metadata:
      labels:
        app: cvleap-server
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:15-alpine
        command:
        - sh
        - -c
        - until pg_isready -h postgres-service -p 5432 -U cvleap_user; do echo waiting for postgres; sleep 2; done;
      containers:
      - name: cvleap-server
        image: cvleap-server:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: cvleap-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: cvleap-config
              key: PORT
        - name: CLIENT_URL
          valueFrom:
            configMapKeyRef:
              name: cvleap-config
              key: CLIENT_URL
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: REDIS_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: JWT_SECRET
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: JWT_REFRESH_SECRET
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: OPENAI_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: ANTHROPIC_API_KEY
        - name: GOOGLE_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: GOOGLE_AI_API_KEY
        - name: ENCRYPTION_MASTER_KEY
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: ENCRYPTION_MASTER_KEY
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: logs-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: cvleap
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: logs-pvc
  namespace: cvleap
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: cvleap-server-service
  namespace: cvleap
spec:
  selector:
    app: cvleap-server
  ports:
  - port: 3000
    targetPort: 3000
    name: http
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-client
  namespace: cvleap
  labels:
    app: cvleap-client
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cvleap-client
  template:
    metadata:
      labels:
        app: cvleap-client
    spec:
      containers:
      - name: cvleap-client
        image: cvleap-client:latest
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: cvleap-client-service
  namespace: cvleap
spec:
  selector:
    app: cvleap-client
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cvleap-ingress
  namespace: cvleap
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - cvleap.example.com
    - api.cvleap.example.com
    secretName: cvleap-tls
  rules:
  - host: cvleap.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cvleap-client-service
            port:
              number: 80
  - host: api.cvleap.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cvleap-server-service
            port:
              number: 3000
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cvleap-server-hpa
  namespace: cvleap
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cvleap-server
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cvleap-client-hpa
  namespace: cvleap
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cvleap-client
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70