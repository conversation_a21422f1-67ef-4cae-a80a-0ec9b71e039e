# CVLeap Server Deployment with gVisor
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-server-gvisor
  namespace: cvleap
  labels:
    app: cvleap
    component: server
    runtime: gvisor
    security: enhanced
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cvleap
      component: server
      runtime: gvisor
  template:
    metadata:
      labels:
        app: cvleap
        component: server
        runtime: gvisor
        security: enhanced
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
        gvisor.dev/runtime: "runsc"
    spec:
      runtimeClassName: gvisor  # Use gVisor runtime
      serviceAccountName: cvleap-server
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: Unconfined  # gVisor handles syscall filtering
      containers:
      - name: cvleap-server
        image: cvleap/server:gvisor-latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: GVISOR_RUNTIME
          value: "true"
        - name: NODE_OPTIONS
          value: "--max-old-space-size=1024 --optimize-for-size"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: jwt-secret
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: cvleap-secrets
              key: openai-api-key
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      - name: app-logs
        emptyDir:
          sizeLimit: 500Mi
      - name: uploads
        persistentVolumeClaim:
          claimName: cvleap-uploads-pvc
      nodeSelector:
        gvisor: "enabled"
      tolerations:
      - key: "gvisor"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - cvleap
                - key: component
                  operator: In
                  values:
                  - server
              topologyKey: kubernetes.io/hostname

---
# CVLeap Client Deployment with gVisor
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-client-gvisor
  namespace: cvleap
  labels:
    app: cvleap
    component: client
    runtime: gvisor
    security: enhanced
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cvleap
      component: client
      runtime: gvisor
  template:
    metadata:
      labels:
        app: cvleap
        component: client
        runtime: gvisor
        security: enhanced
      annotations:
        gvisor.dev/runtime: "runsc"
    spec:
      runtimeClassName: gvisor  # Use gVisor runtime
      serviceAccountName: cvleap-client
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: Unconfined  # gVisor handles syscall filtering
      containers:
      - name: cvleap-client
        image: cvleap/client:gvisor-latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        env:
        - name: GVISOR_RUNTIME
          value: "true"
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 50Mi
      - name: nginx-cache
        emptyDir:
          sizeLimit: 100Mi
      - name: nginx-run
        emptyDir:
          sizeLimit: 10Mi
      nodeSelector:
        gvisor: "enabled"
      tolerations:
      - key: "gvisor"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
# Job Runner Deployment with Enhanced gVisor Security
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-job-runner-gvisor
  namespace: cvleap
  labels:
    app: cvleap
    component: job-runner
    runtime: gvisor-strict
    security: maximum
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cvleap
      component: job-runner
      runtime: gvisor-strict
  template:
    metadata:
      labels:
        app: cvleap
        component: job-runner
        runtime: gvisor-strict
        security: maximum
      annotations:
        gvisor.dev/runtime: "runsc"
        gvisor.dev/security-profile: "strict"
    spec:
      runtimeClassName: gvisor-strict  # Use strict gVisor runtime
      serviceAccountName: cvleap-job-runner
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: Unconfined  # gVisor handles syscall filtering
      containers:
      - name: job-runner
        image: cvleap/job-runner:gvisor-latest
        imagePullPolicy: Always
        env:
        - name: NODE_ENV
          value: "production"
        - name: GVISOR_RUNTIME
          value: "true"
        - name: SECURITY_PROFILE
          value: "strict"
        - name: NODE_OPTIONS
          value: "--max-old-space-size=512 --optimize-for-size"
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: job-workspace
          mountPath: /workspace
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 50Mi
      - name: job-workspace
        emptyDir:
          sizeLimit: 100Mi
      nodeSelector:
        gvisor-strict: "enabled"
      tolerations:
      - key: "gvisor-strict"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - cvleap
              - key: component
                operator: In
                values:
                - job-runner
            topologyKey: kubernetes.io/hostname
