version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cvleap-postgres
    environment:
      POSTGRES_DB: cvleap
      POSTGRES_USER: cvleap_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cvleap_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cvleap_user -d cvleap"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cvleap-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-network

  # Backend Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: cvleap-server
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: postgresql://cvleap_user:${POSTGRES_PASSWORD:-cvleap_password}@postgres:5432/cvleap
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-secure-jwt-secret}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      NOVITA_API_KEY: ${NOVITA_API_KEY}
      AZURE_STORAGE_ACCOUNT_NAME: ${AZURE_STORAGE_ACCOUNT_NAME}
      AZURE_STORAGE_ACCOUNT_KEY: ${AZURE_STORAGE_ACCOUNT_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      CLIENT_URL: ${CLIENT_URL:-http://localhost:3001}
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - uploads_data:/app/uploads
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cvleap-network

  # Frontend Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: cvleap-client
    ports:
      - "3001:80"
    depends_on:
      - server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cvleap-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: cvleap-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - client
      - server
    networks:
      - cvleap-network

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  cvleap-network:
    driver: bridge