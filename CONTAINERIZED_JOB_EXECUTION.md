# Containerized Job Execution System - Implementation Guide

## Overview

This document describes the enhanced autonomous job submission system with containerization and human-in-the-loop capabilities implemented for CVLeap.

## 🏗️ Architecture

### Core Components

1. **ContainerService** - Manages Docker containers for isolated job execution
2. **TerminalService** - Provides WebSocket-based real-time terminal streaming
3. **ApprovalService** - Implements human-in-the-loop approval workflow
4. **JobExecutionAuditService** - Comprehensive audit logging and compliance
5. **ContainerizedJobController** - API endpoints for the system

### Integration

The system integrates with the existing `EnhancedJobApplicationService` to provide:
- Containerized execution environment
- Real-time monitoring via WebSocket terminals
- Human approval workflow with parameter modification
- Comprehensive audit trail
- Enhanced security through container isolation

## 🐳 Container Execution

### Features

- **Isolated Execution**: Each job runs in a separate Docker container
- **Resource Management**: Configurable CPU and memory limits
- **Security**: Non-root user, read-only filesystem, network isolation
- **Automatic Cleanup**: Containers are automatically removed after completion

### Configuration

```javascript
containerService.config = {
  baseImage: 'node:18-alpine',
  resourceLimits: {
    memory: '512m',
    cpus: '0.5'
  },
  timeout: 600000, // 10 minutes
  maxConcurrentContainers: 5
}
```

### Usage

```javascript
const containerId = await containerService.createJobContainer(jobData, terminalCallback);
```

## 📺 Real-time Terminal Interface

### Features

- **Live Output Streaming**: Real-time container output via WebSocket
- **Multiple Clients**: Multiple users can monitor the same terminal
- **History Retention**: Terminal history preserved for review
- **Connection Management**: Automatic reconnection and cleanup

### WebSocket Protocol

```javascript
// Connect to terminal
ws = new WebSocket(`ws://localhost:3000/ws/terminal?terminalId=${terminalId}&token=${authToken}`);

// Message types
{
  "type": "terminal_output",
  "terminalId": "uuid",
  "data": {
    "type": "stdout|stderr",
    "content": "log message",
    "timestamp": "2023-12-01T10:00:00Z"
  }
}
```

## 🤝 Human-in-the-Loop Approval

### Approval Workflow

1. **Job Analysis**: Automatic risk assessment and confidence scoring
2. **Approval Request**: Create approval request with recommendations
3. **Human Review**: Users can approve, reject, or modify job parameters
4. **Execution**: Approved jobs proceed to containerized execution

### Auto-Approval Criteria

- Confidence score > 90%
- Known companies
- Low risk assessment
- No custom parameters

### API Usage

```javascript
// Submit job for approval
POST /api/containerized-jobs/submit
{
  "jobTitle": "Senior Software Engineer",
  "company": "Tech Corp",
  "jobUrl": "https://example.com/jobs/123"
}

// Approve job
POST /api/containerized-jobs/approvals/{approvalId}/approve
{
  "comments": "Looks good",
  "modifications": {
    "coverLetter": "Updated cover letter"
  }
}
```

## 📊 Audit and Monitoring

### Audit Categories

- **JOB_APPROVAL**: Job submission, approval, rejection
- **CONTAINER_EXECUTION**: Container lifecycle events
- **TERMINAL_ACCESS**: Terminal session management
- **HUMAN_INTERVENTION**: Manual interventions and modifications
- **SECURITY**: Security-related events

### Compliance Features

- **Comprehensive Logging**: All actions are logged with metadata
- **Audit Trail**: Complete history per job execution
- **Data Export**: CSV and JSON export capabilities
- **Retention Policies**: Configurable data retention (default 90 days)

## 🔧 Configuration

### Environment Variables

```bash
# Enable enhanced features
USE_ENHANCED_AUTOMATION=true
USE_CONTAINERIZED_EXECUTION=true

# Skip Puppeteer download for testing
PUPPETEER_SKIP_DOWNLOAD=true

# Database and cache
DATABASE_URL=postgresql://user:password@localhost:5432/cvleap
REDIS_URL=redis://localhost:6379
```

### Service Configuration

```javascript
config: {
  containerizedExecution: {
    enabled: true,
    requireApproval: true,
    autoApprovalThreshold: 0.9,
    maxConcurrentContainers: 3,
    auditAll: true
  }
}
```

## 📡 API Endpoints

### Enhanced Job Application

- `POST /api/enhanced-applications/submit-containerized` - Submit job for containerized execution
- `GET /api/enhanced-applications/containerized-status` - Get system status
- `GET /api/enhanced-applications/health` - Enhanced health check

### Containerized Jobs

- `POST /api/containerized-jobs/submit` - Submit job with approval workflow
- `GET /api/containerized-jobs/approvals/pending` - Get pending approvals
- `POST /api/containerized-jobs/approvals/{id}/approve` - Approve job
- `POST /api/containerized-jobs/approvals/{id}/reject` - Reject job
- `GET /api/containerized-jobs/containers` - List active containers
- `GET /api/containerized-jobs/terminals/{id}` - Get terminal info
- `GET /api/containerized-jobs/audit/{jobId}` - Get audit trail

### WebSocket Endpoints

- `ws://localhost:3000/ws/terminal?terminalId={id}&token={jwt}` - Terminal streaming

## 🌐 Web Interface

### Terminal Viewer

Access the terminal viewer at: `http://localhost:3000/terminal/terminal.html`

Features:
- Job submission form
- Real-time approval workflow
- Live terminal output
- Connection status monitoring

## 🧪 Testing

### Running Tests

```bash
# Test core services
npm run test

# Test containerized system
node tests/containerizedJobSystem.test.js

# Integration tests
node tests/integrationTest.js
```

### Test Coverage

- Container service functionality
- Terminal streaming
- Approval workflow
- Audit logging
- Service integration
- Health monitoring

## 🚀 Deployment

### Prerequisites

- Docker Engine installed and running
- Node.js 18+ runtime
- PostgreSQL database
- Redis cache

### Docker Deployment

The system includes updated Docker Compose configuration:

```yaml
services:
  server:
    build: ./server
    environment:
      USE_ENHANCED_AUTOMATION: "true"
      USE_CONTAINERIZED_EXECUTION: "true"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # For container management
      - uploads_data:/app/uploads
```

### Security Considerations

1. **Container Isolation**: Each job runs in isolated environment
2. **Resource Limits**: CPU and memory constraints prevent resource exhaustion
3. **Network Security**: Containers isolated from host network by default
4. **User Permissions**: Non-root execution in containers
5. **Audit Trail**: All actions logged for compliance

## 📈 Monitoring and Metrics

### Performance Metrics

- Container execution time
- Success/failure rates
- Resource utilization
- Approval workflow timing

### Health Checks

```javascript
GET /api/containerized-jobs/health
{
  "containers": { "status": "healthy", "activeContainers": 2 },
  "terminals": { "totalTerminals": 5, "activeClients": 3 },
  "approvals": { "pendingApprovals": 1 },
  "audit": { "totalEvents": 150, "recentActivity": { "last24h": 12 } }
}
```

## 🔮 Future Enhancements

### Planned Features

1. **Advanced Container Orchestration**: Kubernetes integration
2. **Multi-Node Execution**: Distributed container execution
3. **Enhanced Security**: Certificate-based authentication
4. **Advanced Analytics**: ML-powered job success prediction
5. **Mobile Interface**: Mobile app for approval workflow

### Scalability Considerations

- Horizontal scaling with Redis clustering
- Container orchestration with Kubernetes
- Database sharding for large-scale deployments
- CDN integration for global distribution

## 📚 Additional Resources

- [Enhanced Job Automation Documentation](ENHANCED_JOB_AUTOMATION.md)
- [Collaboration Implementation Guide](COLLABORATION_IMPLEMENTATION.md)
- [API Reference](docs/api-reference.md)
- [Security Guidelines](docs/security.md)

---

**Implementation Status**: ✅ Complete and tested
**Version**: 1.0.0
**Last Updated**: December 2023