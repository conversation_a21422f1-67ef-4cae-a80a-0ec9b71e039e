# CVleap production Ready

This repository provides a minimal prototype implementing a subset of the features outlined in `requirements.md`.

## Structure

- **client** – React + TypeScript front‑end created with Vite.
- **server** – Node.js + Express API serving a sample resume and the client build.
- **.github/workflows** – CI/CD workflows with Prisma offline support.

## Setup

1. Install dependencies for both client and server:

```bash
cd client
npm install --legacy-peer-deps
npm run build
cd ../server
npm install
```

2. Generate Prisma client (with fallback):

```bash
cd server
npm run generate-safe  # Falls back gracefully if binaries unavailable
```

3. Start the server:

```bash
npm start
```

The server runs on port 3000 and serves the React app from `client/dist`.
It exposes `/api/resume` for loading and saving resume data.
The resume is stored in `server/resume.json` and can be updated via a `POST` request.

## CI/CD

The project includes GitHub Actions workflows that handle Prisma binary downloads and firewall restrictions:
- Automatic CI on push/PR with PostgreSQL testing
- Fallback to SQLite when Prisma binaries are unavailable
- Manual setup workflow for environment preparation

See `.github/workflows/README.md` for detailed information.
