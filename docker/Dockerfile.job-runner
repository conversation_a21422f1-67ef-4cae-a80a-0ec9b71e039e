# Enhanced Security Dockerfile for Job Runner
FROM node:18-alpine

# Security: Create non-root user
RUN addgroup -g 1000 cvleap && adduser -D -u 1000 -G cvleap cvleap

# Security: Update packages and remove vulnerable packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/* /tmp/*

# Security: Set working directory
WORKDIR /app

# Security: Install only production dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Security: Copy application files with proper ownership
COPY --chown=cvleap:cvleap . .

# Security: Set proper file permissions
RUN chmod -R 755 /app && \
    chmod 644 /app/package*.json

# Security: Remove any potential sensitive files
RUN find /app -name "*.key" -o -name "*.pem" -o -name "*.p12" -delete

# Security: Switch to non-root user
USER cvleap

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Security: Set resource limits in runtime
CMD ["node", "job.js"]

# Labels for security scanning
LABEL security.scan="enabled"
LABEL security.profile="strict"
LABEL version="1.0.0"
LABEL description="Secure job execution container for CVLeap"