import { test, expect } from '@playwright/test';

test.describe('CVleap Application', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should load the homepage successfully', async ({ page }) => {
    // Check that the page loads
    await expect(page).toHaveTitle(/CVleap/);
    
    // Check for main navigation elements
    await expect(page.locator('nav')).toBeVisible();
  });

  test('should display the landing page content', async ({ page }) => {
    // Check for key landing page elements
    await expect(page.locator('h1')).toContainText(/CVleap|Resume|Professional/);
    
    // Check for call-to-action buttons
    await expect(page.locator('button, a').filter({ hasText: /Get Started|Sign Up|Create/ })).toBeVisible();
  });

  test('should navigate to registration page', async ({ page }) => {
    // Click on registration link/button
    await page.click('text=Sign Up');
    
    // Check that we're on the registration page
    await expect(page).toHaveURL(/register|signup/);
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    // Click on login link/button
    await page.click('text=Login');
    
    // Check that we're on the login page
    await expect(page).toHaveURL(/login|signin/);
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that mobile navigation works
    const hamburgerMenu = page.locator('[aria-label*="menu"], .hamburger, [data-testid="mobile-menu"]');
    if (await hamburgerMenu.isVisible()) {
      await hamburgerMenu.click();
      await expect(page.locator('nav')).toBeVisible();
    }
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('body')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('body')).toBeVisible();
  });
});

test.describe('Authentication Flow', () => {
  test('should register a new user', async ({ page }) => {
    await page.goto('/');
    await page.click('text=Sign Up');
    
    // Fill out registration form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[name="name"], input[name="firstName"]', 'Test User');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    
    // Submit the form
    await page.click('button[type="submit"], button:has-text("Register")');
    
    // Check for success message or redirect to dashboard
    await expect(page).toHaveURL(/dashboard|profile/, { timeout: 10000 });
  });

  test('should login existing user', async ({ page }) => {
    await page.goto('/');
    await page.click('text=Login');
    
    // Fill out login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    
    // Submit the form
    await page.click('button[type="submit"], button:has-text("Login")');
    
    // Check for successful login
    await expect(page).toHaveURL(/dashboard|profile/, { timeout: 10000 });
  });

  test('should handle invalid login credentials', async ({ page }) => {
    await page.goto('/');
    await page.click('text=Login');
    
    // Fill out login form with invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    // Submit the form
    await page.click('button[type="submit"], button:has-text("Login")');
    
    // Check for error message
    await expect(page.locator('text=/Invalid|Error|Wrong/')).toBeVisible();
  });

  test('should logout user', async ({ page }) => {
    // First login
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await expect(page).toHaveURL(/dashboard/, { timeout: 10000 });
    
    // Click logout
    await page.click('text=Logout');
    
    // Check redirect to home/login
    await expect(page).toHaveURL(/\/$/);
  });
});

test.describe('Resume Builder', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/dashboard/, { timeout: 10000 });
  });

  test('should navigate to resume builder', async ({ page }) => {
    // Click on create new resume or similar
    await page.click('text=/Create|New Resume|Build/');
    
    // Check that we're in the resume builder
    await expect(page).toHaveURL(/resume|builder/);
    await expect(page.locator('h1, h2').filter({ hasText: /Resume|Builder/ })).toBeVisible();
  });

  test('should display template selection', async ({ page }) => {
    await page.goto('/resume/new');
    
    // Check for template gallery
    await expect(page.locator('[data-testid="template-gallery"], .template-grid')).toBeVisible();
    
    // Check for multiple templates
    const templates = page.locator('.template-card, [data-testid="template-card"]');
    await expect(templates).toHaveCount(3, { timeout: 10000 }); // At least 3 templates
  });

  test('should select and customize a template', async ({ page }) => {
    await page.goto('/resume/new');
    
    // Select first template
    await page.click('.template-card:first-child, [data-testid="template-card"]:first-child');
    
    // Check that builder interface loads
    await expect(page.locator('form, .resume-form')).toBeVisible();
    
    // Fill out basic information
    await page.fill('input[name="name"]', 'John Doe');
    await page.fill('input[name="title"]', 'Software Engineer');
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Check for real-time preview
    await expect(page.locator('.preview, [data-testid="resume-preview"]')).toContainText('John Doe');
  });

  test('should save resume draft', async ({ page }) => {
    await page.goto('/resume/new');
    
    // Select template and fill basic info
    await page.click('.template-card:first-child');
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="title"]', 'Test Title');
    
    // Save the resume
    await page.click('button:has-text("Save"), [data-testid="save-button"]');
    
    // Check for success message
    await expect(page.locator('text=/Saved|Success/')).toBeVisible();
  });

  test('should add work experience section', async ({ page }) => {
    await page.goto('/resume/builder');
    
    // Add experience entry
    await page.click('button:has-text("Add Experience"), [data-testid="add-experience"]');
    
    // Fill experience details
    await page.fill('input[name*="company"]', 'Test Company');
    await page.fill('input[name*="position"], input[name*="title"]', 'Software Developer');
    await page.fill('input[name*="start"]', '2020');
    await page.fill('input[name*="end"]', '2023');
    
    // Check preview updates
    await expect(page.locator('.preview')).toContainText('Test Company');
  });

  test('should drag and drop resume sections', async ({ page }) => {
    await page.goto('/resume/builder');
    
    // Check for draggable sections
    const sections = page.locator('[draggable="true"], .draggable-section');
    await expect(sections.first()).toBeVisible();
    
    // Test drag and drop functionality (simplified)
    // In a real test, you would simulate actual drag and drop
    const firstSection = sections.first();
    const secondSection = sections.nth(1);
    
    if (await firstSection.isVisible() && await secondSection.isVisible()) {
      // Check that sections can be reordered
      await expect(firstSection).toBeVisible();
      await expect(secondSection).toBeVisible();
    }
  });
});

test.describe('AI Features', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/dashboard/, { timeout: 10000 });
  });

  test('should access AI enhancement features', async ({ page }) => {
    await page.goto('/resume/builder');
    
    // Look for AI enhancement buttons
    const aiButton = page.locator('button:has-text("AI"), button:has-text("Enhance"), [data-testid="ai-enhance"]');
    if (await aiButton.isVisible()) {
      await aiButton.click();
      
      // Check for AI interface
      await expect(page.locator('.ai-panel, [data-testid="ai-panel"]')).toBeVisible();
    }
  });

  test('should generate content suggestions', async ({ page }) => {
    await page.goto('/resume/builder');
    
    // Fill some basic info first
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="title"]', 'Developer');
    
    // Try to use AI suggestions
    const suggestButton = page.locator('button:has-text("Suggest"), button:has-text("Generate")');
    if (await suggestButton.isVisible()) {
      await suggestButton.click();
      
      // Wait for suggestions to load
      await expect(page.locator('.suggestions, [data-testid="suggestions"]')).toBeVisible({ timeout: 15000 });
    }
  });
});

test.describe('Dashboard and Analytics', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SecurePassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/dashboard/, { timeout: 10000 });
  });

  test('should display dashboard with user data', async ({ page }) => {
    // Check for dashboard elements
    await expect(page.locator('h1, h2').filter({ hasText: /Dashboard|Welcome/ })).toBeVisible();
    
    // Check for user-specific content
    await expect(page.locator('text=Test User, text=<EMAIL>')).toBeVisible();
    
    // Check for resume list or cards
    await expect(page.locator('.resume-card, [data-testid="resume-card"]')).toBeVisible();
  });

  test('should navigate to analytics page', async ({ page }) => {
    // Look for analytics navigation
    const analyticsLink = page.locator('a:has-text("Analytics"), a:has-text("Reports")');
    if (await analyticsLink.isVisible()) {
      await analyticsLink.click();
      
      // Check for analytics content
      await expect(page).toHaveURL(/analytics|reports/);
      await expect(page.locator('.chart, .graph, [data-testid="chart"]')).toBeVisible();
    }
  });

  test('should display job applications tracking', async ({ page }) => {
    // Look for applications section
    const applicationsLink = page.locator('a:has-text("Applications"), a:has-text("Jobs")');
    if (await applicationsLink.isVisible()) {
      await applicationsLink.click();
      
      // Check for applications interface
      await expect(page.locator('.application-list, [data-testid="applications"]')).toBeVisible();
    }
  });
});

test.describe('Performance and Accessibility', () => {
  test('should meet basic performance standards', async ({ page }) => {
    await page.goto('/');
    
    // Measure page load performance
    const loadTime = await page.evaluate(() => {
      return performance.timing.loadEventEnd - performance.timing.navigationStart;
    });
    
    // Page should load within reasonable time (5 seconds)
    expect(loadTime).toBeLessThan(5000);
  });

  test('should be accessible', async ({ page }) => {
    await page.goto('/');
    
    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      // Images should have alt text (can be empty for decorative images)
      expect(alt !== null).toBeTruthy();
    }
    
    // Check for proper heading structure
    await expect(page.locator('h1')).toHaveCount(1); // Should have exactly one h1
    
    // Check for focus management
    await page.keyboard.press('Tab');
    const focusedElement = await page.locator(':focus');
    await expect(focusedElement).toBeVisible();
  });

  test('should work offline (PWA)', async ({ page, context }) => {
    await page.goto('/');
    
    // Wait for service worker to be registered
    await page.waitForFunction(() => 'serviceWorker' in navigator);
    
    // Go offline
    await context.setOffline(true);
    
    // Try to navigate - should show offline page or cached content
    await page.goto('/');
    
    // Should still display something (cached content or offline page)
    await expect(page.locator('body')).toBeVisible();
    
    // Check for offline indicator
    const offlineIndicator = page.locator('text=/offline/i, [data-testid="offline"]');
    if (await offlineIndicator.isVisible()) {
      await expect(offlineIndicator).toBeVisible();
    }
  });
});