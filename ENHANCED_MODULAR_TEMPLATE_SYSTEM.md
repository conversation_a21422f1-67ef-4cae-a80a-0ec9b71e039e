# Enhanced Modular Template System

## Overview

The Enhanced Modular Template System is a comprehensive drag-and-drop resume builder that provides advanced features for creating professional, customizable resumes. This system extends the existing modular resume functionality with enhanced accessibility, performance optimizations, and mobile-first design.

## Key Features

### 🎯 Core Functionality
- **Advanced Drag & Drop**: Intuitive section reordering with visual feedback
- **Snap-to-Grid**: Precise positioning with magnetic snap functionality
- **Real-time Preview**: Live updates with device-responsive views
- **Undo/Redo**: Comprehensive history management with 50-level undo stack

### 📱 Mobile Optimization
- **Touch-Friendly**: Optimized for mobile devices with haptic feedback
- **Gesture Recognition**: Support for swipe, pinch, and long-press gestures
- **Responsive Design**: Seamless experience across all screen sizes
- **Performance**: Smooth 60fps animations on mobile devices

### ♿ Accessibility (WCAG 2.1 AA Compliant)
- **Screen Reader Support**: Full ARIA labels and live announcements
- **Keyboard Navigation**: Complete keyboard accessibility with shortcuts
- **High Contrast**: Support for high contrast and reduced motion preferences
- **Focus Management**: Intelligent focus handling during interactions

### ⚡ Performance Optimizations
- **Lazy Loading**: Components load only when needed
- **Virtual Scrolling**: Efficient rendering of large lists
- **Smart Caching**: Intelligent memoization and state management
- **Bundle Optimization**: Code splitting and tree shaking

## File Structure

```
client/src/
├── components/
│   ├── DragDrop/
│   │   └── EnhancedDragDropComponents.tsx    # Advanced drag-drop components
│   └── ResumeBuilder/
│       └── ModularTemplate/
│           └── EnhancedModularTemplateBuilder.tsx  # Main builder component
├── hooks/
│   └── useDragDrop.ts                        # Drag-drop state management
├── store/
│   └── slices/
│       └── moduleLayoutSlice.ts              # Enhanced Redux state
├── types/
│   └── moduleTypes.ts                        # Enhanced type definitions
├── pages/
│   └── EnhancedModularTemplatePage.tsx       # Demo/showcase page
└── test/
    └── enhancedModularTemplateTests.ts       # Test suite
```

## Component Usage

### Basic Implementation

```tsx
import EnhancedModularTemplateBuilder from '../components/ResumeBuilder/ModularTemplate/EnhancedModularTemplateBuilder';
import { EnhancedDragDropProvider } from '../components/DragDrop/EnhancedDragDropComponents';

function MyResumeBuilder() {
  const handleSave = (template) => {
    // Handle template saving
  };

  const handleExport = (format) => {
    // Handle template export
  };

  return (
    <EnhancedDragDropProvider
      options={{
        snapToGrid: true,
        showGrid: false,
        gridSize: 20,
        autoSpacing: true,
        performance: 'smooth',
        accessibility: true
      }}
    >
      <EnhancedModularTemplateBuilder
        template={currentTemplate}
        onSave={handleSave}
        onExport={handleExport}
      />
    </EnhancedDragDropProvider>
  );
}
```

### Custom Drag-Drop Hook

```tsx
import { useDragDrop } from '../hooks/useDragDrop';

function CustomDragDropComponent({ sections }) {
  const { state, handlers, announcerRef } = useDragDrop({
    sections,
    enableSnapToGrid: true,
    enableAnnouncements: true,
    onSectionReorder: (fromIndex, toIndex) => {
      console.log(`Moved section from ${fromIndex} to ${toIndex}`);
    }
  });

  return (
    <DragDropContext
      onDragStart={handlers.onDragStart}
      onDragUpdate={handlers.onDragUpdate}
      onDragEnd={handlers.onDragEnd}
    >
      {/* Your drag-drop content */}
      <div ref={announcerRef} className="sr-only" aria-live="assertive" />
    </DragDropContext>
  );
}
```

### Enhanced State Management

```tsx
import { useSelector, useDispatch } from 'react-redux';
import {
  addSectionEnhanced,
  reorderSectionsEnhanced,
  toggleSnapToGrid,
  setPerformanceMode
} from '../store/slices/moduleLayoutSlice';

function StateManagementExample() {
  const dispatch = useDispatch();
  const { dragState, performance, accessibility } = useSelector(
    state => state.enhancedModuleLayout
  );

  const addSection = () => {
    dispatch(addSectionEnhanced({
      section: newSection,
      position: 2,
      trackInteraction: true
    }));
  };

  const toggleGrid = () => {
    dispatch(toggleSnapToGrid());
  };

  return (
    <div>
      <button onClick={addSection}>Add Section</button>
      <button onClick={toggleGrid}>
        {dragState.snapToGrid ? 'Disable' : 'Enable'} Grid
      </button>
    </div>
  );
}
```

## Configuration Options

### Drag-Drop Settings

```typescript
interface DragDropSettings {
  snapToGrid: boolean;        // Enable snap-to-grid functionality
  gridSize: number;          // Grid size in pixels (10-50)
  showGrid: boolean;         // Show visual grid overlay
  autoSpacing: boolean;      // Automatic spacing between sections
  collisionDetection: boolean; // Prevent overlapping sections
  smoothAnimations: boolean;  // Enable smooth animations
  touchOptimized: boolean;   // Optimize for touch devices
}
```

### Performance Settings

```typescript
interface PerformanceSettings {
  mode: 'performance' | 'quality';  // Performance vs visual quality
  lazyLoading: boolean;             // Enable lazy loading
  virtualScrolling: boolean;        // Enable virtual scrolling
  debounceTime: number;             // Debounce time for updates (ms)
  throttleTime: number;             // Throttle time for events (ms)
  cacheSize: number;                // Cache size for memoization
}
```

### Accessibility Settings

```typescript
interface AccessibilitySettings {
  enableScreenReaderAnnouncements: boolean;  // Screen reader support
  showKeyboardHints: boolean;                // Show keyboard shortcuts
  highContrastMode: boolean;                 // High contrast mode
  reducedMotion: boolean;                    // Reduced motion support
  focusManagement: boolean;                  // Automatic focus management
  ariaLabels: Record<string, string>;       // Custom ARIA labels
}
```

## Keyboard Shortcuts

- **Ctrl + ↑/↓**: Move selected section up/down
- **Ctrl + G**: Toggle grid visibility
- **Ctrl + H**: Toggle keyboard hints
- **Tab**: Navigate between sections
- **Enter**: Edit selected section
- **Escape**: Cancel current operation
- **Ctrl + Z**: Undo last action
- **Ctrl + Y**: Redo last action

## Mobile Gestures

- **Long Press**: Select section for reordering
- **Drag**: Move section to new position
- **Swipe**: Quick actions (delete, duplicate)
- **Pinch**: Zoom in/out of template
- **Double Tap**: Edit section content

## Performance Guidelines

### Best Practices

1. **Use Lazy Loading**: Enable lazy loading for sections with heavy content
2. **Optimize Images**: Compress images and use appropriate formats
3. **Limit Animations**: Use `performance: 'fast'` on low-end devices
4. **Batch Updates**: Group multiple state changes together
5. **Monitor Memory**: Clear unused sections from cache regularly

### Performance Monitoring

```typescript
import { recordPerformance } from '../store/slices/moduleLayoutSlice';

// Track render performance
const startTime = performance.now();
// ... rendering logic
const endTime = performance.now();

dispatch(recordPerformance({
  renderTime: endTime - startTime
}));
```

## Testing

### Running Tests

```bash
# Run the enhanced template system tests
npm test -- --testPathPattern=enhancedModularTemplateTests

# Run all tests
npm test
```

### Test Coverage

The test suite covers:
- Drag-drop functionality
- State management
- Accessibility features
- Mobile touch interactions
- Performance optimizations
- Snap-to-grid system

## Browser Support

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+
- **Mobile Safari**: 14+
- **Chrome Mobile**: 88+

## API Reference

### Components

#### EnhancedModularTemplateBuilder

Main component for building modular templates with enhanced features.

**Props:**
- `template?: ModularTemplate` - Initial template data
- `className?: string` - Additional CSS classes
- `onSave?: (template: ModularTemplate) => void` - Save callback
- `onExport?: (format: string) => void` - Export callback

#### EnhancedDragDropProvider

Provider component for drag-drop context and settings.

**Props:**
- `children: React.ReactNode` - Child components
- `options?: DragDropProviderOptions` - Configuration options

### Hooks

#### useDragDrop

Hook for managing drag-drop state and interactions.

**Parameters:**
- `sections: ModularSection[]` - Array of sections
- `enableSnapToGrid?: boolean` - Enable snap-to-grid
- `enableAnnouncements?: boolean` - Enable screen reader announcements
- `onSectionReorder?: (from: number, to: number) => void` - Reorder callback

**Returns:**
- `state: DragDropState` - Current drag-drop state
- `handlers: DragDropHandlers` - Event handlers
- `announcerRef: React.RefObject<HTMLDivElement>` - Screen reader announcer ref

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/enhanced-template`
3. Make your changes
4. Add tests for new functionality
5. Ensure accessibility compliance
6. Submit a pull request

## License

This enhanced modular template system is part of the CVLeap project and follows the same licensing terms.