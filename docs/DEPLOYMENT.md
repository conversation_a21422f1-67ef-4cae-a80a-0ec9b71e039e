# CVLeap Deployment Guide

This guide covers deploying CVLeap to production environments with enterprise-grade security, performance, and reliability.

## 🏗️ **Infrastructure Requirements**

### Minimum System Requirements

**Application Server:**
- CPU: 4 cores (8 recommended)
- RAM: 8GB (16GB recommended)
- Storage: 100GB SSD (500GB recommended)
- OS: Ubuntu 20.04 LTS or CentOS 8

**Database Server:**
- CPU: 4 cores (8 recommended)
- RAM: 16GB (32GB recommended)
- Storage: 500GB SSD with backup storage
- PostgreSQL 13+ or MySQL 8+

**Cache Server:**
- CPU: 2 cores (4 recommended)
- RAM: 8GB (16GB recommended)
- Redis 6+ with persistence enabled

### Network Requirements

- **Load Balancer**: HTTPS termination, health checks
- **CDN**: Static asset delivery (CloudFlare, AWS CloudFront)
- **Firewall**: Restrict access to necessary ports only
- **SSL Certificate**: Valid TLS 1.3 certificate

## 🐳 **Docker Deployment**

### Enhanced Security with gVisor (Recommended for Production)

CVLeap supports enhanced container security using gVisor runtime for VM-level isolation without the overhead of traditional VMs.

#### gVisor Installation

```bash
# Install gVisor runtime
curl -fsSL https://gvisor.dev/archive.key | sudo gpg --dearmor -o /usr/share/keyrings/gvisor-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/gvisor-archive-keyring.gpg] https://storage.googleapis.com/gvisor/releases release main" | sudo tee /etc/apt/sources.list.d/gvisor.list > /dev/null
sudo apt-get update && sudo apt-get install -y runsc

# Configure Docker to use gVisor
sudo mkdir -p /etc/docker
cat <<EOF | sudo tee /etc/docker/daemon.json
{
  "runtimes": {
    "runsc": {
      "path": "/usr/bin/runsc"
    }
  }
}
EOF

sudo systemctl restart docker

# Verify gVisor installation
docker run --rm --runtime=runsc hello-world
```

#### gVisor Deployment

```bash
# Deploy with enhanced security
docker-compose -f docker-compose.gvisor.yml up -d

# Monitor gVisor performance
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
```

### Docker Compose (Standard)

1. **Clone Repository**
   ```bash
   git clone https://github.com/moss101/cvleap.git
   cd cvleap
   ```

2. **Configure Environment**
   ```bash
   cp .env.production.example .env.production
   # Edit .env.production with your configuration
   ```

3. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Docker Compose Configuration

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: cvleap
      POSTGRES_USER: cvleap
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cvleap"]
      interval: 30s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## ☁️ **Cloud Deployment**

### AWS Deployment

#### Using AWS ECS with Fargate

1. **Create ECS Cluster**
   ```bash
   aws ecs create-cluster --cluster-name cvleap-production
   ```

2. **Deploy Task Definition**
   ```json
   {
     "family": "cvleap-app",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "1024",
     "memory": "2048",
     "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "cvleap-app",
         "image": "your-account.dkr.ecr.region.amazonaws.com/cvleap:latest",
         "portMappings": [
           {
             "containerPort": 3000,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "NODE_ENV",
             "value": "production"
           }
         ],
         "secrets": [
           {
             "name": "DATABASE_URL",
             "valueFrom": "arn:aws:secretsmanager:region:account:secret:cvleap/database-url"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/cvleap",
             "awslogs-region": "us-east-1",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

3. **Create Service**
   ```bash
   aws ecs create-service \
     --cluster cvleap-production \
     --service-name cvleap-app \
     --task-definition cvleap-app:1 \
     --desired-count 2 \
     --launch-type FARGATE \
     --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
   ```

#### Infrastructure as Code (Terraform)

```hcl
# main.tf
provider "aws" {
  region = var.aws_region
}

# VPC and Networking
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  
  name = "cvleap-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["${var.aws_region}a", "${var.aws_region}b"]
  private_subnets = ["********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = false
  
  tags = {
    Environment = "production"
    Application = "cvleap"
  }
}

# RDS PostgreSQL
resource "aws_db_instance" "postgres" {
  identifier = "cvleap-postgres"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type         = "gp2"
  storage_encrypted    = true
  
  db_name  = "cvleap"
  username = "cvleap"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.postgres.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "cvleap-postgres-final-snapshot"
  
  tags = {
    Environment = "production"
    Application = "cvleap"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "redis" {
  name       = "cvleap-redis-subnet-group"
  subnet_ids = module.vpc.private_subnets
}

resource "aws_elasticache_replication_group" "redis" {
  replication_group_id       = "cvleap-redis"
  description                = "CVLeap Redis cluster"
  
  node_type                  = "cache.t3.medium"
  port                       = 6379
  parameter_group_name       = "default.redis7"
  
  num_cache_clusters         = 2
  automatic_failover_enabled = true
  multi_az_enabled          = true
  
  subnet_group_name = aws_elasticache_subnet_group.redis.name
  security_group_ids = [aws_security_group.redis.id]
  
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                 = var.redis_password
  
  tags = {
    Environment = "production"
    Application = "cvleap"
  }
}

# Application Load Balancer
resource "aws_lb" "app" {
  name               = "cvleap-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets           = module.vpc.public_subnets
  
  enable_deletion_protection = true
  
  tags = {
    Environment = "production"
    Application = "cvleap"
  }
}
```

### Google Cloud Platform (GCP)

#### Using Google Kubernetes Engine (GKE) with gVisor

1. **Create GKE Cluster with gVisor Support**
   ```bash
   # Create cluster with gVisor node pool
   gcloud container clusters create cvleap-cluster \
     --zone=us-central1-a \
     --num-nodes=3 \
     --machine-type=e2-standard-4 \
     --enable-autoscaling \
     --min-nodes=2 \
     --max-nodes=10 \
     --enable-sandbox="type=gvisor"

   # Add gVisor node pool for enhanced security
   gcloud container node-pools create gvisor-pool \
     --cluster=cvleap-cluster \
     --zone=us-central1-a \
     --machine-type=e2-standard-4 \
     --num-nodes=2 \
     --enable-autoscaling \
     --min-nodes=1 \
     --max-nodes=5 \
     --sandbox="type=gvisor" \
     --node-taints="gvisor=true:NoSchedule"
   ```

2. **Configure gVisor RuntimeClass**
   ```bash
   # Apply gVisor runtime configuration
   kubectl apply -f k8s/gvisor-runtime-class.yaml
   ```

3. **Deploy Application with gVisor**
   ```bash
   # Deploy with enhanced security
   kubectl apply -f k8s/cvleap-gvisor-deployment.yaml

   # Verify gVisor runtime
   kubectl get pods -o wide
   kubectl describe pod <pod-name> | grep "Runtime Class"
   ```

4. **Monitor gVisor Performance**
   ```bash
   # Check gVisor metrics
   kubectl top pods --containers

   # Monitor syscall latency
   kubectl logs -l runtime=gvisor | grep "syscall_latency"

   # View gVisor-specific metrics
   kubectl port-forward svc/prometheus 9090:9090
   # Navigate to http://localhost:9090 and query gVisor metrics
   ```

5. **Standard Kubernetes Deployment (without gVisor)**
   ```yaml
   # k8s/deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: cvleap-app
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: cvleap-app
     template:
       metadata:
         labels:
           app: cvleap-app
       spec:
         containers:
         - name: cvleap-app
           image: gcr.io/your-project/cvleap:latest
           ports:
           - containerPort: 3000
           env:
           - name: NODE_ENV
             value: "production"
           - name: DATABASE_URL
             valueFrom:
               secretKeyRef:
                 name: cvleap-secrets
                 key: database-url
           resources:
             requests:
               memory: "1Gi"
               cpu: "500m"
             limits:
               memory: "2Gi"
               cpu: "1000m"
           livenessProbe:
             httpGet:
               path: /health
               port: 3000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /health
               port: 3000
             initialDelaySeconds: 5
             periodSeconds: 5
   ```

## 🔧 **Configuration Management**

### Environment Variables

Create production environment configuration:

```bash
# .env.production
NODE_ENV=production
PORT=3000

# Application
APP_NAME=CVLeap
APP_URL=https://cvleap.com
API_BASE_URL=https://api.cvleap.com

# Database
DATABASE_URL=************************************/cvleap
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_TIMEOUT=30000

# Redis
REDIS_URL=redis://user:password@host:6379
REDIS_SESSION_DB=1
REDIS_CACHE_DB=0

# Security
ENCRYPTION_MASTER_KEY=your-256-bit-encryption-key
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
SESSION_SECRET=your-session-secret
SESSION_TIMEOUT=28800000

# External APIs
OPENAI_API_KEY=your-openai-api-key
INDEED_API_KEY=your-indeed-api-key
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Monitoring
LOG_LEVEL=info
ENABLE_MONITORING=true
SENTRY_DSN=your-sentry-dsn

# Performance
CACHE_DEFAULT_TTL=3600
COMPRESSION_ENABLED=true
RATE_LIMIT_WINDOW=3600000
RATE_LIMIT_MAX=1000

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=cvleap-backups
```

### Secrets Management

#### Using AWS Secrets Manager

```bash
# Store database credentials
aws secretsmanager create-secret \
  --name "cvleap/database" \
  --description "CVLeap database credentials" \
  --secret-string '{"username":"cvleap","password":"secure-password","host":"db.example.com","port":"5432","database":"cvleap"}'

# Store API keys
aws secretsmanager create-secret \
  --name "cvleap/api-keys" \
  --description "CVLeap external API keys" \
  --secret-string '{"openai":"sk-...","indeed":"api-key-...","linkedin_client_id":"client-id","linkedin_client_secret":"client-secret"}'
```

#### Using Kubernetes Secrets

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: cvleap-secrets
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  jwt-secret: <base64-encoded-jwt-secret>
  openai-api-key: <base64-encoded-openai-key>
```

## 🔒 **Security Configuration**

### SSL/TLS Configuration

#### Nginx SSL Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name cvleap.com www.cvleap.com;
    
    ssl_certificate /etc/nginx/ssl/cvleap.com.crt;
    ssl_certificate_key /etc/nginx/ssl/cvleap.com.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    location / {
        proxy_pass http://app:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name cvleap.com www.cvleap.com;
    return 301 https://$server_name$request_uri;
}
```

### Firewall Configuration

```bash
# UFW (Ubuntu Firewall)
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable

# Allow specific IPs for database access
ufw allow from ********/24 to any port 5432
ufw allow from ********/24 to any port 6379
```

## 📊 **Monitoring & Logging**

### Application Monitoring

#### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'cvleap-app'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "CVLeap Production Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### Log Management

#### Centralized Logging with ELK Stack

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

## 🔄 **CI/CD Pipeline**

### GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run test:security

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: |
          docker build -t cvleap:${{ github.sha }} .
          docker tag cvleap:${{ github.sha }} cvleap:latest
      - name: Push to registry
        run: |
          echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
          docker push cvleap:${{ github.sha }}
          docker push cvleap:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # Deploy using your preferred method
          # AWS ECS, Kubernetes, Docker Swarm, etc.
```

## 🔧 **Maintenance**

### Database Maintenance

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="cvleap_backup_$DATE.sql"

pg_dump $DATABASE_URL > /backups/$BACKUP_FILE
gzip /backups/$BACKUP_FILE

# Upload to S3
aws s3 cp /backups/$BACKUP_FILE.gz s3://cvleap-backups/

# Cleanup old backups (keep 30 days)
find /backups -name "*.gz" -mtime +30 -delete
```

### Health Checks

```bash
# health-check.sh
#!/bin/bash

# Check application health
curl -f http://localhost:3000/health || exit 1

# Check database connectivity
pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER || exit 1

# Check Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping || exit 1

echo "All health checks passed"
```

### Performance Tuning

```bash
# PostgreSQL tuning
# postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

# Redis tuning
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🆘 **Troubleshooting**

### Common Issues

1. **High Memory Usage**
   ```bash
   # Check memory usage
   free -h
   docker stats
   
   # Optimize Node.js memory
   export NODE_OPTIONS="--max-old-space-size=2048"
   ```

2. **Database Connection Issues**
   ```bash
   # Check connection pool
   SELECT count(*) FROM pg_stat_activity;
   
   # Increase connection limits
   max_connections = 200
   ```

3. **Redis Connection Issues**
   ```bash
   # Check Redis status
   redis-cli info replication
   
   # Monitor connections
   redis-cli info clients
   ```

### Emergency Procedures

1. **Application Rollback**
   ```bash
   # Docker rollback
   docker service update --rollback cvleap-app
   
   # Kubernetes rollback
   kubectl rollout undo deployment/cvleap-app
   ```

2. **Database Recovery**
   ```bash
   # Restore from backup
   pg_restore -d cvleap /backups/latest_backup.sql
   ```

3. **Scale Up Resources**
   ```bash
   # Scale application instances
   docker service scale cvleap-app=5
   
   # Kubernetes scaling
   kubectl scale deployment cvleap-app --replicas=5
   ```

---

This deployment guide provides comprehensive instructions for deploying CVLeap to production environments with enterprise-grade security, monitoring, and reliability. Follow the specific sections relevant to your infrastructure choice and customize configurations based on your requirements.
