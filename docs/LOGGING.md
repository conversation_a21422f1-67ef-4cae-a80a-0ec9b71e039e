# Enhanced Logging System

This document describes the comprehensive logging system implemented in `server/utils/logger.js`.

## Features

### Core Logging Capabilities
- **Multiple Log Levels**: ERROR, WARN, INFO, DEBUG, TRACE with configurable filtering
- **Colored Console Output**: Different colors for each log level for better readability
- **File Logging**: Optional JSON file logging with automatic rotation
- **Structured Logging**: Consistent JSON format with metadata and context

### Advanced Features
- **Request Correlation IDs**: Automatic generation and tracking across async operations
- **Context Preservation**: Maintains user and request context throughout request lifecycle
- **Performance Metrics Integration**: Coordinates with metrics collector for monitoring
- **Request Middleware**: Comprehensive HTTP request/response logging
- **Database Operation Logging**: Specialized logging for database operations with timing

## Usage Examples

### Basic Logging
```javascript
const { logger } = require('./utils/logger');

// Log at different levels
logger.error('Database connection failed', { error: error.message });
logger.warn('Rate limit approaching', { current: 95, limit: 100 });
logger.info('User logged in', { userId: 123, ip: '***********' });
logger.debug('Processing request', { requestId: 'req-123' });
logger.trace('Function entry', { function: 'processData', args: [...] });
```

### Request Middleware
```javascript
const express = require('express');
const { logger } = require('./utils/logger');

const app = express();

// Add logging middleware - automatically handles correlation IDs
app.use(logger.createRequestMiddleware());

app.get('/api/users', (req, res) => {
  // req.correlationId is automatically available
  logger.info('Processing user request', { 
    correlationId: req.correlationId,
    userId: req.user?.id 
  });
  
  // All subsequent logging will include the correlation context
  res.json({ users: [] });
});
```

### Performance Logging
```javascript
// Database operations
logger.logDatabase('SELECT', 'users', 45, { count: 150, query: 'active users' });

// Custom metrics
logger.logMetric('response_time', 250, 'ms', { endpoint: '/api/data' });
logger.logMetric('memory_usage', 512, 'MB', { process: 'worker-1' });
```

### Context Management
```javascript
// Set context for correlation tracking
logger.setContext({ 
  correlationId: 'req-abc-123',
  userId: 456,
  operation: 'data-processing'
});

// All subsequent logs will include this context
logger.info('Starting data processing');  // Includes correlationId, userId, operation

// Clear context when done
logger.clearContext();
```

## Configuration

### Environment Variables
- `LOG_LEVEL`: Set the minimum log level (ERROR, WARN, INFO, DEBUG, TRACE)
- `ENABLE_FILE_LOGGING`: Set to 'true' to enable file logging
- `LOG_DIR`: Directory for log files (default: server/logs)

### Programmatic Configuration
```javascript
const { Logger } = require('./utils/logger');

const customLogger = new Logger({
  level: 'DEBUG',                    // Minimum log level
  enableConsole: true,               // Console output
  enableFile: true,                  // File output
  logDir: '/var/log/myapp',         // Log directory
  maxFileSize: 50 * 1024 * 1024,    // 50MB max file size
  maxFiles: 10,                      // Keep 10 rotated files
  serviceName: 'my-service'          // Service identifier
});
```

## Log Format

All logs follow a consistent JSON structure:

```json
{
  "timestamp": "2025-06-11T23:47:03.123Z",
  "level": "INFO",
  "service": "cvleap-server",
  "message": "User logged in successfully",
  "correlationId": "05bf57c0304e4a6bc592ab1de2754b52",
  "userId": 123,
  "sessionId": "sess-abc-456",
  "ip": "***********",
  "userAgent": "Mozilla/5.0...",
  "pid": 3705,
  "environment": "development"
}
```

## File Rotation

When file logging is enabled:
- Files are automatically rotated when they exceed `maxFileSize`
- Old files are numbered (.1, .2, .3, etc.)
- Only `maxFiles` rotated files are kept
- Oldest files are automatically deleted

## Integration with Error Handling

The error handler automatically uses the structured logger:

```javascript
const { errorHandler } = require('./middleware/errorHandler');

// Errors are automatically logged with full context
app.use(errorHandler);
```

Error logs include:
- Full error details (message, stack trace, error code)
- Request context (method, URL, user agent, IP)
- Correlation ID for request tracking
- User information if available

## Metrics Integration

The logger optionally integrates with the metrics collector:

```javascript
// This will both log the metric and record it in metrics collector
logger.logMetric('api_response_time', 150, 'ms', { endpoint: '/api/users' });

// Database logging also records performance metrics
logger.logDatabase('SELECT', 'users', 25, { count: 100 });
```

## Best Practices

1. **Use Appropriate Log Levels**:
   - `ERROR`: System errors, exceptions, failures
   - `WARN`: Warning conditions, degraded performance
   - `INFO`: General information, business events
   - `DEBUG`: Detailed diagnostic information
   - `TRACE`: Very detailed execution flow

2. **Include Relevant Context**:
   ```javascript
   logger.info('Processing payment', {
     userId: user.id,
     amount: payment.amount,
     currency: payment.currency,
     paymentMethod: payment.method
   });
   ```

3. **Use Correlation IDs**:
   - Always use the middleware for HTTP requests
   - Pass correlation IDs through async operations
   - Include in error reports and debugging

4. **Performance Logging**:
   - Log database operation timing
   - Track API response times
   - Monitor memory and CPU metrics

5. **Security Considerations**:
   - Never log sensitive data (passwords, tokens, credit cards)
   - Use appropriate log levels for production
   - Ensure log files are properly secured

## Testing

The logging system includes comprehensive tests in `server/tests/logger.test.js`:

```bash
cd server
node tests/logger.test.js
```

Tests cover:
- Log level filtering
- Message formatting
- Context preservation
- File logging and rotation
- Correlation ID generation
- Middleware functionality
- Environment configuration