# CVleap Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying CVleap to production environments with enterprise-grade security, monitoring, and scalability features.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Security Configuration](#security-configuration)
4. [Database Setup](#database-setup)
5. [Container Deployment](#container-deployment)
6. [Kubernetes Deployment](#kubernetes-deployment)
7. [Monitoring Setup](#monitoring-setup)
8. [Backup Configuration](#backup-configuration)
9. [Performance Optimization](#performance-optimization)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Minimum Production Requirements:**
- CPU: 4 cores
- RAM: 8GB
- Storage: 50GB SSD
- Network: 1Gbps

**Recommended Production Requirements:**
- CPU: 8 cores
- RAM: 16GB
- Storage: 100GB SSD
- Network: 10Gbps

### Software Dependencies

- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.24+ (for K8s deployment)
- PostgreSQL 15+ or SQLite 3.38+
- Redis 7.0+
- Node.js 18+ (for development)

### External Services

- SSL Certificate (Let's Encrypt recommended)
- Email Service (SendGrid, AWS SES, or SMTP)
- Cloud Storage (Azure Blob, AWS S3) - Optional
- Monitoring Service (Prometheus, Grafana) - Optional

## Environment Setup

### 1. Create Production Environment File

```bash
# Copy the sample environment file
cp .env.docker .env.production

# Edit with production values
nano .env.production
```

### 2. Required Environment Variables

**Critical Security Variables:**
```bash
# JWT Configuration
JWT_SECRET=your_256_bit_secret_key_here
JWT_REFRESH_SECRET=your_256_bit_refresh_secret_key_here

# Encryption
ENCRYPTION_MASTER_KEY=$(openssl rand -base64 32)

# Database
DATABASE_URL=******************************************************/cvleap
POSTGRES_PASSWORD=secure_database_password

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=secure_redis_password
```

**Application Configuration:**
```bash
NODE_ENV=production
APP_NAME=cvleap
PORT=3000
CLIENT_URL=https://your-domain.com

# CORS Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000
```

## Security Configuration

### 1. SSL/TLS Setup

**Using Let's Encrypt with Certbot:**
```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 3. Security Headers

The application automatically sets security headers:
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin
- Permissions-Policy: geolocation=(), microphone=(), camera=()

## Monitoring Setup

### 1. Application Metrics

The application exposes metrics at `/metrics` in Prometheus format:

```bash
# View metrics
curl http://localhost:3000/metrics

# Key metrics monitored:
# - http_requests_total
# - http_request_duration_ms
# - db_queries_total
# - db_query_duration_ms
# - cache_hits_total
# - ai_requests_total
```

### 2. Health Monitoring

```bash
# Basic health check
curl http://localhost:3000/health

# Advanced health check with dependencies
curl http://localhost:3000/health/advanced

# Kubernetes probes
kubectl describe pod cvleap-server-xxx -n cvleap
```

## Backup Configuration

### 1. Automated Backup Setup

```bash
# Configure backup script
cp scripts/backup.sh /usr/local/bin/cvleap-backup
chmod +x /usr/local/bin/cvleap-backup

# Set environment variables for backup
export BACKUP_ENCRYPTION=true
export BACKUP_ENCRYPTION_KEY="your_backup_key"
export BACKUP_S3_ENABLED=true
export BACKUP_S3_BUCKET="your-backup-bucket"
```

### 2. Schedule Automated Backups

```bash
# Add to crontab
crontab -e

# Daily backup at 2 AM
0 2 * * * /usr/local/bin/cvleap-backup backup

# Weekly cleanup
0 3 * * 0 /usr/local/bin/cvleap-backup cleanup
```

## Troubleshooting

### 1. Common Issues

**Application Won't Start:**
```bash
# Check logs
docker-compose logs server

# Common issues:
# - Missing environment variables
# - Database connection failure
# - Port conflicts
```

**Database Connection Issues:**
```bash
# Test database connection
psql -h localhost -U cvleap_user -d cvleap

# Check database logs
docker-compose logs postgres
```

## Security Checklist

- [ ] SSL/TLS certificates configured and auto-renewing
- [ ] Firewall rules properly configured
- [ ] Strong passwords and secrets generated
- [ ] Database access restricted
- [ ] Application security headers enabled
- [ ] Rate limiting configured
- [ ] Audit logging enabled
- [ ] Backup encryption enabled
- [ ] Monitoring and alerting configured
- [ ] Security patches up to date

---

*For complete deployment instructions, see the full documentation.*