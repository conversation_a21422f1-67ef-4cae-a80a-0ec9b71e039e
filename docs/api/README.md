# CVLeap API Documentation

## Overview

The CVLeap API provides a comprehensive RESTful interface for managing users, resumes, job applications, and AI-powered career optimization features. All endpoints are secured with JWT authentication and role-based access control.

## Base URL

```
Production: https://api.cvleap.com/api/v1
Development: http://localhost:3000/api/v1
```

## Authentication

### JWT Token Authentication

All API requests require a valid JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### Obtaining Tokens

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "role": "user"
    }
  }
}
```

### Token Refresh

```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

## Rate Limiting

API requests are rate-limited to prevent abuse:

- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour
- **Login attempts**: 5 attempts per 15 minutes per IP

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Error Handling

The API uses standard HTTP status codes and returns errors in a consistent format:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  }
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | VALIDATION_ERROR | Request validation failed |
| 401 | UNAUTHORIZED | Authentication required |
| 403 | FORBIDDEN | Insufficient permissions |
| 404 | NOT_FOUND | Resource not found |
| 429 | RATE_LIMITED | Rate limit exceeded |
| 500 | INTERNAL_ERROR | Server error |

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "acceptTerms": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### POST /auth/login
Authenticate user and receive tokens.

#### POST /auth/logout
Invalidate current session and tokens.

#### POST /auth/refresh
Refresh access token using refresh token.

#### POST /auth/forgot-password
Request password reset email.

#### POST /auth/reset-password
Reset password using reset token.

### Users

#### GET /users/profile
Get current user's profile information.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "profile": {
      "title": "Software Engineer",
      "location": "San Francisco, CA",
      "experience": "5 years",
      "skills": ["JavaScript", "React", "Node.js"]
    },
    "preferences": {
      "jobAlerts": true,
      "newsletter": false
    }
  }
}
```

#### PUT /users/profile
Update user profile information.

#### POST /users/upload-avatar
Upload user profile picture.

#### DELETE /users/account
Delete user account (requires password confirmation).

### Resumes

#### GET /resumes
Get user's resumes.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `status` (string): Filter by status (draft, published, archived)

**Response:**
```json
{
  "success": true,
  "data": {
    "resumes": [
      {
        "id": "resume-uuid",
        "title": "Software Engineer Resume",
        "status": "published",
        "aiScore": 85,
        "lastModified": "2024-01-15T10:30:00Z",
        "sections": {
          "personalInfo": {...},
          "experience": [...],
          "education": [...],
          "skills": [...]
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 3,
      "pages": 1
    }
  }
}
```

#### POST /resumes
Create a new resume.

#### GET /resumes/:id
Get specific resume by ID.

#### PUT /resumes/:id
Update resume content.

#### DELETE /resumes/:id
Delete resume.

#### POST /resumes/:id/analyze
Get AI analysis and optimization suggestions for resume.

**Response:**
```json
{
  "success": true,
  "data": {
    "score": 85,
    "analysis": {
      "strengths": [
        "Strong technical skills section",
        "Quantified achievements"
      ],
      "improvements": [
        "Add more action verbs",
        "Include relevant keywords"
      ],
      "atsCompatibility": 92,
      "readabilityScore": 88
    },
    "suggestions": [
      {
        "section": "experience",
        "type": "enhancement",
        "message": "Consider adding metrics to this achievement",
        "priority": "high"
      }
    ]
  }
}
```

#### POST /resumes/:id/export
Export resume in various formats (PDF, DOCX, HTML).

### Jobs

#### GET /jobs/search
Search for job opportunities.

**Query Parameters:**
- `q` (string): Search query
- `location` (string): Job location
- `remote` (boolean): Remote jobs only
- `salary_min` (number): Minimum salary
- `salary_max` (number): Maximum salary
- `experience` (string): Experience level
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "id": "job-uuid",
        "title": "Senior Software Engineer",
        "company": "Tech Corp",
        "location": "San Francisco, CA",
        "remote": true,
        "salary": {
          "min": 120000,
          "max": 180000,
          "currency": "USD"
        },
        "description": "Job description...",
        "requirements": ["5+ years experience", "React", "Node.js"],
        "postedDate": "2024-01-15T10:30:00Z",
        "source": "indeed",
        "matchScore": 92
      }
    ],
    "pagination": {...},
    "filters": {
      "appliedFilters": {...},
      "availableFilters": {...}
    }
  }
}
```

#### GET /jobs/:id
Get detailed job information.

#### POST /jobs/:id/apply
Apply for a job position.

#### GET /jobs/recommendations
Get personalized job recommendations.

### Applications

#### GET /applications
Get user's job applications.

#### POST /applications
Create new job application.

#### GET /applications/:id
Get application details.

#### PUT /applications/:id
Update application status or notes.

#### DELETE /applications/:id
Delete application.

### AI Services

#### POST /ai/resume-optimize
Get AI-powered resume optimization suggestions.

#### POST /ai/cover-letter
Generate AI cover letter for specific job.

#### POST /ai/interview-prep
Get AI-generated interview questions and tips.

#### POST /ai/salary-insights
Get salary insights and market data.

### Analytics

#### GET /analytics/dashboard
Get user dashboard analytics.

#### GET /analytics/resume-performance
Get resume performance metrics.

#### GET /analytics/job-market
Get job market insights and trends.

## Webhooks

CVLeap supports webhooks for real-time notifications:

### Webhook Events

- `resume.analyzed` - Resume analysis completed
- `job.matched` - New job match found
- `application.status_changed` - Application status updated

### Webhook Payload

```json
{
  "event": "resume.analyzed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "resumeId": "resume-uuid",
    "userId": "user-uuid",
    "score": 85,
    "improvements": 3
  }
}
```

## SDKs and Libraries

### JavaScript/Node.js

```bash
npm install @cvleap/sdk
```

```javascript
import CVLeap from '@cvleap/sdk';

const client = new CVLeap({
  apiKey: 'your-api-key',
  baseURL: 'https://api.cvleap.com/api/v1'
});

// Get user profile
const profile = await client.users.getProfile();

// Search jobs
const jobs = await client.jobs.search({
  q: 'software engineer',
  location: 'San Francisco'
});
```

### Python

```bash
pip install cvleap-python
```

```python
from cvleap import CVLeap

client = CVLeap(api_key='your-api-key')

# Get resumes
resumes = client.resumes.list()

# Analyze resume
analysis = client.resumes.analyze(resume_id)
```

## Testing

### Sandbox Environment

Use the sandbox environment for testing:

```
Sandbox URL: https://sandbox-api.cvleap.com/api/v1
```

### Test Data

The sandbox includes test data for development:

- Test user accounts
- Sample resumes
- Mock job listings
- Simulated AI responses

### API Testing Tools

- **Postman Collection**: [Download](https://api.cvleap.com/postman)
- **OpenAPI Spec**: [Download](https://api.cvleap.com/openapi.json)
- **Insomnia Collection**: [Download](https://api.cvleap.com/insomnia)

## Support

- **API Status**: [status.cvleap.com](https://status.cvleap.com)
- **Documentation**: [docs.cvleap.com](https://docs.cvleap.com)
- **Support**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub Issues**: [github.com/moss101/cvleap/issues](https://github.com/moss101/cvleap/issues)

## Changelog

### v1.2.0 (2024-01-15)
- Added AI-powered job matching
- Enhanced resume analysis
- Improved rate limiting
- Added webhook support

### v1.1.0 (2024-01-01)
- Added cover letter generation
- Enhanced security features
- Improved error handling
- Added analytics endpoints

### v1.0.0 (2023-12-01)
- Initial API release
- Core authentication and user management
- Resume management and analysis
- Job search functionality
