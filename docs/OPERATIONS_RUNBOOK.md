# CVleap Operations Runbook

## Quick Reference

### Emergency Contacts
- **On-Call Engineer**: <EMAIL>
- **DevOps Team**: <EMAIL>  
- **Security Team**: <EMAIL>
- **Database Admin**: <EMAIL>

### Critical Services
- **Application**: http://localhost:3000/health
- **Database**: PostgreSQL on port 5432
- **Cache**: Redis on port 6379
- **Monitoring**: http://localhost:3000/metrics

## Common Procedures

### 1. Health Check Verification

```bash
# Basic health check
curl -f http://localhost:3000/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": "1h 30m 45s",
  "version": "1.0.0",
  "environment": "production"
}
```

### 2. Service Restart

**Docker Compose:**
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart server
docker-compose restart postgres
docker-compose restart redis
```

**Kubernetes:**
```bash
# Restart server deployment
kubectl rollout restart deployment/cvleap-server -n cvleap

# Check rollout status
kubectl rollout status deployment/cvleap-server -n cvleap
```

### 3. Log Analysis

**Application Logs:**
```bash
# View real-time logs
docker-compose logs -f server

# Search for errors
docker-compose logs server | grep ERROR

# Check last 100 lines
docker-compose logs --tail=100 server
```

**Audit Logs:**
```bash
# View audit logs
cat logs/audit/audit-$(date +%Y-%m-%d).log

# Search for security events
grep "SECURITY" logs/audit/*.log

# Failed authentication attempts
grep "AUTH.*FAILED" logs/audit/*.log
```

### 4. Performance Monitoring

**Check Application Metrics:**
```bash
# View Prometheus metrics
curl -s http://localhost:3000/metrics | grep -E "(http_requests|db_queries|memory_usage)"

# Get metrics summary
curl -s http://localhost:3000/health/metrics-summary
```

**Database Performance:**
```sql
-- Check active connections
SELECT count(*) as active_connections FROM pg_stat_activity;

-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

**Memory and CPU Usage:**
```bash
# Container resource usage
docker stats

# System resources
htop
free -h
df -h
```

## Incident Response

### 1. Service Down

**Immediate Actions:**
1. Check service health: `curl http://localhost:3000/health`
2. Check container status: `docker-compose ps`
3. Review recent logs: `docker-compose logs --tail=50 server`
4. Check system resources: `htop`, `df -h`

**Resolution Steps:**
```bash
# 1. Restart services
docker-compose restart

# 2. If restart fails, rebuild
docker-compose down
docker-compose up -d --build

# 3. Check health after restart
curl -f http://localhost:3000/health
```

### 2. Database Issues

**Database Connection Failures:**
```bash
# Check database container
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Test connection manually
docker-compose exec postgres psql -U cvleap_user -d cvleap -c "SELECT 1;"
```

**High Database Load:**
```sql
-- Check active queries
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- Kill long-running query if needed
SELECT pg_terminate_backend(PID);
```

### 3. Memory/CPU Issues

**High Memory Usage:**
```bash
# Check memory usage by container
docker stats --no-stream

# Check application heap usage
curl -s http://localhost:3000/metrics | grep memory_usage

# Restart service if memory leak suspected
docker-compose restart server
```

**High CPU Usage:**
```bash
# Check CPU usage
top -p $(docker-compose exec server pgrep node)

# Check for CPU-intensive queries
curl -s http://localhost:3000/metrics | grep -E "(http_request_duration|db_query_duration)"
```

### 4. Security Incidents

**Suspicious Activity:**
```bash
# Check recent security events
grep "SECURITY" logs/audit/audit-$(date +%Y-%m-%d).log | tail -20

# Check failed login attempts
grep "AUTH.*FAILED" logs/audit/*.log | tail -20

# Check rate limiting triggers
curl -s http://localhost:3000/metrics | grep rate_limit
```

**Immediate Response:**
1. Document the incident
2. Check audit logs for affected users
3. Consider temporary rate limit adjustment
4. Notify security team if needed

## Maintenance Procedures

### 1. Planned Maintenance

**Pre-maintenance Checklist:**
- [ ] Schedule maintenance window
- [ ] Notify users of planned downtime
- [ ] Create backup before changes
- [ ] Prepare rollback plan

**Maintenance Steps:**
```bash
# 1. Create backup
./scripts/backup.sh backup

# 2. Put application in maintenance mode
docker-compose exec server kill -USR2 1

# 3. Perform maintenance tasks
# (updates, migrations, etc.)

# 4. Restart services
docker-compose restart

# 5. Verify functionality
curl -f http://localhost:3000/health
```

### 2. Database Maintenance

**Regular Maintenance:**
```sql
-- Update table statistics
ANALYZE;

-- Vacuum and reindex
VACUUM ANALYZE;
REINDEX DATABASE cvleap;

-- Check database size
SELECT pg_size_pretty(pg_database_size('cvleap'));
```

**Weekly Maintenance:**
```bash
# Database backup
./scripts/backup.sh backup

# Log rotation
find logs/ -name "*.log" -mtime +7 -delete

# Clean up old Docker images
docker image prune -f
```

### 3. Security Updates

**Update Process:**
```bash
# 1. Update base images
docker-compose pull

# 2. Rebuild containers
docker-compose build --no-cache

# 3. Deploy updates
docker-compose up -d

# 4. Verify security
./scripts/security-check.sh
```

## Backup and Recovery

### 1. Backup Verification

```bash
# Check backup status
./scripts/backup.sh test

# List recent backups
ls -la backups/ | head -10

# Verify backup integrity
sha256sum -c backups/latest_backup.sha256
```

### 2. Disaster Recovery

**Database Recovery:**
```bash
# Stop application
docker-compose stop server

# Restore database
cat backups/cvleap_backup_YYYYMMDD_database.sql | \
  docker-compose exec -T postgres psql -U cvleap_user -d cvleap

# Start application
docker-compose start server
```

**Full System Recovery:**
```bash
# 1. Deploy infrastructure
docker-compose up -d postgres redis

# 2. Restore database
# (see database recovery above)

# 3. Restore application files
tar -xzf backups/cvleap_backup_YYYYMMDD_files.tar.gz

# 4. Start application
docker-compose up -d server client
```

## Scaling Operations

### 1. Horizontal Scaling

**Docker Swarm:**
```bash
# Scale server instances
docker service scale cvleap_server=5

# Check service status
docker service ls
```

**Kubernetes:**
```bash
# Scale deployment
kubectl scale deployment cvleap-server --replicas=5 -n cvleap

# Check pod status
kubectl get pods -n cvleap
```

### 2. Performance Tuning

**Database Optimization:**
```sql
-- Increase connection limits if needed
ALTER SYSTEM SET max_connections = 200;
SELECT pg_reload_conf();

-- Optimize query performance
CREATE INDEX CONCURRENTLY idx_performance ON table_name (column_name);
```

**Application Tuning:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=2048"

# Adjust rate limits for high traffic
export RATE_LIMIT_MAX=2000
```

## Monitoring and Alerting

### 1. Key Metrics to Monitor

**Application Metrics:**
- Request rate (requests/second)
- Response time (95th percentile < 500ms)
- Error rate (< 1%)
- Memory usage (< 80% of limit)
- CPU usage (< 70% of limit)

**Infrastructure Metrics:**
- Database connections (< 80% of max)
- Disk space (< 85% full)
- Network I/O
- Container health

### 2. Alert Thresholds

**Critical Alerts:**
- Service down (0 healthy instances)
- Database down
- Error rate > 5%
- Response time > 2000ms (95th percentile)

**Warning Alerts:**
- Error rate > 1%
- Response time > 500ms (95th percentile)
- Memory usage > 80%
- Disk space > 85%

### 3. Alert Response

**Service Down:**
1. Check service health endpoints
2. Review application logs
3. Restart services if needed
4. Escalate if issue persists > 5 minutes

**High Error Rate:**
1. Check error logs for patterns
2. Identify affected endpoints
3. Check database and cache status
4. Consider rate limiting if attack suspected

## Contact Information

### Escalation Matrix

**Level 1 - On-Call Engineer**
- Initial response: 5 minutes
- Resolution target: 30 minutes
- Scope: Service restarts, basic troubleshooting

**Level 2 - DevOps Team**
- Response: 15 minutes
- Resolution target: 2 hours
- Scope: Infrastructure issues, deployments

**Level 3 - Architecture Team**
- Response: 1 hour
- Resolution target: 4 hours
- Scope: Complex technical issues, design changes

### Communication Channels

- **Slack**: #cvleap-ops
- **Email**: <EMAIL>
- **Phone**: ******-CVL-EAPS (emergency only)
- **Status Page**: https://status.cvleap.com

---

*Keep this runbook updated with any changes to procedures or contact information.*