# Environment variables for Docker Compose
# Copy this file to .env and update the values

# Database
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=********************************************************************/cvleap

# Application
JWT_SECRET=your_super_secure_jwt_secret_key_here
CLIENT_URL=http://localhost:3001

# AI Services (Optional - set only if you have API keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# File Storage (Optional)
AZURE_STORAGE_ACCOUNT_NAME=your_azure_storage_account
AZURE_STORAGE_ACCOUNT_KEY=your_azure_storage_key

# Email Service (Optional)
SENDGRID_API_KEY=your_sendgrid_api_key

# Payment Processing (Optional)
STRIPE_SECRET_KEY=your_stripe_secret_key