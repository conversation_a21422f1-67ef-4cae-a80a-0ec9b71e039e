# CVLeap Template Export System Documentation

## 🎯 **Overview**

The CVLeap template export system provides comprehensive document generation capabilities, replacing mock implementations with fully functional PDF, Word, and Google Docs export functionality. The system supports multiple professional resume templates with customization options and enterprise-grade security.

## ✅ **Features Implemented**

### **1. PDF Generation System**
- ✅ **Professional PDF generation** using Puppeteer with high-quality rendering
- ✅ **Multiple template layouts** (Modern, Classic, Creative, ATS-friendly)
- ✅ **Dynamic content injection** from user profile and resume data
- ✅ **PDF optimization** for file size and print quality
- ✅ **Watermarking and branding** options for CVLeap
- ✅ **Custom fonts and styling** with proper spacing and formatting

### **2. Word Document (DOCX) Export System**
- ✅ **Real DOCX generation** using docx library
- ✅ **Professional Word templates** matching PDF layouts
- ✅ **Dynamic content population** from user data
- ✅ **Proper Word formatting** (styles, fonts, tables, bullet points)
- ✅ **Cross-platform compatibility** for Microsoft Word
- ✅ **Template customization** options (colors, fonts, sections)

### **3. Google Docs Export Integration**
- ✅ **Google Docs API integration** with OAuth2 authentication
- ✅ **Programmatic document creation** and population
- ✅ **Real-time collaboration** features through sharing
- ✅ **Template synchronization** and version control
- ✅ **Permission management** for shared documents
- ✅ **Rate limiting and error handling** for Google API

## 🏗️ **Architecture Overview**

### **Service Layer Architecture**
```
┌─────────────────────────────────────────────────────────┐
│                Template Export Routes                    │
│  • Export Endpoints    • Preview Generation             │
│  • OAuth Integration  • Statistics & Analytics          │
├─────────────────────────────────────────────────────────┤
│              Template Export Service                     │
│  • PDF Generation     • Word Document Creation          │
│  • Google Docs API    • Template Management             │
├─────────────────────────────────────────────────────────┤
│  Puppeteer (PDF)     │  docx Library    │  Google APIs  │
│  • HTML Rendering    │  • DOCX Creation │  • Docs API   │
│  • PDF Generation    │  • Formatting    │  • Drive API  │
├─────────────────────────────────────────────────────────┤
│              Template Engine (Handlebars)               │
│  • Template Compilation  • Data Injection               │
│  • Customization        • Security Sanitization        │
└─────────────────────────────────────────────────────────┘
```

## 🚀 **API Endpoints**

### **Template Export Endpoints**

#### `POST /api/templates/:id/export`
Export template in multiple formats with comprehensive options.

**Request:**
```bash
curl -X POST /api/templates/1/export \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "formats": ["pdf", "docx", "googledocs"],
    "resumeData": {
      "personalInfo": {
        "fullName": "John Doe",
        "email": "<EMAIL>",
        "phone": "+****************",
        "location": "San Francisco, CA",
        "title": "Senior Software Engineer"
      },
      "summary": "Experienced software engineer...",
      "experience": [...],
      "education": [...],
      "skills": {...}
    },
    "customization": {
      "colors": {
        "primary": "#2563eb",
        "secondary": "#64748b"
      },
      "fonts": {
        "primary": "Arial, sans-serif",
        "secondary": "Georgia, serif"
      }
    },
    "options": {
      "quality": "high",
      "googleAccessToken": "ya29.a0AfH6..."
    },
    "includeWatermark": false,
    "language": "en"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "exportId": "uuid-export-id",
    "results": {
      "pdf": {
        "success": true,
        "filename": "resume_modern_1234567890.pdf",
        "url": "/api/templates/download/resume_modern_1234567890.pdf",
        "size": 245760,
        "metadata": {
          "template": "modern",
          "pages": 2,
          "quality": "high",
          "watermarked": false
        }
      },
      "docx": {
        "success": true,
        "filename": "resume_modern_1234567890.docx",
        "url": "/api/templates/download/resume_modern_1234567890.docx",
        "size": 156432,
        "metadata": {
          "template": "modern",
          "wordVersion": "2016+",
          "atsCompatible": true
        }
      },
      "googledocs": {
        "success": true,
        "shareUrl": "https://docs.google.com/document/d/doc-id/edit",
        "metadata": {
          "documentId": "google-doc-id",
          "sharing": "private",
          "collaborative": true
        }
      }
    },
    "metadata": {
      "templateId": "1",
      "templateName": "Modern Professional",
      "formats": ["pdf", "docx", "googledocs"],
      "processingTime": 3500,
      "generatedAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### `POST /api/templates/:id/preview`
Generate template preview with sample or provided data.

**Request:**
```bash
curl -X POST /api/templates/1/preview \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "format": "pdf",
    "customization": {
      "colors": { "primary": "#ff0000" }
    }
  }'
```

#### `GET /api/templates/export/templates`
Get available export templates with metadata.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "modern",
      "name": "Modern Professional",
      "description": "Clean, modern design with accent colors",
      "atsOptimized": true,
      "colors": {
        "primary": "#2563eb",
        "secondary": "#64748b",
        "accent": "#0ea5e9"
      },
      "supportedFormats": ["pdf", "docx", "googledocs"]
    }
  ]
}
```

### **Google Docs Integration Endpoints**

#### `GET /api/templates/export/google/auth-url`
Generate Google OAuth URL for Docs integration.

**Response:**
```json
{
  "success": true,
  "data": {
    "authUrl": "https://accounts.google.com/oauth2/auth?client_id=...",
    "state": "user-id"
  }
}
```

#### `POST /api/templates/export/google/callback`
Handle Google OAuth callback and exchange code for tokens.

**Request:**
```json
{
  "code": "authorization-code-from-google"
}
```

### **Utility Endpoints**

#### `POST /api/templates/export/validate`
Validate resume data before export.

#### `GET /api/templates/export/statistics`
Get comprehensive export statistics and performance metrics.

#### `GET /api/templates/download/:filename`
Download generated export files with proper content types.

## 🔧 **Template System**

### **Available Templates**

| Template | Description | ATS Optimized | Best For |
|----------|-------------|---------------|----------|
| **Modern** | Clean, modern design with accent colors | ✅ | Technology, Finance, Healthcare |
| **Classic** | Traditional format for conservative industries | ✅ | Executive, Management, Consulting |
| **Creative** | Eye-catching design for creative professionals | ❌ | Design, Marketing, Media |
| **ATS** | Specifically optimized for ATS parsing | ✅ | All industries (maximum compatibility) |

### **Template Customization Options**

```javascript
{
  "customization": {
    "colors": {
      "primary": "#2563eb",    // Main heading color
      "secondary": "#64748b",  // Subheading color
      "accent": "#0ea5e9"      // Accent elements
    },
    "fonts": {
      "primary": "Arial, sans-serif",    // Main text font
      "secondary": "Georgia, serif"      // Heading font
    },
    "layout": "standard",               // Layout variant
    "spacing": "normal"                 // Spacing density
  }
}
```

### **Template Structure**

Each template supports the following sections:
- **Header** - Name, title, contact information
- **Professional Summary** - Career overview
- **Work Experience** - Employment history with achievements
- **Education** - Academic background
- **Skills** - Technical and soft skills by category
- **Projects** - Portfolio projects with technologies
- **Certifications** - Professional certifications

## 🔐 **Security Features**

### **Data Sanitization**
- **Input validation** and sanitization to prevent XSS attacks
- **Template injection protection** against malicious Handlebars code
- **File path validation** to prevent directory traversal
- **Content filtering** to remove potentially harmful patterns

### **Access Control**
- **JWT authentication** required for all export endpoints
- **User-based file ownership** and access control
- **Rate limiting** to prevent abuse (configurable per user)
- **Secure file storage** with proper access permissions

### **Google Docs Security**
- **OAuth2 authentication** with proper scope management
- **Token encryption** and secure storage
- **Permission validation** before document creation
- **API rate limiting** compliance with Google's policies

## 📊 **Performance & Monitoring**

### **Performance Metrics**
- **Export processing time** tracking per format
- **Success/failure rates** by template and format
- **File size optimization** monitoring
- **Concurrent export handling** with queue management

### **Monitoring Features**
- **Export statistics** with detailed breakdowns
- **Error tracking** and alerting
- **Performance analytics** for optimization
- **Usage patterns** analysis for capacity planning

## 🧪 **Testing Coverage**

### **Test Categories**
- ✅ **Unit tests** for all export format handlers
- ✅ **Integration tests** for template generation workflows
- ✅ **Security tests** for input validation and sanitization
- ✅ **Performance tests** for concurrent export scenarios
- ✅ **Error handling tests** for API failures and timeouts
- ✅ **Google Docs API tests** with OAuth flow simulation

### **Test Scenarios**
- ✅ **Valid export requests** with complete resume data
- ✅ **Invalid data handling** with missing required fields
- ✅ **Template customization** with various options
- ✅ **Multi-format exports** with different combinations
- ✅ **Google OAuth flow** end-to-end testing
- ✅ **Error recovery** from API failures
- ✅ **Concurrent exports** and resource management

## 🔄 **Configuration**

### **Environment Variables**

```bash
# Google Docs Integration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# Template Settings
TEMPLATE_CACHE_SIZE=100
TEMPLATE_CLEANUP_INTERVAL=3600000  # 1 hour
EXPORT_DIRECTORY=./exports
TEMPLATES_DIRECTORY=./templates

# Performance Settings
MAX_CONCURRENT_EXPORTS=5
EXPORT_TIMEOUT=30000  # 30 seconds
PDF_QUALITY=high
WATERMARK_ENABLED=true

# Security Settings
RATE_LIMIT_EXPORTS=10  # per 15 minutes
SANITIZE_INPUT=true
VALIDATE_TEMPLATES=true
```

### **Template Configuration**

Templates are configured in the service with:
- **Color schemes** for different professional contexts
- **Font combinations** optimized for readability
- **Layout options** for various content densities
- **ATS optimization** settings for parsing compatibility

## 🚨 **Error Handling**

### **Common Error Codes**

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `EXPORT_FAILED` | General export failure | 500 |
| `INVALID_FORMAT` | Unsupported export format | 400 |
| `TEMPLATE_NOT_FOUND` | Template ID not found | 404 |
| `INVALID_DATA` | Resume data validation failed | 400 |
| `GOOGLE_AUTH_FAILED` | Google OAuth authentication failed | 401 |
| `PROCESSING_TIMEOUT` | Export processing timeout | 408 |
| `RATE_LIMIT_EXCEEDED` | Too many export requests | 429 |

### **Error Response Format**
```json
{
  "success": false,
  "error": "Human readable error message",
  "code": "ERROR_CODE",
  "metadata": {
    "templateId": "1",
    "formats": ["pdf"],
    "processingTime": 1500
  }
}
```

## 🔧 **Troubleshooting**

### **Common Issues**

1. **PDF generation failures**
   - Check Puppeteer installation and dependencies
   - Verify system fonts availability
   - Review memory and CPU resources

2. **Word document formatting issues**
   - Validate docx library version compatibility
   - Check template structure and styling
   - Verify content length limits

3. **Google Docs API errors**
   - Verify OAuth credentials and scopes
   - Check API quotas and rate limits
   - Validate user permissions

### **Debug Mode**
```bash
DEBUG_TEMPLATE_EXPORT=true npm start
```

## 🚀 **Production Deployment**

### **Pre-deployment Checklist**
- ✅ Google API credentials configured
- ✅ Template directories created with proper permissions
- ✅ Export directory configured with cleanup policies
- ✅ Rate limiting configured appropriately
- ✅ Monitoring and logging enabled
- ✅ Error alerting configured

### **Performance Optimization**
- Configure appropriate concurrency limits
- Enable template caching for frequently used templates
- Set up file cleanup automation
- Monitor memory usage for large exports
- Implement export queue for high-volume scenarios

This comprehensive template export system provides enterprise-grade document generation capabilities with security, performance, and scalability! 🎯
