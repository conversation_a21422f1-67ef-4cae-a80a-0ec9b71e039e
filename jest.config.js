module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/server/tests/setup.js'],
  
  // Test directories
  testMatch: [
    '<rootDir>/server/tests/**/*.test.js',
    '<rootDir>/server/tests/**/*.spec.js'
  ],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    // Specific thresholds for critical components
    './server/utils/encryptionService.js': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './server/middleware/rbac.js': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './server/middleware/security.js': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },
  
  // Files to collect coverage from
  collectCoverageFrom: [
    'server/**/*.js',
    '!server/tests/**',
    '!server/node_modules/**',
    '!server/coverage/**',
    '!server/database.sqlite*',
    '!server/logs/**'
  ],
  
  // Test timeout
  testTimeout: 30000,
  
  // Module paths
  modulePaths: ['<rootDir>/server'],
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Module name mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/server/$1',
    '^@tests/(.*)$': '<rootDir>/server/tests/$1'
  },
  
  // Global variables
  globals: {
    'process.env.NODE_ENV': 'test',
    'process.env.JWT_SECRET': 'test_jwt_secret_for_testing_only',
    'process.env.ENCRYPTION_MASTER_KEY': 'test_master_key_32_characters_long'
  },
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test suites configuration
  projects: [
    {
      displayName: 'Unit Tests',
      testMatch: ['<rootDir>/server/tests/unit/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/server/tests/setup.js']
    },
    {
      displayName: 'Integration Tests',
      testMatch: ['<rootDir>/server/tests/integration/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/server/tests/setup.js']
    },
    {
      displayName: 'Security Tests',
      testMatch: ['<rootDir>/server/tests/security/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/server/tests/setup.js']
    },
    {
      displayName: 'E2E Tests',
      testMatch: ['<rootDir>/server/tests/e2e/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/server/tests/setup.js']
    }
  ],
  
  // Reporters
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage/html-report',
        filename: 'report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'CVLeap Test Report'
      }
    ],
    [
      'jest-junit',
      {
        outputDirectory: './coverage',
        outputName: 'junit.xml',
        ancestorSeparator: ' › ',
        uniqueOutputName: 'false',
        suiteNameTemplate: '{filepath}',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}'
      }
    ]
  ],
  
  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/logs/',
    '/.git/'
  ],
  
  // Coverage ignore patterns
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/',
    '/coverage/',
    '/logs/',
    '/.git/',
    '/database.sqlite'
  ],
  
  // Mock configuration
  moduleFileExtensions: ['js', 'json', 'node'],
  
  // Force exit after tests complete
  forceExit: true,
  
  // Detect open handles
  detectOpenHandles: true,
  
  // Maximum worker processes
  maxWorkers: '50%',
  
  // Cache directory
  cacheDirectory: '<rootDir>/.jest-cache',
  
  // Bail configuration
  bail: false,
  
  // Notify configuration
  notify: false,
  
  // Silent mode
  silent: false
};
