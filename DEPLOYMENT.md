# CVleap Deployment Guide

This guide covers deploying CVleap using Docker for production or development environments.

## Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose installed
- Node.js 18+ (for local development)
- Git

### Production Deployment

1. **Clone the repository:**
```bash
git clone https://github.com/moss101/cvleap.git
cd cvleap
```

2. **Configure environment:**
```bash
cp .env.docker .env
# Edit .env with your secure values
```

3. **Deploy with one command:**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

The deployment script will:
- Check dependencies
- Validate configuration
- Create backups
- Build and deploy services
- Run health checks
- Display service status

### Development Setup

1. **Start development environment:**
```bash
docker-compose -f docker-compose.dev.yml up -d
```

2. **Access services:**
- Frontend: http://localhost:5173
- Backend: http://localhost:3000
- Database: localhost:5433

## Configuration

### Environment Variables

Required variables in `.env`:

```bash
# Database
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=****************************************************/cvleap

# Security
JWT_SECRET=your_super_secure_jwt_secret_key_here

# Optional: AI Services
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Optional: File Storage
AZURE_STORAGE_ACCOUNT_NAME=your_azure_storage_account
AZURE_STORAGE_ACCOUNT_KEY=your_azure_storage_key

# Optional: Email Service
SENDGRID_API_KEY=your_sendgrid_api_key

# Optional: Payments
STRIPE_SECRET_KEY=your_stripe_secret_key
```

## Services

### Production Services (docker-compose.yml)
- **PostgreSQL**: Primary database
- **Redis**: Caching and session storage  
- **Server**: Node.js/Express API
- **Client**: React frontend with Nginx
- **Nginx**: Reverse proxy (optional)

### Development Services (docker-compose.dev.yml)
- **PostgreSQL Dev**: Development database
- **Redis Dev**: Development cache
- **Server Dev**: API with hot reload
- **Client Dev**: Frontend with hot reload

## Management Commands

### Deployment Script Commands
```bash
# Full deployment
./scripts/deploy.sh deploy

# Stop services
./scripts/deploy.sh stop

# Restart services  
./scripts/deploy.sh restart

# View logs
./scripts/deploy.sh logs

# Check status
./scripts/deploy.sh status

# Create backup
./scripts/deploy.sh backup

# Cleanup old containers/images
./scripts/deploy.sh cleanup
```

### Docker Compose Commands
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and restart
docker-compose up -d --build

# View service status
docker-compose ps
```

## Monitoring and Maintenance

### Health Checks
- Server: http://localhost:3000/health
- Client: http://localhost:3001/health

### Log Locations
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs server
docker-compose logs client
docker-compose logs postgres
```

### Database Management
```bash
# Connect to database
docker-compose exec postgres psql -U cvleap_user -d cvleap

# Create backup
docker-compose exec postgres pg_dump -U cvleap_user cvleap > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U cvleap_user -d cvleap < backup.sql
```

### Performance Monitoring
```bash
# View resource usage
docker stats

# View container info
docker-compose ps
```

## Troubleshooting

### Common Issues

**Services won't start:**
```bash
# Check logs
docker-compose logs

# Rebuild images
docker-compose build --no-cache
docker-compose up -d
```

**Database connection issues:**
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Reset database
docker-compose down
docker volume rm cvleap_postgres_data
docker-compose up -d
```

**Permission issues:**
```bash
# Fix script permissions
chmod +x scripts/deploy.sh

# Fix file ownership
sudo chown -R $USER:$USER .
```

### Performance Issues

**Slow response times:**
- Check Redis cache status
- Monitor database connections
- Review application logs

**High memory usage:**
- Adjust Docker memory limits
- Review caching settings
- Monitor Node.js heap usage

## Security Considerations

### Production Security
- Use strong passwords for all services
- Enable SSL/TLS with valid certificates
- Regularly update Docker images
- Monitor for security vulnerabilities
- Use firewall rules to restrict access

### Network Security
- Configure reverse proxy (Nginx)
- Use private networks for internal communication
- Implement rate limiting
- Monitor access logs

## Backup and Recovery

### Automated Backups
The deployment script automatically creates backups before updates:
- Database dumps in `./backups/`
- Uploaded files archived
- Timestamped backup files

### Manual Backup
```bash
# Create manual backup
./scripts/deploy.sh backup

# Or use Docker commands
docker-compose exec postgres pg_dump -U cvleap_user cvleap > manual_backup.sql
```

### Recovery Process
```bash
# Restore from backup
docker-compose exec -T postgres psql -U cvleap_user -d cvleap < backup.sql

# Restore uploaded files
tar -xzf uploads_backup.tar.gz
```

## Scaling

### Horizontal Scaling
To scale the application:

1. **Load Balancer**: Add Nginx/HAProxy
2. **Multiple App Instances**: Scale server containers
3. **Database**: Consider read replicas
4. **Cache**: Redis cluster for high availability
5. **File Storage**: External storage (S3, Azure Blob)

### Configuration for Scaling
```yaml
# In docker-compose.yml
server:
  deploy:
    replicas: 3
    update_config:
      parallelism: 1
      delay: 10s
```

## Support

For issues and questions:
1. Check logs: `docker-compose logs`
2. Review this guide
3. Check GitHub issues
4. Create new issue with logs and configuration

## Version History

- **v1.0.0**: Initial Docker implementation
- **v1.1.0**: Added comprehensive testing
- **v1.2.0**: PWA features and enhanced deployment