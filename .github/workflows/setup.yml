name: Setup Environment

on:
  workflow_dispatch:  # Manual trigger
    inputs:
      force_prisma_download:
        description: 'Force Prisma binary download'
        required: false
        default: 'false'
        type: boolean

env:
  NODE_VERSION: '18'
  PRISMA_CLI_BINARY_TARGETS: 'native,debian-openssl-3.0.x'

jobs:
  setup:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            server/package-lock.json
            client/package-lock.json

      - name: Install server dependencies
        working-directory: ./server
        run: npm ci

      - name: Cache Prisma binaries
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/prisma
            node_modules/.prisma
            server/node_modules/.prisma
          key: ${{ runner.os }}-prisma-setup-${{ hashFiles('server/prisma/schema.prisma') }}-${{ hashFiles('server/package-lock.json') }}

      - name: Download and cache Prisma binaries
        working-directory: ./server
        run: |
          echo "Downloading Prisma binaries for caching..."
          if npx prisma generate --no-engine || ${{ github.event.inputs.force_prisma_download == 'true' }}; then
            echo "Prisma binaries downloaded successfully"
          else
            echo "Prisma binary download failed"
            exit 1
          fi

      - name: Install client dependencies
        working-directory: ./client
        run: npm ci --legacy-peer-deps

      - name: Verify setup
        run: |
          echo "Setup completed successfully"
          echo "Node.js version: $(node --version)"
          echo "NPM version: $(npm --version)"
          cd server && npm run check-prisma