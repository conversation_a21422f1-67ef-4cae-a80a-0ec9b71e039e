# GitHub Actions Workflows

This directory contains GitHub Actions workflows to handle CI/CD and environment setup for the CVleap project.

## Workflows

### CI Workflow (ci.yml)
- **Trigger**: Push/PR to main and develop branches
- **Purpose**: Full CI pipeline with build, test, and validation
- **Features**:
  - Node.js 18 setup with npm caching
  - PostgreSQL service for testing
  - Prisma binary caching and offline fallback support
  - Build validation for both client and server
  - Health checks and basic testing

### Setup Workflow (setup.yml)
- **Trigger**: Manual dispatch
- **Purpose**: Environment setup and Prisma binary caching
- **Features**:
  - Manual trigger for one-time setup
  - Force Prisma binary download option
  - Caches binaries for future CI runs

## Firewall Handling

The workflows are designed to handle firewall restrictions that block Prisma binary downloads:

1. **Early Binary Download**: Prisma binaries are downloaded and cached before firewall restrictions
2. **Graceful Fallback**: If Prisma generation fails, the application falls back to SQLite
3. **Retry Logic**: Safe scripts that continue execution even if Prisma operations fail
4. **Binary Caching**: Aggressive caching of Prisma binaries across workflow runs

## Environment Variables

- `PRISMA_CLI_BINARY_TARGETS`: Specifies binary targets for caching
- `DATABASE_URL`: PostgreSQL connection string for testing
- `NODE_ENV`: Environment setting for different behavior modes

## Testing Locally

To test the fallback behavior locally:

```bash
# Server with Prisma fallback
cd server
npm run generate-safe  # Will fail gracefully if binaries unavailable
npm run check-prisma   # Check if Prisma client is available
npm start              # Will use SQLite fallback if Prisma unavailable

# Client build
cd client
npm install --legacy-peer-deps
npm run build
npm run lint
```