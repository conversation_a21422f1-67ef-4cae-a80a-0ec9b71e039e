const fs = require('fs').promises;
const path = require('path');

class JobApplicationService {
  constructor() {
    this.applicationQueue = [];
    this.isProcessing = false;
    this.automationConfig = {
      maxConcurrentApplications: 3,
      delayBetweenApplications: 5000, // 5 seconds
      maxRetries: 3,
      timeoutPerApplication: 300000, // 5 minutes
    };
  }

  // Queue a job application for automated submission
  async queueApplication(applicationData) {
    const queueItem = {
      id: this.generateApplicationId(),
      userId: applicationData.userId,
      jobId: applicationData.jobId,
      resumeId: applicationData.resumeId,
      jobUrl: applicationData.jobUrl,
      jobTitle: applicationData.jobTitle,
      company: applicationData.company,
      coverLetter: applicationData.coverLetter,
      customAnswers: applicationData.customAnswers || {},
      priority: applicationData.priority || 'normal',
      scheduledFor: applicationData.scheduledFor || new Date(),
      status: 'queued',
      attempts: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.applicationQueue.push(queueItem);
    this.sortQueueByPriority();
    
    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return queueItem;
  }

  // Process the application queue
  async processQueue() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('Starting job application queue processing...');

    try {
      while (this.applicationQueue.length > 0) {
        const activeApplications = this.applicationQueue
          .filter(app => app.status === 'processing')
          .length;

        if (activeApplications >= this.automationConfig.maxConcurrentApplications) {
          await this.sleep(1000);
          continue;
        }

        const nextApplication = this.getNextQueuedApplication();
        if (!nextApplication) {
          await this.sleep(1000);
          continue;
        }

        // Process the application
        this.processApplication(nextApplication);
        
        // Delay between starting applications
        await this.sleep(this.automationConfig.delayBetweenApplications);
      }
    } finally {
      this.isProcessing = false;
      console.log('Job application queue processing completed');
    }
  }

  // Process a single application
  async processApplication(application) {
    application.status = 'processing';
    application.attempts++;
    application.startedAt = new Date();

    console.log(`Processing application ${application.id} for ${application.jobTitle} at ${application.company}`);

    try {
      // Simulate browser automation for job application
      // In a real implementation, this would use Puppeteer or similar
      const result = await this.simulateJobApplication(application);
      
      if (result.success) {
        application.status = 'completed';
        application.completedAt = new Date();
        application.submissionId = result.submissionId;
        application.submissionUrl = result.submissionUrl;
        
        // Record successful application in database
        await this.recordApplicationResult(application, 'success', result);
        
        console.log(`✓ Successfully applied to ${application.jobTitle} at ${application.company}`);
      } else {
        throw new Error(result.error || 'Application failed');
      }
    } catch (error) {
      console.error(`✗ Failed to apply to ${application.jobTitle} at ${application.company}:`, error.message);
      
      if (application.attempts < this.automationConfig.maxRetries) {
        // Retry with exponential backoff
        const delay = Math.pow(2, application.attempts) * 30000; // 30s, 60s, 120s
        application.status = 'retry_scheduled';
        application.retryAt = new Date(Date.now() + delay);
        
        setTimeout(() => {
          application.status = 'queued';
        }, delay);
      } else {
        application.status = 'failed';
        application.failedAt = new Date();
        application.error = error.message;
        
        // Record failed application
        await this.recordApplicationResult(application, 'failed', { error: error.message });
      }
    }

    // Remove from queue if completed or failed
    if (['completed', 'failed'].includes(application.status)) {
      this.removeFromQueue(application.id);
    }

    application.updatedAt = new Date();
  }

  // Simulate browser automation for job application
  async simulateJobApplication(application) {
    // This would be replaced with actual Puppeteer automation
    // For now, we'll simulate the process with realistic timing
    
    const steps = [
      { name: 'Loading job page', duration: 2000 },
      { name: 'Filling personal information', duration: 3000 },
      { name: 'Uploading resume', duration: 2000 },
      { name: 'Adding cover letter', duration: 2000 },
      { name: 'Answering screening questions', duration: 4000 },
      { name: 'Submitting application', duration: 1000 }
    ];

    for (const step of steps) {
      console.log(`  - ${step.name}...`);
      await this.sleep(step.duration);
      
      // Simulate occasional failures for realism
      if (Math.random() < 0.05) { // 5% failure rate
        throw new Error(`Failed during: ${step.name}`);
      }
    }

    // Simulate success with mock data
    return {
      success: true,
      submissionId: `APP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      submissionUrl: `${application.jobUrl}?applied=true`,
      timestamp: new Date().toISOString(),
      automationType: 'browser_automation'
    };
  }

  // Record application result in database
  async recordApplicationResult(application, status, result) {
    // This would integrate with the existing database
    // For now, we'll log to a file
    const logEntry = {
      applicationId: application.id,
      userId: application.userId,
      jobId: application.jobId,
      jobTitle: application.jobTitle,
      company: application.company,
      status: status,
      submittedAt: new Date().toISOString(),
      attempts: application.attempts,
      automationType: 'browser_automation',
      result: result
    };

    try {
      const logPath = path.join(__dirname, 'application_logs.json');
      let logs = [];
      
      try {
        const existingLogs = await fs.readFile(logPath, 'utf8');
        logs = JSON.parse(existingLogs);
      } catch (err) {
        // File doesn't exist or is empty
      }
      
      logs.push(logEntry);
      await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Failed to record application result:', error);
    }
  }

  // Get application statistics
  async getApplicationStats(userId) {
    try {
      const logPath = path.join(__dirname, 'application_logs.json');
      const logs = JSON.parse(await fs.readFile(logPath, 'utf8'));
      
      const userLogs = logs.filter(log => log.userId === userId);
      
      const stats = {
        total: userLogs.length,
        successful: userLogs.filter(log => log.status === 'success').length,
        failed: userLogs.filter(log => log.status === 'failed').length,
        pending: this.applicationQueue.filter(app => app.userId === userId).length,
        successRate: 0,
        averageTime: 0,
        recentApplications: userLogs.slice(-10)
      };

      if (stats.total > 0) {
        stats.successRate = (stats.successful / stats.total) * 100;
      }

      return stats;
    } catch (error) {
      return {
        total: 0,
        successful: 0,
        failed: 0,
        pending: 0,
        successRate: 0,
        averageTime: 0,
        recentApplications: []
      };
    }
  }

  // Optimize application timing based on success patterns
  getOptimalApplicationTiming(jobData) {
    // Simulate ML-based timing optimization
    const dayOfWeek = new Date().getDay();
    const hour = new Date().getHours();
    
    // Best times based on industry research simulation
    const optimalTimes = {
      tech: { days: [1, 2, 3], hours: [9, 10, 14, 15] }, // Mon-Wed, 9-10am, 2-3pm
      finance: { days: [1, 2], hours: [8, 9, 13, 14] }, // Mon-Tue, 8-9am, 1-2pm
      healthcare: { days: [2, 3, 4], hours: [10, 11, 15, 16] }, // Tue-Thu, 10-11am, 3-4pm
      default: { days: [1, 2, 3], hours: [9, 10, 14, 15] }
    };

    const industry = this.detectIndustry(jobData);
    const timing = optimalTimes[industry] || optimalTimes.default;
    
    // Find next optimal time
    const now = new Date();
    const nextOptimalTime = this.findNextOptimalTime(now, timing);
    
    return {
      recommendedTime: nextOptimalTime,
      confidence: 0.85,
      reasoning: `Based on industry patterns for ${industry} sector`,
      industryDetected: industry
    };
  }

  // Helper methods
  generateApplicationId() {
    return `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  sortQueueByPriority() {
    const priorityOrder = { high: 3, normal: 2, low: 1 };
    this.applicationQueue.sort((a, b) => {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return new Date(a.scheduledFor) - new Date(b.scheduledFor);
    });
  }

  getNextQueuedApplication() {
    return this.applicationQueue.find(app => 
      app.status === 'queued' && 
      new Date(app.scheduledFor) <= new Date()
    );
  }

  removeFromQueue(applicationId) {
    this.applicationQueue = this.applicationQueue.filter(app => app.id !== applicationId);
  }

  detectIndustry(jobData) {
    const title = (jobData.title || '').toLowerCase();
    const company = (jobData.company || '').toLowerCase();
    const description = (jobData.description || '').toLowerCase();
    
    const text = `${title} ${company} ${description}`;
    
    if (text.includes('software') || text.includes('developer') || text.includes('engineer')) {
      return 'tech';
    } else if (text.includes('finance') || text.includes('bank') || text.includes('investment')) {
      return 'finance';
    } else if (text.includes('health') || text.includes('medical') || text.includes('hospital')) {
      return 'healthcare';
    }
    
    return 'default';
  }

  findNextOptimalTime(from, timing) {
    const { days, hours } = timing;
    let candidate = new Date(from);
    
    // Find next optimal day and hour
    for (let i = 0; i < 14; i++) { // Look up to 2 weeks ahead
      const dayOfWeek = candidate.getDay();
      if (days.includes(dayOfWeek)) {
        for (const hour of hours) {
          const timeCandidate = new Date(candidate);
          timeCandidate.setHours(hour, 0, 0, 0);
          
          if (timeCandidate > from) {
            return timeCandidate;
          }
        }
      }
      candidate.setDate(candidate.getDate() + 1);
    }
    
    // Fallback to tomorrow at 9 AM
    const fallback = new Date(from);
    fallback.setDate(fallback.getDate() + 1);
    fallback.setHours(9, 0, 0, 0);
    return fallback;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get queue status
  getQueueStatus() {
    const statusCounts = {};
    this.applicationQueue.forEach(app => {
      statusCounts[app.status] = (statusCounts[app.status] || 0) + 1;
    });

    return {
      total: this.applicationQueue.length,
      isProcessing: this.isProcessing,
      statusBreakdown: statusCounts,
      nextApplication: this.getNextQueuedApplication()?.id || null
    };
  }

  // Get queue history for analytics
  getQueueHistory() {
    // Return recent completed applications
    return this.applicationQueue
      .filter(app => ['completed', 'failed'].includes(app.status))
      .slice(-50) // Last 50 completed applications
      .map(app => ({
        id: app.id,
        jobTitle: app.jobTitle,
        company: app.company,
        status: app.status,
        attempts: app.attempts,
        completedAt: app.updatedAt,
        processingTime: app.updatedAt ? 
          new Date(app.updatedAt) - new Date(app.createdAt) : null
      }))
      .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt));
  }
}

module.exports = JobApplicationService;