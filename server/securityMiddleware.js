const rateLimit = require('express-rate-limit');

class SecurityMiddleware {
  constructor() {
    this.rateLimitConfig = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.'
      },
      standardHeaders: true,
      legacyHeaders: false,
    };

    this.aiRateLimitConfig = {
      windowMs: 60 * 1000, // 1 minute
      max: 10, // limit AI requests to 10 per minute
      message: {
        error: 'AI request limit exceeded. Please wait before making more AI requests.'
      },
      standardHeaders: true,
      legacyHeaders: false,
    };

    this.authRateLimitConfig = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // limit login attempts to 5 per 15 minutes
      message: {
        error: 'Too many login attempts from this IP, please try again later.'
      },
      skipSuccessfulRequests: true,
      standardHeaders: true,
      legacyHeaders: false,
    };
  }

  // General rate limiting middleware
  createGeneralRateLimit() {
    return rateLimit(this.rateLimitConfig);
  }

  // AI-specific rate limiting (more restrictive)
  createAIRateLimit() {
    return rateLimit(this.aiRateLimitConfig);
  }

  // Authentication rate limiting (most restrictive)
  createAuthRateLimit() {
    return rateLimit(this.authRateLimitConfig);
  }

  // Input validation middleware
  validateInput(req, res, next) {
    // Sanitize and validate common inputs
    try {
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /eval\s*\(/i,
        /expression\s*\(/i
      ];

      const checkValue = (value) => {
        if (typeof value === 'string') {
          for (const pattern of suspiciousPatterns) {
            if (pattern.test(value)) {
              throw new Error('Potentially malicious input detected');
            }
          }
          // Basic length check
          if (value.length > 10000) {
            throw new Error('Input too long');
          }
        }
        return value;
      };

      const validateObject = (obj) => {
        if (obj && typeof obj === 'object') {
          for (const [key, value] of Object.entries(obj)) {
            if (Array.isArray(value)) {
              value.forEach(checkValue);
            } else if (typeof value === 'object' && value !== null) {
              validateObject(value);
            } else {
              checkValue(value);
            }
          }
        }
      };

      // Validate request body
      if (req.body) {
        validateObject(req.body);
      }

      // Validate query parameters
      if (req.query) {
        validateObject(req.query);
      }

      next();
    } catch (error) {
      console.warn('Input validation failed:', error.message, {
        ip: req.ip,
        path: req.path,
        method: req.method
      });
      
      return res.status(400).json({
        error: 'Invalid input detected',
        details: 'Request contains potentially harmful content'
      });
    }
  }

  // Request logging middleware
  requestLogger(req, res, next) {
    const start = Date.now();
    const originalSend = res.send;

    res.send = function(data) {
      const duration = Date.now() - start;
      
      // Log request details
      console.log(JSON.stringify({
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        duration: duration,
        statusCode: res.statusCode,
        userId: req.user?.userId || null,
        contentLength: data ? data.length : 0
      }));

      originalSend.call(this, data);
    };

    next();
  }

  // Security headers middleware
  securityHeaders(req, res, next) {
    // Set security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    next();
  }

  // Enhanced error handling middleware
  errorHandler(err, req, res, next) {
    // Log the error
    console.error('Application error:', {
      timestamp: new Date().toISOString(),
      error: err.message,
      stack: err.stack,
      path: req.path,
      method: req.method,
      ip: req.ip,
      userId: req.user?.userId || null
    });

    // Don't leak sensitive information in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    let statusCode = 500;
    let message = 'Internal server error';

    // Handle specific error types
    if (err.name === 'ValidationError') {
      statusCode = 400;
      message = 'Validation error';
    } else if (err.name === 'UnauthorizedError') {
      statusCode = 401;
      message = 'Unauthorized';
    } else if (err.name === 'ForbiddenError') {
      statusCode = 403;
      message = 'Forbidden';
    } else if (err.statusCode) {
      statusCode = err.statusCode;
      message = err.message;
    }

    const errorResponse = {
      error: message,
      timestamp: new Date().toISOString(),
      path: req.path
    };

    // Include stack trace in development
    if (isDevelopment) {
      errorResponse.stack = err.stack;
      errorResponse.details = err.message;
    }

    res.status(statusCode).json(errorResponse);
  }

  // Database query timeout middleware
  queryTimeout(timeoutMs = 30000) {
    return (req, res, next) => {
      req.queryTimeout = timeoutMs;
      next();
    };
  }

  // CORS configuration
  corsOptions() {
    return {
      origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        
        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:5173',
          'https://cvleap.com',
          'https://www.cvleap.com'
        ];
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      optionsSuccessStatus: 200 // some legacy browsers (IE11, various SmartTVs) choke on 204
    };
  }

  // API key validation for external integrations
  validateApiKey(req, res, next) {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({ error: 'API key required' });
    }

    // Validate API key format and existence
    if (!/^[a-zA-Z0-9_-]{32,}$/.test(apiKey)) {
      return res.status(401).json({ error: 'Invalid API key format' });
    }

    // Here you would typically check against a database
    // For now, we'll use environment variable
    const validApiKey = process.env.API_KEY;
    
    if (!validApiKey || apiKey !== validApiKey) {
      console.warn('Invalid API key attempt:', {
        ip: req.ip,
        apiKey: apiKey.substring(0, 8) + '...',
        path: req.path
      });
      
      return res.status(401).json({ error: 'Invalid API key' });
    }

    next();
  }

  // Request size limiter
  requestSizeLimit(limitMb = 10) {
    return (req, res, next) => {
      const contentLength = parseInt(req.headers['content-length'] || '0');
      const maxSize = limitMb * 1024 * 1024; // Convert MB to bytes
      
      if (contentLength > maxSize) {
        return res.status(413).json({
          error: 'Request entity too large',
          maxSize: `${limitMb}MB`
        });
      }
      
      next();
    };
  }

  // IP whitelist middleware (for admin endpoints)
  ipWhitelist(allowedIPs = []) {
    return (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      
      if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
        console.warn('IP not whitelisted:', {
          ip: clientIP,
          path: req.path,
          method: req.method
        });
        
        return res.status(403).json({ error: 'Access denied' });
      }
      
      next();
    };
  }
}

module.exports = SecurityMiddleware;