# Multi-stage build for optimized and secure container
FROM node:18-alpine AS base

# Install security updates and necessary packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Create app directory and user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S server -u 1001 -G nodejs

WORKDIR /app

# Install dependencies only when needed
FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    npm audit fix --force || true

# Build the application
FROM base AS builder
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Build application
RUN npm run build 2>/dev/null || echo "No build script found"

# Remove dev dependencies and clean up
RUN npm prune --production

# Production image
FROM base AS runner

# Set security-focused environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Copy production dependencies with proper ownership
COPY --from=deps --chown=server:nodejs /app/node_modules ./node_modules

# Copy built application with proper ownership
COPY --from=builder --chown=server:nodejs /app .

# Create necessary directories with proper permissions
RUN mkdir -p logs uploads && \
    chown -R server:nodejs logs uploads && \
    chmod 755 logs uploads

# Remove unnecessary files for security
RUN rm -rf .git .github tests *.md package-lock.json || true

# Switch to non-root user
USER server

# Expose port
EXPOSE 3000

# Health check with enhanced monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Graceful shutdown handling
STOPSIGNAL SIGTERM

# Start the application with proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]