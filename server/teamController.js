const database = require('./database');
const notificationService = require('./notificationService');

/**
 * Team Workspace Controller
 * Handles organization management, team members, and collaborative workspaces
 */
class TeamController {
  
  /**
   * Create a new organization/workspace
   */
  async createOrganization(req, res) {
    try {
      const { name, description } = req.body;
      const userId = req.user.userId;

      if (!name || name.trim().length === 0) {
        return res.status(400).json({ error: 'Organization name is required' });
      }

      // Generate unique slug
      const slug = this.generateSlug(name);
      
      // Check if slug already exists
      const existingOrg = await this.getOrganizationBySlug(slug);
      if (existingOrg) {
        return res.status(400).json({ error: 'Organization name already taken' });
      }

      // Create organization
      const orgId = await this.createOrganizationRecord(userId, name, slug, description);
      
      // Add creator as admin
      await this.addOrganizationMember(orgId, userId, 'admin', userId);

      const organization = await this.getOrganizationById(orgId);
      
      res.status(201).json({
        message: 'Organization created successfully',
        organization
      });
      
    } catch (error) {
      console.error('Create organization error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get user's organizations
   */
  async getUserOrganizations(req, res) {
    try {
      const userId = req.user.userId;
      const organizations = await this.getUserOrganizationList(userId);
      
      res.json({ organizations });
    } catch (error) {
      console.error('Get user organizations error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get organization details
   */
  async getOrganization(req, res) {
    try {
      const { orgId } = req.params;
      const userId = req.user.userId;

      // Check if user is member
      const isMember = await this.isOrganizationMember(orgId, userId);
      if (!isMember) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const organization = await this.getOrganizationDetails(orgId);
      const members = await this.getOrganizationMembers(orgId);
      const stats = await this.getOrganizationStats(orgId);

      res.json({
        organization,
        members,
        stats
      });
    } catch (error) {
      console.error('Get organization error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Invite team member
   */
  async inviteTeamMember(req, res) {
    try {
      const { orgId } = req.params;
      const { email, role = 'viewer' } = req.body;
      const inviterId = req.user.userId;

      // Check if inviter has admin permissions
      const hasPermission = await this.hasAdminPermission(orgId, inviterId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Admin permission required' });
      }

      // Check if user exists
      const user = await this.getUserByEmail(email);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Check if already a member
      const isAlreadyMember = await this.isOrganizationMember(orgId, user.id);
      if (isAlreadyMember) {
        return res.status(400).json({ error: 'User is already a team member' });
      }

      // Add team member
      await this.addOrganizationMember(orgId, user.id, role, inviterId);

      // Get inviter details for notification
      const inviter = await this.getUserById(inviterId);
      const organization = await this.getOrganizationById(orgId);

      // Send notification
      notificationService.notifyCollaborationInvite(
        user.id,
        inviter.name,
        organization.name,
        'team workspace'
      );

      // Notify other team members
      notificationService.notifyTeamMemberJoined(orgId, user.name, inviter.name);

      res.json({
        message: 'Team member invited successfully',
        member: {
          id: user.id,
          name: user.name,
          email: user.email,
          role,
          status: 'active'
        }
      });
    } catch (error) {
      console.error('Invite team member error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update member role
   */
  async updateMemberRole(req, res) {
    try {
      const { orgId, memberId } = req.params;
      const { role } = req.body;
      const userId = req.user.userId;

      // Check admin permission
      const hasPermission = await this.hasAdminPermission(orgId, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Admin permission required' });
      }

      // Validate role
      const validRoles = ['admin', 'editor', 'reviewer', 'viewer'];
      if (!validRoles.includes(role)) {
        return res.status(400).json({ error: 'Invalid role' });
      }

      // Update member role
      await this.updateOrganizationMemberRole(orgId, memberId, role);

      res.json({
        message: 'Member role updated successfully'
      });
    } catch (error) {
      console.error('Update member role error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Remove team member
   */
  async removeTeamMember(req, res) {
    try {
      const { orgId, memberId } = req.params;
      const userId = req.user.userId;

      // Check admin permission
      const hasPermission = await this.hasAdminPermission(orgId, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Admin permission required' });
      }

      // Cannot remove organization owner
      const organization = await this.getOrganizationById(orgId);
      if (organization.owner_id === parseInt(memberId)) {
        return res.status(400).json({ error: 'Cannot remove organization owner' });
      }

      // Remove member
      await this.removeOrganizationMember(orgId, memberId);

      res.json({
        message: 'Team member removed successfully'
      });
    } catch (error) {
      console.error('Remove team member error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Share resume with team
   */
  async shareResumeWithTeam(req, res) {
    try {
      const { resumeId } = req.params;
      const { organizationId, accessType = 'view' } = req.body;
      const userId = req.user.userId;

      // Check if user owns the resume
      const resume = await this.getResumeById(resumeId, userId);
      if (!resume) {
        return res.status(404).json({ error: 'Resume not found' });
      }

      // Check if user is member of organization
      const isMember = await this.isOrganizationMember(organizationId, userId);
      if (!isMember) {
        return res.status(403).json({ error: 'Not a member of this organization' });
      }

      // Create collaboration record
      await this.createResumeCollaboration(resumeId, organizationId, accessType, userId);

      res.json({
        message: 'Resume shared with team successfully'
      });
    } catch (error) {
      console.error('Share resume error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get team shared resumes
   */
  async getTeamResumes(req, res) {
    try {
      const { orgId } = req.params;
      const userId = req.user.userId;

      // Check if user is member
      const isMember = await this.isOrganizationMember(orgId, userId);
      if (!isMember) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const resumes = await this.getOrganizationSharedResumes(orgId);
      
      res.json({ resumes });
    } catch (error) {
      console.error('Get team resumes error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Database helper methods
   */
  generateSlug(name) {
    return name.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      + '-' + Math.random().toString(36).substr(2, 6);
  }

  async getOrganizationBySlug(slug) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id FROM organizations WHERE slug = ?',
        [slug],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async createOrganizationRecord(ownerId, name, slug, description) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT INTO organizations (owner_id, name, slug, description) 
         VALUES (?, ?, ?, ?)`,
        [ownerId, name, slug, description],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
  }

  async addOrganizationMember(orgId, userId, role, invitedBy) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT INTO organization_members (organization_id, user_id, role, invited_by) 
         VALUES (?, ?, ?, ?)`,
        [orgId, userId, role, invitedBy],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async getUserOrganizationList(userId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT o.*, om.role, om.joined_at 
         FROM organizations o 
         JOIN organization_members om ON o.id = om.organization_id 
         WHERE om.user_id = ? AND om.status = 'active'
         ORDER BY om.joined_at DESC`,
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  async isOrganizationMember(orgId, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        `SELECT id FROM organization_members 
         WHERE organization_id = ? AND user_id = ? AND status = 'active'`,
        [orgId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(!!row);
        }
      );
    });
  }

  async hasAdminPermission(orgId, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        `SELECT role FROM organization_members 
         WHERE organization_id = ? AND user_id = ? AND status = 'active'`,
        [orgId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row && row.role === 'admin');
        }
      );
    });
  }

  async getOrganizationById(orgId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM organizations WHERE id = ?',
        [orgId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getOrganizationDetails(orgId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        `SELECT o.*, u.name as owner_name 
         FROM organizations o 
         JOIN users u ON o.owner_id = u.id 
         WHERE o.id = ?`,
        [orgId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getOrganizationMembers(orgId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT u.id, u.name, u.email, om.role, om.joined_at, om.status 
         FROM users u 
         JOIN organization_members om ON u.id = om.user_id 
         WHERE om.organization_id = ? AND om.status = 'active'
         ORDER BY om.joined_at ASC`,
        [orgId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  async getOrganizationStats(orgId) {
    return new Promise((resolve, reject) => {
      const stats = {};
      
      // Get member count
      database.get().get(
        'SELECT COUNT(*) as member_count FROM organization_members WHERE organization_id = ? AND status = "active"',
        [orgId],
        (err, row) => {
          if (err) {
            reject(err);
            return;
          }
          stats.memberCount = row.member_count;
          
          // Get shared resume count
          database.get().get(
            'SELECT COUNT(*) as resume_count FROM resume_collaborations WHERE organization_id = ?',
            [orgId],
            (err2, row2) => {
              if (err2) {
                reject(err2);
                return;
              }
              stats.sharedResumeCount = row2.resume_count;
              resolve(stats);
            }
          );
        }
      );
    });
  }

  async getUserByEmail(email) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id, name, email FROM users WHERE email = ?',
        [email],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getUserById(userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id, name, email FROM users WHERE id = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async updateOrganizationMemberRole(orgId, memberId, role) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE organization_members SET role = ? WHERE organization_id = ? AND user_id = ?',
        [role, orgId, memberId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async removeOrganizationMember(orgId, memberId) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE organization_members SET status = "removed" WHERE organization_id = ? AND user_id = ?',
        [orgId, memberId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async getResumeById(resumeId, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resumes WHERE id = ? AND user_id = ? AND is_active = 1',
        [resumeId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async createResumeCollaboration(resumeId, organizationId, accessType, createdBy) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT INTO resume_collaborations (resume_id, organization_id, access_type, created_by) 
         VALUES (?, ?, ?, ?)`,
        [resumeId, organizationId, accessType, createdBy],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async getOrganizationSharedResumes(orgId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT r.*, rc.access_type, rc.created_at as shared_at, u.name as owner_name 
         FROM resumes r 
         JOIN resume_collaborations rc ON r.id = rc.resume_id 
         JOIN users u ON r.user_id = u.id 
         WHERE rc.organization_id = ? AND r.is_active = 1
         ORDER BY rc.created_at DESC`,
        [orgId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }
}

module.exports = TeamController;