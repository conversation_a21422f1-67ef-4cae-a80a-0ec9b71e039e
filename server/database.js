const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = process.env.DB_PATH || path.join(__dirname, 'database.sqlite');

class Database {
  constructor() {
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error connecting to database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.init();
      }
    });
  }

  init() {
    // Create users table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create resumes table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS resumes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        data TEXT NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create jobs table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        company TEXT NOT NULL,
        location TEXT,
        description TEXT,
        url TEXT,
        status TEXT DEFAULT 'saved',
        applied_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create applications table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS applications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        job_id INTEGER NOT NULL,
        resume_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending',
        cover_letter TEXT,
        notes TEXT,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (job_id) REFERENCES jobs (id),
        FOREIGN KEY (resume_id) REFERENCES resumes (id)
      )
    `);

    // Create analytics events table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS analytics_events (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        event_type TEXT NOT NULL,
        event_data TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create application metrics table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS application_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        application_id INTEGER NOT NULL,
        metric_type TEXT NOT NULL,
        metric_value REAL NOT NULL,
        recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (application_id) REFERENCES applications (id)
      )
    `);

    // Create resume performance table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        views INTEGER DEFAULT 0,
        downloads INTEGER DEFAULT 0,
        applications INTEGER DEFAULT 0,
        responses INTEGER DEFAULT 0,
        interviews INTEGER DEFAULT 0,
        offers INTEGER DEFAULT 0,
        ats_score REAL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resume_id) REFERENCES resumes (id)
      )
    `);

    // Create email campaign metrics table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS email_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        campaign_type TEXT NOT NULL,
        sent_count INTEGER DEFAULT 0,
        opened_count INTEGER DEFAULT 0,
        clicked_count INTEGER DEFAULT 0,
        replied_count INTEGER DEFAULT 0,
        sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create job search patterns table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS job_search_patterns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        pattern_type TEXT NOT NULL,
        pattern_data TEXT NOT NULL,
        success_rate REAL,
        recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create success predictions table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS success_predictions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        application_id INTEGER,
        predicted_score REAL NOT NULL,
        actual_outcome TEXT,
        prediction_accuracy REAL,
        factors TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (application_id) REFERENCES applications (id)
      )
    `);

    // Create resume templates table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        industry TEXT,
        ats_score INTEGER DEFAULT 85,
        is_premium BOOLEAN DEFAULT 0,
        is_customizable BOOLEAN DEFAULT 1,
        structure TEXT NOT NULL,
        preview_url TEXT,
        usage_count INTEGER DEFAULT 0,
        rating REAL DEFAULT 4.5,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    // Create template analytics table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_analytics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        usage_type TEXT NOT NULL,
        success_score REAL,
        application_count INTEGER DEFAULT 0,
        response_rate REAL DEFAULT 0,
        usage_data TEXT,
        used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES resume_templates (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Add usage_data column if it doesn't exist (for backward compatibility)
    this.db.run(`
      ALTER TABLE template_analytics ADD COLUMN usage_data TEXT
    `, (err) => {
      // Ignore error if column already exists
      if (err && !err.message.includes('duplicate column name')) {
        console.log('Note: usage_data column already exists or cannot be added');
      }
    });

    // Add rating_count column to resume_templates if it doesn't exist
    this.db.run(`
      ALTER TABLE resume_templates ADD COLUMN rating_count INTEGER DEFAULT 0
    `, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.log('Note: rating_count column already exists or cannot be added');
      }
    });

    // Create template ratings table for individual user ratings
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_ratings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES resume_templates (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(template_id, user_id)
      )
    `);

    // Create template variants table for A/B testing
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_variants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        parent_template_id INTEGER NOT NULL,
        variant_name TEXT NOT NULL,
        variant_description TEXT,
        structure TEXT NOT NULL,
        test_percentage REAL DEFAULT 50.0,
        is_active BOOLEAN DEFAULT 1,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_template_id) REFERENCES resume_templates (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    // Create template performance metrics table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_performance_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER NOT NULL,
        metric_date DATE NOT NULL,
        view_count INTEGER DEFAULT 0,
        selection_count INTEGER DEFAULT 0,
        completion_count INTEGER DEFAULT 0,
        export_count INTEGER DEFAULT 0,
        avg_time_to_complete REAL DEFAULT 0,
        user_satisfaction_score REAL DEFAULT 0,
        ats_pass_rate REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES resume_templates (id),
        UNIQUE(template_id, metric_date)
      )
    `);

    // Create template recommendations table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_recommendations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        template_id INTEGER NOT NULL,
        recommendation_score REAL NOT NULL,
        recommendation_reason TEXT,
        is_shown BOOLEAN DEFAULT 0,
        is_clicked BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (template_id) REFERENCES resume_templates (id)
      )
    `);

    // Create template customizations table to track user customizations
    this.db.run(`
      CREATE TABLE IF NOT EXISTS template_customizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        template_id INTEGER NOT NULL,
        customization_data TEXT NOT NULL,
        is_saved BOOLEAN DEFAULT 0,
        name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (template_id) REFERENCES resume_templates (id)
      )
    `);

    // Create gamification tables
    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_gamification_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL UNIQUE,
        level INTEGER DEFAULT 1,
        xp INTEGER DEFAULT 0,
        total_applications INTEGER DEFAULT 0,
        response_rate REAL DEFAULT 0,
        interview_rate REAL DEFAULT 0,
        streak INTEGER DEFAULT 0,
        completed_goals INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_achievements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        achievement_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        icon TEXT NOT NULL,
        max_progress INTEGER NOT NULL,
        progress INTEGER DEFAULT 0,
        rarity TEXT DEFAULT 'bronze',
        unlocked BOOLEAN DEFAULT 0,
        unlocked_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(user_id, achievement_id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_goals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        target INTEGER NOT NULL,
        current INTEGER DEFAULT 0,
        deadline DATE NOT NULL,
        category TEXT DEFAULT 'applications',
        priority TEXT DEFAULT 'medium',
        completed BOOLEAN DEFAULT 0,
        completed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS xp_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        xp_gained INTEGER NOT NULL,
        reason TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create career coaching sessions table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS career_coaching_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_type TEXT NOT NULL,
        input_data TEXT NOT NULL,
        ai_recommendations TEXT NOT NULL,
        feedback_score INTEGER,
        session_metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create skill gap analysis table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS skill_gap_analysis (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        target_role TEXT NOT NULL,
        current_skills TEXT NOT NULL,
        missing_skills TEXT NOT NULL,
        recommended_actions TEXT NOT NULL,
        priority_score REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create job matching scores table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS job_matching_scores (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        job_id INTEGER NOT NULL,
        overall_score REAL NOT NULL,
        skill_match_score REAL,
        experience_match_score REAL,
        culture_fit_score REAL,
        growth_potential_score REAL,
        match_breakdown TEXT,
        recommendations TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (job_id) REFERENCES jobs (id)
      )
    `);

    // Create parsed documents table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS parsed_documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        document_type TEXT NOT NULL,
        original_filename TEXT NOT NULL,
        parsed_data TEXT NOT NULL,
        confidence_score REAL,
        processing_metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Collaboration tables - Team Workspace Management
    this.db.run(`
      CREATE TABLE IF NOT EXISTS organizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        owner_id INTEGER NOT NULL,
        subscription_plan TEXT DEFAULT 'free',
        max_members INTEGER DEFAULT 5,
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (owner_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS organization_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        organization_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        role TEXT NOT NULL DEFAULT 'viewer',
        invited_by INTEGER,
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active',
        permissions TEXT DEFAULT '{}',
        FOREIGN KEY (organization_id) REFERENCES organizations (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (invited_by) REFERENCES users (id),
        UNIQUE(organization_id, user_id)
      )
    `);

    // Collaborative resume editing tables
    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_collaborations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        organization_id INTEGER,
        access_type TEXT DEFAULT 'private',
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (organization_id) REFERENCES organizations (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        permission_type TEXT NOT NULL DEFAULT 'view',
        granted_by INTEGER NOT NULL,
        granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (granted_by) REFERENCES users (id),
        UNIQUE(resume_id, user_id)
      )
    `);

    // Real-time collaboration tracking
    this.db.run(`
      CREATE TABLE IF NOT EXISTS editing_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        session_id TEXT NOT NULL,
        cursor_position TEXT DEFAULT '{}',
        current_selection TEXT DEFAULT '{}',
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active',
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(session_id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_changes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        change_type TEXT NOT NULL,
        section_id TEXT,
        old_content TEXT,
        new_content TEXT,
        change_position INTEGER,
        change_metadata TEXT DEFAULT '{}',
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        operation_id TEXT,
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Comment and review system
    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_comments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        parent_comment_id INTEGER,
        content TEXT NOT NULL,
        section_id TEXT,
        position_data TEXT DEFAULT '{}',
        comment_type TEXT DEFAULT 'comment',
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (parent_comment_id) REFERENCES resume_comments (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS resume_reviews (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        reviewer_id INTEGER NOT NULL,
        review_type TEXT DEFAULT 'general',
        status TEXT DEFAULT 'pending',
        overall_rating INTEGER,
        feedback TEXT,
        suggestions TEXT DEFAULT '[]',
        review_data TEXT DEFAULT '{}',
        requested_by INTEGER,
        requested_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (resume_id) REFERENCES resumes (id),
        FOREIGN KEY (reviewer_id) REFERENCES users (id),
        FOREIGN KEY (requested_by) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS review_suggestions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        review_id INTEGER NOT NULL,
        section_id TEXT NOT NULL,
        suggestion_type TEXT NOT NULL,
        original_text TEXT,
        suggested_text TEXT,
        reasoning TEXT,
        status TEXT DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (review_id) REFERENCES resume_reviews (id)
      )
    `);

    // Notification tracking for collaboration
    this.db.run(`
      CREATE TABLE IF NOT EXISTS collaboration_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        notification_type TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER NOT NULL,
        message TEXT NOT NULL,
        data TEXT DEFAULT '{}',
        read_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Business Intelligence and Advanced Analytics tables (from PR #24)
    this.db.run(`
      CREATE TABLE IF NOT EXISTS market_intelligence (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        industry TEXT NOT NULL,
        salary_median INTEGER,
        salary_range_min INTEGER,
        salary_range_max INTEGER,
        demand_score REAL DEFAULT 0,
        growth_rate REAL DEFAULT 0,
        job_postings_count INTEGER DEFAULT 0,
        skills_in_demand TEXT DEFAULT '[]',
        market_trends TEXT DEFAULT '{}',
        geographic_data TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_engagement_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_duration INTEGER DEFAULT 0,
        pages_visited INTEGER DEFAULT 0,
        actions_taken INTEGER DEFAULT 0,
        engagement_score REAL DEFAULT 0,
        last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
        feature_usage TEXT DEFAULT '{}',
        behavioral_patterns TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS executive_kpis (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        kpi_name TEXT NOT NULL,
        kpi_value REAL NOT NULL,
        kpi_target REAL,
        trend_direction TEXT DEFAULT 'stable',
        trend_percentage REAL DEFAULT 0,
        time_period TEXT DEFAULT 'monthly',
        category TEXT DEFAULT 'general',
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS revenue_projections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projection_type TEXT NOT NULL,
        time_horizon TEXT NOT NULL,
        projected_value REAL NOT NULL,
        confidence_level REAL DEFAULT 0.75,
        growth_factors TEXT DEFAULT '{}',
        risk_factors TEXT DEFAULT '{}',
        assumptions TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS churn_predictions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        churn_probability REAL NOT NULL,
        risk_level TEXT NOT NULL,
        contributing_factors TEXT DEFAULT '{}',
        recommended_actions TEXT DEFAULT '{}',
        prediction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        model_version TEXT DEFAULT 'v1.0',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS platform_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        metric_name TEXT NOT NULL,
        metric_value REAL NOT NULL,
        metric_type TEXT DEFAULT 'count',
        aggregation_period TEXT DEFAULT 'daily',
        metadata TEXT DEFAULT '{}',
        recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS business_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        metric_category TEXT NOT NULL,
        revenue REAL DEFAULT 0,
        conversion_rate REAL DEFAULT 0,
        retention_rate REAL DEFAULT 0,
        customer_acquisition_cost REAL DEFAULT 0,
        lifetime_value REAL DEFAULT 0,
        monthly_recurring_revenue REAL DEFAULT 0,
        churn_rate REAL DEFAULT 0,
        growth_metrics TEXT DEFAULT '{}',
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS skills_demand_forecast (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        skill_name TEXT NOT NULL,
        current_demand REAL DEFAULT 0,
        projected_growth REAL DEFAULT 0,
        market_saturation REAL DEFAULT 0,
        salary_impact REAL DEFAULT 0,
        industry_relevance TEXT DEFAULT '{}',
        forecast_horizon TEXT DEFAULT '12_months',
        confidence_score REAL DEFAULT 0.5,
        data_sources TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS realtime_events (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        event_type TEXT NOT NULL,
        event_source TEXT NOT NULL,
        event_data TEXT DEFAULT '{}',
        processed BOOLEAN DEFAULT 0,
        processing_time REAL,
        insights_generated TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        processed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS custom_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        report_name TEXT NOT NULL,
        report_type TEXT NOT NULL,
        parameters TEXT DEFAULT '{}',
        generated_data TEXT DEFAULT '{}',
        format TEXT DEFAULT 'json',
        status TEXT DEFAULT 'pending',
        file_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Loop Creation System tables
    this.db.run(`
      CREATE TABLE IF NOT EXISTS job_loops (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'draft',
        configuration TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_run_at DATETIME,
        next_run_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS loop_configurations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loop_id INTEGER NOT NULL,
        job_titles TEXT NOT NULL,
        locations TEXT NOT NULL,
        industries TEXT,
        experience_levels TEXT,
        salary_min INTEGER,
        salary_max INTEGER,
        remote_options TEXT DEFAULT 'any',
        excluded_companies TEXT DEFAULT '[]',
        keywords TEXT DEFAULT '[]',
        schedule_type TEXT DEFAULT 'continuous',
        schedule_config TEXT DEFAULT '{}',
        max_applications_per_day INTEGER DEFAULT 5,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (loop_id) REFERENCES job_loops (id) ON DELETE CASCADE
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS discovered_jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loop_id INTEGER NOT NULL,
        external_id TEXT NOT NULL,
        source_platform TEXT NOT NULL,
        title TEXT NOT NULL,
        company TEXT NOT NULL,
        location TEXT,
        description TEXT,
        salary_min INTEGER,
        salary_max INTEGER,
        url TEXT NOT NULL,
        relevance_score REAL DEFAULT 0,
        match_factors TEXT DEFAULT '{}',
        status TEXT DEFAULT 'discovered',
        discovered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        applied_at DATETIME,
        FOREIGN KEY (loop_id) REFERENCES job_loops (id) ON DELETE CASCADE,
        UNIQUE(loop_id, external_id, source_platform)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS loop_analytics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loop_id INTEGER NOT NULL,
        metric_type TEXT NOT NULL,
        metric_value REAL NOT NULL,
        additional_data TEXT DEFAULT '{}',
        recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (loop_id) REFERENCES job_loops (id) ON DELETE CASCADE
      )
    `);

    // Email Automation System Tables
    // Create email campaigns table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS email_campaigns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        template_id INTEGER,
        status TEXT DEFAULT 'draft',
        schedule_type TEXT DEFAULT 'immediate',
        scheduled_at DATETIME,
        target_companies TEXT,
        target_roles TEXT,
        target_count INTEGER DEFAULT 0,
        sent_count INTEGER DEFAULT 0,
        opened_count INTEGER DEFAULT 0,
        clicked_count INTEGER DEFAULT 0,
        replied_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (template_id) REFERENCES email_templates (id)
      )
    `);

    // Create email templates table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS email_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        subject TEXT NOT NULL,
        content TEXT NOT NULL,
        variables TEXT DEFAULT '[]',
        is_system_template BOOLEAN DEFAULT 0,
        usage_count INTEGER DEFAULT 0,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    // Create recruiter contacts table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS recruiter_contacts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        email TEXT NOT NULL,
        name TEXT,
        company TEXT,
        title TEXT,
        verification_status TEXT DEFAULT 'unverified',
        discovery_method TEXT,
        discovery_data TEXT DEFAULT '{}',
        last_contacted DATETIME,
        response_status TEXT DEFAULT 'none',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(user_id, email)
      )
    `);

    // Create email logs table for tracking all sent emails
    this.db.run(`
      CREATE TABLE IF NOT EXISTS email_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        campaign_id INTEGER,
        recipient_email TEXT NOT NULL,
        subject TEXT NOT NULL,
        content TEXT NOT NULL,
        status TEXT DEFAULT 'queued',
        tracking_id TEXT UNIQUE,
        opened_at DATETIME,
        clicked_at DATETIME,
        replied_at DATETIME,
        bounce_reason TEXT,
        send_error TEXT,
        sent_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id)
      )
    `);

    console.log('Database tables initialized');

    // Add missing columns for backward compatibility
    this.db.run(`
      ALTER TABLE email_logs ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    `, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.log('Note: updated_at column already exists in email_logs or cannot be added');
      }
    });
  }

  get() {
    return this.db;
  }

  close() {
    this.db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed');
      }
    });
  }
}

module.exports = new Database();