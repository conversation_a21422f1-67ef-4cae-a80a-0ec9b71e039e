# AI Service Integrations - CV<PERSON>eap

This document provides comprehensive information about the AI service integrations implemented in the CVLeap application.

## Overview

CVLeap now supports multiple AI service providers with automatic fallback capabilities, ensuring high availability and reliability for AI-powered features.

## Supported AI Providers

### 1. OpenAI (GPT-4)
- **Models**: GPT-4o (primary), GPT-3.5-turbo (fallback)
- **Environment Variable**: `OPENAI_API_KEY`
- **Priority**: 1 (highest)
- **Features**: Chat completions, text generation
- **Rate Limits**: Handled with exponential backoff

### 2. Anthropic Claude
- **Models**: Claude-3.5-<PERSON><PERSON> (primary), <PERSON>-<PERSON>-<PERSON><PERSON> (fallback)
- **Environment Variable**: `ANTHROPIC_API_KEY`
- **Priority**: 2
- **Features**: Advanced reasoning, long-form content generation
- **Rate Limits**: Handled with exponential backoff

### 3. Google Gemini
- **Models**: Gemini-1.5-Pro (primary), Gemini-1.5-Flash (fallback)
- **Environment Variable**: `GOOGLE_AI_API_KEY`
- **Priority**: 3
- **Features**: Multimodal capabilities, fast inference
- **Rate Limits**: Handled with exponential backoff

### 4. Groq Cloud
- **Models**: Llama-3.1-70B-Versatile (primary), Llama-3.1-8B-Instant (fallback)
- **Environment Variable**: `GROQ_API_KEY`
- **Priority**: 4
- **Features**: Ultra-fast inference, open-source models
- **Rate Limits**: Handled with exponential backoff

### 5. Novita AI
- **Models**: Meta-Llama-3.1-70B-Instruct (primary), Meta-Llama-3.1-8B-Instruct (fallback)
- **Environment Variable**: `NOVITA_API_KEY`
- **Priority**: 5
- **Features**: Cost-effective inference, multiple model options
- **Rate Limits**: Handled with exponential backoff

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# AI Service API Keys (Optional - set only if you have API keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
NOVITA_API_KEY=your_novita_api_key_here
```

### Getting API Keys

1. **OpenAI**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Anthropic**: Visit [Anthropic Console](https://console.anthropic.com/)
3. **Google AI**: Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
4. **Groq**: Visit [Groq Console](https://console.groq.com/keys)
5. **Novita AI**: Visit [Novita AI Dashboard](https://novita.ai/settings/key-management)

## Usage

### Basic Usage

```javascript
const MultiModelAIClient = require('./multiModelAIClient');

const client = new MultiModelAIClient();

// Generate content with automatic fallback
const result = await client.generateWithFallback('Your prompt here', {
  preferredModel: 'claude',  // Optional: specify preferred model
  temperature: 0.7,          // Optional: creativity level (0-1)
  maxTokens: 1500,          // Optional: maximum response length
  maxRetries: 3             // Optional: retry attempts per model
});

console.log(result.content);    // Generated content
console.log(result.modelUsed);  // Which model was actually used
console.log(result.attempt);    // Which attempt succeeded
```

### Advanced Usage

```javascript
// Check available models
const availableModels = client.getAvailableModels();
console.log('Available models:', availableModels);

// Check model status
const modelStatus = client.getModelStatus();
console.log('Model status:', modelStatus);

// Generate with specific model (no fallback)
const result = await client.generateWithModel('openai', 'Your prompt', {
  temperature: 0.5,
  maxTokens: 2000
});
```

## Fallback Chain

The system automatically tries models in priority order:
1. OpenAI GPT-4 (if configured)
2. Anthropic Claude (if configured)
3. Google Gemini (if configured)
4. Groq Cloud (if configured)
5. Novita AI (if configured)

If a preferred model is specified, it will be tried first, followed by the remaining models in priority order.

## Error Handling

The system handles various error scenarios:

- **Authentication Errors**: Invalid API keys
- **Rate Limiting**: Automatic retry with exponential backoff
- **Network Issues**: Timeout and connection errors
- **Model Unavailability**: Automatic fallback to next available model
- **Quota Exceeded**: Graceful degradation

## Retry Logic

- **Exponential Backoff**: 1s, 2s, 4s, 8s delays between retries
- **Maximum Retries**: Configurable per request (default: 3)
- **Per-Model Retries**: Each model gets its own retry attempts
- **Fallback Chain**: If all retries fail for one model, try the next

## Performance Considerations

- **Concurrent Requests**: The client supports multiple simultaneous requests
- **Connection Pooling**: Efficient HTTP connection management
- **Response Caching**: Consider implementing caching for repeated requests
- **Model Selection**: Choose faster models (like Groq) for real-time applications

## Monitoring and Logging

The system provides comprehensive logging:

```javascript
// Enable debug logging
process.env.DEBUG = 'ai-client:*';

// Logs include:
// - Client initialization status
// - Request/response timing
// - Error details and retry attempts
// - Fallback chain execution
// - Model performance metrics
```

## Testing

### Unit Tests

Run the comprehensive test suite:

```bash
npm test -- tests/multiModelAIClient.test.js
```

### Integration Tests

Test with real API calls:

```bash
node test-ai-integrations.js
```

### Manual Testing

```javascript
const client = new MultiModelAIClient();

// Test specific provider
const result = await client.generateWithFallback('Hello, world!', {
  preferredModel: 'claude'
});
```

## Security Best Practices

1. **API Key Management**: Store API keys in environment variables, never in code
2. **Rate Limiting**: Implement application-level rate limiting
3. **Input Validation**: Sanitize prompts before sending to AI services
4. **Output Filtering**: Review AI responses for sensitive content
5. **Audit Logging**: Log AI service usage for compliance

## Troubleshooting

### Common Issues

1. **"Client not initialized"**: Check if API key is set and valid
2. **"Rate limit exceeded"**: Implement request throttling or upgrade API plan
3. **"All models failed"**: Verify at least one API key is configured correctly
4. **Slow responses**: Consider using faster models like Groq for real-time features

### Debug Mode

Enable detailed logging:

```bash
DEBUG=ai-client:* node your-app.js
```

### Health Check

Check AI service status:

```bash
curl http://localhost:3000/health/status
```

## Cost Optimization

1. **Model Selection**: Use appropriate models for each use case
2. **Token Management**: Optimize prompt length and response limits
3. **Caching**: Cache responses for repeated requests
4. **Fallback Strategy**: Use cheaper models as fallbacks
5. **Usage Monitoring**: Track API usage and costs

## Future Enhancements

- [ ] Response streaming for real-time applications
- [ ] Custom model fine-tuning support
- [ ] Advanced prompt templating
- [ ] Multi-modal input support (images, audio)
- [ ] Conversation memory management
- [ ] A/B testing for model performance
- [ ] Cost tracking and budgeting
- [ ] Custom retry strategies per use case
