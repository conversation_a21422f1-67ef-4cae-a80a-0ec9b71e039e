const AnalyticsService = require('./analyticsService');
const cacheService = require('./cacheService');

class AnalyticsController {
  constructor() {
    this.analyticsService = new AnalyticsService();
    this.cacheService = cacheService;
  }

  async getDashboard(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const timeRange = req.query.timeRange || '30 days';

      const analytics = await this.analyticsService.getUserAnalytics(userId, timeRange);
      
      res.json({
        message: 'Analytics dashboard data retrieved successfully',
        analytics
      });
    } catch (error) {
      console.error('Get dashboard error:', error);
      res.status(500).json({ error: 'Failed to retrieve analytics dashboard' });
    }
  }

  async getDashboardAnalytics(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const analytics = await this.analyticsService.getDashboardAnalytics(userId);

      res.json({
        message: 'Dashboard analytics retrieved successfully',
        analytics
      });
    } catch (error) {
      console.error('Get dashboard analytics error:', error);
      res.status(500).json({ error: 'Failed to get dashboard analytics' });
    }
  }

  async trackEvent(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { eventType, eventData, metadata } = req.body;

      if (!eventType) {
        return res.status(400).json({ error: 'Event type is required' });
      }

      const eventId = await this.analyticsService.trackEvent(
        userId, 
        eventType, 
        eventData || {}, 
        metadata || {}
      );
      
      res.json({
        message: 'Event tracked successfully',
        eventId
      });
    } catch (error) {
      console.error('Track event error:', error);
      res.status(500).json({ error: 'Failed to track event' });
    }
  }

  async updateResumeMetrics(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { resumeId } = req.params;
      const metrics = req.body;

      // Verify resume belongs to user
      const resume = await this.getResumeOwnership(userId, resumeId);
      if (!resume) {
        return res.status(403).json({ error: 'Resume not found or access denied' });
      }

      await this.analyticsService.updateResumePerformance(resumeId, metrics);
      
      res.json({
        message: 'Resume metrics updated successfully'
      });
    } catch (error) {
      console.error('Update resume metrics error:', error);
      res.status(500).json({ error: 'Failed to update resume metrics' });
    }
  }

  async getPredictiveAnalytics(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { jobTitle, company, description, location } = req.body;

      if (!jobTitle || !company) {
        return res.status(400).json({
          error: 'Job title and company are required'
        });
      }

      const jobData = { title: jobTitle, company, description, location };
      const prediction = await this.analyticsService.getPredictiveAnalytics(userId, jobData);

      res.json({
        message: 'Predictive analytics generated successfully',
        prediction
      });
    } catch (error) {
      console.error('Get predictive analytics error:', error);
      res.status(500).json({ error: 'Failed to generate predictive analytics' });
    }
  }

  async getApplicationMetrics(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const stats = await this.analyticsService.getApplicationStatistics(userId);

      res.json({
        message: 'Application metrics retrieved successfully',
        metrics: stats
      });
    } catch (error) {
      console.error('Get application metrics error:', error);
      res.status(500).json({ error: 'Failed to get application metrics' });
    }
  }

  async getJobMarketInsights(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const insights = await this.analyticsService.getJobMarketInsights(userId);

      res.json({
        message: 'Job market insights retrieved successfully',
        insights
      });
    } catch (error) {
      console.error('Get job market insights error:', error);
      res.status(500).json({ error: 'Failed to get job market insights' });
    }
  }

  async getResumePerformance(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const performance = await this.analyticsService.getResumePerformanceMetrics(userId);

      res.json({
        message: 'Resume performance analysis retrieved successfully',
        performance
      });
    } catch (error) {
      console.error('Get resume performance error:', error);
      res.status(500).json({ error: 'Failed to get resume performance analysis' });
    }
  }

  async getSuccessPatterns(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const patterns = await this.analyticsService.getSuccessPatterns(userId);
      
      res.json({
        message: 'Success patterns analyzed successfully',
        patterns
      });
    } catch (error) {
      console.error('Get success patterns error:', error);
      res.status(500).json({ error: 'Failed to analyze success patterns' });
    }
  }

  async getWeeklyTrends(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const trends = await this.analyticsService.getWeeklyTrends(userId);

      res.json({
        message: 'Weekly trends retrieved successfully',
        trends
      });
    } catch (error) {
      console.error('Get weekly trends error:', error);
      res.status(500).json({ error: 'Failed to get weekly trends' });
    }
  }

  async getOptimizationRecommendations(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const recommendations = await this.analyticsService.generateOptimizationRecommendations(userId);
      
      res.json({
        message: 'Optimization recommendations generated successfully',
        recommendations
      });
    } catch (error) {
      console.error('Get optimization recommendations error:', error);
      res.status(500).json({ error: 'Failed to generate optimization recommendations' });
    }
  }

  async getDetailedMetrics(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { metricType } = req.params;
      const timeRange = req.query.timeRange || '30 days';

      let metrics;
      
      switch (metricType) {
        case 'applications':
          metrics = await this.analyticsService.getApplicationStats(userId, 
            this.analyticsService.getTimeFilter(timeRange));
          break;
        case 'resumes':
          metrics = await this.analyticsService.getResumePerformance(userId);
          break;
        case 'emails':
          metrics = await this.analyticsService.getEmailMetrics(userId, 
            this.analyticsService.getTimeFilter(timeRange));
          break;
        default:
          return res.status(400).json({ error: 'Invalid metric type' });
      }
      
      res.json({
        message: `${metricType} metrics retrieved successfully`,
        metrics
      });
    } catch (error) {
      console.error('Get detailed metrics error:', error);
      res.status(500).json({ error: 'Failed to retrieve detailed metrics' });
    }
  }

  async generateReport(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { reportType, timeRange, format } = req.body;

      if (!reportType) {
        return res.status(400).json({ error: 'Report type is required' });
      }

      const analytics = await this.analyticsService.getUserAnalytics(userId, timeRange || '30 days');
      const recommendations = await this.analyticsService.generateOptimizationRecommendations(userId);

      const report = {
        reportType,
        timeRange: timeRange || '30 days',
        generatedAt: new Date().toISOString(),
        summary: this.generateReportSummary(analytics),
        analytics,
        recommendations,
        insights: this.generateInsights(analytics)
      };

      if (format === 'pdf') {
        report.note = 'PDF generation feature would be implemented here';
      }
      
      res.json({
        message: 'Report generated successfully',
        report
      });
    } catch (error) {
      console.error('Generate report error:', error);
      res.status(500).json({ error: 'Failed to generate report' });
    }
  }

  generateReportSummary(analytics) {
    const { applicationStats, resumePerformance } = analytics;
    
    return {
      totalApplications: applicationStats.total_applications,
      successRate: applicationStats.success_rate,
      totalResumes: resumePerformance.length,
      avgResponseRate: resumePerformance.reduce((sum, resume) => 
        sum + (resume.response_rate || 0), 0) / resumePerformance.length || 0,
      topPerformingResume: resumePerformance.reduce((best, current) => 
        (current.response_rate || 0) > (best.response_rate || 0) ? current : best, resumePerformance[0])
    };
  }

  generateInsights(analytics) {
    const insights = [];
    const { applicationStats, resumePerformance, successPatterns } = analytics;

    if (applicationStats.total_applications > 0) {
      if (applicationStats.success_rate > 20) {
        insights.push({
          type: 'positive',
          category: 'applications',
          message: `Your ${applicationStats.success_rate.toFixed(1)}% success rate is above average!`
        });
      } else if (applicationStats.success_rate < 5) {
        insights.push({
          type: 'improvement',
          category: 'applications',
          message: 'Consider refining your targeting strategy to improve success rates.'
        });
      }
    }

    const bestResume = resumePerformance.reduce((best, current) => 
      (current.response_rate || 0) > (best.response_rate || 0) ? current : best, resumePerformance[0]);
    
    if (bestResume && bestResume.response_rate > 15) {
      insights.push({
        type: 'positive',
        category: 'resume',
        message: `Your "${bestResume.title}" resume has a ${bestResume.response_rate.toFixed(1)}% response rate!`
      });
    }

    if (successPatterns.length > 0) {
      const topPattern = successPatterns[0];
      insights.push({
        type: 'pattern',
        category: 'optimization',
        message: topPattern.description
      });
    }

    return insights;
  }

  // AI-Powered Analytics Controllers
  async getAIInsights(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { resumeData, includeApplications = true } = req.body;

      let jobApplications = [];
      if (includeApplications) {
        // Get recent applications for analysis
        jobApplications = await this.analyticsService.getUserApplications(userId, 10);
      }

      const insights = await this.analyticsService.generateAIInsights(
        userId,
        resumeData || null,
        jobApplications
      );
      
      res.json({
        message: 'AI insights generated successfully',
        insights
      });
    } catch (error) {
      console.error('AI insights error:', error);
      res.status(500).json({ error: 'Failed to generate AI insights' });
    }
  }

  async predictApplicationSuccess(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { resumeData, jobData } = req.body;

      if (!resumeData || !jobData) {
        return res.status(400).json({ 
          error: 'Resume data and job data are required' 
        });
      }

      // Get user's application history for context
      const userHistory = await this.analyticsService.getUserApplications(userId, 5);

      const prediction = await this.analyticsService.predictApplicationSuccess(
        resumeData,
        jobData,
        userHistory
      );
      
      res.json({
        message: 'Application success prediction completed',
        prediction
      });
    } catch (error) {
      console.error('Success prediction error:', error);
      res.status(500).json({ error: 'Failed to predict application success' });
    }
  }

  async getMarketTrends(req, res) {
    try {
      const { industry, skills } = req.query;

      // Get market data based on query parameters
      const industryData = industry ? [{ industry, growth: 'stable' }] : [];
      const skillsData = skills ? skills.split(',').map(skill => ({ skill: skill.trim(), demand: 'medium' })) : [];

      const trends = await this.analyticsService.generateMarketTrends(
        industryData,
        skillsData
      );
      
      res.json({
        message: 'Market trends analysis completed',
        trends
      });
    } catch (error) {
      console.error('Market trends error:', error);
      res.status(500).json({ error: 'Failed to analyze market trends' });
    }
  }

  async getPersonalizedStrategy(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const { goals } = req.body;

      // Get user data for strategy generation
      const userApplications = await this.analyticsService.getUserApplications(userId, 20);
      const userResumes = await this.analyticsService.getUserResumes(userId);
      const userAnalytics = await this.analyticsService.getUserAnalytics(userId);

      const userData = {
        userId,
        applications: userApplications,
        resumes: userResumes,
        analytics: userAnalytics
      };

      const strategy = await this.analyticsService.generatePersonalizedStrategy(
        userData,
        goals || {}
      );
      
      res.json({
        message: 'Personalized strategy generated successfully',
        strategy
      });
    } catch (error) {
      console.error('Personalized strategy error:', error);
      res.status(500).json({ error: 'Failed to generate personalized strategy' });
    }
  }

  async getEnhancedDashboard(req, res) {
    try {
      const userId = req.user.id || req.user.userId;
      const timeRange = req.query.timeRange || '30 days';

      // Get standard analytics
      const standardAnalytics = await this.analyticsService.getUserAnalytics(userId, timeRange);
      
      // Get AI-powered insights
      const userApplications = await this.analyticsService.getUserApplications(userId, 10);
      const aiInsights = await this.analyticsService.generateAIInsights(
        userId,
        null,
        userApplications
      );

      // Get market trends
      const marketTrends = await this.analyticsService.generateMarketTrends([], []);

      const enhancedDashboard = {
        ...standardAnalytics,
        aiInsights,
        marketTrends,
        lastUpdated: new Date().toISOString()
      };
      
      res.json({
        message: 'Enhanced dashboard data retrieved successfully',
        dashboard: enhancedDashboard
      });
    } catch (error) {
      console.error('Enhanced dashboard error:', error);
      res.status(500).json({ error: 'Failed to retrieve enhanced dashboard data' });
    }
  }

  async getResumeOwnership(userId, resumeId) {
    return new Promise((resolve, reject) => {
      const database = require('./database');
      database.get().get(
        'SELECT id FROM resumes WHERE id = ? AND user_id = ?',
        [resumeId, userId],
        (err, row) => {
          if (err) {
            reject(err);
          } else {
            resolve(row);
          }
        }
      );
    });
  }
}

module.exports = AnalyticsController;