// AI Configuration and Model Management
class AIConfig {
  constructor() {
    this.models = {
      openai: {
        name: 'OpenAI GPT-4',
        provider: 'openai',
        model: 'gpt-4o',
        fallback: 'gpt-3.5-turbo',
        enabled: !!process.env.OPENAI_API_KEY,
        priority: 1,
        maxTokens: 4096,
        temperature: 0.7
      },
      claude: {
        name: 'Anthropic Claude 3.5',
        provider: 'anthropic',
        model: 'claude-3-5-sonnet-20241022',
        enabled: !!process.env.ANTHROPIC_API_KEY,
        priority: 2,
        maxTokens: 4096,
        temperature: 0.7
      },
      gemini: {
        name: 'Google Gemini Pro',
        provider: 'google',
        model: 'gemini-pro',
        enabled: !!process.env.GOOGLE_AI_API_KEY,
        priority: 3,
        maxTokens: 4096,
        temperature: 0.7
      }
    };

    this.fallbackChain = this.buildFallbackChain();
  }

  buildFallbackChain() {
    return Object.entries(this.models)
      .filter(([, config]) => config.enabled)
      .sort(([, a], [, b]) => a.priority - b.priority)
      .map(([key]) => key);
  }

  getAvailableModels() {
    return Object.entries(this.models)
      .filter(([, config]) => config.enabled)
      .map(([key, config]) => ({
        key,
        name: config.name,
        provider: config.provider,
        priority: config.priority
      }));
  }

  getModelConfig(modelKey) {
    return this.models[modelKey];
  }

  getPrimaryModel() {
    return this.fallbackChain[0] || null;
  }

  getNextFallback(currentModel) {
    const currentIndex = this.fallbackChain.indexOf(currentModel);
    return currentIndex !== -1 && currentIndex < this.fallbackChain.length - 1
      ? this.fallbackChain[currentIndex + 1]
      : null;
  }

  isModelAvailable(modelKey) {
    return this.models[modelKey]?.enabled || false;
  }
}

module.exports = AIConfig;