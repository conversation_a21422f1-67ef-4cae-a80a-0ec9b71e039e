#!/usr/bin/env node

/**
 * Manual test script for AI service integrations
 * This script tests the actual implementations without mocking
 */

require('dotenv').config();
const MultiModelAIClient = require('./multiModelAIClient');

async function testAIIntegrations() {
  console.log('🧪 Testing AI Service Integrations...\n');

  // Initialize the client
  const client = new MultiModelAIClient();
  
  // Check available models
  const availableModels = client.getAvailableModels();
  console.log('📋 Available Models:');
  availableModels.forEach(model => {
    console.log(`  - ${model.name} (${model.key}) - Priority: ${model.priority}`);
  });
  console.log();

  // Check model status
  const modelStatus = client.getModelStatus();
  console.log('🔍 Model Status:');
  modelStatus.forEach(status => {
    console.log(`  - ${status.name}: ${status.status} (Client: ${status.clientInitialized ? 'Yes' : 'No'})`);
  });
  console.log();

  // Test prompt
  const testPrompt = "Write a brief professional summary for a software engineer with 3 years of experience in JavaScript and React.";

  // Test each available model
  for (const model of availableModels) {
    if (modelStatus.find(s => s.key === model.key)?.clientInitialized) {
      console.log(`🤖 Testing ${model.name}...`);
      try {
        const startTime = Date.now();
        const result = await client.generateWithFallback(testPrompt, {
          preferredModel: model.key,
          maxRetries: 1,
          temperature: 0.7,
          maxTokens: 200
        });
        const endTime = Date.now();
        
        console.log(`✅ Success! (${endTime - startTime}ms)`);
        console.log(`   Model Used: ${result.modelUsed}`);
        console.log(`   Attempt: ${result.attempt}`);
        console.log(`   Response: ${result.content.substring(0, 100)}...`);
        console.log();
      } catch (error) {
        console.log(`❌ Failed: ${error.message}`);
        console.log();
      }
    } else {
      console.log(`⏭️  Skipping ${model.name} (client not initialized)`);
      console.log();
    }
  }

  // Test fallback chain
  console.log('🔄 Testing Fallback Chain...');
  try {
    const result = await client.generateWithFallback(testPrompt, {
      temperature: 0.7,
      maxTokens: 200,
      maxRetries: 1
    });
    console.log(`✅ Fallback Success!`);
    console.log(`   Model Used: ${result.modelUsed}`);
    console.log(`   Response: ${result.content.substring(0, 100)}...`);
  } catch (error) {
    console.log(`❌ Fallback Failed: ${error.message}`);
  }
  console.log();

  console.log('🏁 Test Complete!');
}

// Run the test
if (require.main === module) {
  testAIIntegrations().catch(console.error);
}

module.exports = testAIIntegrations;
