/**
 * Example demonstrating enhanced error handling usage
 * Shows how to use custom error classes and asyncHandler wrapper
 */

const { 
  AuthenticationError, 
  ValidationError, 
  NotFoundError,
  asyncHandler 
} = require('../middleware/errorHandler');

/**
 * Example controller showing proper error handling usage
 */
class ExampleController {
  
  /**
   * Example using asyncHandler wrapper - automatically catches errors
   */
  getUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    // Validation
    if (!id || isNaN(id)) {
      throw new ValidationError('User ID must be a valid number');
    }
    
    // Simulate database call
    const user = await this.findUserById(id);
    
    if (!user) {
      throw new NotFoundError('User');
    }
    
    res.json({
      success: true,
      data: user
    });
  });

  /**
   * Example with manual error handling (legacy style)
   */
  async updateUser(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      // Authentication check
      if (req.user.id !== parseInt(id) && req.user.role !== 'admin') {
        throw new AuthenticationError('Cannot update other user accounts');
      }
      
      // Validation
      if (!updateData.email || !this.isValidEmail(updateData.email)) {
        throw new ValidationError('Valid email is required', [
          { field: 'email', message: 'Must be a valid email address' }
        ]);
      }
      
      const updatedUser = await this.updateUserById(id, updateData);
      
      res.json({
        success: true,
        data: updatedUser
      });
      
    } catch (error) {
      // Pass error to global error handler
      next(error);
    }
  }

  /**
   * Example helper methods
   */
  async findUserById(id) {
    // Simulate database call that might fail
    if (id === '999') {
      throw new Error('Database connection failed'); // Will be caught by errorHandler
    }
    
    if (id === '404') {
      return null; // User not found
    }
    
    return {
      id: parseInt(id),
      name: 'John Doe',
      email: '<EMAIL>'
    };
  }

  async updateUserById(id, data) {
    // Simulate database update
    return {
      id: parseInt(id),
      ...data,
      updatedAt: new Date()
    };
  }

  isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
}

/**
 * Usage examples in route definitions
 */
const exampleRoutes = `
// With asyncHandler - automatically catches async errors
app.get('/api/users/:id', authenticate, exampleController.getUser);

// Manual error handling - you handle try/catch
app.put('/api/users/:id', authenticate, exampleController.updateUser.bind(exampleController));

// Multiple middleware with asyncHandler
app.post('/api/users', 
  authenticate, 
  validateUserInput, 
  asyncHandler(async (req, res) => {
    const newUser = await userService.createUser(req.body);
    res.status(201).json({ success: true, data: newUser });
  })
);
`;

/**
 * Error throwing examples
 */
const errorExamples = `
// Throw custom errors anywhere in your code:

// Validation errors
throw new ValidationError('Email is required');
throw new ValidationError('Invalid input data', [
  { field: 'email', message: 'Must be valid email' },
  { field: 'password', message: 'Must be at least 8 characters' }
]);

// Authentication errors  
throw new AuthenticationError('Invalid credentials');
throw new AuthenticationError('Token has expired');

// Authorization errors
throw new AuthorizationError('Access denied');
throw new AuthorizationError('Insufficient permissions');

// Not found errors
throw new NotFoundError('User');
throw new NotFoundError('Resource');

// Conflict errors
throw new ConflictError('Email already exists');

// Rate limit errors
throw new RateLimitError('Too many requests');

// External service errors
throw new ExternalServiceError('PaymentService', 'Payment processing failed');
`;

module.exports = {
  ExampleController,
  exampleRoutes,
  errorExamples
};