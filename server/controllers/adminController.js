const database = require('../database');
const bcrypt = require('bcrypt');
const { logger } = require('../utils/logger');
const encryptionService = require('../utils/encryptionService');

/**
 * Admin Controller
 * Handles admin user management and system administration
 */

/**
 * Get all users with pagination and filtering
 */
async function getUsers(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = '',
      status = '',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const db = database.get();

    // Build WHERE clause
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (search) {
      whereClause += ' AND (u.email LIKE ? OR u.name LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (role) {
      whereClause += ' AND u.user_role = ?';
      params.push(role);
    }

    if (status) {
      whereClause += ' AND u.account_status = ?';
      params.push(status);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      ${whereClause}
    `;
    const countResult = await db.get(countQuery, params);
    const total = countResult.total;

    // Get users with admin information
    const usersQuery = `
      SELECT 
        u.id, u.email, u.name, u.first_name, u.last_name,
        u.user_role, u.account_status, u.is_active, u.email_verified,
        u.subscription_tier, u.last_login_at, u.failed_login_attempts,
        u.two_factor_enabled, u.created_at, u.updated_at,
        au.admin_level, au.last_admin_action_at,
        COUNT(r.id) as resume_count,
        COUNT(j.id) as job_count
      FROM users u
      LEFT JOIN admin_users au ON u.id = au.user_id
      LEFT JOIN resumes r ON u.id = r.user_id AND r.is_active = true
      LEFT JOIN jobs j ON u.id = j.user_id
      ${whereClause}
      GROUP BY u.id
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    const users = await db.all(usersQuery, [...params, limit, offset]);

    // Log admin action
    await logAdminAction(req.user.id, 'USER_LIST_VIEWED', {
      filters: { search, role, status },
      pagination: { page, limit },
      resultCount: users.length
    }, req);

    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          ...user,
          // Remove sensitive fields
          password_hash: undefined
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Admin: Get users error', {
      error: error.message,
      adminId: req.user?.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve users',
      code: 'GET_USERS_ERROR'
    });
  }
}

/**
 * Get specific user details
 */
async function getUser(req, res) {
  try {
    const { userId } = req.params;
    const db = database.get();

    const user = await db.get(`
      SELECT 
        u.*, 
        au.admin_level, au.permissions as admin_permissions,
        au.last_admin_action_at, au.created_by as admin_created_by
      FROM users u
      LEFT JOIN admin_users au ON u.id = au.user_id
      WHERE u.id = ?
    `, [userId]);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get user statistics
    const stats = await db.get(`
      SELECT 
        COUNT(DISTINCT r.id) as resume_count,
        COUNT(DISTINCT j.id) as job_count,
        COUNT(DISTINCT ja.id) as application_count
      FROM users u
      LEFT JOIN resumes r ON u.id = r.user_id AND r.is_active = true
      LEFT JOIN jobs j ON u.id = j.user_id
      LEFT JOIN job_applications ja ON u.id = ja.user_id
      WHERE u.id = ?
    `, [userId]);

    // Parse admin permissions
    let adminPermissions = {};
    try {
      adminPermissions = user.admin_permissions ? JSON.parse(user.admin_permissions) : {};
    } catch (error) {
      logger.warn('Admin: Failed to parse admin permissions', {
        userId,
        error: error.message
      });
    }

    // Log admin action
    await logAdminAction(req.user.id, 'USER_VIEWED', {
      targetUserId: userId
    }, req);

    res.json({
      success: true,
      data: {
        user: {
          ...user,
          password_hash: undefined, // Remove sensitive field
          admin_permissions: adminPermissions
        },
        stats
      }
    });
  } catch (error) {
    logger.error('Admin: Get user error', {
      error: error.message,
      userId: req.params.userId,
      adminId: req.user?.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve user',
      code: 'GET_USER_ERROR'
    });
  }
}

/**
 * Update user information
 */
async function updateUser(req, res) {
  try {
    const { userId } = req.params;
    const {
      name,
      first_name,
      last_name,
      user_role,
      account_status,
      is_active,
      email_verified,
      subscription_tier
    } = req.body;

    const db = database.get();

    // Get current user data for audit log
    const currentUser = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if admin is trying to modify a super_admin (only super_admin can do this)
    if (currentUser.user_role === 'super_admin' && req.user.user_role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        error: 'Cannot modify super admin user',
        code: 'CANNOT_MODIFY_SUPER_ADMIN'
      });
    }

    // Build update query
    const updateFields = [];
    const updateParams = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }
    if (first_name !== undefined) {
      updateFields.push('first_name = ?');
      updateParams.push(first_name);
    }
    if (last_name !== undefined) {
      updateFields.push('last_name = ?');
      updateParams.push(last_name);
    }
    if (user_role !== undefined && req.user.user_role === 'super_admin') {
      updateFields.push('user_role = ?');
      updateParams.push(user_role);
    }
    if (account_status !== undefined) {
      updateFields.push('account_status = ?');
      updateParams.push(account_status);
    }
    if (is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateParams.push(is_active);
    }
    if (email_verified !== undefined) {
      updateFields.push('email_verified = ?');
      updateParams.push(email_verified);
    }
    if (subscription_tier !== undefined) {
      updateFields.push('subscription_tier = ?');
      updateParams.push(subscription_tier);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid fields to update',
        code: 'NO_UPDATE_FIELDS'
      });
    }

    updateFields.push('updated_at = ?');
    updateParams.push(new Date().toISOString());
    updateParams.push(userId);

    const updateQuery = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await db.run(updateQuery, updateParams);

    // Get updated user
    const updatedUser = await db.get('SELECT * FROM users WHERE id = ?', [userId]);

    // Log admin action
    await logAdminAction(req.user.id, 'USER_UPDATED', {
      targetUserId: userId,
      changes: req.body,
      oldValues: {
        name: currentUser.name,
        user_role: currentUser.user_role,
        account_status: currentUser.account_status,
        is_active: currentUser.is_active
      }
    }, req);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: {
          ...updatedUser,
          password_hash: undefined
        }
      }
    });
  } catch (error) {
    logger.error('Admin: Update user error', {
      error: error.message,
      userId: req.params.userId,
      adminId: req.user?.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to update user',
      code: 'UPDATE_USER_ERROR'
    });
  }
}

/**
 * Create new admin user
 */
async function createAdminUser(req, res) {
  try {
    const {
      email,
      password,
      name,
      first_name,
      last_name,
      admin_level = 1,
      permissions = {}
    } = req.body;

    // Only super_admin can create admin users
    if (req.user.user_role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        error: 'Only super admin can create admin users',
        code: 'INSUFFICIENT_PRIVILEGES'
      });
    }

    const db = database.get();

    // Check if user already exists
    const existingUser = await db.get('SELECT id FROM users WHERE email = ?', [email]);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'User with this email already exists',
        code: 'USER_EXISTS'
      });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 14);

    // Create user
    const userResult = await db.run(`
      INSERT INTO users (
        email, password_hash, name, first_name, last_name,
        user_role, account_status, email_verified, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      email, passwordHash, name, first_name, last_name,
      'admin', 'active', true, true
    ]);

    const userId = userResult.lastID;

    // Create admin user entry
    await db.run(`
      INSERT INTO admin_users (
        user_id, admin_level, permissions, created_by
      ) VALUES (?, ?, ?, ?)
    `, [
      userId,
      admin_level,
      JSON.stringify(permissions),
      req.user.id
    ]);

    // Log admin action
    await logAdminAction(req.user.id, 'ADMIN_USER_CREATED', {
      newAdminUserId: userId,
      email,
      admin_level
    }, req);

    res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      data: {
        userId,
        email,
        admin_level
      }
    });
  } catch (error) {
    logger.error('Admin: Create admin user error', {
      error: error.message,
      adminId: req.user?.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to create admin user',
      code: 'CREATE_ADMIN_ERROR'
    });
  }
}

/**
 * Get system statistics
 */
async function getSystemStats(req, res) {
  try {
    const db = database.get();

    // Get user statistics
    const userStats = await db.get(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN user_role = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN user_role = 'super_admin' THEN 1 END) as super_admin_users,
        COUNT(CASE WHEN account_status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN email_verified = true THEN 1 END) as verified_users,
        COUNT(CASE WHEN created_at > datetime('now', '-30 days') THEN 1 END) as new_users_30d
      FROM users
    `);

    // Get content statistics
    const contentStats = await db.get(`
      SELECT 
        COUNT(DISTINCT r.id) as total_resumes,
        COUNT(DISTINCT j.id) as total_jobs,
        COUNT(DISTINCT ja.id) as total_applications,
        COUNT(CASE WHEN r.created_at > datetime('now', '-7 days') THEN 1 END) as new_resumes_7d,
        COUNT(CASE WHEN j.created_at > datetime('now', '-7 days') THEN 1 END) as new_jobs_7d
      FROM resumes r, jobs j, job_applications ja
    `);

    // Get recent audit events
    const recentAuditEvents = await db.all(`
      SELECT action, COUNT(*) as count
      FROM audit_logs
      WHERE created_at > datetime('now', '-24 hours')
      GROUP BY action
      ORDER BY count DESC
      LIMIT 10
    `);

    // Log admin action
    await logAdminAction(req.user.id, 'SYSTEM_STATS_VIEWED', {}, req);

    res.json({
      success: true,
      data: {
        users: userStats,
        content: contentStats,
        recentActivity: recentAuditEvents,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Admin: Get system stats error', {
      error: error.message,
      adminId: req.user?.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system statistics',
      code: 'GET_STATS_ERROR'
    });
  }
}

/**
 * Log admin actions to audit log
 * @param {string} adminId - Admin user ID
 * @param {string} action - Action performed
 * @param {Object} metadata - Additional metadata
 * @param {Object} req - Express request object
 */
async function logAdminAction(adminId, action, metadata, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      adminId,
      action,
      'admin_action',
      null,
      JSON.stringify(metadata),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Admin: Failed to log admin action', {
      error: error.message,
      adminId,
      action
    });
  }
}

module.exports = {
  getUsers,
  getUser,
  updateUser,
  createAdminUser,
  getSystemStats
};
