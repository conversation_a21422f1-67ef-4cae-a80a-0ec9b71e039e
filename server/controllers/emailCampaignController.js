const EmailService = require('../services/emailService');
const RecruiterDiscoveryService = require('../services/recruiterDiscovery');
const database = require('../database');

/**
 * Email Campaign Controller
 * Handles all email automation endpoints
 */
class EmailCampaignController {
  constructor() {
    this.emailService = new EmailService();
    this.recruiterService = new RecruiterDiscoveryService();
    this.db = database.get();
  }

  /**
   * Create email campaign
   */
  async createCampaign(req, res) {
    try {
      const userId = req.user.userId; // Use userId instead of id
      const {
        name,
        subject,
        content,
        template_id,
        schedule_type = 'immediate',
        scheduled_at,
        target_companies,
        target_roles
      } = req.body;

      if (!name || !subject) {
        return res.status(400).json({
          error: 'Campaign name and subject are required'
        });
      }

      const campaignId = await this.saveCampaign({
        user_id: userId,
        name,
        subject,
        template_id,
        schedule_type,
        scheduled_at,
        target_companies: JSON.stringify(target_companies || []),
        target_roles: JSON.stringify(target_roles || [])
      });

      res.json({
        success: true,
        message: 'Campaign created successfully',
        campaign_id: campaignId
      });

    } catch (error) {
      console.error('Create campaign error:', error);
      res.status(500).json({ error: 'Failed to create campaign' });
    }
  }

  /**
   * Get user campaigns
   */
  async getCampaigns(req, res) {
    try {
      const userId = req.user.userId;
      const { status, limit = 50 } = req.query;

      const campaigns = await this.getUserCampaigns(userId, { status, limit });
      
      res.json({
        success: true,
        campaigns
      });

    } catch (error) {
      console.error('Get campaigns error:', error);
      res.status(500).json({ error: 'Failed to fetch campaigns' });
    }
  }

  /**
   * Update campaign
   */
  async updateCampaign(req, res) {
    try {
      const userId = req.user.userId;
      const campaignId = req.params.id;
      const updateData = req.body;

      const changes = await this.updateCampaignData(userId, campaignId, updateData);
      
      if (changes === 0) {
        return res.status(404).json({ error: 'Campaign not found' });
      }

      res.json({
        success: true,
        message: 'Campaign updated successfully'
      });

    } catch (error) {
      console.error('Update campaign error:', error);
      res.status(500).json({ error: 'Failed to update campaign' });
    }
  }

  /**
   * Delete campaign
   */
  async deleteCampaign(req, res) {
    try {
      const userId = req.user.userId;
      const campaignId = req.params.id;

      const changes = await this.deleteCampaignData(userId, campaignId);
      
      if (changes === 0) {
        return res.status(404).json({ error: 'Campaign not found' });
      }

      res.json({
        success: true,
        message: 'Campaign deleted successfully'
      });

    } catch (error) {
      console.error('Delete campaign error:', error);
      res.status(500).json({ error: 'Failed to delete campaign' });
    }
  }

  /**
   * Send campaign
   */
  async sendCampaign(req, res) {
    try {
      const userId = req.user.userId;
      const campaignId = req.params.id;

      // Verify campaign belongs to user
      const campaign = await this.emailService.getCampaign(campaignId);
      if (!campaign || campaign.user_id !== userId) {
        return res.status(404).json({ error: 'Campaign not found' });
      }

      if (campaign.status === 'sent') {
        return res.status(400).json({ error: 'Campaign already sent' });
      }

      // Send campaign
      const result = await this.emailService.sendCampaign(campaignId);

      res.json({
        success: true,
        message: 'Campaign sent successfully',
        ...result
      });

    } catch (error) {
      console.error('Send campaign error:', error);
      res.status(500).json({ error: 'Failed to send campaign' });
    }
  }

  /**
   * Create email template
   */
  async createTemplate(req, res) {
    try {
      const userId = req.user.userId;
      const { name, type, subject, content, variables } = req.body;

      if (!name || !type || !subject || !content) {
        return res.status(400).json({
          error: 'Template name, type, subject, and content are required'
        });
      }

      const templateId = await this.saveTemplate({
        name,
        type,
        subject,
        content,
        variables: JSON.stringify(variables || []),
        created_by: userId
      });

      res.json({
        success: true,
        message: 'Template created successfully',
        template_id: templateId
      });

    } catch (error) {
      console.error('Create template error:', error);
      res.status(500).json({ error: 'Failed to create template' });
    }
  }

  /**
   * Get email templates
   */
  async getTemplates(req, res) {
    try {
      const userId = req.user.userId;
      const { type, include_system = 'true' } = req.query;

      const templates = await this.getUserTemplates(userId, {
        type,
        include_system: include_system === 'true'
      });

      res.json({
        success: true,
        templates
      });

    } catch (error) {
      console.error('Get templates error:', error);
      res.status(500).json({ error: 'Failed to fetch templates' });
    }
  }

  /**
   * Send individual email
   */
  async sendIndividualEmail(req, res) {
    try {
      const userId = req.user.userId;
      const { recipient_email, subject, content, template_id } = req.body;

      if (!recipient_email || !subject) {
        return res.status(400).json({
          error: 'Recipient email and subject are required'
        });
      }

      let emailContent = content;
      
      // Use template if specified
      if (template_id && !content) {
        const template = await this.emailService.getTemplate(template_id);
        if (template) {
          emailContent = template.content;
        }
      }

      if (!emailContent) {
        return res.status(400).json({
          error: 'Email content is required'
        });
      }

      const result = await this.emailService.sendEmail(
        userId,
        recipient_email,
        subject,
        emailContent
      );

      res.json({
        success: true,
        message: 'Email sent successfully',
        ...result
      });

    } catch (error) {
      console.error('Send email error:', error);
      res.status(500).json({ error: 'Failed to send email' });
    }
  }

  /**
   * Get email analytics
   */
  async getEmailAnalytics(req, res) {
    try {
      const userId = req.user.userId;
      const { campaign_id, period = '30d' } = req.query;

      const analytics = await this.emailService.getEmailAnalytics(
        userId,
        campaign_id || null
      );

      res.json({
        success: true,
        analytics,
        period
      });

    } catch (error) {
      console.error('Get analytics error:', error);
      res.status(500).json({ error: 'Failed to fetch analytics' });
    }
  }

  /**
   * Discover recruiters for companies
   */
  async discoverRecruiters(req, res) {
    try {
      const userId = req.user.userId;
      const { company_name, company_domain } = req.body;

      if (!company_name || !company_domain) {
        return res.status(400).json({
          error: 'Company name and domain are required'
        });
      }

      const result = await this.recruiterService.discoverRecruiters(
        userId,
        company_name,
        company_domain
      );

      res.json({
        success: true,
        message: `Discovered ${result.discovered} potential recruiters`,
        ...result
      });

    } catch (error) {
      console.error('Discover recruiters error:', error);
      res.status(500).json({ error: 'Failed to discover recruiters' });
    }
  }

  /**
   * Get recruiter contacts
   */
  async getRecruiterContacts(req, res) {
    try {
      const userId = req.user.userId;
      const filters = req.query;

      const contacts = await this.recruiterService.getRecruiterContacts(userId, filters);
      const statistics = await this.recruiterService.getContactStatistics(userId);

      res.json({
        success: true,
        contacts,
        statistics
      });

    } catch (error) {
      console.error('Get contacts error:', error);
      res.status(500).json({ error: 'Failed to fetch contacts' });
    }
  }

  /**
   * Update recruiter contact
   */
  async updateRecruiterContact(req, res) {
    try {
      const userId = req.user.userId;
      const contactId = req.params.id;
      const updateData = req.body;

      const changes = await this.recruiterService.updateRecruiterContact(
        userId,
        contactId,
        updateData
      );

      if (changes === 0) {
        return res.status(404).json({ error: 'Contact not found' });
      }

      res.json({
        success: true,
        message: 'Contact updated successfully'
      });

    } catch (error) {
      console.error('Update contact error:', error);
      res.status(500).json({ error: 'Failed to update contact' });
    }
  }

  /**
   * Track email opens
   */
  async trackEmailOpen(req, res) {
    try {
      const trackingId = req.params.trackingId;
      
      await this.emailService.trackEmailOpen(trackingId);
      
      // Return 1x1 transparent pixel
      const pixel = Buffer.from([
        71,73,70,56,57,97,1,0,1,0,128,0,0,255,255,255,0,0,0,33,249,4,1,0,0,0,0,44,0,0,0,0,1,0,1,0,0,2,2,68,1,0,59
      ]);
      
      res.writeHead(200, {
        'Content-Type': 'image/gif',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      });
      res.end(pixel);

    } catch (error) {
      console.error('Track email open error:', error);
      res.status(500).end();
    }
  }

  /**
   * Track email clicks
   */
  async trackEmailClick(req, res) {
    try {
      const trackingId = req.params.trackingId;
      const targetUrl = req.query.url;

      if (!targetUrl) {
        return res.status(400).json({ error: 'Target URL required' });
      }

      await this.emailService.trackEmailClick(trackingId, targetUrl);
      
      // Redirect to target URL
      res.redirect(decodeURIComponent(targetUrl));

    } catch (error) {
      console.error('Track email click error:', error);
      res.status(500).json({ error: 'Failed to track click' });
    }
  }

  /**
   * Database helper methods
   */
  async saveCampaign(campaignData) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO email_campaigns (
          user_id, name, subject, template_id, status, schedule_type,
          scheduled_at, target_companies, target_roles, created_at
        ) VALUES (?, ?, ?, ?, 'draft', ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;
      
      const params = [
        campaignData.user_id,
        campaignData.name,
        campaignData.subject,
        campaignData.template_id,
        campaignData.schedule_type || 'immediate',
        campaignData.scheduled_at,
        campaignData.target_companies,
        campaignData.target_roles
      ];
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  async getUserCampaigns(userId, filters = {}) {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM email_campaigns WHERE user_id = ?';
      const params = [userId];
      
      if (filters.status) {
        query += ' AND status = ?';
        params.push(filters.status);
      }
      
      query += ' ORDER BY created_at DESC';
      
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(parseInt(filters.limit));
      }
      
      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  async updateCampaignData(userId, campaignId, updateData) {
    return new Promise((resolve, reject) => {
      const fields = [];
      const params = [];
      
      const allowedFields = ['name', 'subject', 'content', 'template_id', 'status', 'schedule_type', 'scheduled_at', 'target_companies', 'target_roles'];
      
      Object.keys(updateData).forEach(key => {
        if (allowedFields.includes(key)) {
          fields.push(`${key} = ?`);
          if (key === 'target_companies' || key === 'target_roles') {
            params.push(JSON.stringify(updateData[key]));
          } else {
            params.push(updateData[key]);
          }
        }
      });
      
      if (fields.length === 0) {
        reject(new Error('No valid fields to update'));
        return;
      }
      
      fields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(userId, campaignId);
      
      const query = `
        UPDATE email_campaigns 
        SET ${fields.join(', ')}
        WHERE user_id = ? AND id = ?
      `;
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async deleteCampaignData(userId, campaignId) {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM email_campaigns WHERE user_id = ? AND id = ?';
      
      this.db.run(query, [userId, campaignId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async saveTemplate(templateData) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO email_templates (
          name, type, subject, content, variables, is_system_template, created_by, created_at
        ) VALUES (?, ?, ?, ?, ?, 0, ?, CURRENT_TIMESTAMP)
      `;
      
      const params = [
        templateData.name,
        templateData.type,
        templateData.subject,
        templateData.content,
        templateData.variables,
        templateData.created_by
      ];
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  async getUserTemplates(userId, filters = {}) {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM email_templates WHERE (created_by = ? OR is_system_template = 1)';
      const params = [userId];
      
      if (filters.type) {
        query += ' AND type = ?';
        params.push(filters.type);
      }
      
      if (!filters.include_system) {
        query += ' AND is_system_template = 0';
      }
      
      query += ' ORDER BY is_system_template DESC, created_at DESC';
      
      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }
}

module.exports = EmailCampaignController;