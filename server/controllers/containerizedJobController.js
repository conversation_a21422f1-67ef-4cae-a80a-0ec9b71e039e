const ContainerService = require('../services/containerService');
const TerminalService = require('../services/terminalService');
const ApprovalService = require('../services/approvalService');
const JobExecutionAuditService = require('../services/jobExecutionAuditService');

/**
 * Containerized Job Execution Controller
 */
class ContainerizedJobController {
  constructor(notificationService = null) {
    this.containerService = new ContainerService();
    this.terminalService = new TerminalService();
    this.approvalService = new ApprovalService(notificationService);
    this.auditService = new JobExecutionAuditService();
    this.notificationService = notificationService;
    
    // Initialize cleanup schedules
    this.initializeCleanupSchedules();
  }

  /**
   * Submit job for approval and containerized execution
   */
  async submitJobForExecution(req, res) {
    try {
      const userId = req.user.userId;
      const jobData = req.body;

      // Validate job data
      if (!jobData.jobTitle || !jobData.company || !jobData.jobUrl) {
        return res.status(400).json({
          error: 'Missing required job data: jobTitle, company, jobUrl'
        });
      }

      // Add job ID if not present
      if (!jobData.id) {
        jobData.id = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Log job submission
      this.auditService.logJobApproval('SUBMITTED', userId, jobData);

      // Create approval request
      const approval = await this.approvalService.createApprovalRequest(
        jobData, 
        userId, 
        {
          confidenceScore: jobData.confidenceScore || 0.8,
          source: 'api',
          userAgent: req.get('User-Agent'),
          ip: req.ip
        }
      );

      // If auto-approved, start execution immediately
      if (approval.status === 'auto_approved') {
        const executionResult = await this.startJobExecution(jobData, userId);
        
        res.json({
          success: true,
          jobId: jobData.id,
          approval: {
            id: approval.id,
            status: approval.status,
            approvedBy: approval.approvedBy
          },
          execution: executionResult
        });
      } else {
        res.json({
          success: true,
          jobId: jobData.id,
          approval: {
            id: approval.id,
            status: approval.status,
            requiresApproval: true,
            reasons: approval.analysisResult.reasons,
            recommendations: approval.analysisResult.recommendations
          }
        });
      }

    } catch (error) {
      console.error('Error submitting job for execution:', error);
      res.status(500).json({
        error: 'Failed to submit job for execution',
        message: error.message
      });
    }
  }

  /**
   * Start containerized job execution
   */
  async startJobExecution(jobData, userId) {
    try {
      // Create terminal session
      const terminalId = this.terminalService.createTerminal(
        `container_${jobData.id}`, 
        userId
      );

      // Set up terminal callback for container logs
      const terminalCallback = (data) => {
        this.terminalService.sendOutput(terminalId, data);
      };

      // Start container
      const containerId = await this.containerService.createJobContainer(
        jobData,
        terminalCallback
      );

      // Log container start
      this.auditService.logContainerExecution(
        'STARTED',
        userId,
        containerId,
        jobData,
        {
          name: `cvleap-job-${containerId}`,
          image: this.containerService.config.baseImage,
          startTime: new Date()
        }
      );

      // Monitor container completion
      this.monitorContainerExecution(containerId, jobData, userId, terminalId);

      return {
        containerId,
        terminalId,
        status: 'started',
        monitorUrl: `/api/containerized-jobs/terminal/${terminalId}`
      };

    } catch (error) {
      // Log execution failure
      this.auditService.logContainerExecution(
        'FAILED',
        userId,
        null,
        jobData,
        { error: error.message }
      );
      
      throw error;
    }
  }

  /**
   * Monitor container execution and handle completion
   */
  async monitorContainerExecution(containerId, jobData, userId, terminalId) {
    const checkInterval = setInterval(async () => {
      try {
        const containerInfo = this.containerService.getContainerInfo(containerId);
        
        if (!containerInfo) {
          clearInterval(checkInterval);
          return;
        }

        if (containerInfo.status === 'completed' || containerInfo.status === 'failed') {
          clearInterval(checkInterval);
          
          // Update terminal status
          this.terminalService.updateTerminalStatus(
            terminalId,
            containerInfo.status,
            {
              exitCode: containerInfo.exitCode,
              duration: containerInfo.endTime - containerInfo.startTime
            }
          );

          // Log completion
          this.auditService.logContainerExecution(
            containerInfo.status === 'completed' ? 'COMPLETED' : 'FAILED',
            userId,
            containerId,
            jobData,
            {
              exitCode: containerInfo.exitCode,
              executionTime: containerInfo.endTime - containerInfo.startTime,
              endTime: containerInfo.endTime
            }
          );

          // Log performance metrics
          this.auditService.logPerformanceMetrics(jobData.id, {
            executionTime: containerInfo.endTime - containerInfo.startTime,
            success: containerInfo.status === 'completed',
            memoryUsage: 'unknown', // Would be populated by actual container stats
            cpuUsage: 'unknown'
          });

          // Notify user of completion
          if (this.notificationService) {
            this.notificationService.sendToUser(userId, {
              type: 'job_execution_completed',
              title: `Job Execution ${containerInfo.status === 'completed' ? 'Completed' : 'Failed'}`,
              message: `Job application for ${jobData.jobTitle} at ${jobData.company} has ${containerInfo.status}`,
              data: {
                jobId: jobData.id,
                containerId,
                terminalId,
                status: containerInfo.status,
                exitCode: containerInfo.exitCode
              }
            });
          }
        }
      } catch (error) {
        console.error('Error monitoring container execution:', error);
        clearInterval(checkInterval);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Get pending approvals for user
   */
  async getPendingApprovals(req, res) {
    try {
      const userId = req.user.userId;
      const filters = {
        priority: req.query.priority,
        company: req.query.company
      };

      const approvals = this.approvalService.getPendingApprovals(userId, filters);

      res.json({
        success: true,
        approvals,
        count: approvals.length
      });

    } catch (error) {
      console.error('Error getting pending approvals:', error);
      res.status(500).json({
        error: 'Failed to get pending approvals',
        message: error.message
      });
    }
  }

  /**
   * Approve job application
   */
  async approveJob(req, res) {
    try {
      const userId = req.user.userId;
      const { approvalId } = req.params;
      const { comments, modifications } = req.body;

      const approval = await this.approvalService.approveJob(
        approvalId,
        userId,
        comments,
        modifications
      );

      // Log approval
      this.auditService.logJobApproval('APPROVED', userId, approval.jobData, {
        approvalId,
        approverId: userId,
        modifications,
        reason: comments
      });

      // Log human intervention if modifications were made
      if (modifications && Object.keys(modifications).length > 0) {
        this.auditService.logHumanIntervention(
          'MODIFIED_JOB',
          userId,
          approval.jobId,
          {
            type: 'pre_execution_modification',
            reason: comments,
            modifications,
            outcome: 'approved'
          }
        );
      }

      // Start job execution
      const executionResult = await this.startJobExecution(approval.jobData, approval.userId);

      res.json({
        success: true,
        approval: {
          id: approval.id,
          status: approval.status,
          approvedBy: approval.approvedBy,
          approvedAt: approval.approvedAt
        },
        execution: executionResult
      });

    } catch (error) {
      console.error('Error approving job:', error);
      res.status(500).json({
        error: 'Failed to approve job',
        message: error.message
      });
    }
  }

  /**
   * Reject job application
   */
  async rejectJob(req, res) {
    try {
      const userId = req.user.userId;
      const { approvalId } = req.params;
      const { reason } = req.body;

      const approval = await this.approvalService.rejectJob(
        approvalId,
        userId,
        reason
      );

      // Log rejection
      this.auditService.logJobApproval('REJECTED', userId, approval.jobData, {
        approvalId,
        approverId: userId,
        reason
      });

      res.json({
        success: true,
        approval: {
          id: approval.id,
          status: approval.status,
          rejectedBy: approval.rejectedBy,
          rejectedAt: approval.rejectedAt,
          reason: approval.rejectionReason
        }
      });

    } catch (error) {
      console.error('Error rejecting job:', error);
      res.status(500).json({
        error: 'Failed to reject job',
        message: error.message
      });
    }
  }

  /**
   * Get container status and logs
   */
  async getContainerStatus(req, res) {
    try {
      const { containerId } = req.params;
      const containerInfo = this.containerService.getContainerInfo(containerId);

      if (!containerInfo) {
        return res.status(404).json({
          error: 'Container not found'
        });
      }

      res.json({
        success: true,
        container: containerInfo
      });

    } catch (error) {
      console.error('Error getting container status:', error);
      res.status(500).json({
        error: 'Failed to get container status',
        message: error.message
      });
    }
  }

  /**
   * Get terminal session info
   */
  async getTerminalInfo(req, res) {
    try {
      const { terminalId } = req.params;
      const userId = req.user.userId;
      
      const terminal = this.terminalService.getTerminal(terminalId);
      
      if (!terminal) {
        return res.status(404).json({
          error: 'Terminal not found'
        });
      }

      // Check access permissions
      if (terminal.userId !== userId) {
        return res.status(403).json({
          error: 'Access denied to terminal'
        });
      }

      res.json({
        success: true,
        terminal
      });

    } catch (error) {
      console.error('Error getting terminal info:', error);
      res.status(500).json({
        error: 'Failed to get terminal info',
        message: error.message
      });
    }
  }

  /**
   * List active containers
   */
  async listActiveContainers(req, res) {
    try {
      const containers = this.containerService.listActiveContainers();

      res.json({
        success: true,
        containers,
        count: containers.length
      });

    } catch (error) {
      console.error('Error listing containers:', error);
      res.status(500).json({
        error: 'Failed to list containers',
        message: error.message
      });
    }
  }

  /**
   * Stop container execution
   */
  async stopContainer(req, res) {
    try {
      const { containerId } = req.params;
      const userId = req.user.userId;
      const { reason } = req.body;

      await this.containerService.stopContainer(containerId, reason || 'manual');

      // Log manual intervention
      this.auditService.logHumanIntervention(
        'STOPPED_CONTAINER',
        userId,
        null, // jobId not available from container
        {
          type: 'manual_stop',
          reason: reason || 'manual',
          containerId,
          outcome: 'stopped'
        }
      );

      res.json({
        success: true,
        message: 'Container stopped successfully'
      });

    } catch (error) {
      console.error('Error stopping container:', error);
      res.status(500).json({
        error: 'Failed to stop container',
        message: error.message
      });
    }
  }

  /**
   * Get job execution audit trail
   */
  async getJobAuditTrail(req, res) {
    try {
      const { jobId } = req.params;
      const filters = {
        category: req.query.category,
        action: req.query.action,
        userId: req.query.userId,
        startTime: req.query.startTime ? new Date(req.query.startTime) : null,
        endTime: req.query.endTime ? new Date(req.query.endTime) : null
      };

      const auditTrail = this.auditService.getJobAuditTrail(jobId, filters);

      res.json({
        success: true,
        auditTrail,
        count: auditTrail.length
      });

    } catch (error) {
      console.error('Error getting audit trail:', error);
      res.status(500).json({
        error: 'Failed to get audit trail',
        message: error.message
      });
    }
  }

  /**
   * Get system health status
   */
  async getHealthStatus(req, res) {
    try {
      const containerHealth = await this.containerService.healthCheck();
      const terminalStats = this.terminalService.getStats();
      const approvalStats = this.approvalService.getStats();
      const auditStats = this.auditService.getStats();

      res.json({
        success: true,
        health: {
          containers: containerHealth,
          terminals: terminalStats,
          approvals: approvalStats,
          audit: auditStats,
          overall: containerHealth.status === 'healthy' ? 'healthy' : 'degraded'
        }
      });

    } catch (error) {
      console.error('Error getting health status:', error);
      res.status(500).json({
        error: 'Failed to get health status',
        message: error.message
      });
    }
  }

  /**
   * Initialize cleanup schedules
   */
  initializeCleanupSchedules() {
    // Cleanup every hour
    setInterval(() => {
      this.containerService.cleanup();
      this.terminalService.cleanup();
      this.approvalService.cleanup();
    }, 60 * 60 * 1000);

    // Audit cleanup daily
    setInterval(() => {
      this.auditService.cleanup(90); // 90 days retention
    }, 24 * 60 * 60 * 1000);

    console.log('🕐 Cleanup schedules initialized');
  }

  /**
   * Handle WebSocket connection for terminal
   */
  handleTerminalWebSocket(ws, req) {
    try {
      const terminalId = req.params.terminalId;
      const userId = req.user?.userId;

      if (!userId) {
        ws.close(1008, 'Authentication required');
        return;
      }

      // Connect client to terminal
      this.terminalService.connectClient(ws, terminalId, userId);

      // Log terminal access
      this.auditService.logTerminalAccess(
        'CONNECTED',
        userId,
        terminalId,
        null, // containerId not directly available
        {
          clientIp: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      // Handle messages
      ws.on('message', (message) => {
        this.terminalService.handleMessage(ws, message);
      });

      // Handle disconnect
      ws.on('close', () => {
        this.auditService.logTerminalAccess(
          'DISCONNECTED',
          userId,
          terminalId,
          null
        );
      });

    } catch (error) {
      console.error('Error handling terminal WebSocket:', error);
      ws.close(1011, 'Internal server error');
    }
  }
}

module.exports = ContainerizedJobController;