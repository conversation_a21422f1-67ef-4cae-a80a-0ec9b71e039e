const express = require('express');
const ResumeController = require('../resumeController');
const { validateRequest, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();
const resumeController = new ResumeController();

// Apply sanitization to all routes
router.use(sanitizeInput);

// Get all resumes for user
router.get('/', resumeController.getResumes.bind(resumeController));

// Create new resume
router.post('/',
  validateRequest(schemas.resume),
  resumeController.createResume.bind(resumeController)
);

// Get specific resume
router.get('/:id', resumeController.getResume.bind(resumeController));

// Update resume
router.put('/:id',
  validateRequest(schemas.resume),
  resumeController.updateResume.bind(resumeController)
);

// Delete resume
router.delete('/:id', resumeController.deleteResume.bind(resumeController));

module.exports = router;