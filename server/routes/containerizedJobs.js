const express = require('express');
const ContainerizedJobController = require('../controllers/containerizedJobController');
const { authenticateToken } = require('../auth');

/**
 * Routes for containerized job execution system
 */
function createContainerizedJobRoutes(notificationService) {
  const router = express.Router();
  const controller = new ContainerizedJobController(notificationService);

  // Apply authentication middleware to all routes
  router.use(authenticateToken);

  // Job submission and approval routes
  router.post('/submit', controller.submitJobForExecution.bind(controller));
  router.get('/approvals/pending', controller.getPendingApprovals.bind(controller));
  router.post('/approvals/:approvalId/approve', controller.approveJob.bind(controller));
  router.post('/approvals/:approvalId/reject', controller.rejectJob.bind(controller));

  // Container management routes
  router.get('/containers', controller.listActiveContainers.bind(controller));
  router.get('/containers/:containerId', controller.getContainerStatus.bind(controller));
  router.post('/containers/:containerId/stop', controller.stopContainer.bind(controller));

  // Terminal management routes
  router.get('/terminals/:terminalId', controller.getTerminalInfo.bind(controller));

  // Audit and monitoring routes
  router.get('/audit/:jobId', controller.getJobAuditTrail.bind(controller));
  router.get('/health', controller.getHealthStatus.bind(controller));

  // WebSocket terminal handler (to be used with WebSocket server)
  router.handleTerminalWebSocket = controller.handleTerminalWebSocket.bind(controller);

  return router;
}

module.exports = createContainerizedJobRoutes;