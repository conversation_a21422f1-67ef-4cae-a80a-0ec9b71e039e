const express = require('express');
const { authenticate } = require('../middleware/auth');
const { sanitizeInput } = require('../middleware/validation');
const Database = require('../database');

const router = express.Router();

// Apply authentication and sanitization to all routes
router.use(authenticate);
router.use(sanitizeInput);

// Default achievements definition
const DEFAULT_ACHIEVEMENTS = [
  {
    id: 'first_application',
    title: 'Getting Started',
    description: 'Submit your first job application',
    icon: '🚀',
    maxProgress: 1,
    rarity: 'bronze'
  },
  {
    id: 'application_milestone_10',
    title: 'Job Hunter',
    description: 'Submit 10 job applications',
    icon: '🎯',
    maxProgress: 10,
    rarity: 'silver'
  },
  {
    id: 'application_milestone_50',
    title: 'Application Master',
    description: 'Submit 50 job applications',
    icon: '👑',
    maxProgress: 50,
    rarity: 'gold'
  },
  {
    id: 'first_response',
    title: 'Response Received',
    description: 'Get your first response from an employer',
    icon: '📧',
    maxProgress: 1,
    rarity: 'silver'
  },
  {
    id: 'interview_scheduled',
    title: 'Interview Ready',
    description: 'Schedule your first interview',
    icon: '💼',
    maxProgress: 1,
    rarity: 'gold'
  },
  {
    id: 'streak_7',
    title: 'Consistent Effort',
    description: 'Maintain a 7-day activity streak',
    icon: '🔥',
    maxProgress: 7,
    rarity: 'silver'
  },
  {
    id: 'perfect_week',
    title: 'Perfect Week',
    description: 'Complete all daily goals for a week',
    icon: '⭐',
    maxProgress: 7,
    rarity: 'gold'
  },
  {
    id: 'goal_achiever',
    title: 'Goal Achiever',
    description: 'Complete 5 personal goals',
    icon: '🏆',
    maxProgress: 5,
    rarity: 'gold'
  }
];

// Initialize user gamification data
const initializeUserGamification = (userId) => {
  const db = Database.get();
  
  // Initialize user stats
  db.run(`
    INSERT OR IGNORE INTO user_gamification_stats 
    (user_id, level, xp, total_applications, response_rate, interview_rate, streak, completed_goals)
    VALUES (?, 1, 0, 0, 0, 0, 0, 0)
  `, [userId]);

  // Initialize achievements
  const stmt = db.prepare(`
    INSERT OR IGNORE INTO user_achievements 
    (user_id, achievement_id, title, description, icon, max_progress, progress, rarity, unlocked)
    VALUES (?, ?, ?, ?, ?, ?, 0, ?, 0)
  `);

  DEFAULT_ACHIEVEMENTS.forEach(achievement => {
    stmt.run([
      userId,
      achievement.id,
      achievement.title,
      achievement.description,
      achievement.icon,
      achievement.maxProgress,
      achievement.rarity
    ]);
  });

  stmt.finalize();
};

// Get user gamification stats
router.get('/stats', (req, res) => {
  const userId = req.user.userId;
  const db = Database.get();

  // Initialize if not exists
  initializeUserGamification(userId);

  db.get(`
    SELECT level, xp, total_applications, response_rate, interview_rate, streak, completed_goals
    FROM user_gamification_stats 
    WHERE user_id = ?
  `, [userId], (err, stats) => {
    if (err) {
      console.error('Stats query error:', err);
      return res.status(500).json({ error: 'Failed to fetch user stats' });
    }

    // Calculate XP needed for next level (exponential curve)
    const xpToNextLevel = Math.floor(100 * Math.pow(1.5, stats.level - 1));

    res.json({
      success: true,
      data: {
        ...stats,
        xpToNextLevel
      }
    });
  });
});

// Get user achievements
router.get('/achievements', (req, res) => {
  const userId = req.user.userId;
  const db = Database.get();

  // Initialize if not exists
  initializeUserGamification(userId);

  db.all(`
    SELECT achievement_id as id, title, description, icon, max_progress as maxProgress, 
           progress, rarity, unlocked, unlocked_at as unlockedAt
    FROM user_achievements 
    WHERE user_id = ? 
    ORDER BY unlocked DESC, rarity DESC
  `, [userId], (err, achievements) => {
    if (err) {
      console.error('Achievements query error:', err);
      return res.status(500).json({ error: 'Failed to fetch achievements' });
    }

    const processedAchievements = achievements.map(achievement => ({
      ...achievement,
      unlocked: Boolean(achievement.unlocked)
    }));

    res.json({
      success: true,
      data: processedAchievements
    });
  });
});

// Get user goals
router.get('/goals', (req, res) => {
  const userId = req.user.userId;
  const db = Database.get();

  db.all(`
    SELECT id, title, description, target, current, deadline, category, priority, completed
    FROM user_goals 
    WHERE user_id = ? AND (completed = 0 OR completed_at >= datetime('now', '-30 days'))
    ORDER BY priority DESC, deadline ASC
  `, [userId], (err, goals) => {
    if (err) {
      console.error('Goals query error:', err);
      return res.status(500).json({ error: 'Failed to fetch goals' });
    }

    res.json({
      success: true,
      data: goals
    });
  });
});

// Create new goal
router.post('/goals', (req, res) => {
  const userId = req.user.userId;
  const { title, description, target, deadline, category, priority } = req.body;
  const db = Database.get();

  if (!title || !target || !deadline) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  db.run(`
    INSERT INTO user_goals (user_id, title, description, target, deadline, category, priority)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [userId, title, description || '', target, deadline, category || 'applications', priority || 'medium'], function(err) {
    if (err) {
      console.error('Goal creation error:', err);
      return res.status(500).json({ error: 'Failed to create goal' });
    }

    // Award XP for setting a goal
    awardXP(userId, 10, 'Goal created');

    res.status(201).json({
      success: true,
      data: { id: this.lastID }
    });
  });
});

// Update goal progress
router.put('/goals/:goalId/progress', (req, res) => {
  const userId = req.user.userId;
  const { goalId } = req.params;
  const { current } = req.body;
  const db = Database.get();

  // Get current goal data
  db.get('SELECT * FROM user_goals WHERE id = ? AND user_id = ?', [goalId, userId], (err, goal) => {
    if (err || !goal) {
      return res.status(404).json({ error: 'Goal not found' });
    }

    const isCompleting = current >= goal.target && goal.current < goal.target;
    const updateFields = isCompleting 
      ? 'current = ?, completed = 1, completed_at = CURRENT_TIMESTAMP'
      : 'current = ?';

    db.run(`UPDATE user_goals SET ${updateFields} WHERE id = ?`, 
      [current, goalId], (err) => {
      if (err) {
        console.error('Goal update error:', err);
        return res.status(500).json({ error: 'Failed to update goal' });
      }

      // Award XP and update stats if goal completed
      if (isCompleting) {
        const xpReward = goal.priority === 'high' ? 50 : goal.priority === 'medium' ? 30 : 20;
        awardXP(userId, xpReward, `Goal completed: ${goal.title}`);
        
        // Update completed goals count
        db.run('UPDATE user_gamification_stats SET completed_goals = completed_goals + 1 WHERE user_id = ?', [userId]);
        
        // Check achievements
        checkAchievements(userId);
      }

      res.json({ success: true });
    });
  });
});

// Helper function to award XP and handle level ups
const awardXP = (userId, xpAmount, reason = '') => {
  const db = Database.get();
  
  db.get('SELECT level, xp FROM user_gamification_stats WHERE user_id = ?', [userId], (err, stats) => {
    if (err || !stats) return;

    const newXP = stats.xp + xpAmount;
    const currentLevelRequirement = Math.floor(100 * Math.pow(1.5, stats.level - 1));
    
    let newLevel = stats.level;
    let remainingXP = newXP;

    // Check for level ups
    while (remainingXP >= currentLevelRequirement) {
      remainingXP -= currentLevelRequirement;
      newLevel++;
    }

    db.run(`
      UPDATE user_gamification_stats 
      SET level = ?, xp = ? 
      WHERE user_id = ?
    `, [newLevel, remainingXP, userId]);

    // Log XP gain
    if (reason) {
      db.run(`
        INSERT INTO xp_logs (user_id, xp_gained, reason, created_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `, [userId, xpAmount, reason]);
    }
  });
};

// Helper function to check and unlock achievements
const checkAchievements = (userId) => {
  const db = Database.get();
  
  // Get user stats for achievement checking
  db.get(`
    SELECT ugs.*, 
           COUNT(CASE WHEN ug.completed = 1 THEN 1 END) as completed_goals_count,
           COUNT(CASE WHEN a.status = 'applied' THEN 1 END) as application_count,
           COUNT(CASE WHEN a.status = 'interview' THEN 1 END) as interview_count
    FROM user_gamification_stats ugs
    LEFT JOIN user_goals ug ON ugs.user_id = ug.user_id
    LEFT JOIN applications a ON ugs.user_id = a.user_id
    WHERE ugs.user_id = ?
    GROUP BY ugs.user_id
  `, [userId], (err, stats) => {
    if (err || !stats) return;

    const achievementChecks = [
      { id: 'first_application', progress: Math.min(stats.application_count, 1) },
      { id: 'application_milestone_10', progress: Math.min(stats.application_count, 10) },
      { id: 'application_milestone_50', progress: Math.min(stats.application_count, 50) },
      { id: 'first_response', progress: Math.min(stats.total_applications > 0 && stats.response_rate > 0 ? 1 : 0, 1) },
      { id: 'interview_scheduled', progress: Math.min(stats.interview_count, 1) },
      { id: 'streak_7', progress: Math.min(stats.streak, 7) },
      { id: 'goal_achiever', progress: Math.min(stats.completed_goals_count, 5) }
    ];

    achievementChecks.forEach(check => {
      db.get(`
        SELECT progress, unlocked FROM user_achievements 
        WHERE user_id = ? AND achievement_id = ?
      `, [userId, check.id], (err, achievement) => {
        if (err || !achievement) return;

        if (check.progress > achievement.progress) {
          const shouldUnlock = check.progress >= achievement.max_progress && !achievement.unlocked;
          
          db.run(`
            UPDATE user_achievements 
            SET progress = ?, unlocked = ?, unlocked_at = ?
            WHERE user_id = ? AND achievement_id = ?
          `, [
            check.progress, 
            shouldUnlock ? 1 : achievement.unlocked,
            shouldUnlock ? new Date().toISOString() : null,
            userId, 
            check.id
          ]);

          if (shouldUnlock) {
            // Award XP for achievement unlock
            const rarityXP = { bronze: 25, silver: 50, gold: 100, platinum: 200 };
            awardXP(userId, rarityXP[achievement.rarity] || 25, `Achievement unlocked: ${achievement.title}`);
          }
        }
      });
    });
  });
};

module.exports = router;