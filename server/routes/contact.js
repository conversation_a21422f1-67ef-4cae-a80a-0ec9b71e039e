const express = require('express');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');
const sgMail = require('@sendgrid/mail');
const config = require('../utils/config');
const SecurityMiddleware = require('../securityMiddleware');

const router = express.Router();
const securityMiddleware = new SecurityMiddleware();

// Get configuration
const getConfig = () => {
  return {
    email: {
      provider: 'sendgrid',
      sendgridApiKey: process.env.SENDGRID_API_KEY,
      fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
      fromName: process.env.EMAIL_FROM_NAME || 'CVleap',
    }
  };
};

// Initialize SendGrid
const initializeEmailService = () => {
  const { email } = getConfig();
  
  if (email.provider === 'sendgrid' && email.sendgridApiKey) {
    sgMail.setApiKey(email.sendgridApiKey);
    return true;
  }
  
  console.warn('SendGrid API key not configured. Contact form emails will be logged only.');
  return false;
};

const emailServiceAvailable = initializeEmailService();

// Rate limiting for contact form - more restrictive
const contactRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 contact form submissions per 15 minutes
  message: {
    success: false,
    error: 'Too many contact form submissions. Please wait before sending another message.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schema for contact form
const contactSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .trim()
    .required()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name must be less than 100 characters',
      'any.required': 'Name is required',
    }),
  email: Joi.string()
    .email()
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email address is required',
    }),
  subject: Joi.string()
    .min(5)
    .max(200)
    .trim()
    .required()
    .messages({
      'string.min': 'Subject must be at least 5 characters long',
      'string.max': 'Subject must be less than 200 characters',
      'any.required': 'Subject is required',
    }),
  message: Joi.string()
    .min(10)
    .max(2000)
    .trim()
    .required()
    .messages({
      'string.min': 'Message must be at least 10 characters long',
      'string.max': 'Message must be less than 2000 characters',
      'any.required': 'Message is required',
    }),
});

// Validation middleware
const validateContactForm = (req, res, next) => {
  const { error, value } = contactSchema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    const validationErrors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: validationErrors,
    });
  }

  req.body = value;
  next();
};

// Email sending utility
const sendContactEmail = async (contactData) => {
  const { email: emailConfig } = getConfig();
  const { name, email, subject, message } = contactData;

  // Email to the site owner/team
  const adminEmail = {
    to: emailConfig.fromEmail, // Send to the site email
    from: {
      email: emailConfig.fromEmail,
      name: emailConfig.fromName,
    },
    subject: `Contact Form: ${subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">New Contact Form Submission</h1>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px;">Contact Details</h2>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #555;">Name:</strong>
              <p style="margin: 5px 0; color: #333;">${name}</p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #555;">Email:</strong>
              <p style="margin: 5px 0; color: #333;">
                <a href="mailto:${email}" style="color: #667eea;">${email}</a>
              </p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #555;">Subject:</strong>
              <p style="margin: 5px 0; color: #333;">${subject}</p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #555;">Message:</strong>
              <div style="margin: 10px 0; padding: 15px; background: #f5f5f5; border-left: 4px solid #667eea; border-radius: 4px;">
                <p style="margin: 0; color: #333; line-height: 1.6;">${message.replace(/\n/g, '<br>')}</p>
              </div>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent from the CVleap contact form.</p>
            <p>Received at: ${new Date().toLocaleString()}</p>
          </div>
        </div>
      </div>
    `,
    text: `
New Contact Form Submission

Name: ${name}
Email: ${email}
Subject: ${subject}

Message:
${message}

Received at: ${new Date().toLocaleString()}
    `,
  };

  // Auto-reply email to the sender
  const autoReplyEmail = {
    to: email,
    from: {
      email: emailConfig.fromEmail,
      name: emailConfig.fromName,
    },
    subject: 'Thank you for contacting CVleap',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Thank You!</h1>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${name},</h2>
            
            <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
              Thank you for reaching out to us! We've received your message and will get back to you within 24 hours.
            </p>
            
            <div style="background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 20px 0;">
              <h3 style="color: #333; margin: 0 0 10px 0;">Your Message:</h3>
              <p style="color: #666; margin: 0;"><strong>Subject:</strong> ${subject}</p>
            </div>
            
            <p style="color: #555; line-height: 1.6;">
              In the meantime, feel free to explore our platform and discover how CVleap can help you create the perfect resume and advance your career.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.CLIENT_URL || 'https://cvleap.com'}" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Visit CVleap
              </a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>Best regards,<br>The CVleap Team</p>
          </div>
        </div>
      </div>
    `,
    text: `
Hi ${name},

Thank you for reaching out to us! We've received your message and will get back to you within 24 hours.

Your Message:
Subject: ${subject}

In the meantime, feel free to explore our platform and discover how CVleap can help you create the perfect resume and advance your career.

Visit us at: ${process.env.CLIENT_URL || 'https://cvleap.com'}

Best regards,
The CVleap Team
    `,
  };

  if (emailServiceAvailable) {
    // Send both emails
    await Promise.all([
      sgMail.send(adminEmail),
      sgMail.send(autoReplyEmail),
    ]);
  } else {
    // Log emails for development/testing
    console.log('--- CONTACT FORM SUBMISSION ---');
    console.log('Admin Email:', JSON.stringify(adminEmail, null, 2));
    console.log('Auto-reply Email:', JSON.stringify(autoReplyEmail, null, 2));
    console.log('--- END EMAIL LOGS ---');
  }
};

// Contact form submission endpoint
router.post('/', 
  contactRateLimit,
  securityMiddleware.securityHeaders,
  validateContactForm,
  async (req, res) => {
    try {
      const contactData = req.body;
      
      // Log the contact attempt for analytics
      console.log(`Contact form submission from ${contactData.email} - Subject: ${contactData.subject}`);
      
      // Send emails
      await sendContactEmail(contactData);
      
      // Return success response
      res.json({
        success: true,
        message: 'Message sent successfully. We\'ll get back to you soon!',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Contact form submission error:', error);
      
      // Don't expose internal errors to client
      res.status(500).json({
        success: false,
        error: 'Failed to send message. Please try again later.',
        timestamp: new Date().toISOString(),
      });
    }
  }
);

// Health check endpoint for contact service
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'contact',
    emailServiceAvailable,
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;