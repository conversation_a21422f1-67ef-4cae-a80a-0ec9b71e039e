const express = require('express');
const { AuthController } = require('../auth');
const { validateRequest, schemas, sanitizeInput } = require('../middleware/validation');
const rateLimit = require('express-rate-limit');

const router = express.Router();
const authController = new AuthController();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting and sanitization to all auth routes
router.use(authLimiter);
router.use(sanitizeInput);

// Register route
router.post('/register', 
  validateRequest(schemas.register),
  authController.register.bind(authController)
);

// Login route
router.post('/login',
  validateRequest(schemas.login),
  authController.login.bind(authController)
);

// Refresh token route (for future implementation)
router.post('/refresh', (req, res) => {
  res.status(501).json({ error: 'Refresh token endpoint not implemented yet' });
});

// Logout route (for future implementation)
router.post('/logout', (req, res) => {
  res.status(200).json({ message: 'Logged out successfully' });
});

module.exports = router;