const express = require('express');
const { sanitizeInput } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');
const AIService = require('../aiService');
const Database = require('../database');

const router = express.Router();
const aiService = new AIService();

// Apply middleware
router.use(sanitizeInput);
router.use(authenticate);

// Analyze career path
router.post('/analyze-career-path', async (req, res) => {
  try {
    const { resumeData, careerGoals } = req.body;
    
    if (!resumeData || !careerGoals) {
      return res.status(400).json({ error: 'Resume data and career goals are required' });
    }
    
    const analysis = await aiService.analyzeCareerPath(resumeData, careerGoals);
    
    // Store coaching session
    const db = Database.get();
    db.run(`
      INSERT INTO career_coaching_sessions (user_id, session_type, input_data, ai_recommendations)
      VALUES (?, ?, ?, ?)
    `, [
      req.user.userId,
      'career_path_analysis',
      JSON.stringify({ resumeData, careerGoals }),
      JSON.stringify(analysis)
    ]);
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    console.error('Career path analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze career path' });
  }
});

// Analyze skill gaps
router.post('/skill-gap-analysis', async (req, res) => {
  try {
    const { currentSkills, targetRole, industry } = req.body;
    
    if (!currentSkills || !targetRole) {
      return res.status(400).json({ error: 'Current skills and target role are required' });
    }
    
    const analysis = await aiService.analyzeSkillGaps(currentSkills, targetRole, industry);
    
    // Store skill gap analysis
    const db = Database.get();
    db.run(`
      INSERT INTO skill_gap_analysis (user_id, target_role, current_skills, missing_skills, recommended_actions, priority_score)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      req.user.userId,
      targetRole,
      JSON.stringify(currentSkills),
      JSON.stringify(analysis.criticalGaps),
      JSON.stringify(analysis.learningPlan),
      analysis.overallGapScore
    ]);
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    console.error('Skill gap analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze skill gaps' });
  }
});

// Generate interview questions
router.post('/interview-prep', async (req, res) => {
  try {
    const { jobDescription, resumeData, difficulty = 'medium' } = req.body;
    
    if (!jobDescription || !resumeData) {
      return res.status(400).json({ error: 'Job description and resume data are required' });
    }
    
    const questions = await aiService.generateInterviewQuestions(jobDescription, resumeData, difficulty);
    
    // Store coaching session
    const db = Database.get();
    db.run(`
      INSERT INTO career_coaching_sessions (user_id, session_type, input_data, ai_recommendations)
      VALUES (?, ?, ?, ?)
    `, [
      req.user.userId,
      'interview_preparation',
      JSON.stringify({ jobDescription, resumeData, difficulty }),
      JSON.stringify(questions)
    ]);
    
    res.json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Interview preparation error:', error);
    res.status(500).json({ error: 'Failed to generate interview questions' });
  }
});

// Salary negotiation advice
router.post('/salary-negotiation', async (req, res) => {
  try {
    const { resumeData, jobOffer, marketData } = req.body;
    
    if (!resumeData || !jobOffer) {
      return res.status(400).json({ error: 'Resume data and job offer details are required' });
    }
    
    const advice = await aiService.generateSalaryNegotiationAdvice(resumeData, jobOffer, marketData);
    
    // Store coaching session
    const db = Database.get();
    db.run(`
      INSERT INTO career_coaching_sessions (user_id, session_type, input_data, ai_recommendations)
      VALUES (?, ?, ?, ?)
    `, [
      req.user.userId,
      'salary_negotiation',
      JSON.stringify({ resumeData, jobOffer, marketData }),
      JSON.stringify(advice)
    ]);
    
    res.json({
      success: true,
      data: advice
    });
  } catch (error) {
    console.error('Salary negotiation advice error:', error);
    res.status(500).json({ error: 'Failed to generate salary negotiation advice' });
  }
});

// LinkedIn profile optimization
router.post('/linkedin-optimization', async (req, res) => {
  try {
    const { resumeData, targetAudience = 'recruiters' } = req.body;
    
    if (!resumeData) {
      return res.status(400).json({ error: 'Resume data is required' });
    }
    
    const optimization = await aiService.optimizeLinkedInProfile(resumeData, targetAudience);
    
    // Store coaching session
    const db = Database.get();
    db.run(`
      INSERT INTO career_coaching_sessions (user_id, session_type, input_data, ai_recommendations)
      VALUES (?, ?, ?, ?)
    `, [
      req.user.userId,
      'linkedin_optimization',
      JSON.stringify({ resumeData, targetAudience }),
      JSON.stringify(optimization)
    ]);
    
    res.json({
      success: true,
      data: optimization
    });
  } catch (error) {
    console.error('LinkedIn optimization error:', error);
    res.status(500).json({ error: 'Failed to optimize LinkedIn profile' });
  }
});

// Get coaching history
router.get('/history', (req, res) => {
  const { sessionType, limit = 20 } = req.query;
  const db = Database.get();
  
  let query = 'SELECT * FROM career_coaching_sessions WHERE user_id = ?';
  const params = [req.user.userId];
  
  if (sessionType) {
    query += ' AND session_type = ?';
    params.push(sessionType);
  }
  
  query += ' ORDER BY created_at DESC LIMIT ?';
  params.push(parseInt(limit));
  
  db.all(query, params, (err, sessions) => {
    if (err) {
      console.error('Coaching history fetch error:', err);
      return res.status(500).json({ error: 'Failed to fetch coaching history' });
    }
    
    const history = sessions.map(session => ({
      ...session,
      input_data: JSON.parse(session.input_data),
      ai_recommendations: JSON.parse(session.ai_recommendations)
    }));
    
    res.json({
      success: true,
      data: history
    });
  });
});

// Rate coaching session
router.post('/:sessionId/feedback', (req, res) => {
  const { sessionId } = req.params;
  const { rating, feedback } = req.body;
  const db = Database.get();
  
  if (!rating || rating < 1 || rating > 5) {
    return res.status(400).json({ error: 'Rating must be between 1 and 5' });
  }
  
  db.run(`
    UPDATE career_coaching_sessions 
    SET feedback_score = ?, session_metadata = ?
    WHERE id = ? AND user_id = ?
  `, [rating, JSON.stringify({ feedback }), sessionId, req.user.userId], (err) => {
    if (err) {
      console.error('Feedback update error:', err);
      return res.status(500).json({ error: 'Failed to update feedback' });
    }
    
    res.json({ success: true });
  });
});

// Get coaching dashboard data
router.get('/dashboard', async (req, res) => {
  try {
    const db = Database.get();
    
    // Get recent coaching sessions
    db.all(`
      SELECT session_type, ai_recommendations, created_at 
      FROM career_coaching_sessions 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT 10
    `, [req.user.userId], (err, sessions) => {
      if (err) {
        console.error('Coaching dashboard error:', err);
        return res.status(500).json({ error: 'Failed to fetch coaching dashboard' });
      }
      
      // Get skill gap analysis
      db.all(`
        SELECT target_role, missing_skills, priority_score, created_at
        FROM skill_gap_analysis 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
      `, [req.user.userId], (err, skillGaps) => {
        if (err) {
          console.error('Skill gap fetch error:', err);
          return res.status(500).json({ error: 'Failed to fetch skill gaps' });
        }
        
        const dashboardData = {
          recentSessions: sessions.map(session => ({
            ...session,
            ai_recommendations: JSON.parse(session.ai_recommendations || '{}')
          })),
          skillGaps: skillGaps.map(gap => ({
            ...gap,
            missing_skills: JSON.parse(gap.missing_skills || '[]')
          })),
          stats: {
            totalSessions: sessions.length,
            skillGapsIdentified: skillGaps.length,
            mostRecentSession: sessions[0]?.created_at || null
          }
        };
        
        res.json({
          success: true,
          data: dashboardData
        });
      });
    });
  } catch (error) {
    console.error('Coaching dashboard error:', error);
    res.status(500).json({ error: 'Failed to fetch coaching dashboard' });
  }
});

module.exports = router;