const express = require('express');
const multer = require('multer');
const { sanitizeInput, validateFileUpload } = require('../middleware/validation');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow only certain file types
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and PDF files are allowed.'));
    }
  },
});

// Apply sanitization to all routes
router.use(sanitizeInput);

// Upload profile picture
router.post('/profile-picture', 
  upload.single('image'),
  validateFileUpload(['image/jpeg', 'image/png', 'image/gif'], 5 * 1024 * 1024),
  (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // For now, just return success
      // In production, this would upload to Azure Blob Storage
      res.json({
        message: 'File uploaded successfully',
        filename: req.file.originalname,
        size: req.file.size,
        url: `/uploads/${req.file.filename}` // Mock URL
      });
    } catch (error) {
      res.status(500).json({ error: 'Upload failed' });
    }
  }
);

// Upload resume file
router.post('/resume', 
  upload.single('file'),
  validateFileUpload(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'], 10 * 1024 * 1024),
  (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // For now, just return success
      // In production, this would process and upload to Azure Blob Storage
      res.json({
        message: 'Resume uploaded successfully',
        filename: req.file.originalname,
        size: req.file.size,
        url: `/uploads/${req.file.filename}` // Mock URL
      });
    } catch (error) {
      res.status(500).json({ error: 'Upload failed' });
    }
  }
);

module.exports = router;