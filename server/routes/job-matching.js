const express = require('express');
const { sanitizeInput } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');
const AIService = require('../aiService');
const Database = require('../database');

const router = express.Router();
const aiService = new AIService();

// Apply middleware
router.use(sanitizeInput);
router.use(authenticate);

// Get job match scores
router.post('/match', async (req, res) => {
  try {
    const { resumeData, jobDescription, companyData = {} } = req.body;
    
    if (!resumeData || !jobDescription) {
      return res.status(400).json({ error: 'Resume data and job description are required' });
    }
    
    // Generate comprehensive job match analysis
    const matchAnalysis = await generateJobMatchAnalysis(resumeData, jobDescription, companyData);
    
    // Store job match score
    const db = Database.get();
    
    // First, find or create job record
    let jobId;
    if (req.body.jobId) {
      jobId = req.body.jobId;
    } else {
      // Create a new job record
      const jobInsert = await new Promise((resolve, reject) => {
        db.run(`
          INSERT INTO jobs (user_id, title, company, description, url, status)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          req.user.userId,
          jobDescription.title || 'Unknown Title',
          companyData.name || 'Unknown Company',
          JSON.stringify(jobDescription),
          jobDescription.url || '',
          'analyzed'
        ], function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        });
      });
      jobId = jobInsert;
    }
    
    // Store match analysis
    db.run(`
      INSERT INTO job_matching_scores (
        user_id, job_id, overall_score, skill_match_score, 
        experience_match_score, culture_fit_score, growth_potential_score,
        match_breakdown, recommendations
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      req.user.userId,
      jobId,
      matchAnalysis.overallScore,
      matchAnalysis.breakdown.skillMatch,
      matchAnalysis.breakdown.experienceMatch,
      matchAnalysis.breakdown.cultureMatch,
      matchAnalysis.breakdown.growthPotential,
      JSON.stringify(matchAnalysis.breakdown),
      JSON.stringify(matchAnalysis.recommendations)
    ]);
    
    res.json({
      success: true,
      data: {
        ...matchAnalysis,
        jobId
      }
    });
    
  } catch (error) {
    console.error('Job matching error:', error);
    res.status(500).json({ error: 'Failed to analyze job match' });
  }
});

// Get personalized job recommendations
router.get('/recommendations', async (req, res) => {
  try {
    const { location, industry, experience_level, skills, limit = 10 } = req.query;
    
    // Get user's resume data for better recommendations
    const db = Database.get();
    
    // Get user's latest resume
    const userResume = await new Promise((resolve, reject) => {
      db.get(`
        SELECT data FROM resumes 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY updated_at DESC LIMIT 1
      `, [req.user.userId], (err, row) => {
        if (err) reject(err);
        else resolve(row ? JSON.parse(row.data) : null);
      });
    });
    
    if (!userResume) {
      return res.status(400).json({ error: 'No resume found. Please create a resume first.' });
    }
    
    // Generate AI-powered job recommendations
    const recommendations = await generateJobRecommendations(userResume, {
      location,
      industry,
      experience_level,
      skills: skills ? skills.split(',') : []
    });
    
    res.json({
      success: true,
      data: recommendations
    });
    
  } catch (error) {
    console.error('Job recommendations error:', error);
    res.status(500).json({ error: 'Failed to generate job recommendations' });
  }
});

// Analyze company culture fit
router.post('/culture-fit', async (req, res) => {
  try {
    const { resumeData, companyData } = req.body;
    
    if (!resumeData || !companyData) {
      return res.status(400).json({ error: 'Resume data and company data are required' });
    }
    
    const cultureFitAnalysis = await analyzeCultureFit(resumeData, companyData);
    
    res.json({
      success: true,
      data: cultureFitAnalysis
    });
    
  } catch (error) {
    console.error('Culture fit analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze culture fit' });
  }
});

// Get market trend analysis
router.get('/market-analysis', async (req, res) => {
  try {
    const { industry, location, role } = req.query;
    
    const marketAnalysis = await generateMarketAnalysis(industry, location, role);
    
    res.json({
      success: true,
      data: marketAnalysis
    });
    
  } catch (error) {
    console.error('Market analysis error:', error);
    res.status(500).json({ error: 'Failed to generate market analysis' });
  }
});

// Get user's match history
router.get('/history', (req, res) => {
  const { limit = 20 } = req.query;
  const db = Database.get();
  
  db.all(`
    SELECT 
      jms.*,
      j.title as job_title,
      j.company,
      j.created_at as analyzed_at
    FROM job_matching_scores jms
    JOIN jobs j ON jms.job_id = j.id
    WHERE jms.user_id = ?
    ORDER BY jms.created_at DESC
    LIMIT ?
  `, [req.user.userId, parseInt(limit)], (err, matches) => {
    if (err) {
      console.error('Match history error:', err);
      return res.status(500).json({ error: 'Failed to fetch match history' });
    }
    
    const history = matches.map(match => ({
      ...match,
      match_breakdown: JSON.parse(match.match_breakdown),
      recommendations: JSON.parse(match.recommendations)
    }));
    
    res.json({
      success: true,
      data: history
    });
  });
});

// Helper functions

async function generateJobMatchAnalysis(resumeData, jobDescription, companyData) {
  const matchPrompt = `
Analyze the job match between the candidate and position using machine learning principles.

Candidate Profile:
- Name: ${resumeData.name}
- Current Title: ${resumeData.title}
- Experience: ${resumeData.experience?.map(exp => `${exp.role} at ${exp.company}`).join('; ') || 'Not specified'}
- Skills: ${resumeData.skills?.join(', ') || 'Not specified'}
- Education: ${resumeData.education?.map(edu => `${edu.degree} from ${edu.institution}`).join('; ') || 'Not specified'}

Job Description:
${JSON.stringify(jobDescription)}

Company Information:
${JSON.stringify(companyData)}

Perform comprehensive analysis including:
1. Skills match analysis (technical and soft skills)
2. Experience level and relevance matching
3. Company culture fit assessment
4. Growth potential evaluation
5. Salary and compensation alignment
6. Location and work-life balance factors

Calculate scores (0-100) for each category and overall score.
Provide specific recommendations for improving match or application strategy.

Format as JSON:
{
  "overallScore": 85,
  "breakdown": {
    "skillMatch": 90,
    "experienceMatch": 80,
    "cultureMatch": 85,
    "growthPotential": 88,
    "compensationFit": 75,
    "locationFit": 95
  },
  "strengths": ["..."],
  "gaps": ["..."],
  "recommendations": [
    {
      "type": "skill_development",
      "priority": "high",
      "action": "...",
      "timeline": "..."
    }
  ],
  "applicationStrategy": {
    "approach": "...",
    "keyPoints": ["..."],
    "timeline": "..."
  },
  "successProbability": 0.75,
  "competitiveAnalysis": {
    "candidateRanking": "top 25%",
    "differentiators": ["..."],
    "marketPosition": "strong"
  }
}
  `;
  
  const result = await aiService.callWithFallback(matchPrompt, {
    temperature: 0.5,
    maxTokens: 2500
  });
  
  return JSON.parse(result);
}

async function generateJobRecommendations(resumeData, preferences) {
  const recommendationPrompt = `
Generate personalized job recommendations based on candidate profile and preferences.

Candidate Profile:
- Current Title: ${resumeData.title}
- Experience: ${resumeData.experience?.map(exp => `${exp.role} at ${exp.company}`).join('; ')}
- Skills: ${resumeData.skills?.join(', ')}
- Education: ${resumeData.education?.map(edu => `${edu.degree}`).join('; ')}

Preferences:
- Location: ${preferences.location || 'Any'}
- Industry: ${preferences.industry || 'Any'}
- Experience Level: ${preferences.experience_level || 'Current level'}
- Target Skills: ${preferences.skills?.join(', ') || 'Not specified'}

Recommend 10 specific job roles that would be excellent matches, including:
1. Job titles and descriptions
2. Required skills and qualifications
3. Salary ranges
4. Growth opportunities
5. Match reasoning

Format as JSON:
{
  "recommendations": [
    {
      "title": "...",
      "company": "...",
      "location": "...",
      "salaryRange": "...",
      "matchScore": 95,
      "description": "...",
      "requirements": ["..."],
      "growthPotential": "...",
      "whyRecommended": "...",
      "applicationTips": ["..."]
    }
  ],
  "marketInsights": {
    "demandLevel": "high",
    "salaryTrends": "...",
    "skillsInDemand": ["..."],
    "careerOutlook": "..."
  },
  "careerPaths": [
    {
      "path": "...",
      "timeline": "...",
      "keyMilestones": ["..."]
    }
  ]
}
  `;
  
  const result = await aiService.callWithFallback(recommendationPrompt, {
    temperature: 0.7,
    maxTokens: 3000
  });
  
  return JSON.parse(result);
}

async function analyzeCultureFit(resumeData, companyData) {
  const cultureFitPrompt = `
Analyze culture fit between candidate and company.

Candidate Profile:
- Background: ${resumeData.title} with experience at ${resumeData.experience?.map(exp => exp.company).join(', ')}
- Skills: ${resumeData.skills?.join(', ')}
- Work Style: Extract from experience descriptions

Company Profile:
${JSON.stringify(companyData)}

Analyze fit across multiple dimensions:
1. Work style alignment
2. Values compatibility
3. Team dynamics fit
4. Growth mindset match
5. Communication style alignment
6. Innovation vs. stability preference

Format as JSON:
{
  "overallFitScore": 82,
  "dimensions": {
    "workStyle": 85,
    "values": 80,
    "teamDynamics": 88,
    "growthMindset": 75,
    "communication": 90,
    "innovation": 70
  },
  "strengths": ["..."],
  "concerns": ["..."],
  "recommendations": ["..."],
  "culturalAlignment": {
    "wellSuited": ["..."],
    "mayStruggleWith": ["..."],
    "adaptationNeeded": ["..."]
  },
  "questionsSuggested": ["..."]
}
  `;
  
  const result = await aiService.callWithFallback(cultureFitPrompt, {
    temperature: 0.6,
    maxTokens: 2000
  });
  
  return JSON.parse(result);
}

async function generateMarketAnalysis(industry, location, role) {
  const marketPrompt = `
Generate comprehensive job market analysis for:
- Industry: ${industry || 'Technology'}
- Location: ${location || 'Global'}
- Role: ${role || 'General'}

Provide analysis including:
1. Market demand trends
2. Salary benchmarks and trends
3. Skills in highest demand
4. Emerging opportunities
5. Competition levels
6. Future outlook

Format as JSON:
{
  "marketDemand": {
    "level": "high|medium|low",
    "trend": "increasing|stable|decreasing",
    "jobOpenings": 1500,
    "growthRate": "15%"
  },
  "salaryBenchmarks": {
    "entry": "...",
    "mid": "...",
    "senior": "...",
    "trend": "..."
  },
  "hotSkills": ["..."],
  "emergingRoles": ["..."],
  "competitionLevel": "moderate",
  "futureOutlook": {
    "shortTerm": "...",
    "longTerm": "...",
    "threats": ["..."],
    "opportunities": ["..."]
  },
  "recommendations": ["..."]
}
  `;
  
  const result = await aiService.callWithFallback(marketPrompt, {
    temperature: 0.4,
    maxTokens: 2000
  });
  
  return JSON.parse(result);
}

module.exports = router;