const express = require('express');
const router = express.Router();
const loopService = require('../services/loopService');
const JobApplicationService = require('../jobApplicationService');
const JobSearchOptimizer = require('../services/jobSearchOptimizer');
const database = require('../database');

/**
 * Dashboard Analytics Controller
 * Provides comprehensive automation metrics and monitoring
 */

const optimizer = new JobSearchOptimizer();
const jobApplicationService = new JobApplicationService();

// GET /api/automation/dashboard - Main automation dashboard data
router.get('/dashboard', async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // Get overview metrics
    const loops = await loopService.getUserLoops(userId);
    const discoveredJobs = await loopService.getDiscoveredJobs(userId, 100);
    const queueStatus = jobApplicationService.getQueueStatus();
    
    // Calculate summary metrics
    const activeLoops = loops.filter(loop => loop.status === 'active').length;
    const totalJobsDiscovered = discoveredJobs.length;
    const jobsAppliedTo = discoveredJobs.filter(job => job.status === 'applied').length;
    const averageRelevanceScore = discoveredJobs.length > 0 
      ? discoveredJobs.reduce((sum, job) => sum + (job.relevanceScore || 0), 0) / discoveredJobs.length 
      : 0;
    
    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentJobs = discoveredJobs.filter(job => 
      new Date(job.discoveredAt) > sevenDaysAgo
    );
    
    // Platform distribution
    const platformStats = {};
    discoveredJobs.forEach(job => {
      platformStats[job.sourcePlatform] = (platformStats[job.sourcePlatform] || 0) + 1;
    });
    
    // Success rate calculation
    const applicationSuccessRate = jobsAppliedTo > 0 
      ? (jobsAppliedTo / totalJobsDiscovered) * 100 
      : 0;
    
    const dashboardData = {
      overview: {
        totalLoops: loops.length,
        activeLoops,
        totalJobsDiscovered,
        jobsAppliedTo,
        applicationSuccessRate: Math.round(applicationSuccessRate * 100) / 100,
        averageRelevanceScore: Math.round(averageRelevanceScore * 100) / 100
      },
      recentActivity: {
        jobsDiscoveredThisWeek: recentJobs.length,
        newApplicationsThisWeek: recentJobs.filter(job => job.status === 'applied').length,
        topPerformingLoop: activeLoops > 0 ? loops[0].name : null
      },
      queueStatus,
      platformDistribution: platformStats,
      loopSummary: loops.map(loop => ({
        id: loop.id,
        name: loop.name,
        status: loop.status,
        jobsFound: discoveredJobs.filter(job => job.loopId === loop.id).length,
        applicationsSubmitted: discoveredJobs.filter(job => 
          job.loopId === loop.id && job.status === 'applied'
        ).length
      }))
    };
    
    res.json({
      success: true,
      data: dashboardData
    });
    
  } catch (error) {
    console.error('Dashboard analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard data',
      message: error.message
    });
  }
});

// GET /api/automation/performance - Detailed performance metrics
router.get('/performance', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { timeframe = '30d', loopId } = req.query;
    
    // Calculate date range
    const daysBack = timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);
    
    // Get performance data
    const db = database.get();
    
    const performanceQuery = `
      SELECT 
        DATE(dj.discovered_at) as date,
        COUNT(*) as jobs_discovered,
        COUNT(CASE WHEN dj.status = 'applied' THEN 1 END) as jobs_applied,
        AVG(dj.relevance_score) as avg_relevance_score,
        dj.source_platform
      FROM discovered_jobs dj
      JOIN job_loops jl ON dj.loop_id = jl.id
      WHERE jl.user_id = ? 
        AND dj.discovered_at >= ?
        ${loopId ? 'AND jl.id = ?' : ''}
      GROUP BY DATE(dj.discovered_at), dj.source_platform
      ORDER BY dj.discovered_at DESC
    `;
    
    const params = [userId, startDate.toISOString()];
    if (loopId) params.push(loopId);
    
    const performanceData = await new Promise((resolve, reject) => {
      db.all(performanceQuery, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });
    
    // Process data for charts
    const dailyStats = {};
    const platformStats = {};
    
    performanceData.forEach(row => {
      const date = row.date;
      const platform = row.source_platform;
      
      if (!dailyStats[date]) {
        dailyStats[date] = {
          date,
          jobsDiscovered: 0,
          jobsApplied: 0,
          avgRelevanceScore: 0,
          count: 0
        };
      }
      
      dailyStats[date].jobsDiscovered += row.jobs_discovered;
      dailyStats[date].jobsApplied += row.jobs_applied;
      dailyStats[date].avgRelevanceScore = 
        (dailyStats[date].avgRelevanceScore * dailyStats[date].count + row.avg_relevance_score) / 
        (dailyStats[date].count + 1);
      dailyStats[date].count++;
      
      if (!platformStats[platform]) {
        platformStats[platform] = {
          platform,
          jobsDiscovered: 0,
          jobsApplied: 0,
          successRate: 0
        };
      }
      
      platformStats[platform].jobsDiscovered += row.jobs_discovered;
      platformStats[platform].jobsApplied += row.jobs_applied;
    });
    
    // Calculate success rates
    Object.values(platformStats).forEach(stats => {
      stats.successRate = stats.jobsDiscovered > 0 
        ? (stats.jobsApplied / stats.jobsDiscovered) * 100 
        : 0;
    });
    
    res.json({
      success: true,
      data: {
        timeframe,
        dailyPerformance: Object.values(dailyStats),
        platformPerformance: Object.values(platformStats),
        summary: {
          totalJobsDiscovered: performanceData.reduce((sum, row) => sum + row.jobs_discovered, 0),
          totalJobsApplied: performanceData.reduce((sum, row) => sum + row.jobs_applied, 0),
          averageRelevanceScore: performanceData.length > 0 
            ? performanceData.reduce((sum, row) => sum + row.avg_relevance_score, 0) / performanceData.length
            : 0
        }
      }
    });
    
  } catch (error) {
    console.error('Performance analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch performance data',
      message: error.message
    });
  }
});

// GET /api/automation/queue - Application queue monitoring
router.get('/queue', async (req, res) => {
  try {
    const queueStatus = jobApplicationService.getQueueStatus();
    const queueHistory = await jobApplicationService.getQueueHistory();
    
    res.json({
      success: true,
      data: {
        currentStatus: queueStatus,
        history: queueHistory,
        recommendations: generateQueueRecommendations(queueStatus)
      }
    });
    
  } catch (error) {
    console.error('Queue monitoring error:', error);
    res.status(500).json({
      error: 'Failed to fetch queue data',
      message: error.message
    });
  }
});

// Helper function to generate optimization recommendations
function generateQueueRecommendations(queueStatus) {
  const recommendations = [];
  
  if (queueStatus.total > 50) {
    recommendations.push({
      type: 'warning',
      message: 'High queue volume detected. Consider adjusting application frequency.',
      action: 'Reduce max applications per day in loop settings'
    });
  }
  
  if (queueStatus.statusBreakdown.failed > 5) {
    recommendations.push({
      type: 'error',
      message: 'Multiple application failures detected. Check automation settings.',
      action: 'Review browser automation configuration and job platform access'
    });
  }
  
  if (queueStatus.total === 0 && !queueStatus.isProcessing) {
    recommendations.push({
      type: 'info',
      message: 'No applications in queue. Your loops may need optimization.',
      action: 'Review loop configurations and job discovery settings'
    });
  }
  
  return recommendations;
}

// GET /api/automation/optimize/:loopId - Loop optimization analysis
router.get('/optimize/:loopId', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.loopId;
    
    // Verify user owns the loop
    const loops = await loopService.getUserLoops(userId);
    const loop = loops.find(l => l.id == loopId);
    
    if (!loop) {
      return res.status(404).json({
        error: 'Loop not found'
      });
    }
    
    const optimization = await optimizer.analyzeLoopPerformance(userId, loopId);
    
    res.json({
      success: true,
      data: optimization
    });
    
  } catch (error) {
    console.error('Loop optimization error:', error);
    res.status(500).json({
      error: 'Failed to analyze loop optimization',
      message: error.message
    });
  }
});

// GET /api/automation/trending - Market trends and opportunities
router.get('/trending', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { industries } = req.query;
    
    const trending = await optimizer.getTrendingOpportunities(
      userId, 
      industries ? industries.split(',') : []
    );
    
    res.json({
      success: true,
      data: trending
    });
    
  } catch (error) {
    console.error('Trending analysis error:', error);
    res.status(500).json({
      error: 'Failed to analyze trending opportunities',
      message: error.message
    });
  }
});

// GET /api/automation/timing - Optimal application timing
router.get('/timing', async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const timing = await optimizer.suggestOptimalTiming(userId);
    
    res.json({
      success: true,
      data: timing
    });
    
  } catch (error) {
    console.error('Timing analysis error:', error);
    res.status(500).json({
      error: 'Failed to analyze optimal timing',
      message: error.message
    });
  }
});

module.exports = router;