const express = require('express');
const AIController = require('../aiController');
const { validateRequest, schemas, sanitizeInput } = require('../middleware/validation');
const rateLimit = require('express-rate-limit');

const router = express.Router();
const aiController = new AIController();

// Rate limiting for AI endpoints
const aiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 AI requests per minute
  message: 'Too many AI requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting and sanitization to all AI routes
router.use(aiLimiter);
router.use(sanitizeInput);

// AI enhancement endpoints
router.post('/enhance-resume',
  validateRequest(schemas.aiRequest),
  aiController.enhanceResume.bind(aiController)
);

router.post('/generate-cover-letter',
  validateRequest(schemas.aiRequest),
  aiController.generateCoverLetter.bind(aiController)
);

router.post('/analyze-ats',
  validateRequest(schemas.aiRequest),
  aiController.analyzeATS.bind(aiController)
);

router.post('/suggest-skills',
  validateRequest(schemas.aiRequest),
  aiController.suggestSkills.bind(aiController)
);

// AI status endpoints
router.get('/models', aiController.getAvailableModels.bind(aiController));
router.get('/status', aiController.getModelStatus.bind(aiController));

// Advanced AI features
router.post('/simulate-interview', aiController.simulateInterview.bind(aiController));
router.post('/predict-job-success', aiController.predictJobSuccess.bind(aiController));

module.exports = router;