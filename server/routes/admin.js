const express = require('express');
const router = express.Router();
const Joi = require('joi');

// Import middleware
const { authenticateAdmin, requireAdminLevel, requireSuperAdmin } = require('../middleware/adminAuth');
const { requireRole, requirePermission } = require('../middleware/rbac');
const { validateRequest } = require('../middleware/validation');

// Import controllers
const adminController = require('../controllers/adminController');

/**
 * Admin Routes
 * All routes require admin authentication and appropriate permissions
 */

// Validation schemas
const getUsersSchema = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    search: Joi.string().max(255).allow(''),
    role: Joi.string().valid('user', 'moderator', 'admin', 'super_admin').allow(''),
    status: Joi.string().valid('active', 'inactive', 'suspended', 'pending_verification').allow(''),
    sortBy: Joi.string().valid('created_at', 'updated_at', 'email', 'name', 'last_login_at').default('created_at'),
    sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
  })
};

const updateUserSchema = {
  body: Joi.object({
    name: Joi.string().min(1).max(255),
    first_name: Joi.string().max(255).allow(''),
    last_name: Joi.string().max(255).allow(''),
    user_role: Joi.string().valid('user', 'moderator', 'admin', 'super_admin'),
    account_status: Joi.string().valid('active', 'inactive', 'suspended', 'pending_verification'),
    is_active: Joi.boolean(),
    email_verified: Joi.boolean(),
    subscription_tier: Joi.string().valid('free', 'basic', 'premium', 'enterprise')
  }).min(1),
  params: Joi.object({
    userId: Joi.string().uuid().required()
  })
};

const createAdminUserSchema = {
  body: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(12).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/).required()
      .messages({
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      }),
    name: Joi.string().min(1).max(255).required(),
    first_name: Joi.string().max(255).allow(''),
    last_name: Joi.string().max(255).allow(''),
    admin_level: Joi.number().integer().min(1).max(9).default(1),
    permissions: Joi.object().default({})
  })
};

// Apply admin authentication to all routes
router.use(authenticateAdmin);

/**
 * @route GET /api/admin/users
 * @desc Get all users with pagination and filtering
 * @access Admin
 */
router.get('/users',
  requireRole(['admin', 'super_admin']),
  requirePermission(['user:read']),
  validateRequest(getUsersSchema),
  adminController.getUsers
);

/**
 * @route GET /api/admin/users/:userId
 * @desc Get specific user details
 * @access Admin
 */
router.get('/users/:userId',
  requireRole(['admin', 'super_admin']),
  requirePermission(['user:read']),
  validateRequest({
    params: Joi.object({
      userId: Joi.string().uuid().required()
    })
  }),
  adminController.getUser
);

/**
 * @route PUT /api/admin/users/:userId
 * @desc Update user information
 * @access Admin
 */
router.put('/users/:userId',
  requireRole(['admin', 'super_admin']),
  requirePermission(['user:update']),
  validateRequest(updateUserSchema),
  adminController.updateUser
);

/**
 * @route POST /api/admin/users/admin
 * @desc Create new admin user
 * @access Super Admin only
 */
router.post('/users/admin',
  requireSuperAdmin,
  requirePermission(['admin:create']),
  validateRequest(createAdminUserSchema),
  adminController.createAdminUser
);

/**
 * @route DELETE /api/admin/users/:userId
 * @desc Delete user (soft delete)
 * @access Super Admin only
 */
router.delete('/users/:userId',
  requireSuperAdmin,
  requirePermission(['user:delete']),
  validateRequest({
    params: Joi.object({
      userId: Joi.string().uuid().required()
    })
  }),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const database = require('../database');
      const { logger } = require('../utils/logger');
      
      const db = database.get();
      
      // Check if user exists and is not a super_admin
      const user = await db.get('SELECT user_role FROM users WHERE id = ?', [userId]);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
      }
      
      if (user.user_role === 'super_admin') {
        return res.status(403).json({
          success: false,
          error: 'Cannot delete super admin user',
          code: 'CANNOT_DELETE_SUPER_ADMIN'
        });
      }
      
      // Soft delete by deactivating account
      await db.run(`
        UPDATE users 
        SET is_active = false, account_status = 'inactive', updated_at = ?
        WHERE id = ?
      `, [new Date().toISOString(), userId]);
      
      // Log admin action
      await db.run(`
        INSERT INTO audit_logs (
          user_id, action, resource_type, resource_id,
          metadata, ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'USER_DELETED',
        'admin_action',
        userId,
        JSON.stringify({ targetUserId: userId }),
        req.ip,
        req.get('User-Agent'),
        new Date().toISOString()
      ]);
      
      logger.info('Admin: User deleted', {
        adminId: req.user.id,
        targetUserId: userId
      });
      
      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      const { logger } = require('../utils/logger');
      logger.error('Admin: Delete user error', {
        error: error.message,
        userId: req.params.userId,
        adminId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Failed to delete user',
        code: 'DELETE_USER_ERROR'
      });
    }
  }
);

/**
 * @route GET /api/admin/stats
 * @desc Get system statistics
 * @access Admin
 */
router.get('/stats',
  requireRole(['admin', 'super_admin']),
  requirePermission(['system:read']),
  adminController.getSystemStats
);

/**
 * @route GET /api/admin/audit-logs
 * @desc Get audit logs with pagination
 * @access Admin
 */
router.get('/audit-logs',
  requireRole(['admin', 'super_admin']),
  requirePermission(['audit:read']),
  validateRequest({
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(50),
      action: Joi.string().max(100).allow(''),
      userId: Joi.string().uuid().allow(''),
      startDate: Joi.date().iso(),
      endDate: Joi.date().iso(),
      sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
    })
  }),
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 50,
        action = '',
        userId = '',
        startDate,
        endDate,
        sortOrder = 'DESC'
      } = req.query;

      const offset = (page - 1) * limit;
      const database = require('../database');
      const db = database.get();

      // Build WHERE clause
      let whereClause = 'WHERE 1=1';
      const params = [];

      if (action) {
        whereClause += ' AND action LIKE ?';
        params.push(`%${action}%`);
      }

      if (userId) {
        whereClause += ' AND user_id = ?';
        params.push(userId);
      }

      if (startDate) {
        whereClause += ' AND created_at >= ?';
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND created_at <= ?';
        params.push(endDate);
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM audit_logs ${whereClause}`;
      const countResult = await db.get(countQuery, params);
      const total = countResult.total;

      // Get audit logs
      const logsQuery = `
        SELECT 
          al.*,
          u.email as user_email,
          u.name as user_name
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ${whereClause}
        ORDER BY al.created_at ${sortOrder}
        LIMIT ? OFFSET ?
      `;

      const logs = await db.all(logsQuery, [...params, limit, offset]);

      // Parse metadata for each log
      const processedLogs = logs.map(log => ({
        ...log,
        metadata: log.metadata ? JSON.parse(log.metadata) : {}
      }));

      res.json({
        success: true,
        data: {
          logs: processedLogs,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      const { logger } = require('../utils/logger');
      logger.error('Admin: Get audit logs error', {
        error: error.message,
        adminId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve audit logs',
        code: 'GET_AUDIT_LOGS_ERROR'
      });
    }
  }
);

/**
 * @route GET /api/admin/health
 * @desc Get system health status
 * @access Admin
 */
router.get('/health',
  requireRole(['admin', 'super_admin']),
  async (req, res) => {
    try {
      const database = require('../database');
      const encryptionService = require('../utils/encryptionService');
      
      // Check database health
      const dbHealth = await database.getHealthStatus();
      
      // Check encryption service
      const encryptionHealth = encryptionService.testEncryption();
      
      // Check system resources
      const systemHealth = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform
      };
      
      const overallHealth = dbHealth.status === 'healthy' && encryptionHealth.success;
      
      res.json({
        success: true,
        data: {
          status: overallHealth ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          components: {
            database: dbHealth,
            encryption: {
              status: encryptionHealth.success ? 'healthy' : 'unhealthy',
              version: encryptionHealth.version
            },
            system: systemHealth
          }
        }
      });
    } catch (error) {
      const { logger } = require('../utils/logger');
      logger.error('Admin: Health check error', {
        error: error.message,
        adminId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Health check failed',
        code: 'HEALTH_CHECK_ERROR'
      });
    }
  }
);

module.exports = router;
