const express = require('express');
const AnalyticsController = require('../analyticsController');
const { sanitizeInput } = require('../middleware/validation');

const router = express.Router();
const analyticsController = new AnalyticsController();

// Apply sanitization to all routes
router.use(sanitizeInput);

// Analytics endpoints
router.get('/dashboard', analyticsController.getDashboard.bind(analyticsController));
router.post('/predict', analyticsController.getPredictiveAnalytics.bind(analyticsController));
router.get('/application-metrics', analyticsController.getApplicationMetrics.bind(analyticsController));
router.get('/job-market', analyticsController.getJobMarketInsights.bind(analyticsController));
router.get('/resume-performance', analyticsController.getResumePerformance.bind(analyticsController));
router.get('/success-patterns', analyticsController.getSuccessPatterns.bind(analyticsController));
router.get('/weekly-trends', analyticsController.getWeeklyTrends.bind(analyticsController));

// Executive & Business Intelligence (from PR #24)
router.get('/executive-dashboard', analyticsController.getEnhancedDashboard.bind(analyticsController));
router.get('/business-performance', (req, res) => {
  // Set default metric type for business performance
  req.params.metricType = 'applications';
  return analyticsController.getDetailedMetrics.call(analyticsController, req, res);
});
router.get('/platform-metrics', analyticsController.getDashboardAnalytics.bind(analyticsController));

// Predictive Analytics (from PR #24)
router.get('/churn-prediction', analyticsController.predictApplicationSuccess.bind(analyticsController));
router.get('/skills-forecast', analyticsController.getMarketTrends.bind(analyticsController));
router.get('/revenue-projection', analyticsController.getOptimizationRecommendations.bind(analyticsController));

// Market Intelligence (from PR #24)
router.get('/market-intelligence', analyticsController.getAIInsights.bind(analyticsController));
router.get('/market-trends', analyticsController.getMarketTrends.bind(analyticsController));

// Real-time & Custom Analytics (from PR #24)
router.post('/realtime-event', analyticsController.trackEvent.bind(analyticsController));
router.post('/custom-report', analyticsController.generateReport.bind(analyticsController));
router.get('/automated-insights', analyticsController.getPersonalizedStrategy.bind(analyticsController));

module.exports = router;