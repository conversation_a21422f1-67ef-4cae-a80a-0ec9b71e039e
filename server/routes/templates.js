const express = require('express');
const { sanitizeInput } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');
const Database = require('../database');
const templateExportService = require('../templateExportService');

const router = express.Router();

// Apply sanitization to all routes
router.use(sanitizeInput);

// Initialize default templates if database is empty
const initializeDefaultTemplates = () => {
  const db = Database.get();
  
  db.get('SELECT COUNT(*) as count FROM resume_templates', (err, row) => {
    if (err || row.count > 0) return;
    
    const defaultTemplates = [
      {
        name: 'Modern Professional',
        description: 'Clean and modern template for professional roles',
        category: 'professional',
        industry: 'Technology,Finance,Healthcare',
        ats_score: 92,
        is_premium: 0,
        structure: JSON.stringify({
          sections: ['header', 'summary', 'experience', 'education', 'skills'],
          layout: 'single-column',
          styling: {
            fonts: ['Inter', 'Arial'],
            colors: { primary: '#2563eb', secondary: '#64748b' },
            spacing: 'modern'
          }
        }),
        preview_url: '/templates/modern-professional.png'
      },
      {
        name: 'Creative Design',
        description: 'Stylish template for creative professionals',
        category: 'creative',
        industry: 'Design,Marketing,Media',
        ats_score: 85,
        is_premium: 1,
        structure: JSON.stringify({
          sections: ['header', 'portfolio', 'experience', 'skills', 'education'],
          layout: 'two-column',
          styling: {
            fonts: ['Montserrat', 'Helvetica'],
            colors: { primary: '#7c3aed', secondary: '#a78bfa' },
            spacing: 'creative'
          }
        }),
        preview_url: '/templates/creative-design.png'
      },
      {
        name: 'Executive Classic',
        description: 'Traditional template for executive positions',
        category: 'executive',
        industry: 'Executive,Management,Consulting',
        ats_score: 95,
        is_premium: 1,
        structure: JSON.stringify({
          sections: ['header', 'executive-summary', 'experience', 'achievements', 'education'],
          layout: 'single-column',
          styling: {
            fonts: ['Times New Roman', 'Georgia'],
            colors: { primary: '#1f2937', secondary: '#6b7280' },
            spacing: 'traditional'
          }
        }),
        preview_url: '/templates/executive-classic.png'
      },
      {
        name: 'ATS Optimized',
        description: 'Specifically designed for ATS parsing with maximum compatibility',
        category: 'ats-optimized',
        industry: 'All',
        ats_score: 98,
        is_premium: 0,
        structure: JSON.stringify({
          sections: ['header', 'summary', 'experience', 'education', 'skills', 'certifications'],
          layout: 'single-column',
          styling: {
            fonts: ['Arial', 'Calibri'],
            colors: { primary: '#000000', secondary: '#333333' },
            spacing: 'minimal'
          }
        }),
        preview_url: '/templates/ats-optimized.png'
      },
      {
        name: 'Tech Engineer',
        description: 'Technical template optimized for software engineering roles',
        category: 'technology',
        industry: 'Technology,Software,Engineering',
        ats_score: 75,
        is_premium: 0,
        structure: JSON.stringify({
          sections: ['header', 'summary', 'technical-skills', 'experience', 'projects', 'education'],
          layout: 'single-column',
          styling: {
            fonts: ['Roboto', 'Arial'],
            colors: { primary: '#059669', secondary: '#10b981' },
            spacing: 'technical'
          }
        }),
        preview_url: '/templates/tech-engineer.png'
      },
      {
        name: 'Healthcare Professional',
        description: 'Professional template for medical and healthcare roles',
        category: 'healthcare',
        industry: 'Healthcare,Medical,Nursing',
        ats_score: 80,
        is_premium: 0,
        structure: JSON.stringify({
          sections: ['header', 'summary', 'experience', 'education', 'certifications', 'skills'],
          layout: 'single-column',
          styling: {
            fonts: ['Open Sans', 'Arial'],
            colors: { primary: '#dc2626', secondary: '#ef4444' },
            spacing: 'professional'
          }
        }),
        preview_url: '/templates/healthcare-professional.png'
      },
      {
        name: 'Sales & Marketing',
        description: 'Achievement-focused template for sales and marketing professionals',
        category: 'sales',
        industry: 'Sales,Marketing,Business Development',
        ats_score: 80,
        is_premium: 0,
        structure: JSON.stringify({
          sections: ['header', 'summary', 'achievements', 'experience', 'education', 'skills'],
          layout: 'single-column',
          styling: {
            fonts: ['Lato', 'Arial'],
            colors: { primary: '#ea580c', secondary: '#fb923c' },
            spacing: 'achievement-focused'
          }
        }),
        preview_url: '/templates/sales-marketing.png'
      }
    ];
    
    const stmt = db.prepare(`
      INSERT INTO resume_templates (name, description, category, industry, ats_score, is_premium, structure, preview_url)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    defaultTemplates.forEach(template => {
      stmt.run([
        template.name,
        template.description,
        template.category,
        template.industry,
        template.ats_score,
        template.is_premium,
        template.structure,
        template.preview_url
      ]);
    });
    
    stmt.finalize();
    console.log('Default resume templates initialized');
  });
};

// Initialize templates on startup
setTimeout(initializeDefaultTemplates, 1000);

// Get available templates with enhanced filtering and sorting
router.get('/', (req, res) => {
  const { 
    category, 
    industry, 
    experienceLevel,
    premium, 
    atsOptimized,
    search,
    sort = 'usage_count',
    limit = 50,
    offset = 0
  } = req.query;
  const db = Database.get();
  
  let query = 'SELECT * FROM resume_templates WHERE 1=1';
  const params = [];
  
  if (category) {
    query += ' AND category = ?';
    params.push(category);
  }
  
  if (industry) {
    query += ' AND (industry LIKE ? OR industry = "All")';
    params.push(`%${industry}%`);
  }

  if (experienceLevel) {
    // Add experience level filtering logic
    query += ' AND (category = ? OR industry LIKE ?)';
    params.push(experienceLevel, `%${experienceLevel}%`);
  }
  
  if (premium !== undefined) {
    query += ' AND is_premium = ?';
    params.push(premium === 'true' ? 1 : 0);
  }

  if (atsOptimized !== undefined) {
    query += ' AND ats_score >= ?';
    params.push(atsOptimized === 'true' ? 90 : 0);
  }

  if (search) {
    query += ' AND (name LIKE ? OR description LIKE ?)';
    params.push(`%${search}%`, `%${search}%`);
  }
  
  // Add sorting
  const validSorts = ['usage_count', 'rating', 'ats_score', 'created_at', 'name'];
  if (validSorts.includes(sort)) {
    if (sort === 'name') {
      query += ` ORDER BY ${sort} ASC`;
    } else {
      query += ` ORDER BY ${sort} DESC`;
    }
  }

  // Add pagination
  query += ' LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Templates query error:', err);
      return res.status(500).json({ error: 'Failed to fetch templates' });
    }
    
    const templates = rows.map(row => ({
      ...row,
      structure: JSON.parse(row.structure),
      industry: row.industry ? row.industry.split(',') : []
    }));
    
    res.json({
      success: true,
      data: templates,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: templates.length
      }
    });
  });
});

// Get specific template with analytics
router.get('/:id', (req, res) => {
  const { id } = req.params;
  const db = Database.get();
  
  db.get('SELECT * FROM resume_templates WHERE id = ?', [id], (err, template) => {
    if (err) {
      console.error('Template fetch error:', err);
      return res.status(500).json({ error: 'Failed to fetch template' });
    }
    
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Get template analytics
    db.get(`
      SELECT 
        AVG(success_score) as avg_success_score,
        SUM(application_count) as total_applications,
        AVG(response_rate) as avg_response_rate
      FROM template_analytics 
      WHERE template_id = ?
    `, [id], (err, analytics) => {
      const templateData = {
        ...template,
        structure: JSON.parse(template.structure),
        industry: template.industry ? template.industry.split(',') : [],
        analytics: analytics || {
          avg_success_score: 0,
          total_applications: 0,
          avg_response_rate: 0
        }
      };
      
      res.json({
        success: true,
        data: templateData
      });
    });
  });
});

// Create custom template
router.post('/', authenticate, (req, res) => {
  const { name, description, category, industry, structure, is_premium = 0 } = req.body;
  const db = Database.get();
  
  if (!name || !category || !structure) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  db.run(`
    INSERT INTO resume_templates (name, description, category, industry, structure, is_premium, created_by)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [
    name,
    description,
    category,
    Array.isArray(industry) ? industry.join(',') : industry,
    JSON.stringify(structure),
    is_premium,
    req.user.userId
  ], function(err) {
    if (err) {
      console.error('Template creation error:', err);
      return res.status(500).json({ error: 'Failed to create template' });
    }
    
    res.status(201).json({
      success: true,
      data: { id: this.lastID }
    });
  });
});

// Update template
router.put('/:id', authenticate, (req, res) => {
  const { id } = req.params;
  const { name, description, category, industry, structure } = req.body;
  const db = Database.get();
  
  // Check if user owns the template or is admin
  db.get('SELECT created_by FROM resume_templates WHERE id = ?', [id], (err, template) => {
    if (err || !template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    if (template.created_by !== req.user.userId) {
      return res.status(403).json({ error: 'Unauthorized' });
    }
    
    db.run(`
      UPDATE resume_templates 
      SET name = ?, description = ?, category = ?, industry = ?, structure = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      name,
      description,
      category,
      Array.isArray(industry) ? industry.join(',') : industry,
      JSON.stringify(structure),
      id
    ], (err) => {
      if (err) {
        console.error('Template update error:', err);
        return res.status(500).json({ error: 'Failed to update template' });
      }
      
      res.json({ success: true });
    });
  });
});

// Delete template
router.delete('/:id', authenticate, (req, res) => {
  const { id } = req.params;
  const db = Database.get();
  
  // Check if user owns the template
  db.get('SELECT created_by FROM resume_templates WHERE id = ?', [id], (err, template) => {
    if (err || !template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    if (template.created_by !== req.user.userId) {
      return res.status(403).json({ error: 'Unauthorized' });
    }
    
    db.run('DELETE FROM resume_templates WHERE id = ?', [id], (err) => {
      if (err) {
        console.error('Template deletion error:', err);
        return res.status(500).json({ error: 'Failed to delete template' });
      }
      
      res.json({ success: true });
    });
  });
});

// Get template analytics
router.get('/:id/analytics', authenticate, (req, res) => {
  const { id } = req.params;
  const { timeRange = '30' } = req.query;
  const db = Database.get();
  
  db.all(`
    SELECT 
      ta.*,
      u.name as user_name
    FROM template_analytics ta
    JOIN users u ON ta.user_id = u.id
    WHERE ta.template_id = ? 
    AND ta.used_at >= datetime('now', '-${parseInt(timeRange)} days')
    ORDER BY ta.used_at DESC
  `, [id], (err, analytics) => {
    if (err) {
      console.error('Template analytics error:', err);
      return res.status(500).json({ error: 'Failed to fetch analytics' });
    }
    
    // Calculate summary statistics
    const summary = analytics.reduce((acc, item) => {
      acc.totalUsage++;
      acc.totalApplications += item.application_count || 0;
      acc.totalSuccessScore += item.success_score || 0;
      acc.totalResponseRate += item.response_rate || 0;
      return acc;
    }, { totalUsage: 0, totalApplications: 0, totalSuccessScore: 0, totalResponseRate: 0 });
    
    res.json({
      success: true,
      data: {
        analytics,
        summary: {
          totalUsage: summary.totalUsage,
          totalApplications: summary.totalApplications,
          avgSuccessScore: summary.totalUsage > 0 ? summary.totalSuccessScore / summary.totalUsage : 0,
          avgResponseRate: summary.totalUsage > 0 ? summary.totalResponseRate / summary.totalUsage : 0
        }
      }
    });
  });
});

// Track template usage
router.post('/:id/usage', authenticate, (req, res) => {
  const { id } = req.params;
  const { usage_type = 'view', success_score, application_count, response_rate } = req.body;
  const db = Database.get();
  
  // Insert usage analytics
  db.run(`
    INSERT INTO template_analytics (template_id, user_id, usage_type, success_score, application_count, response_rate)
    VALUES (?, ?, ?, ?, ?, ?)
  `, [id, req.user.userId, usage_type, success_score, application_count, response_rate], (err) => {
    if (err) {
      console.error('Template usage tracking error:', err);
      return res.status(500).json({ error: 'Failed to track usage' });
    }
    
    // Update template usage count
    db.run('UPDATE resume_templates SET usage_count = usage_count + 1 WHERE id = ?', [id]);
    
    res.json({ success: true });
  });
});

// Track template usage (alternative endpoint from PR #10)
router.post('/:id/use', authenticate, (req, res) => {
  const { id } = req.params;
  const { usageData } = req.body;
  const db = Database.get();
  
  // Extract data from the usageData object
  const usage_type = 'use';
  const { selectedSections, customizations, completionTime } = usageData || {};
  
  // Insert usage analytics
  db.run(`
    INSERT INTO template_analytics (template_id, user_id, usage_type, usage_data)
    VALUES (?, ?, ?, ?)
  `, [id, req.user.userId, usage_type, JSON.stringify(usageData || {})], (err) => {
    if (err) {
      console.error('Template usage tracking error:', err);
      return res.status(500).json({ error: 'Failed to track usage' });
    }
    
    // Update template usage count
    db.run('UPDATE resume_templates SET usage_count = usage_count + 1 WHERE id = ?', [id]);
    
    res.json({ 
      success: true,
      data: {
        id: id,
        tracked: true,
        timestamp: new Date().toISOString()
      }
    });
  });
});

// Get template recommendations
router.post('/recommendations', authenticate, (req, res) => {
  const { industry, experienceLevel, targetRole, skills = [], limit = 10 } = req.body;
  const db = Database.get();

  // Build recommendation query
  let query = `
    SELECT t.*, AVG(ta.success_score) as avg_success_score, COUNT(ta.id) as usage_count
    FROM resume_templates t
    LEFT JOIN template_analytics ta ON t.id = ta.template_id
    WHERE 1=1
  `;
  const params = [];

  if (industry) {
    query += ' AND (t.industry LIKE ? OR t.industry = "All")';
    params.push(`%${industry}%`);
  }

  if (experienceLevel) {
    query += ' AND (t.category = ? OR t.industry LIKE ?)';
    params.push(experienceLevel, `%${experienceLevel}%`);
  }

  // Add skill-based filtering (simplified)
  if (skills.length > 0) {
    const skillConditions = skills.map(() => 't.description LIKE ?').join(' OR ');
    query += ` AND (${skillConditions})`;
    params.push(...skills.map(skill => `%${skill}%`));
  }

  query += `
    GROUP BY t.id
    ORDER BY avg_success_score DESC, usage_count DESC
    LIMIT ?
  `;
  params.push(parseInt(limit));

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Template recommendations error:', err);
      return res.status(500).json({ error: 'Failed to get recommendations' });
    }

    const recommendations = rows.map(row => ({
      ...row,
      structure: JSON.parse(row.structure),
      industry: row.industry ? row.industry.split(',') : [],
      recommendationScore: row.avg_success_score || 0
    }));

    res.json({
      success: true,
      data: recommendations
    });
  });
});

// Duplicate template
router.post('/:id/duplicate', authenticate, (req, res) => {
  const { id } = req.params;
  const { customizations } = req.body;
  const db = Database.get();

  // Get original template
  db.get('SELECT * FROM resume_templates WHERE id = ?', [id], (err, template) => {
    if (err || !template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Create duplicate with customizations
    const duplicatedTemplate = {
      ...template,
      name: `${template.name} (Copy)`,
      structure: customizations ? 
        JSON.stringify({ ...JSON.parse(template.structure), ...customizations }) :
        template.structure,
      created_by: req.user.userId,
      is_premium: 0 // User duplicates are not premium by default
    };

    db.run(`
      INSERT INTO resume_templates (name, description, category, industry, structure, is_premium, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      duplicatedTemplate.name,
      duplicatedTemplate.description,
      duplicatedTemplate.category,
      duplicatedTemplate.industry,
      duplicatedTemplate.structure,
      duplicatedTemplate.is_premium,
      duplicatedTemplate.created_by
    ], function(err) {
      if (err) {
        console.error('Template duplication error:', err);
        return res.status(500).json({ error: 'Failed to duplicate template' });
      }

      res.status(201).json({
        success: true,
        data: { id: this.lastID }
      });
    });
  });
});

// Enhanced template export with comprehensive document generation
router.post('/:id/export', authenticate, async (req, res) => {
  const startTime = Date.now();
  const { id } = req.params;
  const {
    formats = ['pdf'],
    resumeData,
    options = {},
    customization = {},
    includeWatermark = false,
    language = 'en'
  } = req.body;

  // Validate formats
  const supportedFormats = ['pdf', 'docx', 'word', 'googledocs', 'google-docs'];
  const requestedFormats = Array.isArray(formats) ? formats : [formats];
  const invalidFormats = requestedFormats.filter(f => !supportedFormats.includes(f.toLowerCase()));

  if (invalidFormats.length > 0) {
    return res.status(400).json({
      success: false,
      error: `Invalid format(s): ${invalidFormats.join(', ')}. Supported formats: ${supportedFormats.join(', ')}`
    });
  }

  // Validate required data
  if (!resumeData || !resumeData.personalInfo) {
    return res.status(400).json({
      success: false,
      error: 'Resume data with personal information is required'
    });
  }

  try {
    // Get template to verify it exists and get template name
    const db = Database.get();
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM resume_templates WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    // Map template ID to template name for the export service
    const templateMapping = {
      '1': 'modern',
      '2': 'creative',
      '3': 'classic',
      '4': 'ats',
      '5': 'modern',
      '6': 'modern',
      '7': 'modern'
    };

    const templateName = templateMapping[id] || 'modern';

    // Prepare export options
    const exportOptions = {
      formats: requestedFormats.map(f => f.toLowerCase().replace('google-docs', 'googledocs')),
      template: templateName,
      customization: {
        ...customization,
        colors: customization.colors || template.structure?.styling?.colors,
        fonts: customization.fonts || template.structure?.styling?.fonts
      },
      includeWatermark,
      language,
      quality: options.quality || 'high',
      userAccessToken: options.googleAccessToken // For Google Docs
    };

    // Generate comprehensive export
    const exportResult = await templateExportService.generateExport(resumeData, exportOptions);

    // Track export in analytics
    db.run(`
      INSERT INTO template_analytics (template_id, user_id, usage_type, usage_data)
      VALUES (?, ?, 'export', ?)
    `, [
      id,
      req.user.userId,
      JSON.stringify({
        formats: requestedFormats,
        templateName,
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      })
    ]);

    // Update template performance metrics
    const today = new Date().toISOString().split('T')[0];
    db.run(`
      INSERT OR REPLACE INTO template_performance_metrics
      (template_id, metric_date, export_count)
      VALUES (?, ?, COALESCE((SELECT export_count FROM template_performance_metrics WHERE template_id = ? AND metric_date = ?), 0) + 1)
    `, [id, today, id, today]);

    // Update template usage count
    db.run('UPDATE resume_templates SET usage_count = usage_count + 1 WHERE id = ?', [id]);

    res.json({
      success: true,
      data: {
        exportId: exportResult.exportId,
        results: exportResult.results,
        metadata: {
          ...exportResult.metadata,
          templateId: id,
          templateName: template.name,
          totalProcessingTime: Date.now() - startTime
        }
      }
    });

  } catch (error) {
    console.error('Template export error:', error);

    // Track failed export
    const db = Database.get();
    db.run(`
      INSERT INTO template_analytics (template_id, user_id, usage_type, usage_data)
      VALUES (?, ?, 'export_failed', ?)
    `, [
      id,
      req.user.userId,
      JSON.stringify({
        error: error.message,
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      })
    ]);

    res.status(500).json({
      success: false,
      error: 'Failed to export template: ' + error.message,
      code: 'EXPORT_FAILED'
    });
  }
});

// Download exported file
router.get('/download/:filename', (req, res) => {
  const { filename } = req.params;
  
  templateExportService.getExportFile(filename)
    .then(result => {
      if (!result.exists) {
        return res.status(404).json({ 
          success: false, 
          error: 'File not found or expired' 
        });
      }

      // Set appropriate headers based on file type
      const ext = filename.split('.').pop().toLowerCase();
      let contentType = 'application/octet-stream';
      let disposition = `attachment; filename="${filename}"`;

      switch (ext) {
        case 'pdf':
          contentType = 'application/pdf';
          break;
        case 'docx':
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'gdoc':
          contentType = 'application/json';
          disposition = `attachment; filename="${filename.replace('.gdoc', '.json')}"`;
          break;
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', disposition);
      res.sendFile(result.filepath);
    })
    .catch(error => {
      console.error('File download error:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Failed to download file' 
      });
    });
});

// Get template performance metrics
router.get('/:id/performance', authenticate, (req, res) => {
  const { id } = req.params;
  const db = Database.get();

  db.all(`
    SELECT 
      COUNT(*) as total_uses,
      AVG(success_score) as avg_success_score,
      AVG(application_count) as avg_applications,
      AVG(response_rate) as avg_response_rate,
      DATE(used_at) as use_date,
      COUNT(*) as daily_uses
    FROM template_analytics 
    WHERE template_id = ? AND used_at >= datetime('now', '-30 days')
    GROUP BY DATE(used_at)
    ORDER BY use_date DESC
  `, [id], (err, analytics) => {
    if (err) {
      console.error('Template performance error:', err);
      return res.status(500).json({ error: 'Failed to fetch performance data' });
    }

    const summary = analytics.reduce((acc, day) => {
      acc.totalUses += day.daily_uses;
      acc.totalSuccessScore += day.avg_success_score || 0;
      acc.totalApplications += day.avg_applications || 0;
      acc.totalResponseRate += day.avg_response_rate || 0;
      return acc;
    }, { totalUses: 0, totalSuccessScore: 0, totalApplications: 0, totalResponseRate: 0 });

    res.json({
      success: true,
      data: {
        dailyMetrics: analytics,
        summary: {
          totalUses: summary.totalUses,
          avgSuccessScore: analytics.length > 0 ? summary.totalSuccessScore / analytics.length : 0,
          avgApplications: analytics.length > 0 ? summary.totalApplications / analytics.length : 0,
          avgResponseRate: analytics.length > 0 ? summary.totalResponseRate / analytics.length : 0
        }
      }
    });
  });
});

// Rate template
router.post('/:id/rate', authenticate, (req, res) => {
  const { id } = req.params;
  const { rating, review } = req.body;
  const db = Database.get();

  if (!rating || rating < 1 || rating > 5) {
    return res.status(400).json({ error: 'Rating must be between 1 and 5' });
  }

  // Insert or update rating
  db.run(`
    INSERT OR REPLACE INTO template_ratings (template_id, user_id, rating, review)
    VALUES (?, ?, ?, ?)
  `, [id, req.user.userId, rating, review], (err) => {
    if (err) {
      console.error('Template rating error:', err);
      return res.status(500).json({ error: 'Failed to save rating' });
    }

    // Update template average rating
    db.get(`
      SELECT AVG(rating) as avg_rating, COUNT(*) as rating_count
      FROM template_ratings 
      WHERE template_id = ?
    `, [id], (err, stats) => {
      if (!err && stats) {
        db.run(`
          UPDATE resume_templates 
          SET rating = ?, rating_count = ?
          WHERE id = ?
        `, [stats.avg_rating, stats.rating_count, id]);
      }
    });

    res.json({ success: true });
  });
});

// Get template statistics
router.get('/statistics', (req, res) => {
  const db = Database.get();

  // Get category statistics
  db.all(`
    SELECT 
      category,
      COUNT(*) as template_count,
      AVG(rating) as avg_rating,
      SUM(usage_count) as total_usage
    FROM resume_templates
    GROUP BY category
    ORDER BY template_count DESC
  `, [], (err, categoryStats) => {
    if (err) {
      console.error('Category statistics error:', err);
      return res.status(500).json({ error: 'Failed to fetch statistics' });
    }

    // Get overall statistics
    db.get(`
      SELECT 
        COUNT(*) as total_templates,
        COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_templates,
        AVG(rating) as overall_avg_rating,
        SUM(usage_count) as total_usage,
        AVG(ats_score) as avg_ats_score
      FROM resume_templates
    `, [], (err, overallStats) => {
      if (err) {
        console.error('Overall statistics error:', err);
        return res.status(500).json({ error: 'Failed to fetch statistics' });
      }

      res.json({
        success: true,
        data: {
          overall: overallStats,
          categories: categoryStats
        }
      });
    });
  });
});

// Get available export templates
router.get('/export/templates', (req, res) => {
  try {
    const availableTemplates = templateExportService.getAvailableTemplates();

    res.json({
      success: true,
      data: availableTemplates
    });
  } catch (error) {
    console.error('Get export templates error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available templates'
    });
  }
});

// Get export statistics
router.get('/export/statistics', authenticate, async (req, res) => {
  try {
    const stats = await templateExportService.getExportStatistics();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get export statistics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get export statistics'
    });
  }
});

// Validate resume data for export
router.post('/export/validate', authenticate, (req, res) => {
  const { resumeData } = req.body;

  if (!resumeData) {
    return res.status(400).json({
      success: false,
      error: 'Resume data is required'
    });
  }

  try {
    const validation = templateExportService.validateTemplateData({ user: resumeData });

    res.json({
      success: true,
      data: validation
    });
  } catch (error) {
    console.error('Resume data validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate resume data'
    });
  }
});

// Generate Google OAuth URL for Google Docs export
router.get('/export/google/auth-url', authenticate, (req, res) => {
  try {
    const authUrl = templateExportService.generateGoogleOAuthUrl(req.user.userId);

    res.json({
      success: true,
      data: {
        authUrl,
        state: req.user.userId
      }
    });
  } catch (error) {
    console.error('Google OAuth URL generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate Google OAuth URL: ' + error.message
    });
  }
});

// Handle Google OAuth callback
router.post('/export/google/callback', authenticate, async (req, res) => {
  const { code } = req.body;

  if (!code) {
    return res.status(400).json({
      success: false,
      error: 'Authorization code is required'
    });
  }

  try {
    const tokenInfo = await templateExportService.handleGoogleOAuthCallback(code);

    // Store token info in user session or database as needed
    // For now, just return it to the client
    res.json({
      success: true,
      data: tokenInfo
    });
  } catch (error) {
    console.error('Google OAuth callback error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to handle Google OAuth callback: ' + error.message
    });
  }
});

// Preview template with sample data
router.post('/:id/preview', authenticate, async (req, res) => {
  const { id } = req.params;
  const { resumeData, format = 'pdf', customization = {} } = req.body;

  try {
    // Get template
    const db = Database.get();
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM resume_templates WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    // Use sample data if no resume data provided
    const sampleData = resumeData || {
      personalInfo: {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'San Francisco, CA',
        title: 'Software Engineer'
      },
      summary: 'Experienced software engineer with expertise in full-stack development.',
      experience: [{
        position: 'Senior Software Engineer',
        company: 'Tech Company',
        duration: '2020 - Present',
        description: 'Lead development of scalable web applications.'
      }],
      education: [{
        degree: 'Bachelor of Science in Computer Science',
        school: 'University of Technology',
        year: '2018'
      }],
      skills: {
        'Programming': ['JavaScript', 'Python', 'Java'],
        'Frameworks': ['React', 'Node.js', 'Django']
      }
    };

    // Map template ID to template name
    const templateMapping = {
      '1': 'modern', '2': 'creative', '3': 'classic', '4': 'ats',
      '5': 'modern', '6': 'modern', '7': 'modern'
    };
    const templateName = templateMapping[id] || 'modern';

    // Generate preview (PDF only for now)
    const exportResult = await templateExportService.generateExport(sampleData, {
      formats: ['pdf'],
      template: templateName,
      customization,
      includeWatermark: true // Always watermark previews
    });

    res.json({
      success: true,
      data: {
        previewUrl: exportResult.results.pdf?.url,
        templateName: template.name,
        sampleDataUsed: !resumeData
      }
    });

  } catch (error) {
    console.error('Template preview error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate template preview: ' + error.message
    });
  }
});

// Validate template structure
router.post('/validate', authenticate, (req, res) => {
  const templateData = req.body;
  const errors = [];

  // Basic validation
  if (!templateData.name) errors.push('Template name is required');
  if (!templateData.category) errors.push('Template category is required');
  if (!templateData.structure) errors.push('Template structure is required');

  // Structure validation
  if (templateData.structure) {
    try {
      const structure = typeof templateData.structure === 'string'
        ? JSON.parse(templateData.structure)
        : templateData.structure;

      if (!structure.sections || !Array.isArray(structure.sections)) {
        errors.push('Template must have sections array');
      }

      if (structure.sections && structure.sections.length === 0) {
        errors.push('Template must have at least one section');
      }
    } catch (err) {
      errors.push('Invalid template structure JSON');
    }
  }

  res.json({
    success: true,
    data: {
      isValid: errors.length === 0,
      errors
    }
  });
});

module.exports = router;