const express = require('express');
const router = express.Router();

/**
 * Enhanced Security and Dependency Management API Routes
 */

// Middleware for authentication (assuming it exists)
const authenticateToken = (req, res, next) => {
  // Simple mock authentication for demo
  req.user = { userId: 'demo-user' };
  next();
};

module.exports = (controller) => {
  // Workflow Management
  router.post('/workflows/execute', authenticateToken, (req, res) => controller.executeWorkflow(req, res));
  
  // Dependency Management
  router.get('/dependencies/graph', authenticateToken, (req, res) => controller.getDependencyGraph(req, res));
  router.get('/dependencies/status', authenticateToken, (req, res) => controller.getDependencyStatus(req, res));
  router.post('/dependencies/start', authenticateToken, (req, res) => controller.startDependencyProcessing(req, res));
  router.post('/dependencies/pause', authenticateToken, (req, res) => controller.pauseDependencyProcessing(req, res));
  router.post('/dependencies/stop', authenticateToken, (req, res) => controller.stopDependencyProcessing(req, res));
  router.post('/dependencies/reset', authenticateToken, (req, res) => controller.resetDependencyService(req, res));
  
  // Monitoring
  router.get('/monitoring/dashboard', authenticateToken, (req, res) => controller.getMonitoringDashboard(req, res));
  router.get('/monitoring/trends', authenticateToken, (req, res) => controller.getPerformanceTrends(req, res));
  router.post('/monitoring/alerts/:alertId/acknowledge', authenticateToken, (req, res) => controller.acknowledgeAlert(req, res));
  
  // Security
  router.get('/security/status', authenticateToken, (req, res) => controller.getSecurityStatus(req, res));
  router.get('/security/events', authenticateToken, (req, res) => controller.getSecurityEvents(req, res));
  router.post('/security/scan', authenticateToken, (req, res) => controller.scanImage(req, res));
  router.get('/security/scans/:scanId', authenticateToken, (req, res) => controller.getScanResults(req, res));
  
  // Sandbox Management
  router.get('/sandbox/status', authenticateToken, (req, res) => controller.getSandboxStatus(req, res));
  router.post('/sandbox/create', authenticateToken, (req, res) => controller.createSecureSandbox(req, res));
  router.get('/sandbox/:sandboxId', authenticateToken, (req, res) => controller.getSandboxInfo(req, res));
  router.delete('/sandbox/:sandboxId', authenticateToken, (req, res) => controller.terminateSandbox(req, res));

  return router;
};