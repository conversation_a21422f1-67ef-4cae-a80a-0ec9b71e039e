const express = require('express');
const router = express.Router();
const loopService = require('../services/loopService');
const jobDiscovery = require('../services/jobDiscovery');
const { body, validationResult } = require('express-validator');

// Validation middleware
const validateLoop = [
  body('name').trim().isLength({ min: 1, max: 255 }).withMessage('Loop name is required and must be less than 255 characters'),
  body('configuration.jobTitles').isArray({ min: 1 }).withMessage('At least one job title is required'),
  body('configuration.locations').isArray({ min: 1 }).withMessage('At least one location is required'),
  body('configuration.maxApplicationsPerDay').optional().isInt({ min: 1, max: 50 }).withMessage('Max applications per day must be between 1 and 50'),
];

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// GET /api/loops - List user's loops
router.get('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loops = await loopService.getUserLoops(userId);
    
    res.json({
      success: true,
      data: loops
    });
  } catch (error) {
    console.error('Error fetching loops:', error);
    res.status(500).json({
      error: 'Failed to fetch loops',
      message: error.message
    });
  }
});

// POST /api/loops - Create new loop
router.post('/', validateLoop, handleValidationErrors, async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopData = req.body;
    
    const newLoop = await loopService.createLoop(userId, loopData);
    
    res.status(201).json({
      success: true,
      data: newLoop,
      message: 'Loop created successfully'
    });
  } catch (error) {
    console.error('Error creating loop:', error);
    res.status(500).json({
      error: 'Failed to create loop',
      message: error.message
    });
  }
});

// GET /api/loops/discovered-jobs - Recently discovered jobs (must come before /:id routes)
router.get('/discovered-jobs', async (req, res) => {
  try {
    const userId = req.user.userId;
    const limit = parseInt(req.query.limit) || 50;
    
    const discoveredJobs = await loopService.getDiscoveredJobs(userId, limit);
    
    res.json({
      success: true,
      data: discoveredJobs
    });
  } catch (error) {
    console.error('Error fetching discovered jobs:', error);
    res.status(500).json({
      error: 'Failed to fetch discovered jobs',
      message: error.message
    });
  }
});

// GET /api/loops/:id - Get specific loop details
router.get('/:id', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    
    const loops = await loopService.getUserLoops(userId);
    const loop = loops.find(l => l.id == loopId);
    
    if (!loop) {
      return res.status(404).json({
        error: 'Loop not found'
      });
    }
    
    res.json({
      success: true,
      data: loop
    });
  } catch (error) {
    console.error('Error fetching loop:', error);
    res.status(500).json({
      error: 'Failed to fetch loop',
      message: error.message
    });
  }
});

// PUT /api/loops/:id - Update loop configuration
router.put('/:id', validateLoop, handleValidationErrors, async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    const updateData = req.body;
    
    const result = await loopService.updateLoop(userId, loopId, updateData);
    
    res.json({
      success: true,
      data: result,
      message: 'Loop updated successfully'
    });
  } catch (error) {
    console.error('Error updating loop:', error);
    res.status(500).json({
      error: 'Failed to update loop',
      message: error.message
    });
  }
});

// DELETE /api/loops/:id - Delete loop
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    
    const result = await loopService.deleteLoop(userId, loopId);
    
    res.json({
      success: true,
      data: result,
      message: 'Loop deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting loop:', error);
    res.status(500).json({
      error: 'Failed to delete loop',
      message: error.message
    });
  }
});

// POST /api/loops/:id/pause - Pause/resume loop
router.post('/:id/pause', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    const { action } = req.body; // 'pause' or 'resume'
    
    let result;
    if (action === 'pause') {
      result = await loopService.pauseLoop(loopId);
    } else if (action === 'resume') {
      result = await loopService.activateLoop(loopId);
    } else {
      return res.status(400).json({
        error: 'Invalid action. Use "pause" or "resume"'
      });
    }
    
    res.json({
      success: true,
      data: result,
      message: `Loop ${action}d successfully`
    });
  } catch (error) {
    console.error(`Error ${req.body.action}ing loop:`, error);
    res.status(500).json({
      error: `Failed to ${req.body.action} loop`,
      message: error.message
    });
  }
});

// GET /api/loops/:id/performance - Loop analytics
router.get('/:id/performance', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    
    const performance = await loopService.getLoopPerformance(userId, loopId);
    
    res.json({
      success: true,
      data: performance
    });
  } catch (error) {
    console.error('Error fetching loop performance:', error);
    res.status(500).json({
      error: 'Failed to fetch loop performance',
      message: error.message
    });
  }
});

// POST /api/loops/:id/test-discovery - Test job discovery for a loop
router.post('/:id/test-discovery', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    
    // Get loop configuration
    const loops = await loopService.getUserLoops(userId);
    const loop = loops.find(l => l.id == loopId);
    
    if (!loop) {
      return res.status(404).json({
        error: 'Loop not found'
      });
    }
    
    // Run test discovery
    const mockConfig = {
      loop_id: loopId,
      job_titles: JSON.stringify(loop.jobTitles),
      locations: JSON.stringify(loop.locations),
      industries: JSON.stringify(loop.industries || []),
      excluded_companies: JSON.stringify(loop.configuration.excludedCompanies || []),
      keywords: JSON.stringify(loop.configuration.keywords || []),
      salary_min: loop.configuration.salaryMin,
      salary_max: loop.configuration.salaryMax,
      remote_options: loop.configuration.remoteOptions || 'any'
    };
    
    const discoveredJobs = await jobDiscovery.discoverJobs(mockConfig);
    
    res.json({
      success: true,
      data: {
        jobsFound: discoveredJobs.length,
        jobs: discoveredJobs.slice(0, 10), // Return first 10 for preview
        avgRelevanceScore: discoveredJobs.reduce((sum, job) => sum + job.relevanceScore, 0) / discoveredJobs.length
      },
      message: 'Test discovery completed successfully'
    });
  } catch (error) {
    console.error('Error testing job discovery:', error);
    res.status(500).json({
      error: 'Failed to test job discovery',
      message: error.message
    });
  }
});

// GET /api/loops/:id/discovery-stats - Get discovery statistics for a loop
router.get('/:id/discovery-stats', async (req, res) => {
  try {
    const userId = req.user.userId;
    const loopId = req.params.id;
    
    // Verify user owns the loop
    const loops = await loopService.getUserLoops(userId);
    const loop = loops.find(l => l.id == loopId);
    
    if (!loop) {
      return res.status(404).json({
        error: 'Loop not found'
      });
    }
    
    const stats = await jobDiscovery.getDiscoveryStats(loopId);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching discovery stats:', error);
    res.status(500).json({
      error: 'Failed to fetch discovery stats',
      message: error.message
    });
  }
});

// POST /api/loops/jobs/:jobId/apply - Mark a discovered job as applied
router.post('/jobs/:jobId/apply', async (req, res) => {
  try {
    const jobId = req.params.jobId;
    
    const result = await jobDiscovery.updateJobStatus(jobId, 'applied');
    
    res.json({
      success: true,
      data: result,
      message: 'Job marked as applied'
    });
  } catch (error) {
    console.error('Error updating job status:', error);
    res.status(500).json({
      error: 'Failed to update job status',
      message: error.message
    });
  }
});

module.exports = router;