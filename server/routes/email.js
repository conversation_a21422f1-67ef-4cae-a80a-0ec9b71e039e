const express = require('express');
const { authenticate } = require('../middleware/auth');
const EmailCampaignController = require('../controllers/emailCampaignController');

const router = express.Router();
const emailController = new EmailCampaignController();

// Campaign management routes
router.post('/campaigns', authenticate, emailController.createCampaign.bind(emailController));
router.get('/campaigns', authenticate, emailController.getCampaigns.bind(emailController));
router.put('/campaigns/:id', authenticate, emailController.updateCampaign.bind(emailController));
router.delete('/campaigns/:id', authenticate, emailController.deleteCampaign.bind(emailController));
router.post('/campaigns/:id/send', authenticate, emailController.sendCampaign.bind(emailController));

// Template management routes
router.post('/templates', authenticate, emailController.createTemplate.bind(emailController));
router.get('/templates', authenticate, emailController.getTemplates.bind(emailController));

// Individual email sending
router.post('/send', authenticate, emailController.sendIndividualEmail.bind(emailController));

// Analytics routes
router.get('/analytics', authenticate, emailController.getEmailAnalytics.bind(emailController));

// Recruiter discovery and management
router.post('/discover-recruiters', authenticate, emailController.discoverRecruiters.bind(emailController));
router.get('/contacts', authenticate, emailController.getRecruiterContacts.bind(emailController));
router.put('/contacts/:id', authenticate, emailController.updateRecruiterContact.bind(emailController));

// Email tracking routes (no authentication required for tracking pixels/links)
router.get('/track/open/:trackingId', emailController.trackEmailOpen.bind(emailController));
router.get('/track/click/:trackingId', emailController.trackEmailClick.bind(emailController));

module.exports = router;