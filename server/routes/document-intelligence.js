const express = require('express');
const multer = require('multer');
const { sanitizeInput, validateFileUpload } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');
const AIService = require('../aiService');
const Database = require('../database');
const pdfParse = require('pdf-parse');
const fs = require('fs').promises;

const router = express.Router();
const aiService = new AIService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, '/tmp/uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, DOC, DOCX, and TXT files are allowed.'));
    }
  }
});

// Apply middleware
router.use(sanitizeInput);
router.use(authenticate);

// Parse resume from uploaded file
router.post('/parse-resume', 
  upload.single('resume'),
  validateFileUpload(['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'], 10 * 1024 * 1024),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }
      
      let extractedText = '';
      const filePath = req.file.path;
    
    // Extract text based on file type
    if (req.file.mimetype === 'application/pdf') {
      const dataBuffer = await fs.readFile(filePath);
      const pdfData = await pdfParse(dataBuffer);
      extractedText = pdfData.text;
    } else if (req.file.mimetype === 'text/plain') {
      extractedText = await fs.readFile(filePath, 'utf8');
    } else {
      // For DOC/DOCX files, you would need additional libraries
      throw new Error('DOC/DOCX parsing not implemented yet');
    }
    
    // Use AI to parse the resume content
    const parsePrompt = `
Parse the following resume text and extract structured information:

${extractedText}

Extract and format as JSON:
{
  "personalInfo": {
    "name": "...",
    "email": "...",
    "phone": "...",
    "location": "...",
    "linkedin": "...",
    "website": "..."
  },
  "summary": "...",
  "experience": [
    {
      "company": "...",
      "title": "...",
      "startDate": "...",
      "endDate": "...",
      "description": "...",
      "achievements": ["..."]
    }
  ],
  "education": [
    {
      "institution": "...",
      "degree": "...",
      "field": "...",
      "graduationDate": "...",
      "gpa": "..."
    }
  ],
  "skills": ["..."],
  "certifications": ["..."],
  "languages": ["..."],
  "confidenceScore": 0.95
}
    `;
    
    const parsedData = await aiService.callWithFallback(parsePrompt, {
      temperature: 0.3,
      maxTokens: 2000
    });
    
    const resumeData = JSON.parse(parsedData);
    
    // Store parsed document
    const db = Database.get();
    db.run(`
      INSERT INTO parsed_documents (user_id, document_type, original_filename, parsed_data, confidence_score)
      VALUES (?, ?, ?, ?, ?)
    `, [
      req.user.userId,
      'resume',
      req.file.originalname,
      JSON.stringify(resumeData),
      resumeData.confidenceScore || 0.8
    ]);
    
    // Clean up uploaded file
    await fs.unlink(filePath);
    
    res.json({
      success: true,
      data: resumeData
    });
    
  } catch (error) {
    console.error('Resume parsing error:', error);
    // Clean up file on error
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('File cleanup error:', unlinkError);
      }
    }
    res.status(500).json({ error: 'Failed to parse resume' });
  }
});

// Generate cover letter
router.post('/generate-cover-letter', async (req, res) => {
  try {
    const { resumeData, jobDescription, companyName, customization } = req.body;
    
    if (!resumeData || !jobDescription || !companyName) {
      return res.status(400).json({ error: 'Resume data, job description, and company name are required' });
    }
    
    const coverLetter = await aiService.generateCoverLetter(resumeData, jobDescription, companyName, {
      customization: customization || {}
    });
    
    res.json({
      success: true,
      data: coverLetter
    });
    
  } catch (error) {
    console.error('Cover letter generation error:', error);
    res.status(500).json({ error: 'Failed to generate cover letter' });
  }
});

// Create/manage portfolio
router.post('/portfolio', async (req, res) => {
  try {
    const { projects, personalInfo, theme = 'modern' } = req.body;
    
    if (!projects || !Array.isArray(projects)) {
      return res.status(400).json({ error: 'Projects array is required' });
    }
    
    const portfolioPrompt = `
Create a professional portfolio structure based on the following projects and personal information:

Personal Info: ${JSON.stringify(personalInfo)}
Projects: ${JSON.stringify(projects)}
Theme: ${theme}

Generate a comprehensive portfolio structure including:
1. About section
2. Project showcases with descriptions
3. Skills highlight
4. Contact information
5. Navigation structure

Format as JSON:
{
  "structure": {
    "sections": ["about", "projects", "skills", "contact"],
    "navigation": {...},
    "theme": "..."
  },
  "content": {
    "about": "...",
    "projects": [...],
    "skills": [...],
    "contact": {...}
  },
  "metadata": {
    "lastUpdated": "...",
    "version": "1.0"
  }
}
    `;
    
    const portfolioData = await aiService.callWithFallback(portfolioPrompt, {
      temperature: 0.7,
      maxTokens: 2500
    });
    
    const portfolio = JSON.parse(portfolioData);
    
    // Store portfolio data
    const db = Database.get();
    db.run(`
      INSERT INTO parsed_documents (user_id, document_type, original_filename, parsed_data, confidence_score)
      VALUES (?, ?, ?, ?, ?)
    `, [
      req.user.userId,
      'portfolio',
      'generated_portfolio.json',
      JSON.stringify(portfolio),
      0.95
    ]);
    
    res.json({
      success: true,
      data: portfolio
    });
    
  } catch (error) {
    console.error('Portfolio creation error:', error);
    res.status(500).json({ error: 'Failed to create portfolio' });
  }
});

// Manage references
router.post('/references', async (req, res) => {
  try {
    const { action, referenceData } = req.body;
    
    if (!action || !referenceData) {
      return res.status(400).json({ error: 'Action and reference data are required' });
    }
    
    let result;
    
    switch (action) {
      case 'create':
        result = await createReferenceManager(referenceData);
        break;
      case 'request':
        result = await generateReferenceRequest(referenceData);
        break;
      case 'follow_up':
        result = await generateFollowUp(referenceData);
        break;
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
    
    res.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    console.error('Reference management error:', error);
    res.status(500).json({ error: 'Failed to manage references' });
  }
});

// Document version control
router.get('/versions/:documentId', (req, res) => {
  const { documentId } = req.params;
  const db = Database.get();
  
  db.all(`
    SELECT id, document_type, original_filename, confidence_score, created_at
    FROM parsed_documents 
    WHERE user_id = ? AND (id = ? OR original_filename LIKE ?)
    ORDER BY created_at DESC
  `, [req.user.userId, documentId, `%${documentId}%`], (err, versions) => {
    if (err) {
      console.error('Version fetch error:', err);
      return res.status(500).json({ error: 'Failed to fetch document versions' });
    }
    
    res.json({
      success: true,
      data: versions
    });
  });
});

// Get parsed documents
router.get('/documents', (req, res) => {
  const { type, limit = 50 } = req.query;
  const db = Database.get();
  
  let query = 'SELECT * FROM parsed_documents WHERE user_id = ?';
  const params = [req.user.userId];
  
  if (type) {
    query += ' AND document_type = ?';
    params.push(type);
  }
  
  query += ' ORDER BY created_at DESC LIMIT ?';
  params.push(parseInt(limit));
  
  db.all(query, params, (err, documents) => {
    if (err) {
      console.error('Documents fetch error:', err);
      return res.status(500).json({ error: 'Failed to fetch documents' });
    }
    
    const documentsWithParsedData = documents.map(doc => ({
      ...doc,
      parsed_data: JSON.parse(doc.parsed_data)
    }));
    
    res.json({
      success: true,
      data: documentsWithParsedData
    });
  });
});

// Helper functions
async function createReferenceManager(referenceData) {
  const managerPrompt = `
Create a reference management system structure for the following references:

References: ${JSON.stringify(referenceData)}

Generate a management structure including:
1. Reference categorization
2. Contact tracking
3. Relationship status
4. Request history
5. Follow-up schedule

Format as JSON:
{
  "references": [...],
  "categories": [...],
  "tracking": {...},
  "schedule": [...],
  "templates": {...}
}
  `;
  
  const result = await aiService.callWithFallback(managerPrompt, {
    temperature: 0.5,
    maxTokens: 1500
  });
  
  return JSON.parse(result);
}

async function generateReferenceRequest(referenceData) {
  const requestPrompt = `
Generate a professional reference request email for:

Reference: ${JSON.stringify(referenceData)}

Include:
1. Subject line
2. Personalized greeting
3. Request explanation
4. Specific details needed
5. Timeline
6. Thank you

Format as JSON:
{
  "subject": "...",
  "body": "...",
  "followUpDate": "...",
  "tips": [...]
}
  `;
  
  const result = await aiService.callWithFallback(requestPrompt, {
    temperature: 0.7,
    maxTokens: 1000
  });
  
  return JSON.parse(result);
}

async function generateFollowUp(referenceData) {
  const followUpPrompt = `
Generate a follow-up message for a reference request:

Original Request: ${JSON.stringify(referenceData)}

Create a polite follow-up that:
1. Acknowledges the original request
2. Provides gentle reminder
3. Offers additional information
4. Shows appreciation

Format as JSON:
{
  "subject": "...",
  "body": "...",
  "tone": "...",
  "timing": "..."
}
  `;
  
  const result = await aiService.callWithFallback(followUpPrompt, {
    temperature: 0.7,
    maxTokens: 800
  });
  
  return JSON.parse(result);
}

module.exports = router;