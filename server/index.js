require('dotenv').config();
const express = require('express');
const path = require('path');
const fs = require('fs');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

// Import enhanced infrastructure
const { setupDatabase } = require('./database/setup');
const authRoutes = require('./routes/auth');
const resumeRoutes = require('./routes/resumes');
const aiRoutes = require('./routes/ai');
const uploadRoutes = require('./routes/uploads');
const analyticsRoutes = require('./routes/analytics');
const templateRoutes = require('./routes/templates');
const paymentRoutes = require('./routes/payments');
const emailRoutes = require('./routes/email');
const { authenticate } = require('./middleware/auth');
const { error<PERSON><PERSON><PERSON>, setupGlobalErrorHandlers } = require('./middleware/errorHandler');
const { generateRequestId } = require('./middleware/requestId');
const { sanitizeInput } = require('./middleware/validation');
const { requestIdMiddleware } = require('./middleware/requestId');

// Import legacy components for backward compatibility
const { AuthController, authenticateToken } = require('./auth');
const ResumeController = require('./resumeController');
const AIController = require('./aiController');
const JobController = require('./jobController');
const JobApplicationController = require('./jobApplicationController');
const AnalyticsController = require('./analyticsController');
const AutomationController = require('./automationController');
const EnhancedJobApplicationController = require('./enhancedJobApplicationController');
const TeamController = require('./teamController');
const CommentController = require('./commentController');
const SecurityMiddleware = require('./securityMiddleware');
const HealthController = require('./healthController');
const cacheService = require('./cacheService');
const notificationService = require('./notificationService');
require('./database'); // Initialize database

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize legacy security middleware
const security = new SecurityMiddleware();

// Initialize controllers
const authController = new AuthController();
const resumeController = new ResumeController();
const aiController = new AIController();
const jobController = new JobController();
const jobApplicationController = new JobApplicationController();
const analyticsController = new AnalyticsController();
const automationController = new AutomationController();
const enhancedJobApplicationController = new EnhancedJobApplicationController(notificationService);
const teamController = new TeamController();
const commentController = new CommentController();

// Legacy resume data for backward compatibility
const resumePath = path.join(__dirname, 'resume.json');

function loadResume() {
  try {
    return JSON.parse(fs.readFileSync(resumePath, 'utf8'));
  } catch {
    return {
      name: 'John Doe',
      title: 'Software Engineer',
      summary: 'Experienced developer with a focus on full stack applications.',
      skills: ['JavaScript', 'Node.js', 'React'],
      experience: [
        {
          company: 'ABC Corp',
          role: 'Frontend Developer',
          start: '2020',
          end: '2022',
        },
      ],
    };
  }
}

let resumeData = loadResume();

// Enhanced middleware stack
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

app.use(compression());
app.use(morgan('combined'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Legacy middleware for backward compatibility
app.set('trust proxy', 1); // Trust first proxy for rate limiting

// Request ID generation for correlation (should be early in middleware stack)
app.use(generateRequestId);

app.use(requestIdMiddleware); // Add request ID for error correlation
app.use(security.securityHeaders);
app.use(security.requestLogger);
app.use(security.createGeneralRateLimit());

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request sanitization
app.use(sanitizeInput);
app.use(security.validateInput);
app.use(security.requestSizeLimit(10)); // 10MB limit

// Health check routes (no authentication required)
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Additional health check endpoints for compatibility
app.get('/health/status', (req, res) => {
  res.json({ status: 'ok', service: 'cvleap-collaboration' });
});

app.get('/health/ready', (req, res) => {
  res.json({ ready: true, timestamp: new Date().toISOString() });
});

app.get('/health/live', (req, res) => {
  res.json({ alive: true, timestamp: new Date().toISOString() });
});

app.get('/metrics', (req, res) => {
  try {
    const collaborationService = require('./collaborationService');
    res.json({
      websocket: notificationService.getStats(),
      collaboration: collaborationService.getStats(),
      cache: cacheService.getStats(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.json({ error: 'Metrics unavailable', timestamp: new Date().toISOString() });
  }
});

// Modern API Routes
app.use('/api/auth', authRoutes);
app.use('/api/resumes', authenticate, resumeRoutes);
app.use('/api/ai', authenticate, aiRoutes);
app.use('/api/uploads', authenticate, uploadRoutes);
app.use('/api/analytics', authenticate, analyticsRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/payments', authenticate, paymentRoutes);
app.use('/api/email', emailRoutes);

// Advanced Features Routes
const careerCoachRoutes = require('./routes/career-coach');
const documentIntelligenceRoutes = require('./routes/document-intelligence');
const jobMatchingRoutes = require('./routes/job-matching');
const gamificationRoutes = require('./routes/gamification');
const contactRoutes = require('./routes/contact');
const loopRoutes = require('./routes/loops');
const automationAnalyticsRoutes = require('./routes/automation-analytics');

app.use('/api/career-coach', careerCoachRoutes);
app.use('/api/documents', documentIntelligenceRoutes);
app.use('/api/job-matching', jobMatchingRoutes);
app.use('/api/gamification', gamificationRoutes);
app.use('/api/contact', contactRoutes);
app.use('/api/loops', authenticate, loopRoutes);
app.use('/api/automation', authenticate, automationAnalyticsRoutes);

// Notifications endpoint
app.get('/api/notifications/stats', authenticateToken, (req, res) => {
  try {
    const stats = notificationService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get notification stats' });
  }
});

// Auth routes (with special rate limiting)
app.post('/api/auth/register', security.createAuthRateLimit(), authController.register.bind(authController));
app.post('/api/auth/login', security.createAuthRateLimit(), authController.login.bind(authController));

// Resume routes (protected)
app.get('/api/resumes', authenticateToken, resumeController.getResumes.bind(resumeController));
app.get('/api/resumes/:id', authenticateToken, resumeController.getResume.bind(resumeController));
app.post('/api/resumes', authenticateToken, resumeController.createResume.bind(resumeController));
app.put('/api/resumes/:id', authenticateToken, resumeController.updateResume.bind(resumeController));
app.delete('/api/resumes/:id', authenticateToken, resumeController.deleteResume.bind(resumeController));

// AI enhancement routes (protected with AI rate limiting)
app.post('/api/ai/enhance-resume', authenticateToken, security.createAIRateLimit(), aiController.enhanceResume.bind(aiController));
app.post('/api/ai/generate-cover-letter', authenticateToken, security.createAIRateLimit(), aiController.generateCoverLetter.bind(aiController));
app.post('/api/ai/analyze-ats', authenticateToken, security.createAIRateLimit(), aiController.analyzeATS.bind(aiController));
app.post('/api/ai/suggest-skills', authenticateToken, security.createAIRateLimit(), aiController.suggestSkills.bind(aiController));
app.get('/api/ai/models', authenticateToken, aiController.getAvailableModels.bind(aiController));
app.get('/api/ai/status', authenticateToken, aiController.getModelStatus.bind(aiController));

// Advanced AI features (protected)
app.post('/api/ai/simulate-interview', authenticateToken, aiController.simulateInterview.bind(aiController));
app.post('/api/ai/predict-job-success', authenticateToken, aiController.predictJobSuccess.bind(aiController));
app.post('/api/ai/generate-industry-prompts', authenticateToken, aiController.generateIndustryPrompts.bind(aiController));
app.post('/api/ai/generate-resume-variations', authenticateToken, aiController.generateResumeVariations.bind(aiController));

// Advanced Resume Analysis Engine (protected with AI rate limiting)
app.post('/api/ai/advanced-scoring', authenticateToken, security.createAIRateLimit(), aiController.advancedResumeScoring.bind(aiController));
app.post('/api/ai/detect-weaknesses', authenticateToken, security.createAIRateLimit(), aiController.detectWeaknesses.bind(aiController));
app.post('/api/ai/amplify-strengths', authenticateToken, security.createAIRateLimit(), aiController.amplifyStrengths.bind(aiController));

// Real-time AI Assistant (protected with AI rate limiting)
app.post('/api/ai/chat', authenticateToken, security.createAIRateLimit(), aiController.chatAssistant.bind(aiController));
app.post('/api/ai/real-time-suggestions', authenticateToken, security.createAIRateLimit(), aiController.getRealTimeSuggestions.bind(aiController));

// Advanced Document Processing (protected with AI rate limiting)
app.post('/api/ai/parse-resume', authenticateToken, security.createAIRateLimit(), aiController.parseResumeContent.bind(aiController));
app.post('/api/ai/extract-data', authenticateToken, security.createAIRateLimit(), aiController.extractIntelligentData.bind(aiController));
app.post('/api/ai/detect-duplicates', authenticateToken, security.createAIRateLimit(), aiController.detectDuplicates.bind(aiController));
app.post('/api/ai/compare-versions', authenticateToken, security.createAIRateLimit(), aiController.compareVersions.bind(aiController));

// AI Template Selection and Design Optimization (protected with AI rate limiting)
app.post('/api/ai/recommend-template', authenticateToken, security.createAIRateLimit(), aiController.recommendTemplate.bind(aiController));
app.post('/api/ai/optimize-layout', authenticateToken, security.createAIRateLimit(), aiController.optimizeLayout.bind(aiController));

// Multi-language and Cultural Adaptation (protected with AI rate limiting)
app.post('/api/ai/translate-resume', authenticateToken, security.createAIRateLimit(), aiController.translateResume.bind(aiController));
app.post('/api/ai/adapt-region', authenticateToken, security.createAIRateLimit(), aiController.adaptForRegion.bind(aiController));

// Phase 1 Enhancement Features (protected with AI rate limiting)
app.post('/api/ai/quantify-achievements', authenticateToken, security.createAIRateLimit(), aiController.quantifyAchievements.bind(aiController));
app.post('/api/ai/personalize-content', authenticateToken, security.createAIRateLimit(), aiController.personalizeForCompanyCulture.bind(aiController));
app.post('/api/ai/recommend-sections', authenticateToken, security.createAIRateLimit(), aiController.recommendResumeSections.bind(aiController));
app.post('/api/ai/analyze-keyword-density', authenticateToken, security.createAIRateLimit(), aiController.analyzeKeywordDensity.bind(aiController));

// Analytics routes (protected)
app.get('/api/analytics/dashboard', authenticateToken, cacheService.middleware(300000), analyticsController.getDashboard.bind(analyticsController));
app.post('/api/analytics/track-event', authenticateToken, analyticsController.trackEvent.bind(analyticsController));
app.put('/api/analytics/resume-metrics/:resumeId', authenticateToken, analyticsController.updateResumeMetrics.bind(analyticsController));
app.get('/api/analytics/success-patterns', authenticateToken, cacheService.middleware(300000), analyticsController.getSuccessPatterns.bind(analyticsController));
app.get('/api/analytics/recommendations', authenticateToken, analyticsController.getOptimizationRecommendations.bind(analyticsController));
app.get('/api/analytics/metrics/:metricType', authenticateToken, analyticsController.getDetailedMetrics.bind(analyticsController));
app.post('/api/analytics/generate-report', authenticateToken, analyticsController.generateReport.bind(analyticsController));
app.post('/api/analytics/predict', authenticateToken, analyticsController.getPredictiveAnalytics.bind(analyticsController));
app.get('/api/analytics/application-metrics', authenticateToken, cacheService.middleware(120000), analyticsController.getApplicationMetrics.bind(analyticsController));
app.get('/api/analytics/job-market', authenticateToken, cacheService.middleware(600000), analyticsController.getJobMarketInsights.bind(analyticsController));
app.get('/api/analytics/resume-performance', authenticateToken, cacheService.middleware(180000), analyticsController.getResumePerformance.bind(analyticsController));
app.get('/api/analytics/weekly-trends', authenticateToken, cacheService.middleware(600000), analyticsController.getWeeklyTrends.bind(analyticsController));

// AI-Powered Analytics (protected with rate limiting)
app.post('/api/analytics/ai-insights', authenticateToken, security.createAIRateLimit(), analyticsController.getAIInsights.bind(analyticsController));
app.post('/api/analytics/predict-success', authenticateToken, security.createAIRateLimit(), analyticsController.predictApplicationSuccess.bind(analyticsController));
app.get('/api/analytics/market-trends', authenticateToken, security.createAIRateLimit(), analyticsController.getMarketTrends.bind(analyticsController));
app.post('/api/analytics/personalized-strategy', authenticateToken, security.createAIRateLimit(), analyticsController.getPersonalizedStrategy.bind(analyticsController));
app.get('/api/analytics/enhanced-dashboard', authenticateToken, cacheService.middleware(180000), analyticsController.getEnhancedDashboard.bind(analyticsController));

// Automation routes (protected)
app.get('/api/automation/optimize-timing', authenticateToken, automationController.optimizeApplicationTiming.bind(automationController));
app.post('/api/automation/analyze-geographic', authenticateToken, automationController.analyzeGeographicOpportunities.bind(automationController));
app.post('/api/automation/analyze-culture-fit', authenticateToken, automationController.analyzeCompanyCultureFit.bind(automationController));
app.get('/api/automation/anti-detection-strategy', authenticateToken, automationController.getAntiDetectionStrategy.bind(automationController));
app.get('/api/automation/application-insights', authenticateToken, automationController.getApplicationInsights.bind(automationController));
app.post('/api/automation/optimize-strategy', authenticateToken, automationController.optimizeApplicationStrategy.bind(automationController));

// Job routes (protected)
app.get('/api/jobs', authenticateToken, jobController.getJobs.bind(jobController));
app.get('/api/jobs/:id', authenticateToken, jobController.getJob.bind(jobController));
app.post('/api/jobs', authenticateToken, jobController.createJob.bind(jobController));
app.put('/api/jobs/:id', authenticateToken, jobController.updateJob.bind(jobController));
app.delete('/api/jobs/:id', authenticateToken, jobController.deleteJob.bind(jobController));

// Job application automation routes (protected)
app.post('/api/applications/queue', authenticateToken, jobApplicationController.queueApplication.bind(jobApplicationController));
app.post('/api/applications/bulk-queue', authenticateToken, jobApplicationController.bulkQueueApplications.bind(jobApplicationController));
app.get('/api/applications/stats', authenticateToken, jobApplicationController.getApplicationStats.bind(jobApplicationController));
app.post('/api/applications/optimal-timing', authenticateToken, jobApplicationController.getOptimalTiming.bind(jobApplicationController));
app.get('/api/applications/queue-status', authenticateToken, jobApplicationController.getQueueStatus.bind(jobApplicationController));
app.delete('/api/applications/:applicationId', authenticateToken, jobApplicationController.cancelApplication.bind(jobApplicationController));
app.put('/api/applications/settings', authenticateToken, jobApplicationController.updateOptimizationSettings.bind(jobApplicationController));

// Enhanced Job Application Routes with Containerization (protected)
app.post('/api/enhanced-applications/submit-containerized', authenticateToken, enhancedJobApplicationController.submitContainerizedJob.bind(enhancedJobApplicationController));
app.get('/api/enhanced-applications/containerized-status', authenticateToken, enhancedJobApplicationController.getContainerizedStatus.bind(enhancedJobApplicationController));
app.get('/api/enhanced-applications/health', authenticateToken, enhancedJobApplicationController.getEnhancedHealthStatus.bind(enhancedJobApplicationController));
app.post('/api/enhanced-applications/queue', authenticateToken, enhancedJobApplicationController.queueApplication.bind(enhancedJobApplicationController));
app.post('/api/enhanced-applications/bulk-queue', authenticateToken, enhancedJobApplicationController.bulkQueueApplications.bind(enhancedJobApplicationController));
app.get('/api/enhanced-applications/stats', authenticateToken, enhancedJobApplicationController.getApplicationStats.bind(enhancedJobApplicationController));
app.get('/api/enhanced-applications/queue-status', authenticateToken, enhancedJobApplicationController.getQueueStatus.bind(enhancedJobApplicationController));

// Enhanced Security and Dependency Management Routes (protected)
const enhancedSecurityRoutes = require('./routes/enhancedSecurity');
app.use('/api', enhancedSecurityRoutes(enhancedJobApplicationController));

// Team Workspace Management API (protected)
app.post('/api/teams/organizations', authenticateToken, teamController.createOrganization.bind(teamController));
app.get('/api/teams/organizations', authenticateToken, teamController.getUserOrganizations.bind(teamController));
app.get('/api/teams/organizations/:orgId', authenticateToken, teamController.getOrganization.bind(teamController));
app.post('/api/teams/organizations/:orgId/invite', authenticateToken, teamController.inviteTeamMember.bind(teamController));
app.put('/api/teams/organizations/:orgId/members/:memberId/role', authenticateToken, teamController.updateMemberRole.bind(teamController));
app.delete('/api/teams/organizations/:orgId/members/:memberId', authenticateToken, teamController.removeTeamMember.bind(teamController));
app.post('/api/teams/resumes/:resumeId/share', authenticateToken, teamController.shareResumeWithTeam.bind(teamController));
app.get('/api/teams/organizations/:orgId/resumes', authenticateToken, teamController.getTeamResumes.bind(teamController));

// Comment and Review System API (protected)
app.post('/api/comments/resumes/:resumeId', authenticateToken, commentController.addComment.bind(commentController));
app.post('/api/comments/:commentId/reply', authenticateToken, commentController.replyToComment.bind(commentController));
app.get('/api/comments/resumes/:resumeId', authenticateToken, commentController.getResumeComments.bind(commentController));
app.put('/api/comments/:commentId', authenticateToken, commentController.updateComment.bind(commentController));
app.delete('/api/comments/:commentId', authenticateToken, commentController.deleteComment.bind(commentController));

// Review System API (protected)
app.post('/api/reviews/resumes/:resumeId/request', authenticateToken, commentController.requestReview.bind(commentController));
app.post('/api/reviews/:reviewId/submit', authenticateToken, commentController.submitReview.bind(commentController));
app.get('/api/reviews/resumes/:resumeId', authenticateToken, commentController.getResumeReviews.bind(commentController));
app.get('/api/reviews/user', authenticateToken, commentController.getUserReviewRequests.bind(commentController));

// Containerized Job Execution System routes
const createContainerizedJobRoutes = require('./routes/containerizedJobs');
app.use('/api/containerized-jobs', createContainerizedJobRoutes(notificationService));

// Health check routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    collaboration: {
      websocketConnections: notificationService.getStats().connectedClients,
      collaborationService: 'ready'
    }
  });
});

app.get('/api/health/detailed', authenticateToken, (req, res) => {
  try {
    const collaborationService = require('./collaborationService');
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      websocket: notificationService.getStats(),
      collaboration: collaborationService.getStats(),
      cache: cacheService.getStats()
    });
  } catch (error) {
    res.status(500).json({ error: 'Health check failed', details: error.message });
  }
});

// Legacy resume endpoint for backward compatibility
app.get('/api/resume', (req, res) => {
  res.json(resumeData);
});

app.post('/api/resume', (req, res) => {
  resumeData = req.body;
  fs.writeFile(resumePath, JSON.stringify(resumeData, null, 2), (err) => {
    if (err) {
      console.error('Failed to save resume:', err);
      return res.status(500).json({ error: 'Failed to save resume' });
    }
    res.json({ status: 'ok' });
  });
});

// Serve static client build if available
app.use('/terminal', express.static(path.join(__dirname, 'public')));
app.use(express.static(path.join(__dirname, '..', 'client', 'dist')));

// Catch-all route for React app
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, '..', 'client', 'dist', 'index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).json({ error: 'Application not built yet' });
  }
});

// Enhanced error handling middleware (must be last)
app.use(errorHandler);
app.use(security.errorHandler);

// Initialize database and start server
async function startServer() {
  try {
    // Setup global error handlers early
    setupGlobalErrorHandlers();
    
    await setupDatabase();
    console.log('Database connection established');
    
    const server = app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
      console.log(`Client URL: ${process.env.CLIENT_URL || 'http://localhost:5173'}`);
    });

    // Initialize WebSocket notifications
    notificationService.initialize(server);

    // Set up WebSocket handling for terminal connections
    const WebSocket = require('ws');
    const url = require('url');
    const containerizedJobRoutes = createContainerizedJobRoutes(notificationService);
    
    // Create additional WebSocket server for terminal connections
    const terminalWss = new WebSocket.Server({
      server,
      path: '/ws/terminal',
      verifyClient: (info) => {
        // Extract terminal ID from URL
        const query = url.parse(info.req.url, true).query;
        return query.terminalId ? true : false;
      }
    });

    terminalWss.on('connection', (ws, req) => {
      const query = url.parse(req.url, true).query;
      req.params = { terminalId: query.terminalId };
      
      // Extract user info from token if provided
      try {
        const token = query.token;
        if (token) {
          const jwt = require('jsonwebtoken');
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          req.user = { userId: decoded.userId };
        }
      } catch (error) {
        console.warn('Terminal WebSocket authentication failed:', error.message);
      }
      
      // Handle terminal WebSocket connection
      containerizedJobRoutes.handleTerminalWebSocket(ws, req);
    });

    console.log('🔌 WebSocket servers initialized');
    console.log('📺 Terminal WebSocket available at /ws/terminal');

    // Graceful shutdown
    const shutdown = () => {
      console.log('Shutting down gracefully...');
      notificationService.shutdown();
      cacheService.destroy();
      server.close(() => {
        console.log('Process terminated');
        process.exit(0);
      });
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();