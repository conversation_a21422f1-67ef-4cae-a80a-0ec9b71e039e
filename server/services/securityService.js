const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

/**
 * Security Service for policy enforcement and vulnerability management
 */
class SecurityService {
  constructor() {
    this.securityPolicies = new Map();
    this.vulnerabilityScans = new Map();
    this.securityEvents = [];
    this.trustedImages = new Set();
    this.blockedImages = new Set();
    
    this.config = {
      scanInterval: 24 * 60 * 60 * 1000, // 24 hours
      maxSecurityEvents: 1000,
      alertThresholds: {
        highRiskVulnerabilities: 5,
        criticalVulnerabilities: 1,
        failedAuthAttempts: 10
      },
      tlsConfig: {
        minVersion: 'TLSv1.2',
        ciphers: [
          'ECDHE-RSA-AES128-GCM-SHA256',
          'ECDHE-RSA-AES256-GCM-SHA384',
          'ECDHE-RSA-AES128-SHA256',
          'ECDHE-RSA-AES256-SHA384'
        ]
      }
    };
    
    this.initializeSecurityPolicies();
  }

  /**
   * Initialize default security policies
   */
  initializeSecurityPolicies() {
    // Container security policies
    this.securityPolicies.set('container_default', {
      name: 'Default Container Policy',
      seccomp: {
        defaultAction: 'SCMP_ACT_ERRNO',
        allowedSyscalls: [
          'read', 'write', 'open', 'close', 'stat', 'fstat', 'lstat',
          'poll', 'lseek', 'mmap', 'mprotect', 'munmap', 'brk',
          'rt_sigaction', 'rt_sigprocmask', 'rt_sigreturn', 'ioctl',
          'pread64', 'pwrite64', 'readv', 'writev', 'access', 'pipe',
          'select', 'sched_yield', 'mremap', 'msync', 'mincore',
          'madvise', 'shmget', 'shmat', 'shmctl', 'dup', 'dup2',
          'pause', 'nanosleep', 'getitimer', 'alarm', 'setitimer',
          'getpid', 'sendfile', 'socket', 'connect', 'accept',
          'sendto', 'recvfrom', 'sendmsg', 'recvmsg', 'shutdown',
          'bind', 'listen', 'getsockname', 'getpeername',
          'socketpair', 'setsockopt', 'getsockopt', 'clone',
          'fork', 'vfork', 'execve', 'exit', 'wait4', 'kill',
          'uname', 'semget', 'semop', 'semctl', 'shmdt', 'msgget',
          'msgsnd', 'msgrcv', 'msgctl', 'fcntl', 'flock', 'fsync',
          'fdatasync', 'truncate', 'ftruncate', 'getdents',
          'getcwd', 'chdir', 'fchdir', 'rename', 'mkdir', 'rmdir',
          'creat', 'link', 'unlink', 'symlink', 'readlink', 'chmod',
          'fchmod', 'chown', 'fchown', 'lchown', 'umask', 'gettimeofday',
          'getrlimit', 'getrusage', 'sysinfo', 'times', 'ptrace',
          'getuid', 'syslog', 'getgid', 'setuid', 'setgid', 'geteuid',
          'getegid', 'setpgid', 'getppid', 'getpgrp', 'setsid',
          'setreuid', 'setregid', 'getgroups', 'setgroups',
          'setresuid', 'getresuid', 'setresgid', 'getresgid',
          'getpgid', 'setfsuid', 'setfsgid', 'getsid', 'capget',
          'capset', 'rt_sigpending', 'rt_sigtimedwait',
          'rt_sigqueueinfo', 'rt_sigsuspend', 'sigaltstack',
          'utime', 'mknod', 'uselib', 'personality', 'ustat',
          'statfs', 'fstatfs', 'sysfs', 'getpriority', 'setpriority',
          'sched_setparam', 'sched_getparam', 'sched_setscheduler',
          'sched_getscheduler', 'sched_get_priority_max',
          'sched_get_priority_min', 'sched_rr_get_interval',
          'mlock', 'munlock', 'mlockall', 'munlockall', 'vhangup',
          'modify_ldt', 'pivot_root', '_sysctl', 'prctl', 'arch_prctl',
          'adjtimex', 'setrlimit', 'chroot', 'sync', 'acct',
          'settimeofday', 'mount', 'umount2', 'swapon', 'swapoff',
          'reboot', 'sethostname', 'setdomainname', 'iopl', 'ioperm',
          'create_module', 'init_module', 'delete_module',
          'get_kernel_syms', 'query_module', 'quotactl', 'nfsservctl',
          'getpmsg', 'putpmsg', 'afs_syscall', 'tuxcall', 'security',
          'gettid', 'readahead', 'setxattr', 'lsetxattr', 'fsetxattr',
          'getxattr', 'lgetxattr', 'fgetxattr', 'listxattr',
          'llistxattr', 'flistxattr', 'removexattr', 'lremovexattr',
          'fremovexattr', 'tkill', 'time', 'futex', 'sched_setaffinity',
          'sched_getaffinity', 'set_thread_area', 'io_setup',
          'io_destroy', 'io_getevents', 'io_submit', 'io_cancel',
          'get_thread_area', 'lookup_dcookie', 'epoll_create',
          'epoll_ctl_old', 'epoll_wait_old', 'remap_file_pages',
          'getdents64', 'set_tid_address', 'restart_syscall',
          'semtimedop', 'fadvise64', 'timer_create', 'timer_settime',
          'timer_gettime', 'timer_getoverrun', 'timer_delete',
          'clock_settime', 'clock_gettime', 'clock_getres',
          'clock_nanosleep', 'exit_group', 'epoll_wait', 'epoll_ctl',
          'tgkill', 'utimes', 'vserver', 'mbind', 'set_mempolicy',
          'get_mempolicy', 'mq_open', 'mq_unlink', 'mq_timedsend',
          'mq_timedreceive', 'mq_notify', 'mq_getsetattr', 'kexec_load',
          'waitid', 'add_key', 'request_key', 'keyctl', 'ioprio_set',
          'ioprio_get', 'inotify_init', 'inotify_add_watch',
          'inotify_rm_watch', 'migrate_pages', 'openat', 'mkdirat',
          'mknodat', 'fchownat', 'futimesat', 'newfstatat', 'unlinkat',
          'renameat', 'linkat', 'symlinkat', 'readlinkat', 'fchmodat',
          'faccessat', 'pselect6', 'ppoll', 'unshare', 'set_robust_list',
          'get_robust_list', 'splice', 'tee', 'sync_file_range',
          'vmsplice', 'move_pages', 'utimensat', 'epoll_pwait',
          'signalfd', 'timerfd_create', 'eventfd', 'fallocate',
          'timerfd_settime', 'timerfd_gettime', 'accept4', 'signalfd4',
          'eventfd2', 'epoll_create1', 'dup3', 'pipe2', 'inotify_init1',
          'preadv', 'pwritev', 'rt_tgsigqueueinfo', 'perf_event_open',
          'recvmmsg', 'fanotify_init', 'fanotify_mark', 'prlimit64',
          'name_to_handle_at', 'open_by_handle_at', 'clock_adjtime',
          'syncfs', 'sendmmsg', 'setns', 'getcpu', 'process_vm_readv',
          'process_vm_writev', 'kcmp', 'finit_module'
        ]
      },
      capabilities: {
        drop: ['ALL'],
        add: ['CHOWN', 'DAC_OVERRIDE', 'FOWNER', 'FSETID', 'KILL', 'SETGID', 'SETUID', 'SETPCAP', 'NET_BIND_SERVICE', 'NET_RAW', 'SYS_CHROOT', 'MKNOD', 'AUDIT_WRITE', 'SETFCAP']
      },
      apparmor: {
        profile: 'docker-default'
      },
      resourceLimits: {
        memory: '512m',
        cpus: '0.5',
        pidsLimit: 100,
        ulimits: {
          nofile: 1024,
          nproc: 50
        }
      }
    });

    // Network security policies
    this.securityPolicies.set('network_restricted', {
      name: 'Restricted Network Policy',
      allowedDomains: [
        'linkedin.com',
        'indeed.com', 
        'glassdoor.com',
        'monster.com',
        'careerbuilder.com'
      ],
      blockedPorts: [22, 23, 135, 139, 445, 1433, 3389],
      allowedPorts: [80, 443],
      dnsServers: ['8.8.8.8', '8.8.4.4'],
      firewallRules: [
        'DROP INPUT tcp --dport 22',
        'DROP INPUT tcp --dport 23',
        'ACCEPT OUTPUT tcp --dport 80',
        'ACCEPT OUTPUT tcp --dport 443'
      ]
    });

    console.log('🔒 Security policies initialized');
  }

  /**
   * Scan container image for vulnerabilities
   */
  async scanContainerImage(imageName) {
    const scanId = crypto.randomUUID();
    const scanResult = {
      id: scanId,
      image: imageName,
      startTime: new Date(),
      status: 'scanning',
      vulnerabilities: [],
      summary: {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        info: 0
      }
    };

    this.vulnerabilityScans.set(scanId, scanResult);
    
    try {
      console.log(`🔍 Starting vulnerability scan for image: ${imageName}`);
      
      // Use Trivy for container scanning (if available)
      const trivyScan = await this.runTrivyScan(imageName);
      if (trivyScan) {
        scanResult.vulnerabilities = trivyScan.vulnerabilities;
        scanResult.summary = trivyScan.summary;
      } else {
        // Fallback to basic checks
        await this.performBasicImageChecks(imageName, scanResult);
      }
      
      scanResult.status = 'completed';
      scanResult.endTime = new Date();
      scanResult.duration = scanResult.endTime - scanResult.startTime;
      
      // Check if image meets security requirements
      const securityAssessment = this.assessImageSecurity(scanResult);
      scanResult.securityAssessment = securityAssessment;
      
      if (securityAssessment.approved) {
        this.trustedImages.add(imageName);
        console.log(`✅ Image ${imageName} approved for use`);
      } else {
        this.blockedImages.add(imageName);
        console.log(`❌ Image ${imageName} blocked due to security issues`);
        
        // Log security event
        this.logSecurityEvent('IMAGE_BLOCKED', {
          image: imageName,
          reason: securityAssessment.reason,
          vulnerabilities: scanResult.summary
        });
      }
      
      return scanResult;
      
    } catch (error) {
      scanResult.status = 'failed';
      scanResult.error = error.message;
      scanResult.endTime = new Date();
      
      console.error(`❌ Vulnerability scan failed for ${imageName}:`, error);
      
      // Err on the side of caution - block unknown images
      this.blockedImages.add(imageName);
      
      return scanResult;
    }
  }

  /**
   * Run Trivy vulnerability scanner
   */
  async runTrivyScan(imageName) {
    return new Promise((resolve) => {
      const trivyProcess = spawn('trivy', [
        'image',
        '--format', 'json',
        '--quiet',
        imageName
      ]);

      let output = '';
      let errorOutput = '';

      trivyProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      trivyProcess.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      trivyProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const scanData = JSON.parse(output);
            const vulnerabilities = [];
            const summary = { critical: 0, high: 0, medium: 0, low: 0, info: 0 };

            // Process Trivy results
            if (scanData.Results) {
              for (const result of scanData.Results) {
                if (result.Vulnerabilities) {
                  for (const vuln of result.Vulnerabilities) {
                    vulnerabilities.push({
                      id: vuln.VulnerabilityID,
                      severity: vuln.Severity,
                      title: vuln.Title,
                      description: vuln.Description,
                      package: vuln.PkgName,
                      version: vuln.InstalledVersion,
                      fixedVersion: vuln.FixedVersion,
                      references: vuln.References
                    });

                    // Update summary
                    const severity = vuln.Severity.toLowerCase();
                    if (summary.hasOwnProperty(severity)) {
                      summary[severity]++;
                    }
                  }
                }
              }
            }

            resolve({ vulnerabilities, summary });
          } catch (parseError) {
            console.error('Failed to parse Trivy output:', parseError);
            resolve(null);
          }
        } else {
          console.log('Trivy not available or failed, using fallback scan');
          resolve(null);
        }
      });

      // Timeout after 5 minutes
      setTimeout(() => {
        trivyProcess.kill();
        resolve(null);
      }, 300000);
    });
  }

  /**
   * Perform basic image security checks
   */
  async performBasicImageChecks(imageName, scanResult) {
    const checks = [
      { name: 'Base Image Check', check: () => this.checkBaseImage(imageName) },
      { name: 'User Check', check: () => this.checkImageUser(imageName) },
      { name: 'Exposed Ports Check', check: () => this.checkExposedPorts(imageName) },
      { name: 'Image Age Check', check: () => this.checkImageAge(imageName) }
    ];

    for (const { name, check } of checks) {
      try {
        const result = await check();
        if (!result.passed) {
          scanResult.vulnerabilities.push({
            id: `BASIC_${name.replace(/\s+/g, '_').toUpperCase()}`,
            severity: result.severity || 'MEDIUM',
            title: name,
            description: result.message,
            package: 'system',
            version: 'unknown'
          });

          // Update summary
          const severity = (result.severity || 'MEDIUM').toLowerCase();
          if (scanResult.summary.hasOwnProperty(severity)) {
            scanResult.summary[severity]++;
          }
        }
      } catch (error) {
        console.error(`Basic check ${name} failed:`, error);
      }
    }
  }

  /**
   * Check if base image is from trusted source
   */
  async checkBaseImage(imageName) {
    const trustedBases = ['node', 'alpine', 'ubuntu', 'debian', 'centos', 'fedora'];
    const imageBase = imageName.split(':')[0].split('/').pop();
    
    if (!trustedBases.includes(imageBase)) {
      return {
        passed: false,
        severity: 'HIGH',
        message: `Untrusted base image: ${imageBase}`
      };
    }
    
    return { passed: true };
  }

  /**
   * Check if image runs as non-root user
   */
  async checkImageUser(imageName) {
    try {
      const inspectResult = await this.inspectImage(imageName);
      const user = inspectResult.Config?.User;
      
      if (!user || user === 'root' || user === '0') {
        return {
          passed: false,
          severity: 'HIGH',
          message: 'Image runs as root user'
        };
      }
      
      return { passed: true };
    } catch (error) {
      return {
        passed: false,
        severity: 'MEDIUM',
        message: 'Could not determine image user'
      };
    }
  }

  /**
   * Check exposed ports for security risks
   */
  async checkExposedPorts(imageName) {
    try {
      const inspectResult = await this.inspectImage(imageName);
      const exposedPorts = inspectResult.Config?.ExposedPorts || {};
      
      const dangerousPorts = ['22/tcp', '23/tcp', '135/tcp', '139/tcp', '445/tcp', '1433/tcp', '3389/tcp'];
      const exposed = Object.keys(exposedPorts);
      
      for (const port of exposed) {
        if (dangerousPorts.includes(port)) {
          return {
            passed: false,
            severity: 'HIGH',
            message: `Dangerous port exposed: ${port}`
          };
        }
      }
      
      return { passed: true };
    } catch (error) {
      return {
        passed: false,
        severity: 'LOW',
        message: 'Could not check exposed ports'
      };
    }
  }

  /**
   * Check if image is too old
   */
  async checkImageAge(imageName) {
    try {
      const inspectResult = await this.inspectImage(imageName);
      const created = new Date(inspectResult.Created);
      const ageInDays = (Date.now() - created.getTime()) / (1000 * 60 * 60 * 24);
      
      if (ageInDays > 365) { // 1 year
        return {
          passed: false,
          severity: 'MEDIUM',
          message: `Image is ${Math.floor(ageInDays)} days old`
        };
      }
      
      return { passed: true };
    } catch (error) {
      return {
        passed: false,
        severity: 'LOW',
        message: 'Could not determine image age'
      };
    }
  }

  /**
   * Inspect Docker image
   */
  async inspectImage(imageName) {
    return new Promise((resolve, reject) => {
      const inspectProcess = spawn('docker', ['inspect', imageName]);
      let output = '';
      
      inspectProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      inspectProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const inspectData = JSON.parse(output);
            resolve(inspectData[0]);
          } catch (parseError) {
            reject(parseError);
          }
        } else {
          reject(new Error(`Docker inspect failed with code ${code}`));
        }
      });
    });
  }

  /**
   * Assess image security based on scan results
   */
  assessImageSecurity(scanResult) {
    const { summary } = scanResult;
    
    // Block images with critical vulnerabilities
    if (summary.critical > 0) {
      return {
        approved: false,
        risk: 'CRITICAL',
        reason: `${summary.critical} critical vulnerabilities found`
      };
    }
    
    // Block images with too many high-risk vulnerabilities
    if (summary.high >= this.config.alertThresholds.highRiskVulnerabilities) {
      return {
        approved: false,
        risk: 'HIGH',
        reason: `${summary.high} high-risk vulnerabilities found`
      };
    }
    
    // Warn about medium vulnerabilities but allow
    if (summary.medium > 10) {
      return {
        approved: true,
        risk: 'MEDIUM',
        reason: `${summary.medium} medium-risk vulnerabilities found`,
        warning: true
      };
    }
    
    return {
      approved: true,
      risk: 'LOW',
      reason: 'Image passed security assessment'
    };
  }

  /**
   * Validate container configuration against security policy
   */
  validateContainerConfig(config, policyName = 'container_default') {
    const policy = this.securityPolicies.get(policyName);
    if (!policy) {
      throw new Error(`Security policy not found: ${policyName}`);
    }
    
    const violations = [];
    
    // Check resource limits
    if (policy.resourceLimits) {
      if (!config.memory || this.parseMemory(config.memory) > this.parseMemory(policy.resourceLimits.memory)) {
        violations.push({
          type: 'resource_limit',
          field: 'memory',
          message: `Memory limit exceeds policy: ${config.memory} > ${policy.resourceLimits.memory}`
        });
      }
      
      if (!config.cpus || parseFloat(config.cpus) > parseFloat(policy.resourceLimits.cpus)) {
        violations.push({
          type: 'resource_limit',
          field: 'cpus',
          message: `CPU limit exceeds policy: ${config.cpus} > ${policy.resourceLimits.cpus}`
        });
      }
    }
    
    // Check user configuration
    if (config.user === 'root' || config.user === '0') {
      violations.push({
        type: 'user_security',
        field: 'user',
        message: 'Container should not run as root user'
      });
    }
    
    // Check network configuration
    if (config.network && config.network !== 'none' && config.network !== 'bridge') {
      violations.push({
        type: 'network_security',
        field: 'network',
        message: 'Only isolated or bridge networks are allowed'
      });
    }
    
    return {
      valid: violations.length === 0,
      violations,
      policy: policyName
    };
  }

  /**
   * Generate seccomp profile for container
   */
  generateSeccompProfile(policyName = 'container_default') {
    const policy = this.securityPolicies.get(policyName);
    if (!policy || !policy.seccomp) {
      return null;
    }
    
    const profile = {
      defaultAction: policy.seccomp.defaultAction,
      architectures: ['SCMP_ARCH_X86_64', 'SCMP_ARCH_X86', 'SCMP_ARCH_X32'],
      syscalls: []
    };
    
    // Add allowed syscalls
    for (const syscall of policy.seccomp.allowedSyscalls) {
      profile.syscalls.push({
        names: [syscall],
        action: 'SCMP_ACT_ALLOW'
      });
    }
    
    return profile;
  }

  /**
   * Generate AppArmor profile
   */
  generateAppArmorProfile(policyName = 'container_default') {
    const policy = this.securityPolicies.get(policyName);
    if (!policy || !policy.apparmor) {
      return null;
    }
    
    return policy.apparmor.profile;
  }

  /**
   * Set up TLS encryption for inter-service communication
   */
  setupTLSEncryption() {
    const tlsOptions = {
      minVersion: this.config.tlsConfig.minVersion,
      ciphers: this.config.tlsConfig.ciphers.join(':'),
      honorCipherOrder: true,
      sessionIdContext: 'cvleap-secure'
    };
    
    return tlsOptions;
  }

  /**
   * Log security event
   */
  logSecurityEvent(eventType, details) {
    const event = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      type: eventType,
      details,
      severity: this.getEventSeverity(eventType)
    };
    
    this.securityEvents.push(event);
    
    // Maintain event history limit
    if (this.securityEvents.length > this.config.maxSecurityEvents) {
      this.securityEvents = this.securityEvents.slice(-this.config.maxSecurityEvents);
    }
    
    console.log(`🔐 Security event logged: ${eventType}`, details);
    
    // Check for alert conditions
    this.checkAlertConditions(event);
    
    return event;
  }

  /**
   * Get event severity level
   */
  getEventSeverity(eventType) {
    const severityMap = {
      'IMAGE_BLOCKED': 'HIGH',
      'CONTAINER_VIOLATION': 'HIGH',
      'NETWORK_VIOLATION': 'MEDIUM',
      'AUTH_FAILURE': 'MEDIUM',
      'RESOURCE_VIOLATION': 'LOW',
      'POLICY_UPDATE': 'INFO'
    };
    
    return severityMap[eventType] || 'MEDIUM';
  }

  /**
   * Check for alert conditions
   */
  checkAlertConditions(event) {
    // Check for pattern of suspicious activity
    const recentEvents = this.securityEvents.filter(e => 
      Date.now() - e.timestamp.getTime() < 3600000 // Last hour
    );
    
    const criticalEvents = recentEvents.filter(e => e.severity === 'HIGH');
    
    if (criticalEvents.length >= this.config.alertThresholds.criticalVulnerabilities) {
      console.log('🚨 SECURITY ALERT: Multiple critical security events detected');
      
      // In a real implementation, send alerts to security team
      this.sendSecurityAlert('CRITICAL_EVENTS_DETECTED', {
        count: criticalEvents.length,
        timeWindow: '1 hour',
        events: criticalEvents.map(e => ({ type: e.type, timestamp: e.timestamp }))
      });
    }
  }

  /**
   * Send security alert (placeholder)
   */
  sendSecurityAlert(alertType, details) {
    // Placeholder for integration with alerting system
    console.log(`🚨 SECURITY ALERT [${alertType}]:`, details);
  }

  /**
   * Utility methods
   */
  parseMemory(memoryString) {
    const units = { b: 1, k: 1024, m: 1024 * 1024, g: 1024 * 1024 * 1024 };
    const match = memoryString.toLowerCase().match(/^(\d+)([bkmg]?)$/);
    
    if (!match) return 0;
    
    const value = parseInt(match[1]);
    const unit = match[2] || 'b';
    
    return value * units[unit];
  }

  /**
   * Get security service status
   */
  getStatus() {
    return {
      securityPolicies: this.securityPolicies.size,
      trustedImages: this.trustedImages.size,
      blockedImages: this.blockedImages.size,
      recentScans: this.vulnerabilityScans.size,
      securityEvents: this.securityEvents.length,
      lastScan: this.getLastScanTime(),
      healthStatus: 'healthy'
    };
  }

  /**
   * Get last scan time
   */
  getLastScanTime() {
    const scans = Array.from(this.vulnerabilityScans.values());
    if (scans.length === 0) return null;
    
    return Math.max(...scans.map(s => s.startTime.getTime()));
  }

  /**
   * Get vulnerability scan results
   */
  getScanResults(scanId) {
    return this.vulnerabilityScans.get(scanId);
  }

  /**
   * Get security events
   */
  getSecurityEvents(limit = 100) {
    return this.securityEvents
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Check if image is trusted
   */
  isImageTrusted(imageName) {
    return this.trustedImages.has(imageName);
  }

  /**
   * Check if image is blocked
   */
  isImageBlocked(imageName) {
    return this.blockedImages.has(imageName);
  }
}

module.exports = SecurityService;