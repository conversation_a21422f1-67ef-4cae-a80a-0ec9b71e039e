const database = require('../database');

class JobDiscoveryService {
  constructor() {
    this.db = database.get();
    this.platforms = {
      indeed: {
        name: 'Indeed',
        baseUrl: 'https://indeed.com',
        enabled: true
      },
      linkedin: {
        name: 'LinkedIn',
        baseUrl: 'https://linkedin.com',
        enabled: true
      },
      glassdoor: {
        name: 'Glassdoor',
        baseUrl: 'https://glassdoor.com',
        enabled: true
      }
    };
  }

  // Main job discovery method
  async discoverJobs(loopConfig) {
    const discoveredJobs = [];
    
    try {
      const jobTitles = JSON.parse(loopConfig.job_titles || '[]');
      const locations = JSON.parse(loopConfig.locations || '[]');
      const industries = JSON.parse(loopConfig.industries || '[]');
      const excludedCompanies = JSON.parse(loopConfig.excluded_companies || '[]');
      const keywords = JSON.parse(loopConfig.keywords || '[]');

      // Simulate job discovery from multiple platforms
      for (const platform of Object.keys(this.platforms)) {
        if (this.platforms[platform].enabled) {
          const platformJobs = await this.searchPlatform(platform, {
            jobTitles,
            locations,
            industries,
            excludedCompanies,
            keywords,
            salaryMin: loopConfig.salary_min,
            salaryMax: loopConfig.salary_max,
            remoteOptions: loopConfig.remote_options
          });

          discoveredJobs.push(...platformJobs);
        }
      }

      // Remove duplicates and score relevance
      const uniqueJobs = this.removeDuplicates(discoveredJobs);
      const scoredJobs = await this.scoreJobRelevance(uniqueJobs, loopConfig);

      // Save discovered jobs to database
      await this.saveDiscoveredJobs(loopConfig.loop_id || loopConfig.id, scoredJobs);

      return scoredJobs;
    } catch (error) {
      console.error('Error in job discovery:', error);
      throw new Error('Job discovery failed');
    }
  }

  // Simulate job search on specific platform
  async searchPlatform(platform, searchCriteria) {
    // In a real implementation, this would use web scraping or APIs
    // For now, we'll simulate with mock data
    
    const mockJobs = this.generateMockJobs(platform, searchCriteria);
    
    // Simulate API delay
    await this.delay(1000);
    
    return mockJobs;
  }

  // Generate mock job data for testing
  generateMockJobs(platform, criteria) {
    const mockCompanies = [
      'TechCorp', 'InnovateSoft', 'DataDriven Inc', 'CloudFirst', 'AI Solutions',
      'DevBuilder', 'CodeCraft', 'ByteWorks', 'PixelPerfect', 'LogicTree'
    ];

    const mockDescriptions = [
      'We are looking for a talented developer to join our growing team...',
      'Exciting opportunity to work with cutting-edge technologies...',
      'Join our dynamic team and help build the future of software...',
      'We offer competitive salary and excellent benefits package...',
      'Remote-friendly position with flexible working arrangements...'
    ];

    const jobs = [];
    const numJobs = Math.floor(Math.random() * 10) + 5; // 5-15 jobs per platform

    for (let i = 0; i < numJobs; i++) {
      const jobTitle = criteria.jobTitles[Math.floor(Math.random() * criteria.jobTitles.length)] || 'Software Developer';
      const company = mockCompanies[Math.floor(Math.random() * mockCompanies.length)];
      const location = criteria.locations[Math.floor(Math.random() * criteria.locations.length)] || 'Remote';
      
      // Skip excluded companies
      if (criteria.excludedCompanies.includes(company)) {
        continue;
      }

      jobs.push({
        externalId: `${platform}_${Date.now()}_${i}`,
        sourcePlatform: platform,
        title: jobTitle,
        company: company,
        location: location,
        description: mockDescriptions[Math.floor(Math.random() * mockDescriptions.length)],
        salaryMin: criteria.salaryMin ? criteria.salaryMin + Math.floor(Math.random() * 20000) : null,
        salaryMax: criteria.salaryMax ? criteria.salaryMax + Math.floor(Math.random() * 30000) : null,
        url: `https://${platform}.com/job/${Date.now()}_${i}`,
        discoveredAt: new Date()
      });
    }

    return jobs;
  }

  // Remove duplicate jobs across platforms
  removeDuplicates(jobs) {
    const seen = new Set();
    const uniqueJobs = [];

    for (const job of jobs) {
      // Create a unique identifier based on title and company
      const identifier = `${job.title.toLowerCase()}_${job.company.toLowerCase()}`;
      
      if (!seen.has(identifier)) {
        seen.add(identifier);
        uniqueJobs.push(job);
      }
    }

    return uniqueJobs;
  }

  // Score job relevance using ML-powered matching
  async scoreJobRelevance(jobs, loopConfig) {
    const jobTitles = JSON.parse(loopConfig.job_titles || '[]');
    const keywords = JSON.parse(loopConfig.keywords || '[]');
    const industries = JSON.parse(loopConfig.industries || '[]');

    return jobs.map(job => {
      let score = 0;
      const matchFactors = {};

      // Title matching (40% weight)
      const titleMatch = this.calculateTitleMatch(job.title, jobTitles);
      score += titleMatch * 0.4;
      matchFactors.titleMatch = titleMatch;

      // Keywords matching (30% weight)
      const keywordMatch = this.calculateKeywordMatch(job.description, keywords);
      score += keywordMatch * 0.3;
      matchFactors.keywordMatch = keywordMatch;

      // Salary matching (20% weight)
      const salaryMatch = this.calculateSalaryMatch(job, loopConfig);
      score += salaryMatch * 0.2;
      matchFactors.salaryMatch = salaryMatch;

      // Location matching (10% weight)
      const locationMatch = this.calculateLocationMatch(job.location, JSON.parse(loopConfig.locations || '[]'));
      score += locationMatch * 0.1;
      matchFactors.locationMatch = locationMatch;

      return {
        ...job,
        relevanceScore: Math.round(score * 100) / 100,
        matchFactors
      };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // Calculate title match score
  calculateTitleMatch(jobTitle, targetTitles) {
    if (!targetTitles.length) return 0.5;

    const jobTitleLower = jobTitle.toLowerCase();
    let maxMatch = 0;

    for (const targetTitle of targetTitles) {
      const targetLower = targetTitle.toLowerCase();
      const words = targetLower.split(' ');
      let matchCount = 0;

      for (const word of words) {
        if (jobTitleLower.includes(word)) {
          matchCount++;
        }
      }

      const match = matchCount / words.length;
      maxMatch = Math.max(maxMatch, match);
    }

    return maxMatch;
  }

  // Calculate keyword match score
  calculateKeywordMatch(description, keywords) {
    if (!keywords.length) return 0.5;

    const descriptionLower = description.toLowerCase();
    let matchCount = 0;

    for (const keyword of keywords) {
      if (descriptionLower.includes(keyword.toLowerCase())) {
        matchCount++;
      }
    }

    return matchCount / keywords.length;
  }

  // Calculate salary match score
  calculateSalaryMatch(job, loopConfig) {
    const minSalary = loopConfig.salary_min;
    const maxSalary = loopConfig.salary_max;

    if (!minSalary && !maxSalary) return 0.5;
    if (!job.salaryMin && !job.salaryMax) return 0.3;

    let score = 0.5;

    if (minSalary && job.salaryMin) {
      if (job.salaryMin >= minSalary) {
        score += 0.3;
      } else {
        score -= 0.2;
      }
    }

    if (maxSalary && job.salaryMax) {
      if (job.salaryMax <= maxSalary) {
        score += 0.2;
      }
    }

    return Math.max(0, Math.min(1, score));
  }

  // Calculate location match score
  calculateLocationMatch(jobLocation, targetLocations) {
    if (!targetLocations.length) return 0.5;
    if (jobLocation.toLowerCase().includes('remote')) return 1.0;

    const jobLocationLower = jobLocation.toLowerCase();
    
    for (const targetLocation of targetLocations) {
      const targetLower = targetLocation.toLowerCase();
      if (jobLocationLower.includes(targetLower) || targetLower.includes(jobLocationLower)) {
        return 1.0;
      }
    }

    return 0.1;
  }

  // Save discovered jobs to database
  async saveDiscoveredJobs(loopId, jobs) {
    try {
      for (const job of jobs) {
        await this.runQuery(
          `INSERT OR IGNORE INTO discovered_jobs (
            loop_id, external_id, source_platform, title, company, location,
            description, salary_min, salary_max, url, relevance_score, match_factors
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            loopId,
            job.externalId,
            job.sourcePlatform,
            job.title,
            job.company,
            job.location,
            job.description,
            job.salaryMin,
            job.salaryMax,
            job.url,
            job.relevanceScore,
            JSON.stringify(job.matchFactors)
          ]
        );
      }
    } catch (error) {
      console.error('Error saving discovered jobs:', error);
      throw new Error('Failed to save discovered jobs');
    }
  }

  // Get job discovery statistics
  async getDiscoveryStats(loopId) {
    try {
      const stats = await this.runQuery(
        `SELECT 
          source_platform,
          COUNT(*) as total_jobs,
          AVG(relevance_score) as avg_relevance,
          COUNT(CASE WHEN status = 'applied' THEN 1 END) as applied_jobs
         FROM discovered_jobs 
         WHERE loop_id = ? 
         GROUP BY source_platform`,
        [loopId]
      );

      return stats.map(stat => ({
        platform: stat.source_platform,
        totalJobs: stat.total_jobs,
        avgRelevance: Math.round(stat.avg_relevance * 100) / 100,
        appliedJobs: stat.applied_jobs
      }));
    } catch (error) {
      console.error('Error fetching discovery stats:', error);
      throw new Error('Failed to fetch discovery stats');
    }
  }

  // Update job status (e.g., when applied)
  async updateJobStatus(jobId, status) {
    try {
      const updateData = { status };
      if (status === 'applied') {
        updateData.applied_at = new Date().toISOString();
      }

      await this.runQuery(
        `UPDATE discovered_jobs SET status = ?, applied_at = ? WHERE id = ?`,
        [status, updateData.applied_at || null, jobId]
      );

      return { success: true };
    } catch (error) {
      console.error('Error updating job status:', error);
      throw new Error('Failed to update job status');
    }
  }

  // Utility method for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility method to promisify database queries
  runQuery(query, params = []) {
    return new Promise((resolve, reject) => {
      if (query.trim().toUpperCase().startsWith('SELECT')) {
        this.db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      } else {
        this.db.run(query, params, function(err) {
          if (err) reject(err);
          else resolve({ lastID: this.lastID, changes: this.changes });
        });
      }
    });
  }
}

module.exports = new JobDiscoveryService();