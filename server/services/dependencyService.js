const { EventEmitter } = require('events');
const crypto = require('crypto');

/**
 * Job Dependency Management Service
 * Handles sophisticated job dependency graphs, conditional execution, and parallel processing
 */
class DependencyService extends EventEmitter {
  constructor() {
    super();
    this.dependencyGraph = new Map(); // jobId -> dependency info
    this.executionQueue = new Map(); // priority -> jobs array
    this.runningJobs = new Map(); // jobId -> execution info
    this.completedJobs = new Map(); // jobId -> results
    this.failedJobs = new Map(); // jobId -> error info
    this.jobResults = new Map(); // jobId -> results for dependency sharing
    
    this.config = {
      maxParallelJobs: 5,
      retryAttempts: 3,
      retryDelay: 5000, // 5 seconds
      executionTimeout: 600000, // 10 minutes
      cascadeFailureLimit: 10 // Max cascade failures before stopping
    };
    
    this.executionEngine = null; // Will be injected
    this.isProcessing = false;
    this.cascadeFailureCount = 0;
  }

  /**
   * Set the execution engine (job application service)
   */
  setExecutionEngine(engine) {
    this.executionEngine = engine;
  }

  /**
   * Add job with dependencies to the graph
   */
  addJob(jobData, dependencies = [], conditions = null, priority = 5) {
    const jobId = jobData.id || crypto.randomUUID();
    jobData.id = jobId;
    
    // Validate dependencies exist
    for (const depId of dependencies) {
      if (!this.dependencyGraph.has(depId) && !this.completedJobs.has(depId)) {
        throw new Error(`Dependency job ${depId} not found`);
      }
    }
    
    const jobInfo = {
      id: jobId,
      data: jobData,
      dependencies: new Set(dependencies),
      dependents: new Set(), // Jobs that depend on this one
      conditions: conditions, // Conditional execution logic
      priority: priority,
      status: 'pending',
      addedAt: new Date(),
      retryCount: 0,
      executionHistory: []
    };
    
    this.dependencyGraph.set(jobId, jobInfo);
    
    // Update dependent relationships
    for (const depId of dependencies) {
      const depJob = this.dependencyGraph.get(depId);
      if (depJob) {
        depJob.dependents.add(jobId);
      }
    }
    
    console.log(`📋 Added job ${jobId} with dependencies: [${dependencies.join(', ')}]`);
    this.emit('jobAdded', { jobId, dependencies, priority });
    
    return jobId;
  }

  /**
   * Create a batch of interdependent jobs
   */
  addJobBatch(jobs) {
    const jobIds = [];
    
    // First pass: add all jobs without dependencies
    for (const jobConfig of jobs) {
      const jobId = this.addJob(jobConfig.data, [], jobConfig.conditions, jobConfig.priority);
      jobIds.push(jobId);
      jobConfig.id = jobId; // Store for dependency linking
    }
    
    // Second pass: update dependencies
    for (let i = 0; i < jobs.length; i++) {
      const jobConfig = jobs[i];
      if (jobConfig.dependsOn && jobConfig.dependsOn.length > 0) {
        const jobInfo = this.dependencyGraph.get(jobIds[i]);
        
        for (const depIndex of jobConfig.dependsOn) {
          if (depIndex < jobIds.length) {
            const depId = jobIds[depIndex];
            jobInfo.dependencies.add(depId);
            
            // Update dependent relationship
            const depJob = this.dependencyGraph.get(depId);
            if (depJob) {
              depJob.dependents.add(jobIds[i]);
            }
          }
        }
      }
    }
    
    console.log(`📦 Added job batch with ${jobIds.length} jobs`);
    return jobIds;
  }

  /**
   * Start processing the dependency graph
   */
  async startProcessing() {
    if (this.isProcessing) {
      console.log('⚠️ Processing already in progress');
      return;
    }
    
    this.isProcessing = true;
    this.cascadeFailureCount = 0;
    
    console.log('🚀 Starting dependency graph processing...');
    this.emit('processingStarted');
    
    try {
      while (this.hasJobsToProcess()) {
        const readyJobs = this.getReadyJobs();
        
        if (readyJobs.length === 0) {
          // Check if we're stuck due to circular dependencies or failures
          if (this.runningJobs.size === 0) {
            console.log('⚠️ No jobs ready to run - possible circular dependency or all jobs blocked');
            break;
          }
          
          // Wait for running jobs to complete
          await this.sleep(1000);
          continue;
        }
        
        // Execute ready jobs in parallel (up to maxParallelJobs)
        const jobsToExecute = readyJobs.slice(0, this.config.maxParallelJobs - this.runningJobs.size);
        
        for (const job of jobsToExecute) {
          this.executeJob(job);
        }
        
        // Wait before next iteration
        await this.sleep(500);
      }
      
      // Wait for remaining jobs to complete
      while (this.runningJobs.size > 0) {
        await this.sleep(1000);
      }
      
    } catch (error) {
      console.error('❌ Processing error:', error);
      this.emit('processingError', error);
    } finally {
      this.isProcessing = false;
      console.log('🏁 Dependency graph processing completed');
      this.emit('processingCompleted', this.getProcessingSummary());
    }
  }

  /**
   * Get jobs that are ready to execute (all dependencies satisfied)
   */
  getReadyJobs() {
    const readyJobs = [];
    
    for (const [jobId, jobInfo] of this.dependencyGraph) {
      if (jobInfo.status === 'pending' && this.areJobDependenciesSatisfied(jobInfo)) {
        // Check conditional execution
        if (jobInfo.conditions && !this.evaluateConditions(jobInfo.conditions)) {
          jobInfo.status = 'skipped';
          console.log(`⏭️ Job ${jobId} skipped due to conditions`);
          this.emit('jobSkipped', { jobId, reason: 'conditions not met' });
          continue;
        }
        
        readyJobs.push(jobInfo);
      }
    }
    
    // Sort by priority (higher priority first)
    return readyJobs.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Check if job dependencies are satisfied
   */
  areJobDependenciesSatisfied(jobInfo) {
    for (const depId of jobInfo.dependencies) {
      const depJob = this.dependencyGraph.get(depId);
      
      // Check if dependency is completed or exists in completed jobs
      if (!this.completedJobs.has(depId) && (!depJob || depJob.status !== 'completed')) {
        return false;
      }
    }
    return true;
  }

  /**
   * Evaluate conditional execution logic
   */
  evaluateConditions(conditions) {
    try {
      // Support different condition types
      if (conditions.type === 'result_based') {
        return this.evaluateResultBasedConditions(conditions);
      } else if (conditions.type === 'success_rate') {
        return this.evaluateSuccessRateConditions(conditions);
      } else if (conditions.type === 'custom') {
        return this.evaluateCustomConditions(conditions);
      }
      
      return true; // Default to execute if no specific conditions
    } catch (error) {
      console.error('Condition evaluation error:', error);
      return false; // Fail safe - don't execute if conditions can't be evaluated
    }
  }

  /**
   * Evaluate result-based conditions
   */
  evaluateResultBasedConditions(conditions) {
    const { dependsOnResults, criteria } = conditions;
    
    for (const depId of dependsOnResults) {
      const result = this.jobResults.get(depId);
      if (!result) return false;
      
      // Check criteria
      for (const criterion of criteria) {
        const value = this.getValueFromResult(result, criterion.field);
        if (!this.compareValues(value, criterion.operator, criterion.value)) {
          return false;
        }
      }
    }
    
    return true;
  }

  /**
   * Evaluate success rate conditions
   */
  evaluateSuccessRateConditions(conditions) {
    const { minimumSuccessRate, windowSize } = conditions;
    const recentJobs = Array.from(this.completedJobs.values())
      .sort((a, b) => b.completedAt - a.completedAt)
      .slice(0, windowSize || 10);
    
    if (recentJobs.length === 0) return true;
    
    const successCount = recentJobs.filter(job => job.success).length;
    const successRate = successCount / recentJobs.length;
    
    return successRate >= (minimumSuccessRate || 0.5);
  }

  /**
   * Evaluate custom conditions using safe evaluation
   */
  evaluateCustomConditions(conditions) {
    // This is a simplified version - in production, use a proper sandboxed evaluator
    const { expression, context } = conditions;
    
    // Create safe context with job results
    const safeContext = {
      ...context,
      results: Object.fromEntries(this.jobResults),
      completed: Array.from(this.completedJobs.keys()),
      failed: Array.from(this.failedJobs.keys())
    };
    
    // Simple expression evaluation (extend as needed)
    return this.evaluateExpression(expression, safeContext);
  }

  /**
   * Execute a single job
   */
  async executeJob(jobInfo) {
    const { id: jobId, data: jobData } = jobInfo;
    
    jobInfo.status = 'running';
    jobInfo.startTime = new Date();
    
    const executionInfo = {
      jobId,
      startTime: new Date(),
      timeout: null
    };
    
    this.runningJobs.set(jobId, executionInfo);
    
    console.log(`🏃 Executing job ${jobId}: ${jobData.company} - ${jobData.position}`);
    this.emit('jobStarted', { jobId, jobData });
    
    // Set execution timeout
    executionInfo.timeout = setTimeout(() => {
      this.handleJobTimeout(jobId);
    }, this.config.executionTimeout);
    
    try {
      // Execute the job using the execution engine
      const result = await this.executionEngine.executeJob(jobData);
      
      // Clear timeout
      if (executionInfo.timeout) {
        clearTimeout(executionInfo.timeout);
      }
      
      await this.handleJobSuccess(jobId, result);
      
    } catch (error) {
      // Clear timeout
      if (executionInfo.timeout) {
        clearTimeout(executionInfo.timeout);
      }
      
      await this.handleJobFailure(jobId, error);
    }
  }

  /**
   * Handle successful job completion
   */
  async handleJobSuccess(jobId, result) {
    const jobInfo = this.dependencyGraph.get(jobId);
    if (!jobInfo) return;
    
    jobInfo.status = 'completed';
    jobInfo.endTime = new Date();
    jobInfo.executionHistory.push({
      attempt: jobInfo.retryCount + 1,
      status: 'success',
      startTime: jobInfo.startTime,
      endTime: jobInfo.endTime,
      duration: jobInfo.endTime - jobInfo.startTime
    });
    
    // Store results for dependency sharing
    this.jobResults.set(jobId, result);
    this.completedJobs.set(jobId, {
      jobId,
      result,
      completedAt: new Date(),
      success: true,
      duration: jobInfo.endTime - jobInfo.startTime
    });
    
    this.runningJobs.delete(jobId);
    
    console.log(`✅ Job ${jobId} completed successfully`);
    this.emit('jobCompleted', { jobId, result, duration: jobInfo.endTime - jobInfo.startTime });
    
    // Check and update dependent jobs
    this.updateDependentJobs(jobId);
  }

  /**
   * Handle job failure
   */
  async handleJobFailure(jobId, error) {
    const jobInfo = this.dependencyGraph.get(jobId);
    if (!jobInfo) return;
    
    jobInfo.retryCount++;
    jobInfo.executionHistory.push({
      attempt: jobInfo.retryCount,
      status: 'failed',
      error: error.message,
      startTime: jobInfo.startTime,
      endTime: new Date()
    });
    
    console.log(`❌ Job ${jobId} failed (attempt ${jobInfo.retryCount}): ${error.message}`);
    
    // Check if we should retry
    if (jobInfo.retryCount < this.config.retryAttempts) {
      jobInfo.status = 'pending';
      console.log(`🔄 Retrying job ${jobId} in ${this.config.retryDelay}ms`);
      
      setTimeout(() => {
        this.emit('jobRetry', { jobId, attempt: jobInfo.retryCount + 1 });
      }, this.config.retryDelay);
    } else {
      // Mark as permanently failed
      jobInfo.status = 'failed';
      this.failedJobs.set(jobId, {
        jobId,
        error: error.message,
        failedAt: new Date(),
        retryCount: jobInfo.retryCount
      });
      
      // Handle cascade failures
      await this.handleCascadeFailure(jobId);
    }
    
    this.runningJobs.delete(jobId);
    this.emit('jobFailed', { jobId, error: error.message, retryCount: jobInfo.retryCount });
  }

  /**
   * Handle job execution timeout
   */
  async handleJobTimeout(jobId) {
    console.log(`⏰ Job ${jobId} timed out`);
    
    const executionInfo = this.runningJobs.get(jobId);
    if (executionInfo && this.executionEngine.cancelJob) {
      try {
        await this.executionEngine.cancelJob(jobId);
      } catch (error) {
        console.error(`Failed to cancel timed out job ${jobId}:`, error);
      }
    }
    
    await this.handleJobFailure(jobId, new Error('Execution timeout'));
  }

  /**
   * Handle cascade failures when a job fails
   */
  async handleCascadeFailure(failedJobId) {
    const failedJob = this.dependencyGraph.get(failedJobId);
    if (!failedJob) return;
    
    this.cascadeFailureCount++;
    const affectedJobs = [];
    
    // Find all jobs that depend on the failed job
    const dependentJobs = this.findAllDependentJobs(failedJobId);
    
    for (const depJobId of dependentJobs) {
      const depJob = this.dependencyGraph.get(depJobId);
      if (depJob && depJob.status === 'pending') {
        depJob.status = 'cancelled';
        affectedJobs.push(depJobId);
        
        this.failedJobs.set(depJobId, {
          jobId: depJobId,
          error: `Dependency ${failedJobId} failed`,
          failedAt: new Date(),
          cascadeFailure: true
        });
      }
    }
    
    console.log(`💔 Cascade failure from ${failedJobId} affected ${affectedJobs.length} jobs`);
    this.emit('cascadeFailure', { failedJobId, affectedJobs });
    
    // Check if we should stop processing due to too many cascade failures
    if (this.cascadeFailureCount >= this.config.cascadeFailureLimit) {
      console.log('🛑 Too many cascade failures - stopping processing');
      this.isProcessing = false;
      this.emit('processingHalted', { reason: 'cascade_failure_limit' });
    }
  }

  /**
   * Find all jobs that depend on a given job (recursively)
   */
  findAllDependentJobs(jobId, visited = new Set()) {
    if (visited.has(jobId)) return [];
    visited.add(jobId);
    
    const dependents = [];
    const jobInfo = this.dependencyGraph.get(jobId);
    
    if (jobInfo) {
      for (const dependentId of jobInfo.dependents) {
        dependents.push(dependentId);
        dependents.push(...this.findAllDependentJobs(dependentId, visited));
      }
    }
    
    return [...new Set(dependents)]; // Remove duplicates
  }

  /**
   * Update dependent jobs when a job completes
   */
  updateDependentJobs(completedJobId) {
    const completedJob = this.dependencyGraph.get(completedJobId);
    if (!completedJob) return;
    
    for (const dependentId of completedJob.dependents) {
      const dependentJob = this.dependencyGraph.get(dependentId);
      if (dependentJob && dependentJob.status === 'pending') {
        // Check if all dependencies are now satisfied
        if (this.areJobDependenciesSatisfied(dependentJob)) {
          console.log(`📋 Job ${dependentId} dependencies satisfied, ready for execution`);
          this.emit('jobReady', { jobId: dependentId });
        }
      }
    }
  }

  /**
   * Check if there are jobs left to process
   */
  hasJobsToProcess() {
    for (const [, jobInfo] of this.dependencyGraph) {
      if (jobInfo.status === 'pending') {
        return true;
      }
    }
    return false;
  }

  /**
   * Get processing summary
   */
  getProcessingSummary() {
    const total = this.dependencyGraph.size;
    const completed = Array.from(this.dependencyGraph.values()).filter(j => j.status === 'completed').length;
    const failed = Array.from(this.dependencyGraph.values()).filter(j => j.status === 'failed').length;
    const cancelled = Array.from(this.dependencyGraph.values()).filter(j => j.status === 'cancelled').length;
    const skipped = Array.from(this.dependencyGraph.values()).filter(j => j.status === 'skipped').length;
    
    return {
      total,
      completed,
      failed,
      cancelled,
      skipped,
      running: this.runningJobs.size,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      cascadeFailures: this.cascadeFailureCount
    };
  }

  /**
   * Get dependency graph visualization data
   */
  getDependencyGraphData() {
    const nodes = [];
    const edges = [];
    
    for (const [jobId, jobInfo] of this.dependencyGraph) {
      nodes.push({
        id: jobId,
        label: `${jobInfo.data.company}\n${jobInfo.data.position}`,
        status: jobInfo.status,
        priority: jobInfo.priority,
        retryCount: jobInfo.retryCount,
        executionTime: jobInfo.endTime ? jobInfo.endTime - jobInfo.startTime : null
      });
      
      for (const depId of jobInfo.dependencies) {
        edges.push({
          from: depId,
          to: jobId,
          type: 'dependency'
        });
      }
    }
    
    return { nodes, edges };
  }

  /**
   * Utility methods
   */
  getValueFromResult(result, field) {
    return field.split('.').reduce((obj, key) => obj?.[key], result);
  }

  compareValues(value1, operator, value2) {
    switch (operator) {
      case '==': return value1 == value2;
      case '===': return value1 === value2;
      case '!=': return value1 != value2;
      case '!==': return value1 !== value2;
      case '>': return value1 > value2;
      case '>=': return value1 >= value2;
      case '<': return value1 < value2;
      case '<=': return value1 <= value2;
      case 'contains': return String(value1).includes(value2);
      case 'startsWith': return String(value1).startsWith(value2);
      case 'endsWith': return String(value1).endsWith(value2);
      default: return false;
    }
  }

  evaluateExpression(expression, context) {
    // Simple expression evaluator - replace with proper sandboxed evaluator in production
    try {
      const func = new Function(...Object.keys(context), `return ${expression}`);
      return func(...Object.values(context));
    } catch (error) {
      console.error('Expression evaluation error:', error);
      return false;
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear completed jobs and reset state
   */
  reset() {
    this.dependencyGraph.clear();
    this.executionQueue.clear();
    this.runningJobs.clear();
    this.completedJobs.clear();
    this.failedJobs.clear();
    this.jobResults.clear();
    this.isProcessing = false;
    this.cascadeFailureCount = 0;
    
    console.log('🧹 Dependency service reset');
    this.emit('serviceReset');
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      totalJobs: this.dependencyGraph.size,
      pendingJobs: Array.from(this.dependencyGraph.values()).filter(j => j.status === 'pending').length,
      runningJobs: this.runningJobs.size,
      completedJobs: this.completedJobs.size,
      failedJobs: this.failedJobs.size,
      cascadeFailureCount: this.cascadeFailureCount,
      lastUpdate: new Date().toISOString()
    };
  }
}

module.exports = DependencyService;