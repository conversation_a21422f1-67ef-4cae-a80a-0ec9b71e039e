const pdfParse = require('pdf-parse');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

/**
 * File Processing Service for document parsing, virus scanning, and metadata extraction
 * Handles resume parsing, content extraction, and security validation
 */
class FileProcessingService {
  constructor() {
    this.tempDir = process.env.TEMP_DIR || '/tmp/cvleap-uploads';
    this.virusScanEnabled = process.env.VIRUS_SCAN_ENABLED === 'true';
    this.maxProcessingTime = 30000; // 30 seconds
    
    // Ensure temp directory exists
    this.ensureTempDir();
  }

  /**
   * Ensure temporary directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create temp directory:', error);
    }
  }

  /**
   * Process uploaded file with comprehensive validation and parsing
   * @param {Buffer} fileBuffer - File buffer
   * @param {Object} fileInfo - File information
   * @param {string} userId - User ID
   * @returns {Object} Processing result
   */
  async processFile(fileBuffer, fileInfo, userId) {
    const startTime = Date.now();
    let tempFilePath = null;

    try {
      const { originalname, mimetype } = fileInfo;
      
      // Generate temporary file path
      const tempFileName = `${crypto.randomUUID()}-${originalname}`;
      tempFilePath = path.join(this.tempDir, tempFileName);

      // Write buffer to temporary file
      await fs.writeFile(tempFilePath, fileBuffer);

      // Perform security validation
      const securityResult = await this.performSecurityValidation(fileBuffer, tempFilePath, mimetype);
      if (!securityResult.safe) {
        throw new Error(`Security validation failed: ${securityResult.reason}`);
      }

      // Extract metadata
      const metadata = await this.extractMetadata(fileBuffer, fileInfo);

      // Process based on file type
      let processingResult = {};
      
      if (mimetype === 'application/pdf') {
        processingResult = await this.processPDF(fileBuffer, originalname);
      } else if (mimetype.includes('word') || mimetype.includes('document')) {
        processingResult = await this.processWordDocument(tempFilePath, originalname);
      } else if (mimetype.startsWith('image/')) {
        processingResult = await this.processImage(fileBuffer, originalname);
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        metadata,
        content: processingResult.content || null,
        extractedText: processingResult.extractedText || null,
        pageCount: processingResult.pageCount || null,
        wordCount: processingResult.wordCount || null,
        processingTime,
        securityValidation: securityResult,
        tempFilePath: null // Don't expose temp file path
      };

    } catch (error) {
      console.error('File processing failed:', error);
      throw new Error(`File processing failed: ${error.message}`);
    } finally {
      // Clean up temporary file
      if (tempFilePath) {
        try {
          await fs.unlink(tempFilePath);
        } catch (error) {
          console.warn('Failed to clean up temp file:', error);
        }
      }
    }
  }

  /**
   * Perform comprehensive security validation
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} filePath - Temporary file path
   * @param {string} mimeType - MIME type
   * @returns {Object} Security validation result
   */
  async performSecurityValidation(fileBuffer, filePath, mimeType) {
    try {
      const validationResults = {
        safe: true,
        checks: {},
        reason: null
      };

      // 1. Magic byte validation
      validationResults.checks.magicBytes = this.validateMagicBytes(fileBuffer, mimeType);
      if (!validationResults.checks.magicBytes.valid) {
        validationResults.safe = false;
        validationResults.reason = 'Invalid file signature';
        return validationResults;
      }

      // 2. File size validation
      validationResults.checks.fileSize = this.validateFileSize(fileBuffer, mimeType);
      if (!validationResults.checks.fileSize.valid) {
        validationResults.safe = false;
        validationResults.reason = 'File size exceeds limits';
        return validationResults;
      }

      // 3. Content validation
      validationResults.checks.content = await this.validateFileContent(fileBuffer, mimeType);
      if (!validationResults.checks.content.valid) {
        validationResults.safe = false;
        validationResults.reason = 'Suspicious file content detected';
        return validationResults;
      }

      // 4. Virus scanning (if enabled)
      if (this.virusScanEnabled) {
        validationResults.checks.virusScan = await this.performVirusScan(filePath);
        if (!validationResults.checks.virusScan.clean) {
          validationResults.safe = false;
          validationResults.reason = 'Virus detected';
          return validationResults;
        }
      }

      return validationResults;

    } catch (error) {
      console.error('Security validation failed:', error);
      return {
        safe: false,
        checks: {},
        reason: `Security validation error: ${error.message}`
      };
    }
  }

  /**
   * Validate file magic bytes against MIME type
   * @param {Buffer} buffer - File buffer
   * @param {string} mimeType - Expected MIME type
   * @returns {Object} Validation result
   */
  validateMagicBytes(buffer, mimeType) {
    const magicBytes = {
      'application/pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
      'image/gif': [0x47, 0x49, 0x46, 0x38],
      'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF (WebP container)
      'application/msword': [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [0x50, 0x4B, 0x03, 0x04] // ZIP
    };

    const expectedBytes = magicBytes[mimeType];
    if (!expectedBytes) {
      return { valid: true, reason: 'No magic byte validation available' };
    }

    const fileHeader = Array.from(buffer.slice(0, expectedBytes.length));
    const matches = expectedBytes.every((byte, index) => fileHeader[index] === byte);

    return {
      valid: matches,
      reason: matches ? 'Magic bytes match' : 'Magic bytes do not match expected format',
      expected: expectedBytes,
      actual: fileHeader
    };
  }

  /**
   * Validate file size against type-specific limits
   * @param {Buffer} buffer - File buffer
   * @param {string} mimeType - MIME type
   * @returns {Object} Validation result
   */
  validateFileSize(buffer, mimeType) {
    const sizeLimits = {
      'application/pdf': 25 * 1024 * 1024, // 25MB
      'application/msword': 25 * 1024 * 1024, // 25MB
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 25 * 1024 * 1024, // 25MB
      'image/jpeg': 10 * 1024 * 1024, // 10MB
      'image/png': 10 * 1024 * 1024, // 10MB
      'image/gif': 10 * 1024 * 1024, // 10MB
      'image/webp': 10 * 1024 * 1024 // 10MB
    };

    const maxSize = sizeLimits[mimeType] || 10 * 1024 * 1024; // Default 10MB
    const fileSize = buffer.length;

    return {
      valid: fileSize <= maxSize,
      reason: fileSize <= maxSize ? 'File size within limits' : `File size ${fileSize} exceeds limit ${maxSize}`,
      fileSize,
      maxSize
    };
  }

  /**
   * Validate file content for suspicious patterns
   * @param {Buffer} buffer - File buffer
   * @param {string} mimeType - MIME type
   * @returns {Object} Validation result
   */
  async validateFileContent(buffer, mimeType) {
    try {
      // Convert buffer to string for pattern matching
      const content = buffer.toString('utf8', 0, Math.min(buffer.length, 10000)); // First 10KB

      // Suspicious patterns to detect
      const suspiciousPatterns = [
        /<script[^>]*>/i,
        /javascript:/i,
        /vbscript:/i,
        /onload\s*=/i,
        /onerror\s*=/i,
        /eval\s*\(/i,
        /document\.write/i,
        /\.exe\b/i,
        /\.bat\b/i,
        /\.cmd\b/i,
        /\.scr\b/i
      ];

      const foundPatterns = suspiciousPatterns.filter(pattern => pattern.test(content));

      return {
        valid: foundPatterns.length === 0,
        reason: foundPatterns.length === 0 ? 'No suspicious content detected' : `Suspicious patterns found: ${foundPatterns.length}`,
        suspiciousPatterns: foundPatterns.map(p => p.toString())
      };

    } catch (error) {
      console.warn('Content validation error:', error);
      return {
        valid: true,
        reason: 'Content validation skipped due to error'
      };
    }
  }

  /**
   * Perform virus scan (placeholder - integrate with actual antivirus service)
   * @param {string} filePath - File path
   * @returns {Object} Scan result
   */
  async performVirusScan(filePath) {
    try {
      // Placeholder for virus scanning integration
      // In production, integrate with services like:
      // - ClamAV
      // - VirusTotal API
      // - Cloud-based antivirus services

      // Simulate virus scan
      await new Promise(resolve => setTimeout(resolve, 100));

      return {
        clean: true,
        scanTime: Date.now(),
        engine: 'placeholder',
        version: '1.0.0'
      };

    } catch (error) {
      console.error('Virus scan failed:', error);
      return {
        clean: false,
        error: error.message
      };
    }
  }

  /**
   * Extract file metadata
   * @param {Buffer} buffer - File buffer
   * @param {Object} fileInfo - File information
   * @returns {Object} Metadata
   */
  async extractMetadata(buffer, fileInfo) {
    try {
      const { originalname, mimetype, size } = fileInfo;

      const metadata = {
        originalName: originalname,
        mimeType: mimetype,
        fileSize: buffer.length,
        uploadSize: size,
        fileExtension: path.extname(originalname).toLowerCase(),
        hash: {
          md5: crypto.createHash('md5').update(buffer).digest('hex'),
          sha256: crypto.createHash('sha256').update(buffer).digest('hex')
        },
        processedAt: new Date().toISOString()
      };

      // Add type-specific metadata
      if (mimetype === 'application/pdf') {
        try {
          const pdfData = await pdfParse(buffer);
          metadata.pdf = {
            pages: pdfData.numpages,
            info: pdfData.info,
            metadata: pdfData.metadata,
            version: pdfData.version
          };
        } catch (error) {
          console.warn('PDF metadata extraction failed:', error);
        }
      }

      return metadata;

    } catch (error) {
      console.error('Metadata extraction failed:', error);
      return {
        originalName: fileInfo.originalname,
        mimeType: fileInfo.mimetype,
        fileSize: buffer.length,
        error: error.message
      };
    }
  }

  /**
   * Process PDF document
   * @param {Buffer} buffer - PDF buffer
   * @param {string} filename - Original filename
   * @returns {Object} Processing result
   */
  async processPDF(buffer, filename) {
    try {
      const pdfData = await pdfParse(buffer);

      return {
        extractedText: pdfData.text,
        pageCount: pdfData.numpages,
        wordCount: pdfData.text.split(/\s+/).length,
        content: {
          text: pdfData.text,
          info: pdfData.info,
          metadata: pdfData.metadata
        }
      };

    } catch (error) {
      console.error('PDF processing failed:', error);
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  /**
   * Process Word document
   * @param {string} filePath - File path
   * @param {string} filename - Original filename
   * @returns {Object} Processing result
   */
  async processWordDocument(filePath, filename) {
    try {
      // For Word documents, we would typically use libraries like:
      // - mammoth (for .docx)
      // - textract
      // - officegen
      
      // Placeholder implementation
      const fileBuffer = await fs.readFile(filePath);
      
      return {
        extractedText: 'Word document text extraction not implemented',
        wordCount: 0,
        content: {
          note: 'Word document processing requires additional libraries'
        }
      };

    } catch (error) {
      console.error('Word document processing failed:', error);
      throw new Error(`Word document processing failed: ${error.message}`);
    }
  }

  /**
   * Process image file
   * @param {Buffer} buffer - Image buffer
   * @param {string} filename - Original filename
   * @returns {Object} Processing result
   */
  async processImage(buffer, filename) {
    try {
      const sharp = require('sharp');
      const imageInfo = await sharp(buffer).metadata();

      return {
        content: {
          width: imageInfo.width,
          height: imageInfo.height,
          format: imageInfo.format,
          channels: imageInfo.channels,
          density: imageInfo.density,
          hasAlpha: imageInfo.hasAlpha,
          orientation: imageInfo.orientation
        }
      };

    } catch (error) {
      console.error('Image processing failed:', error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Clean up old temporary files
   * @param {number} maxAge - Maximum age in milliseconds
   */
  async cleanupTempFiles(maxAge = 3600000) { // 1 hour default
    try {
      const files = await fs.readdir(this.tempDir);
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          console.log(`Cleaned up old temp file: ${file}`);
        }
      }

    } catch (error) {
      console.error('Temp file cleanup failed:', error);
    }
  }
}

module.exports = new FileProcessingService();
