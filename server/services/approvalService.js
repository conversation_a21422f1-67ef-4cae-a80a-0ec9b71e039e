const { v4: uuidv4 } = require('uuid');

/**
 * Approval Service for human-in-the-loop workflow
 */
class ApprovalService {
  constructor(notificationService = null) {
    this.pendingApprovals = new Map(); // approvalId -> approval data
    this.approvalHistory = new Map(); // jobId -> approval history
    this.notificationService = notificationService;
    this.config = {
      autoApprovalThreshold: 0.9, // Auto-approve jobs with confidence > 90%
      approvalTimeout: 24 * 60 * 60 * 1000, // 24 hours
      requiresApproval: {
        newCompany: true,
        lowConfidence: true,
        highValue: true,
        customParameters: true
      }
    };
  }

  /**
   * Create approval request for job application
   */
  async createApprovalRequest(jobData, userId, metadata = {}) {
    const approvalId = uuidv4();
    
    // Analyze if approval is needed
    const analysisResult = this.analyzeJobForApproval(jobData, metadata);
    
    const approval = {
      id: approvalId,
      jobId: jobData.id,
      userId,
      jobData,
      metadata,
      analysisResult,
      status: 'pending',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + this.config.approvalTimeout),
      priority: this.calculatePriority(jobData, analysisResult),
      approvers: this.getRequiredApprovers(userId, analysisResult),
      modifications: [],
      comments: []
    };

    // Auto-approve if conditions are met
    if (this.shouldAutoApprove(approval)) {
      approval.status = 'auto_approved';
      approval.approvedAt = new Date();
      approval.approvedBy = 'system';
      console.log(`✅ Auto-approved job application ${jobData.id}`);
    } else {
      this.pendingApprovals.set(approvalId, approval);
      await this.notifyApprovers(approval);
      console.log(`⏳ Created approval request ${approvalId} for job ${jobData.id}`);
    }

    // Store in history
    const history = this.approvalHistory.get(jobData.id) || [];
    history.push({
      approvalId,
      status: approval.status,
      createdAt: approval.createdAt,
      analysisResult: approval.analysisResult
    });
    this.approvalHistory.set(jobData.id, history);

    return approval;
  }

  /**
   * Analyze job to determine if approval is needed
   */
  analyzeJobForApproval(jobData, metadata) {
    const analysis = {
      requiresApproval: false,
      reasons: [],
      confidence: 0.8,
      riskLevel: 'low',
      recommendations: []
    };

    // Check if it's a new company
    if (this.isNewCompany(jobData.company)) {
      analysis.requiresApproval = true;
      analysis.reasons.push('new_company');
      analysis.riskLevel = 'medium';
    }

    // Check confidence level
    if (metadata.confidenceScore < 0.7) {
      analysis.requiresApproval = true;
      analysis.reasons.push('low_confidence');
      analysis.confidence = metadata.confidenceScore;
    }

    // Check for custom parameters
    if (jobData.customAnswers && Object.keys(jobData.customAnswers).length > 0) {
      analysis.requiresApproval = true;
      analysis.reasons.push('custom_parameters');
    }

    // Check salary/value
    if (this.isHighValuePosition(jobData)) {
      analysis.requiresApproval = true;
      analysis.reasons.push('high_value');
      analysis.riskLevel = 'high';
    }

    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(jobData, analysis);

    return analysis;
  }

  /**
   * Calculate approval priority
   */
  calculatePriority(jobData, analysisResult) {
    let priority = 'normal';

    if (analysisResult.riskLevel === 'high') {
      priority = 'high';
    } else if (this.isUrgentJob(jobData)) {
      priority = 'high';
    } else if (analysisResult.reasons.includes('new_company')) {
      priority = 'medium';
    }

    return priority;
  }

  /**
   * Get required approvers for the job
   */
  getRequiredApprovers(userId, analysisResult) {
    const approvers = [userId]; // Self-approval by default

    // Add additional approvers based on risk level
    if (analysisResult.riskLevel === 'high') {
      approvers.push('admin'); // Require admin approval
    }

    return approvers;
  }

  /**
   * Check if job should be auto-approved
   */
  shouldAutoApprove(approval) {
    const { analysisResult } = approval;
    
    return (
      !analysisResult.requiresApproval ||
      (analysisResult.confidence >= this.config.autoApprovalThreshold &&
       analysisResult.riskLevel === 'low' &&
       !analysisResult.reasons.includes('custom_parameters'))
    );
  }

  /**
   * Approve job application
   */
  async approveJob(approvalId, approverId, comments = '', modifications = {}) {
    const approval = this.pendingApprovals.get(approvalId);
    if (!approval) {
      throw new Error('Approval request not found');
    }

    if (approval.status !== 'pending') {
      throw new Error(`Cannot approve job in status: ${approval.status}`);
    }

    // Apply modifications if provided
    if (Object.keys(modifications).length > 0) {
      approval.modifications.push({
        modifiedBy: approverId,
        modifiedAt: new Date(),
        changes: modifications,
        reason: comments
      });
      
      // Update job data with modifications
      Object.assign(approval.jobData, modifications);
    }

    // Add approval comment
    if (comments) {
      approval.comments.push({
        author: approverId,
        content: comments,
        timestamp: new Date(),
        type: 'approval'
      });
    }

    approval.status = 'approved';
    approval.approvedAt = new Date();
    approval.approvedBy = approverId;

    // Remove from pending
    this.pendingApprovals.delete(approvalId);

    // Update history
    const history = this.approvalHistory.get(approval.jobId) || [];
    history.push({
      approvalId,
      status: 'approved',
      approvedAt: approval.approvedAt,
      approvedBy: approverId,
      modifications: approval.modifications.length > 0
    });
    this.approvalHistory.set(approval.jobId, history);

    // Notify user
    await this.notifyApprovalDecision(approval, 'approved');

    console.log(`✅ Job ${approval.jobId} approved by ${approverId}`);
    return approval;
  }

  /**
   * Reject job application
   */
  async rejectJob(approvalId, approverId, reason = '') {
    const approval = this.pendingApprovals.get(approvalId);
    if (!approval) {
      throw new Error('Approval request not found');
    }

    if (approval.status !== 'pending') {
      throw new Error(`Cannot reject job in status: ${approval.status}`);
    }

    // Add rejection comment
    approval.comments.push({
      author: approverId,
      content: reason,
      timestamp: new Date(),
      type: 'rejection'
    });

    approval.status = 'rejected';
    approval.rejectedAt = new Date();
    approval.rejectedBy = approverId;
    approval.rejectionReason = reason;

    // Remove from pending
    this.pendingApprovals.delete(approvalId);

    // Update history
    const history = this.approvalHistory.get(approval.jobId) || [];
    history.push({
      approvalId,
      status: 'rejected',
      rejectedAt: approval.rejectedAt,
      rejectedBy: approverId,
      reason
    });
    this.approvalHistory.set(approval.jobId, history);

    // Notify user
    await this.notifyApprovalDecision(approval, 'rejected');

    console.log(`❌ Job ${approval.jobId} rejected by ${approverId}: ${reason}`);
    return approval;
  }

  /**
   * Get pending approvals for user
   */
  getPendingApprovals(userId, filters = {}) {
    const userApprovals = [];
    
    for (const approval of this.pendingApprovals.values()) {
      // Check if user is an approver
      if (approval.approvers.includes(userId) || approval.userId === userId) {
        // Apply filters
        if (filters.priority && approval.priority !== filters.priority) {
          continue;
        }
        if (filters.company && !approval.jobData.company.toLowerCase().includes(filters.company.toLowerCase())) {
          continue;
        }
        
        userApprovals.push(this.sanitizeApprovalForUser(approval));
      }
    }

    // Sort by priority and creation date
    return userApprovals.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, normal: 1 };
      const priorityDiff = priorityWeight[b.priority] - priorityWeight[a.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  }

  /**
   * Get approval by ID
   */
  getApproval(approvalId) {
    return this.pendingApprovals.get(approvalId);
  }

  /**
   * Get approval history for job
   */
  getJobApprovalHistory(jobId) {
    return this.approvalHistory.get(jobId) || [];
  }

  /**
   * Notify approvers about new approval request
   */
  async notifyApprovers(approval) {
    if (!this.notificationService) return;

    const notification = {
      type: 'approval_request',
      title: 'Job Application Approval Required',
      message: `New job application for ${approval.jobData.jobTitle} at ${approval.jobData.company} requires approval`,
      data: {
        approvalId: approval.id,
        jobId: approval.jobId,
        priority: approval.priority,
        reasons: approval.analysisResult.reasons
      }
    };

    for (const approverId of approval.approvers) {
      try {
        await this.notificationService.sendToUser(approverId, notification);
      } catch (error) {
        console.error(`Failed to notify approver ${approverId}:`, error);
      }
    }
  }

  /**
   * Notify about approval decision
   */
  async notifyApprovalDecision(approval, decision) {
    if (!this.notificationService) return;

    const notification = {
      type: 'approval_decision',
      title: `Job Application ${decision.charAt(0).toUpperCase() + decision.slice(1)}`,
      message: `Your job application for ${approval.jobData.jobTitle} at ${approval.jobData.company} has been ${decision}`,
      data: {
        approvalId: approval.id,
        jobId: approval.jobId,
        decision,
        approvedBy: approval.approvedBy || approval.rejectedBy
      }
    };

    try {
      await this.notificationService.sendToUser(approval.userId, notification);
    } catch (error) {
      console.error(`Failed to notify user ${approval.userId}:`, error);
    }
  }

  /**
   * Sanitize approval data for user display
   */
  sanitizeApprovalForUser(approval) {
    return {
      id: approval.id,
      jobId: approval.jobId,
      jobData: {
        id: approval.jobData.id,
        jobTitle: approval.jobData.jobTitle,
        company: approval.jobData.company,
        jobUrl: approval.jobData.jobUrl,
        priority: approval.jobData.priority
      },
      status: approval.status,
      createdAt: approval.createdAt,
      expiresAt: approval.expiresAt,
      priority: approval.priority,
      analysisResult: approval.analysisResult,
      comments: approval.comments,
      modifications: approval.modifications
    };
  }

  /**
   * Helper methods for analysis
   */
  isNewCompany(company) {
    // Placeholder: would check against known companies database
    return Math.random() < 0.3; // 30% chance of being "new"
  }

  isHighValuePosition(jobData) {
    // Check for senior positions or high-value keywords
    const highValueKeywords = ['senior', 'lead', 'principal', 'director', 'manager', 'architect'];
    const title = jobData.jobTitle.toLowerCase();
    return highValueKeywords.some(keyword => title.includes(keyword));
  }

  isUrgentJob(jobData) {
    // Check if job is time-sensitive
    return jobData.priority === 'high' || jobData.urgent === true;
  }

  generateRecommendations(jobData, analysis) {
    const recommendations = [];

    if (analysis.reasons.includes('low_confidence')) {
      recommendations.push('Review job requirements and improve resume matching');
    }

    if (analysis.reasons.includes('new_company')) {
      recommendations.push('Research company culture and values before applying');
    }

    if (analysis.reasons.includes('custom_parameters')) {
      recommendations.push('Review custom answers for accuracy and relevance');
    }

    return recommendations;
  }

  /**
   * Cleanup expired approvals
   */
  cleanup() {
    const now = new Date();
    let expiredCount = 0;

    for (const [approvalId, approval] of this.pendingApprovals.entries()) {
      if (now > approval.expiresAt) {
        approval.status = 'expired';
        approval.expiredAt = now;
        
        // Remove from pending
        this.pendingApprovals.delete(approvalId);
        
        // Update history
        const history = this.approvalHistory.get(approval.jobId) || [];
        history.push({
          approvalId,
          status: 'expired',
          expiredAt: now
        });
        this.approvalHistory.set(approval.jobId, history);
        
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      console.log(`🧹 Cleaned up ${expiredCount} expired approval requests`);
    }
  }

  /**
   * Get service statistics
   */
  getStats() {
    const stats = {
      pendingApprovals: this.pendingApprovals.size,
      totalJobsInHistory: this.approvalHistory.size,
      approvalsByPriority: {},
      approvalsByStatus: {}
    };

    for (const approval of this.pendingApprovals.values()) {
      stats.approvalsByPriority[approval.priority] = 
        (stats.approvalsByPriority[approval.priority] || 0) + 1;
      stats.approvalsByStatus[approval.status] = 
        (stats.approvalsByStatus[approval.status] || 0) + 1;
    }

    return stats;
  }
}

module.exports = ApprovalService;