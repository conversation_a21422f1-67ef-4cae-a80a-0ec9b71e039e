const database = require('../database');
const cron = require('node-cron');

class LoopService {
  constructor() {
    this.db = database.get();
    this.runningLoops = new Map(); // Track active cron jobs
    this.initializeScheduler();
  }

  // Initialize the loop scheduler
  initializeScheduler() {
    // Run every 30 minutes to check for loops that need to run
    cron.schedule('*/30 * * * *', () => {
      this.checkAndRunLoops();
    });
  }

  // Create a new job search loop
  async createLoop(userId, loopData) {
    const { name, description, configuration } = loopData;
    
    try {
      // Insert loop
      const loopResult = await this.runQuery(
        `INSERT INTO job_loops (user_id, name, description, status, configuration) 
         VALUES (?, ?, ?, 'draft', ?)`,
        [userId, name, description, JSON.stringify(configuration)]
      );

      const loopId = loopResult.lastID;

      // Insert configuration details
      const config = configuration;
      await this.runQuery(
        `INSERT INTO loop_configurations (
          loop_id, job_titles, locations, industries, experience_levels,
          salary_min, salary_max, remote_options, excluded_companies,
          keywords, schedule_type, schedule_config, max_applications_per_day
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          loopId,
          JSON.stringify(config.jobTitles || []),
          JSON.stringify(config.locations || []),
          JSON.stringify(config.industries || []),
          JSON.stringify(config.experienceLevels || []),
          config.salaryMin || null,
          config.salaryMax || null,
          config.remoteOptions || 'any',
          JSON.stringify(config.excludedCompanies || []),
          JSON.stringify(config.keywords || []),
          config.scheduleType || 'continuous',
          JSON.stringify(config.scheduleConfig || {}),
          config.maxApplicationsPerDay || 5
        ]
      );

      return { id: loopId, ...loopData, status: 'draft' };
    } catch (error) {
      console.error('Error creating loop:', error);
      throw new Error('Failed to create loop');
    }
  }

  // Get user's loops
  async getUserLoops(userId) {
    try {
      const loops = await this.runQuery(
        `SELECT l.*, lc.* FROM job_loops l 
         LEFT JOIN loop_configurations lc ON l.id = lc.loop_id 
         WHERE l.user_id = ? ORDER BY l.created_at DESC`,
        [userId]
      );

      return loops.map(loop => ({
        id: loop.id,
        name: loop.name,
        description: loop.description,
        status: loop.status,
        configuration: JSON.parse(loop.configuration || '{}'),
        createdAt: loop.created_at,
        updatedAt: loop.updated_at,
        lastRunAt: loop.last_run_at,
        nextRunAt: loop.next_run_at,
        jobTitles: JSON.parse(loop.job_titles || '[]'),
        locations: JSON.parse(loop.locations || '[]'),
        industries: JSON.parse(loop.industries || '[]'),
        maxApplicationsPerDay: loop.max_applications_per_day
      }));
    } catch (error) {
      console.error('Error fetching user loops:', error);
      throw new Error('Failed to fetch loops');
    }
  }

  // Update loop configuration
  async updateLoop(userId, loopId, updateData) {
    try {
      const { name, description, configuration, status } = updateData;

      // Update main loop record
      await this.runQuery(
        `UPDATE job_loops SET name = ?, description = ?, configuration = ?, 
         status = ?, updated_at = CURRENT_TIMESTAMP 
         WHERE id = ? AND user_id = ?`,
        [name, description, JSON.stringify(configuration), status, loopId, userId]
      );

      // Update configuration if provided
      if (configuration) {
        await this.runQuery(
          `UPDATE loop_configurations SET 
           job_titles = ?, locations = ?, industries = ?, experience_levels = ?,
           salary_min = ?, salary_max = ?, remote_options = ?, excluded_companies = ?,
           keywords = ?, schedule_type = ?, schedule_config = ?, max_applications_per_day = ?,
           updated_at = CURRENT_TIMESTAMP
           WHERE loop_id = ?`,
          [
            JSON.stringify(configuration.jobTitles || []),
            JSON.stringify(configuration.locations || []),
            JSON.stringify(configuration.industries || []),
            JSON.stringify(configuration.experienceLevels || []),
            configuration.salaryMin || null,
            configuration.salaryMax || null,
            configuration.remoteOptions || 'any',
            JSON.stringify(configuration.excludedCompanies || []),
            JSON.stringify(configuration.keywords || []),
            configuration.scheduleType || 'continuous',
            JSON.stringify(configuration.scheduleConfig || {}),
            configuration.maxApplicationsPerDay || 5,
            loopId
          ]
        );
      }

      // Handle loop activation/deactivation
      if (status === 'active') {
        await this.activateLoop(loopId);
      } else if (status === 'paused') {
        await this.pauseLoop(loopId);
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating loop:', error);
      throw new Error('Failed to update loop');
    }
  }

  // Delete a loop
  async deleteLoop(userId, loopId) {
    try {
      // Stop the loop if it's running
      await this.pauseLoop(loopId);

      // Delete loop (cascade will handle related records)
      await this.runQuery(
        'DELETE FROM job_loops WHERE id = ? AND user_id = ?',
        [loopId, userId]
      );

      return { success: true };
    } catch (error) {
      console.error('Error deleting loop:', error);
      throw new Error('Failed to delete loop');
    }
  }

  // Activate a loop
  async activateLoop(loopId) {
    try {
      await this.runQuery(
        `UPDATE job_loops SET status = 'active', 
         next_run_at = DATETIME('now', '+2 hours') WHERE id = ?`,
        [loopId]
      );

      // Schedule the loop for regular execution
      this.scheduleLoop(loopId);
    } catch (error) {
      console.error('Error activating loop:', error);
      throw new Error('Failed to activate loop');
    }
  }

  // Pause a loop
  async pauseLoop(loopId) {
    try {
      await this.runQuery(
        'UPDATE job_loops SET status = \'paused\' WHERE id = ?',
        [loopId]
      );

      // Stop the scheduled execution
      if (this.runningLoops.has(loopId)) {
        this.runningLoops.get(loopId).destroy();
        this.runningLoops.delete(loopId);
      }
    } catch (error) {
      console.error('Error pausing loop:', error);
      throw new Error('Failed to pause loop');
    }
  }

  // Schedule a loop for execution
  scheduleLoop(loopId) {
    if (this.runningLoops.has(loopId)) {
      return; // Already scheduled
    }

    // Run every 2 hours (can be made configurable)
    const task = cron.schedule('0 */2 * * *', async () => {
      await this.executeLoop(loopId);
    }, { scheduled: false });

    this.runningLoops.set(loopId, task);
    task.start();
  }

  // Execute a loop - discover and process jobs
  async executeLoop(loopId) {
    try {
      console.log(`Executing loop ${loopId}`);
      
      // Update last run time
      await this.runQuery(
        `UPDATE job_loops SET last_run_at = CURRENT_TIMESTAMP,
         next_run_at = DATETIME('now', '+2 hours') WHERE id = ?`,
        [loopId]
      );

      // Get loop configuration
      const loop = await this.runQuery(
        `SELECT l.*, lc.* FROM job_loops l 
         JOIN loop_configurations lc ON l.id = lc.loop_id 
         WHERE l.id = ?`,
        [loopId]
      );

      if (!loop || loop.length === 0) {
        console.error(`Loop ${loopId} not found`);
        return;
      }

      const loopData = loop[0];
      
      // Use job discovery service to find new jobs
      const jobDiscovery = require('./jobDiscovery');
      const discoveredJobs = await jobDiscovery.discoverJobs(loopData);

      // Record analytics
      await this.recordLoopMetric(loopId, 'jobs_discovered', discoveredJobs.length);
      
      console.log(`Loop ${loopId} discovered ${discoveredJobs.length} jobs`);
    } catch (error) {
      console.error(`Error executing loop ${loopId}:`, error);
      await this.recordLoopMetric(loopId, 'execution_error', 1);
    }
  }

  // Check and run loops that are due
  async checkAndRunLoops() {
    try {
      const dueLoops = await this.runQuery(
        `SELECT id FROM job_loops 
         WHERE status = 'active' AND (next_run_at IS NULL OR next_run_at <= CURRENT_TIMESTAMP)`
      );

      for (const loop of dueLoops) {
        await this.executeLoop(loop.id);
      }
    } catch (error) {
      console.error('Error checking due loops:', error);
    }
  }

  // Get loop performance analytics
  async getLoopPerformance(userId, loopId) {
    try {
      const analytics = await this.runQuery(
        `SELECT la.metric_type, la.metric_value, la.additional_data, la.recorded_at
         FROM loop_analytics la
         JOIN job_loops l ON la.loop_id = l.id
         WHERE l.user_id = ? AND la.loop_id = ?
         ORDER BY la.recorded_at DESC LIMIT 100`,
        [userId, loopId]
      );

      const discoveredJobs = await this.runQuery(
        `SELECT COUNT(*) as total, 
         COUNT(CASE WHEN status = 'applied' THEN 1 END) as applied
         FROM discovered_jobs 
         WHERE loop_id = ?`,
        [loopId]
      );

      return {
        analytics: analytics.map(a => ({
          metricType: a.metric_type,
          value: a.metric_value,
          data: JSON.parse(a.additional_data || '{}'),
          recordedAt: a.recorded_at
        })),
        summary: {
          totalDiscovered: discoveredJobs[0]?.total || 0,
          totalApplied: discoveredJobs[0]?.applied || 0
        }
      };
    } catch (error) {
      console.error('Error fetching loop performance:', error);
      throw new Error('Failed to fetch loop performance');
    }
  }

  // Record a loop metric
  async recordLoopMetric(loopId, metricType, value, additionalData = {}) {
    try {
      await this.runQuery(
        `INSERT INTO loop_analytics (loop_id, metric_type, metric_value, additional_data)
         VALUES (?, ?, ?, ?)`,
        [loopId, metricType, value, JSON.stringify(additionalData)]
      );
    } catch (error) {
      console.error('Error recording loop metric:', error);
    }
  }

  // Get recently discovered jobs for all user loops
  async getDiscoveredJobs(userId, limit = 50) {
    try {
      const jobs = await this.runQuery(
        `SELECT dj.*, l.name as loop_name 
         FROM discovered_jobs dj
         JOIN job_loops l ON dj.loop_id = l.id
         WHERE l.user_id = ?
         ORDER BY dj.discovered_at DESC LIMIT ?`,
        [userId, limit]
      );

      return jobs.map(job => ({
        id: job.id,
        loopId: job.loop_id,
        loopName: job.loop_name,
        title: job.title,
        company: job.company,
        location: job.location,
        description: job.description,
        url: job.url,
        sourcePlatform: job.source_platform,
        relevanceScore: job.relevance_score,
        matchFactors: JSON.parse(job.match_factors || '{}'),
        status: job.status,
        discoveredAt: job.discovered_at,
        appliedAt: job.applied_at
      }));
    } catch (error) {
      console.error('Error fetching discovered jobs:', error);
      throw new Error('Failed to fetch discovered jobs');
    }
  }

  // Utility method to promisify database queries
  runQuery(query, params = []) {
    return new Promise((resolve, reject) => {
      if (query.trim().toUpperCase().startsWith('SELECT')) {
        this.db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      } else {
        this.db.run(query, params, function(err) {
          if (err) reject(err);
          else resolve({ lastID: this.lastID, changes: this.changes });
        });
      }
    });
  }
}

module.exports = new LoopService();