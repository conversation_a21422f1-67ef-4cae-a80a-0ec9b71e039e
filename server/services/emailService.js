const database = require('../database');
const crypto = require('crypto');

/**
 * Email Service for handling email automation with SendGrid integration
 * Includes development mode fallback for testing without API keys
 */
class EmailService {
  constructor() {
    this.db = database.get();
    this.sendgridApiKey = process.env.SENDGRID_API_KEY;
    this.developmentMode = !this.sendgridApiKey;
    this.baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    
    if (this.developmentMode) {
      console.log('📧 Email Service: Running in development mode (no SendGrid API key)');
    } else {
      console.log('📧 Email Service: SendGrid integration enabled');
    }
  }

  /**
   * Send individual email with tracking
   */
  async sendEmail(userId, recipientEmail, subject, content, campaignId = null) {
    try {
      const trackingId = this.generateTrackingId();
      
      // Process content for personalization and tracking
      const processedContent = this.addTrackingPixel(content, trackingId);
      const processedLinks = this.processLinksForTracking(processedContent, trackingId);
      
      // Log email to database
      const emailLog = await this.logEmail(userId, recipientEmail, subject, processedLinks, trackingId, campaignId);
      
      if (this.developmentMode) {
        // Development mode: simulate sending
        console.log(`📧 [DEV MODE] Email sent to ${recipientEmail}`);
        console.log(`Subject: ${subject}`);
        console.log(`Tracking ID: ${trackingId}`);
        
        // Update log as sent
        await this.updateEmailStatus(emailLog.id, 'sent');
        
        return {
          success: true,
          messageId: `dev_${trackingId}`,
          trackingId: trackingId,
          developmentMode: true
        };
      } else {
        // Production mode: send via SendGrid
        const result = await this.sendViaProvider(recipientEmail, subject, processedLinks);
        
        if (result.success) {
          await this.updateEmailStatus(emailLog.id, 'sent');
        } else {
          await this.updateEmailStatus(emailLog.id, 'failed', result.error);
        }
        
        return {
          ...result,
          trackingId: trackingId
        };
      }
    } catch (error) {
      console.error('Email sending error:', error);
      throw error;
    }
  }

  /**
   * Send bulk email campaign
   */
  async sendCampaign(campaignId) {
    try {
      // Get campaign details
      const campaign = await this.getCampaign(campaignId);
      if (!campaign) {
        throw new Error('Campaign not found');
      }

      // Get template if specified
      let content = campaign.content || '';
      if (campaign.template_id) {
        const template = await this.getTemplate(campaign.template_id);
        if (template) {
          content = template.content;
        }
      }

      // Get recipient list
      const recipients = await this.getCampaignRecipients(campaignId, campaign.target_companies, campaign.target_roles);
      
      if (recipients.length === 0) {
        throw new Error('No recipients found for campaign');
      }

      // Send emails with rate limiting
      let successCount = 0;
      let failureCount = 0;
      
      for (const recipient of recipients) {
        try {
          // Personalize content
          const personalizedSubject = this.personalizeContent(campaign.subject, recipient);
          const personalizedContent = this.personalizeContent(content, recipient);
          
          const result = await this.sendEmail(
            campaign.user_id,
            recipient.email,
            personalizedSubject,
            personalizedContent,
            campaignId
          );
          
          if (result.success) {
            successCount++;
          } else {
            failureCount++;
          }
          
          // Rate limiting: delay between emails
          await this.delay(2000);
          
        } catch (error) {
          console.error(`Failed to send email to ${recipient.email}:`, error);
          failureCount++;
        }
      }

      // Update campaign statistics
      await this.updateCampaignStats(campaignId, successCount, failureCount);
      
      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        total: recipients.length
      };

    } catch (error) {
      console.error('Campaign sending error:', error);
      throw error;
    }
  }

  /**
   * Personalize email content with recipient data
   */
  personalizeContent(content, recipient) {
    let personalized = content;
    
    // Basic personalization variables
    const replacements = {
      '{{name}}': recipient.name || 'there',
      '{{firstName}}': this.extractFirstName(recipient.name),
      '{{company}}': recipient.company || 'your company',
      '{{companyName}}': recipient.company || 'your company',
      '{{role}}': recipient.title || 'professional',
      '{{jobTitle}}': recipient.title || 'professional',
      '{{email}}': recipient.email || '',
      '{{senderName}}': 'Job Seeker', // This could come from user profile
      '{{skills}}': 'relevant skills', // This could come from user profile
      '{{experience}}': 'professional experience' // This could come from user profile
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      personalized = personalized.replace(new RegExp(placeholder, 'g'), value);
    });

    return personalized;
  }

  /**
   * Add tracking pixel to email content
   */
  addTrackingPixel(content, trackingId) {
    const trackingPixel = `<img src="${this.baseUrl}/api/email/track/open/${trackingId}" width="1" height="1" style="display:none;" />`;
    return content + trackingPixel;
  }

  /**
   * Process links for click tracking
   */
  processLinksForTracking(content, trackingId) {
    // Simple link processing - in production, this would be more sophisticated
    return content.replace(
      /<a href="([^"]+)"/g,
      `<a href="${this.baseUrl}/api/email/track/click/${trackingId}?url=$1"`
    );
  }

  /**
   * Generate unique tracking ID
   */
  generateTrackingId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Log email to database
   */
  async logEmail(userId, recipientEmail, subject, content, trackingId, campaignId = null) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO email_logs (user_id, campaign_id, recipient_email, subject, content, tracking_id, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, 'queued', CURRENT_TIMESTAMP)
      `;
      
      this.db.run(query, [userId, campaignId, recipientEmail, subject, content, trackingId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID });
        }
      });
    });
  }

  /**
   * Update email status
   */
  async updateEmailStatus(emailLogId, status, error = null) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE email_logs 
        SET status = ?, send_error = ?, sent_at = CASE WHEN ? = 'sent' THEN CURRENT_TIMESTAMP ELSE sent_at END, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      this.db.run(query, [status, error, status, emailLogId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Get campaign details
   */
  async getCampaign(campaignId) {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM email_campaigns WHERE id = ?';
      this.db.get(query, [campaignId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * Get email template
   */
  async getTemplate(templateId) {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM email_templates WHERE id = ?';
      this.db.get(query, [templateId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * Get campaign recipients
   */
  async getCampaignRecipients(campaignId, targetCompanies, targetRoles) {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM recruiter_contacts WHERE 1=1';
      const params = [];
      
      if (targetCompanies) {
        const companies = JSON.parse(targetCompanies || '[]');
        if (companies.length > 0) {
          query += ` AND company IN (${companies.map(() => '?').join(',')})`;
          params.push(...companies);
        }
      }
      
      if (targetRoles) {
        const roles = JSON.parse(targetRoles || '[]');
        if (roles.length > 0) {
          query += ` AND (${roles.map(() => 'title LIKE ?').join(' OR ')})`;
          params.push(...roles.map(role => `%${role}%`));
        }
      }
      
      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Update campaign statistics
   */
  async updateCampaignStats(campaignId, sent, failed) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE email_campaigns 
        SET sent_count = sent_count + ?, status = 'sent', updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      this.db.run(query, [sent, campaignId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Track email opens
   */
  async trackEmailOpen(trackingId) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE email_logs 
        SET opened_at = COALESCE(opened_at, CURRENT_TIMESTAMP)
        WHERE tracking_id = ?
      `;
      
      this.db.run(query, [trackingId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Track email clicks
   */
  async trackEmailClick(trackingId, url) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE email_logs 
        SET clicked_at = COALESCE(clicked_at, CURRENT_TIMESTAMP)
        WHERE tracking_id = ?
      `;
      
      this.db.run(query, [trackingId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes, redirectUrl: url });
        }
      });
    });
  }

  /**
   * Get email analytics for campaigns
   */
  async getEmailAnalytics(userId, campaignId = null) {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT 
          COUNT(*) as total_sent,
          COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as total_opened,
          COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as total_clicked,
          COUNT(CASE WHEN replied_at IS NOT NULL THEN 1 END) as total_replied,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as total_failed
        FROM email_logs 
        WHERE user_id = ?
      `;
      
      const params = [userId];
      
      if (campaignId) {
        query += ' AND campaign_id = ?';
        params.push(campaignId);
      }
      
      this.db.get(query, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          const analytics = {
            ...row,
            open_rate: row.total_sent > 0 ? (row.total_opened / row.total_sent * 100).toFixed(2) : 0,
            click_rate: row.total_sent > 0 ? (row.total_clicked / row.total_sent * 100).toFixed(2) : 0,
            reply_rate: row.total_sent > 0 ? (row.total_replied / row.total_sent * 100).toFixed(2) : 0
          };
          resolve(analytics);
        }
      });
    });
  }

  /**
   * Utility functions
   */
  extractFirstName(fullName) {
    if (!fullName) return 'there';
    return fullName.split(' ')[0];
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Send via email provider (placeholder for SendGrid integration)
   */
  async sendViaProvider(recipientEmail, subject, content) {
    // In production, this would integrate with SendGrid API
    // For now, returning success for development
    return {
      success: true,
      messageId: `sg_${Date.now()}`,
      providedBy: 'sendgrid'
    };
  }
}

module.exports = EmailService;