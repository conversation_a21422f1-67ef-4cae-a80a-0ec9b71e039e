const { EventEmitter } = require('events');
const { spawn } = require('child_process');
const os = require('os');

/**
 * Real-time Performance Monitoring Service
 * Provides comprehensive system and application monitoring with optimization recommendations
 */
class MonitoringService extends EventEmitter {
  constructor() {
    super();
    
    this.metrics = new Map();
    this.alerts = [];
    this.performanceHistory = [];
    this.resourceTracking = new Map();
    this.anomalyDetection = new Map();
    
    this.config = {
      monitoring: {
        interval: 5000, // 5 seconds
        historyRetention: 1000, // Keep last 1000 measurements
        alertThresholds: {
          cpu: 80, // 80%
          memory: 85, // 85%
          diskUsage: 90, // 90%
          responseTime: 5000, // 5 seconds
          errorRate: 5 // 5%
        }
      },
      anomaly: {
        detectionWindow: 50, // Last 50 measurements
        deviationThreshold: 2, // 2 standard deviations
        minimumSamples: 10
      },
      optimization: {
        analysisInterval: 60000, // 1 minute
        recommendationThreshold: 0.7 // 70% confidence
      }
    };
    
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.optimizationInterval = null;
    
    this.initializeMetrics();
  }

  /**
   * Initialize monitoring metrics
   */
  initializeMetrics() {
    const metricDefinitions = [
      { name: 'system.cpu.usage', type: 'gauge', description: 'CPU usage percentage' },
      { name: 'system.memory.usage', type: 'gauge', description: 'Memory usage percentage' },
      { name: 'system.memory.available', type: 'gauge', description: 'Available memory in bytes' },
      { name: 'system.disk.usage', type: 'gauge', description: 'Disk usage percentage' },
      { name: 'system.network.io.bytes_sent', type: 'counter', description: 'Network bytes sent' },
      { name: 'system.network.io.bytes_received', type: 'counter', description: 'Network bytes received' },
      { name: 'application.requests.total', type: 'counter', description: 'Total requests processed' },
      { name: 'application.requests.duration', type: 'histogram', description: 'Request duration' },
      { name: 'application.errors.total', type: 'counter', description: 'Total errors' },
      { name: 'application.jobs.active', type: 'gauge', description: 'Active jobs count' },
      { name: 'application.jobs.completed', type: 'counter', description: 'Completed jobs count' },
      { name: 'application.jobs.failed', type: 'counter', description: 'Failed jobs count' },
      { name: 'application.containers.active', type: 'gauge', description: 'Active containers' },
      { name: 'application.sandboxes.active', type: 'gauge', description: 'Active sandboxes' },
      { name: 'browser.sessions.active', type: 'gauge', description: 'Active browser sessions' },
      { name: 'browser.automation.success_rate', type: 'gauge', description: 'Browser automation success rate' }
    ];

    for (const metric of metricDefinitions) {
      this.metrics.set(metric.name, {
        ...metric,
        value: metric.type === 'counter' ? 0 : null,
        lastUpdated: null,
        history: []
      });
    }

    console.log(`📊 Initialized ${metricDefinitions.length} monitoring metrics`);
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️ Monitoring already active');
      return;
    }

    this.isMonitoring = true;
    console.log('🔍 Starting performance monitoring...');

    // Start system monitoring
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, this.config.monitoring.interval);

    // Start optimization analysis
    this.optimizationInterval = setInterval(() => {
      this.analyzePerformance();
    }, this.config.optimization.analysisInterval);

    this.emit('monitoringStarted');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      console.log('⚠️ Monitoring not active');
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }

    console.log('⏹️ Monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Collect system metrics
   */
  async collectSystemMetrics() {
    try {
      const timestamp = Date.now();
      
      // CPU metrics
      const cpuUsage = await this.getCPUUsage();
      this.setMetric('system.cpu.usage', cpuUsage, timestamp);

      // Memory metrics
      const memory = this.getMemoryMetrics();
      this.setMetric('system.memory.usage', memory.usagePercent, timestamp);
      this.setMetric('system.memory.available', memory.available, timestamp);

      // Disk metrics
      const diskUsage = await this.getDiskUsage();
      this.setMetric('system.disk.usage', diskUsage, timestamp);

      // Network metrics
      const networkStats = await this.getNetworkStats();
      if (networkStats) {
        this.setMetric('system.network.io.bytes_sent', networkStats.bytesSent, timestamp);
        this.setMetric('system.network.io.bytes_received', networkStats.bytesReceived, timestamp);
      }

      // Check for alerts
      this.checkAlerts(timestamp);

      // Detect anomalies
      this.detectAnomalies(timestamp);

    } catch (error) {
      console.error('❌ Error collecting system metrics:', error);
    }
  }

  /**
   * Get CPU usage percentage
   */
  async getCPUUsage() {
    return new Promise((resolve) => {
      const start = process.cpuUsage();
      const startTime = Date.now();

      setTimeout(() => {
        const end = process.cpuUsage(start);
        const elapsedTime = Date.now() - startTime;
        
        const totalCPUTime = (end.user + end.system) / 1000; // Convert to milliseconds
        const cpuUsage = (totalCPUTime / elapsedTime) * 100;
        
        resolve(Math.min(100, Math.max(0, cpuUsage)));
      }, 100);
    });
  }

  /**
   * Get memory metrics
   */
  getMemoryMetrics() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    
    return {
      total: totalMemory,
      used: usedMemory,
      available: freeMemory,
      usagePercent: (usedMemory / totalMemory) * 100
    };
  }

  /**
   * Get disk usage percentage
   */
  async getDiskUsage() {
    return new Promise((resolve) => {
      const dfProcess = spawn('df', ['-h', '/']);
      let output = '';

      dfProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      dfProcess.on('close', () => {
        try {
          const lines = output.trim().split('\n');
          if (lines.length >= 2) {
            const diskLine = lines[1].split(/\s+/);
            const usage = diskLine[4]?.replace('%', '');
            resolve(parseInt(usage) || 0);
          } else {
            resolve(0);
          }
        } catch (error) {
          resolve(0);
        }
      });

      dfProcess.on('error', () => {
        resolve(0);
      });
    });
  }

  /**
   * Get network statistics
   */
  async getNetworkStats() {
    try {
      // For Linux systems
      const stats = await this.readFile('/proc/net/dev');
      const lines = stats.split('\n');
      
      let totalBytesSent = 0;
      let totalBytesReceived = 0;

      for (const line of lines) {
        if (line.includes(':') && !line.includes('lo:')) { // Skip loopback
          const parts = line.split(/\s+/).filter(p => p);
          if (parts.length >= 10) {
            totalBytesReceived += parseInt(parts[1]) || 0;
            totalBytesSent += parseInt(parts[9]) || 0;
          }
        }
      }

      return {
        bytesReceived: totalBytesReceived,
        bytesSent: totalBytesSent
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Set metric value
   */
  setMetric(name, value, timestamp = Date.now()) {
    let metric = this.metrics.get(name);
    
    // Create metric if it doesn't exist
    if (!metric) {
      metric = {
        name,
        type: 'gauge', // Default type
        description: `Dynamic metric: ${name}`,
        value: null,
        lastUpdated: null,
        history: []
      };
      this.metrics.set(name, metric);
    }

    const previousValue = metric.value;
    metric.value = value;
    metric.lastUpdated = timestamp;

    // Add to history
    metric.history.push({ value, timestamp });
    
    // Maintain history limit
    if (metric.history.length > this.config.monitoring.historyRetention) {
      metric.history = metric.history.slice(-this.config.monitoring.historyRetention);
    }

    // Emit metric update event
    this.emit('metricUpdated', { name, value, previousValue, timestamp });
  }

  /**
   * Get metric value
   */
  getMetric(name) {
    const metric = this.metrics.get(name);
    return metric ? { value: metric.value, lastUpdated: metric.lastUpdated } : null;
  }

  /**
   * Get metric history
   */
  getMetricHistory(name, limit = 100) {
    const metric = this.metrics.get(name);
    if (!metric) return [];
    
    return metric.history.slice(-limit);
  }

  /**
   * Check for alert conditions
   */
  checkAlerts(timestamp) {
    const thresholds = this.config.monitoring.alertThresholds;
    const alerts = [];

    // CPU usage alert
    const cpuUsage = this.getMetric('system.cpu.usage')?.value;
    if (cpuUsage && cpuUsage > thresholds.cpu) {
      alerts.push({
        type: 'HIGH_CPU_USAGE',
        severity: 'warning',
        message: `CPU usage is ${cpuUsage.toFixed(1)}% (threshold: ${thresholds.cpu}%)`,
        value: cpuUsage,
        threshold: thresholds.cpu
      });
    }

    // Memory usage alert
    const memoryUsage = this.getMetric('system.memory.usage')?.value;
    if (memoryUsage && memoryUsage > thresholds.memory) {
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        severity: 'warning',
        message: `Memory usage is ${memoryUsage.toFixed(1)}% (threshold: ${thresholds.memory}%)`,
        value: memoryUsage,
        threshold: thresholds.memory
      });
    }

    // Disk usage alert
    const diskUsage = this.getMetric('system.disk.usage')?.value;
    if (diskUsage && diskUsage > thresholds.diskUsage) {
      alerts.push({
        type: 'HIGH_DISK_USAGE',
        severity: 'critical',
        message: `Disk usage is ${diskUsage}% (threshold: ${thresholds.diskUsage}%)`,
        value: diskUsage,
        threshold: thresholds.diskUsage
      });
    }

    // Process alerts
    for (const alert of alerts) {
      this.processAlert(alert, timestamp);
    }
  }

  /**
   * Process and log alerts
   */
  processAlert(alert, timestamp) {
    const alertId = `${alert.type}_${timestamp}`;
    const existingAlert = this.alerts.find(a => 
      a.type === alert.type && 
      timestamp - a.timestamp < 300000 // 5 minutes
    );

    if (existingAlert) {
      return; // Avoid duplicate alerts
    }

    const alertRecord = {
      id: alertId,
      ...alert,
      timestamp,
      acknowledged: false
    };

    this.alerts.push(alertRecord);

    // Maintain alert history
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }

    console.log(`🚨 ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`);
    this.emit('alertTriggered', alertRecord);
  }

  /**
   * Detect performance anomalies
   */
  detectAnomalies(timestamp) {
    const metricsToAnalyze = [
      'system.cpu.usage',
      'system.memory.usage',
      'application.requests.duration'
    ];

    for (const metricName of metricsToAnalyze) {
      const metric = this.metrics.get(metricName);
      if (!metric || metric.history.length < this.config.anomaly.minimumSamples) {
        continue;
      }

      const recentData = metric.history.slice(-this.config.anomaly.detectionWindow);
      const values = recentData.map(d => d.value);
      
      const stats = this.calculateStatistics(values);
      const currentValue = metric.value;
      
      // Check if current value is anomalous
      const zScore = Math.abs((currentValue - stats.mean) / stats.stdDev);
      
      if (zScore > this.config.anomaly.deviationThreshold) {
        this.recordAnomaly(metricName, currentValue, stats, zScore, timestamp);
      }
    }
  }

  /**
   * Record performance anomaly
   */
  recordAnomaly(metricName, value, stats, zScore, timestamp) {
    const anomalyKey = `${metricName}_${Math.floor(timestamp / 60000)}`; // Group by minute
    
    if (this.anomalyDetection.has(anomalyKey)) {
      return; // Already recorded for this minute
    }

    const anomaly = {
      metric: metricName,
      value,
      expectedRange: {
        mean: stats.mean,
        stdDev: stats.stdDev,
        min: stats.mean - (2 * stats.stdDev),
        max: stats.mean + (2 * stats.stdDev)
      },
      zScore,
      severity: zScore > 3 ? 'high' : 'medium',
      timestamp
    };

    this.anomalyDetection.set(anomalyKey, anomaly);

    console.log(`📈 ANOMALY DETECTED: ${metricName} = ${value} (z-score: ${zScore.toFixed(2)})`);
    this.emit('anomalyDetected', anomaly);
  }

  /**
   * Calculate statistics for anomaly detection
   */
  calculateStatistics(values) {
    const n = values.length;
    const mean = values.reduce((sum, val) => sum + val, 0) / n;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;
    const stdDev = Math.sqrt(variance);

    return {
      count: n,
      mean,
      variance,
      stdDev,
      min: Math.min(...values),
      max: Math.max(...values)
    };
  }

  /**
   * Analyze performance and generate recommendations
   */
  analyzePerformance() {
    try {
      const recommendations = [];
      
      // Analyze CPU performance
      const cpuRecommendations = this.analyzeCPUPerformance();
      recommendations.push(...cpuRecommendations);

      // Analyze memory performance
      const memoryRecommendations = this.analyzeMemoryPerformance();
      recommendations.push(...memoryRecommendations);

      // Analyze application performance
      const appRecommendations = this.analyzeApplicationPerformance();
      recommendations.push(...appRecommendations);

      // Process recommendations
      if (recommendations.length > 0) {
        console.log(`💡 Generated ${recommendations.length} performance recommendations`);
        this.emit('recommendationsGenerated', recommendations);
        
        // Store recent recommendations
        this.performanceHistory.push({
          timestamp: Date.now(),
          recommendations
        });

        // Maintain history
        if (this.performanceHistory.length > 50) {
          this.performanceHistory = this.performanceHistory.slice(-50);
        }
      }

    } catch (error) {
      console.error('❌ Error analyzing performance:', error);
    }
  }

  /**
   * Analyze CPU performance
   */
  analyzeCPUPerformance() {
    const recommendations = [];
    const cpuHistory = this.getMetricHistory('system.cpu.usage', 20);
    
    if (cpuHistory.length < 10) return recommendations;

    const recentCPU = cpuHistory.slice(-10).map(h => h.value);
    const avgCPU = recentCPU.reduce((sum, val) => sum + val, 0) / recentCPU.length;

    if (avgCPU > 70) {
      recommendations.push({
        type: 'cpu_optimization',
        priority: 'high',
        title: 'High CPU Usage Detected',
        description: `Average CPU usage is ${avgCPU.toFixed(1)}% over the last 10 measurements`,
        recommendations: [
          'Consider reducing concurrent job executions',
          'Optimize browser automation scripts',
          'Implement job queuing with rate limiting',
          'Consider horizontal scaling'
        ],
        confidence: 0.8
      });
    }

    return recommendations;
  }

  /**
   * Analyze memory performance
   */
  analyzeMemoryPerformance() {
    const recommendations = [];
    const memoryHistory = this.getMetricHistory('system.memory.usage', 20);
    
    if (memoryHistory.length < 10) return recommendations;

    const recentMemory = memoryHistory.slice(-10).map(h => h.value);
    const avgMemory = recentMemory.reduce((sum, val) => sum + val, 0) / recentMemory.length;

    if (avgMemory > 75) {
      recommendations.push({
        type: 'memory_optimization',
        priority: 'high',
        title: 'High Memory Usage Detected',
        description: `Average memory usage is ${avgMemory.toFixed(1)}% over the last 10 measurements`,
        recommendations: [
          'Implement memory-efficient browser session management',
          'Add garbage collection optimization',
          'Consider reducing browser instances',
          'Implement result caching with TTL'
        ],
        confidence: 0.85
      });
    }

    return recommendations;
  }

  /**
   * Analyze application performance
   */
  analyzeApplicationPerformance() {
    const recommendations = [];
    
    // Analyze job success rates
    const activeJobs = this.getMetric('application.jobs.active')?.value || 0;
    const failedJobs = this.getMetric('application.jobs.failed')?.value || 0;
    const completedJobs = this.getMetric('application.jobs.completed')?.value || 0;
    
    if (completedJobs + failedJobs > 0) {
      const successRate = completedJobs / (completedJobs + failedJobs);
      
      if (successRate < 0.8) {
        recommendations.push({
          type: 'job_reliability',
          priority: 'medium',
          title: 'Low Job Success Rate',
          description: `Job success rate is ${(successRate * 100).toFixed(1)}%`,
          recommendations: [
            'Implement better error handling and retry logic',
            'Add platform-specific optimization',
            'Improve anti-detection mechanisms',
            'Add job validation before execution'
          ],
          confidence: 0.75
        });
      }
    }

    return recommendations;
  }

  /**
   * Track application-specific metrics
   */
  trackJobExecution(jobId, startTime) {
    this.resourceTracking.set(jobId, {
      startTime,
      initialCPU: this.getMetric('system.cpu.usage')?.value || 0,
      initialMemory: this.getMetric('system.memory.usage')?.value || 0
    });

    // Increment active jobs
    const activeJobs = this.getMetric('application.jobs.active')?.value || 0;
    this.setMetric('application.jobs.active', activeJobs + 1);
  }

  /**
   * Track job completion
   */
  trackJobCompletion(jobId, success = true, duration = null) {
    const tracking = this.resourceTracking.get(jobId);
    if (tracking) {
      const actualDuration = duration || (Date.now() - tracking.startTime);
      
      // Update metrics
      const activeJobs = this.getMetric('application.jobs.active')?.value || 0;
      this.setMetric('application.jobs.active', Math.max(0, activeJobs - 1));
      
      if (success) {
        const completed = this.getMetric('application.jobs.completed')?.value || 0;
        this.setMetric('application.jobs.completed', completed + 1);
      } else {
        const failed = this.getMetric('application.jobs.failed')?.value || 0;
        this.setMetric('application.jobs.failed', failed + 1);
      }

      // Track duration
      this.setMetric('application.requests.duration', actualDuration);

      this.resourceTracking.delete(jobId);
    }
  }

  /**
   * Get comprehensive monitoring dashboard data
   */
  getDashboardData() {
    const currentTime = Date.now();
    
    return {
      timestamp: currentTime,
      system: {
        cpu: this.getMetric('system.cpu.usage'),
        memory: this.getMetric('system.memory.usage'),
        disk: this.getMetric('system.disk.usage'),
        uptime: process.uptime()
      },
      application: {
        activeJobs: this.getMetric('application.jobs.active'),
        completedJobs: this.getMetric('application.jobs.completed'),
        failedJobs: this.getMetric('application.jobs.failed'),
        activeContainers: this.getMetric('application.containers.active'),
        activeSandboxes: this.getMetric('application.sandboxes.active')
      },
      alerts: {
        active: this.alerts.filter(a => !a.acknowledged),
        total: this.alerts.length
      },
      anomalies: {
        recent: Array.from(this.anomalyDetection.values()).filter(a => 
          currentTime - a.timestamp < 3600000 // Last hour
        ),
        total: this.anomalyDetection.size
      },
      recommendations: this.performanceHistory.slice(-5).flatMap(h => h.recommendations)
    };
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(hours = 24) {
    const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
    const trends = {};

    for (const [metricName, metric] of this.metrics) {
      const recentHistory = metric.history.filter(h => h.timestamp > cutoffTime);
      
      if (recentHistory.length > 0) {
        const values = recentHistory.map(h => h.value);
        const stats = this.calculateStatistics(values);
        
        trends[metricName] = {
          current: metric.value,
          average: stats.mean,
          min: stats.min,
          max: stats.max,
          trend: this.calculateTrend(recentHistory),
          dataPoints: recentHistory.length
        };
      }
    }

    return trends;
  }

  /**
   * Calculate trend direction
   */
  calculateTrend(history) {
    if (history.length < 2) return 'stable';
    
    const firstHalf = history.slice(0, Math.floor(history.length / 2));
    const secondHalf = history.slice(Math.floor(history.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, h) => sum + h.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, h) => sum + h.value, 0) / secondHalf.length;
    
    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (Math.abs(changePercent) < 5) return 'stable';
    return changePercent > 0 ? 'increasing' : 'decreasing';
  }

  /**
   * Utility method to read files (async)
   */
  async readFile(filePath) {
    const fs = require('fs').promises;
    return await fs.readFile(filePath, 'utf8');
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      monitoring: this.isMonitoring,
      metrics: this.metrics.size,
      alerts: this.alerts.filter(a => !a.acknowledged).length,
      anomalies: this.anomalyDetection.size,
      lastUpdate: Math.max(...Array.from(this.metrics.values()).map(m => m.lastUpdated || 0)),
      status: 'healthy'
    };
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = Date.now();
      this.emit('alertAcknowledged', alert);
      return true;
    }
    return false;
  }
}

module.exports = MonitoringService;