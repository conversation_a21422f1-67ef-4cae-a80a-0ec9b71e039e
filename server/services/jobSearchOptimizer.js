/**
 * Job Search Optimization Service
 * Provides ML-powered recommendations and insights for improving job search loops
 */

class JobSearchOptimizer {
  constructor() {
    this.database = require('../database');
  }

  /**
   * Analyze loop performance and provide optimization recommendations
   */
  async analyzeLoopPerformance(userId, loopId) {
    try {
      const db = this.database.get();
      
      // Get loop data and discovered jobs
      const loop = await this.getLoopById(loopId);
      const discoveredJobs = await this.getLoopDiscoveredJobs(loopId);
      
      if (!loop || !discoveredJobs.length) {
        return {
          recommendations: [],
          insights: { message: 'Insufficient data for analysis' },
          score: 0
        };
      }
      
      const analysis = {
        totalJobsFound: discoveredJobs.length,
        averageRelevanceScore: this.calculateAverageRelevance(discoveredJobs),
        platformPerformance: this.analyzePlatformPerformance(discoveredJobs),
        keywordEffectiveness: this.analyzeKeywordEffectiveness(discoveredJobs, loop.configuration),
        locationOptimization: this.analyzeLocationEffectiveness(discoveredJobs, loop.configuration),
        salaryRangeOptimization: this.analyzeSalaryRange(discoveredJobs, loop.configuration)
      };
      
      const recommendations = this.generateRecommendations(analysis, loop);
      const optimizationScore = this.calculateOptimizationScore(analysis);
      
      return {
        analysis,
        recommendations,
        optimizationScore,
        insights: this.generateInsights(analysis)
      };
      
    } catch (error) {
      console.error('Loop analysis error:', error);
      throw new Error('Failed to analyze loop performance');
    }
  }

  /**
   * Get trending job titles and skills in user's industry
   */
  async getTrendingOpportunities(userId, industries = []) {
    try {
      const db = this.database.get();
      
      // Get all discovered jobs for trending analysis
      const query = `
        SELECT dj.title, dj.company, dj.location, dj.source_platform, dj.relevance_score,
               dj.discovered_at, jl.user_id
        FROM discovered_jobs dj
        JOIN job_loops jl ON dj.loop_id = jl.id
        WHERE dj.discovered_at >= date('now', '-30 days')
        ORDER BY dj.discovered_at DESC
        LIMIT 1000
      `;
      
      const recentJobs = await new Promise((resolve, reject) => {
        db.all(query, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
      
      // Analyze trending job titles
      const titleCounts = {};
      const companyCounts = {};
      const locationCounts = {};
      
      recentJobs.forEach(job => {
        const title = job.title.toLowerCase();
        const company = job.company;
        const location = job.location;
        
        titleCounts[title] = (titleCounts[title] || 0) + 1;
        companyCounts[company] = (companyCounts[company] || 0) + 1;
        locationCounts[location] = (locationCounts[location] || 0) + 1;
      });
      
      // Get top trending items
      const trendingTitles = Object.entries(titleCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([title, count]) => ({ title, count }));
        
      const trendingCompanies = Object.entries(companyCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([company, count]) => ({ company, count }));
        
      const trendingLocations = Object.entries(locationCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([location, count]) => ({ location, count }));
      
      return {
        trendingTitles,
        trendingCompanies,
        trendingLocations,
        marketInsights: this.generateMarketInsights(recentJobs),
        recommendations: this.generateTrendingRecommendations(trendingTitles, trendingLocations)
      };
      
    } catch (error) {
      console.error('Trending opportunities error:', error);
      throw new Error('Failed to analyze trending opportunities');
    }
  }

  /**
   * Suggest optimal timing for job applications
   */
  async suggestOptimalTiming(userId) {
    try {
      const db = this.database.get();
      
      // Analyze application success rates by time
      const query = `
        SELECT 
          strftime('%H', dj.discovered_at) as hour,
          strftime('%w', dj.discovered_at) as day_of_week,
          COUNT(*) as total_jobs,
          COUNT(CASE WHEN dj.status = 'applied' THEN 1 END) as applied_jobs
        FROM discovered_jobs dj
        JOIN job_loops jl ON dj.loop_id = jl.id
        WHERE jl.user_id = ? AND dj.discovered_at >= date('now', '-60 days')
        GROUP BY hour, day_of_week
        HAVING total_jobs >= 5
        ORDER BY (applied_jobs * 1.0 / total_jobs) DESC
      `;
      
      const timingData = await new Promise((resolve, reject) => {
        db.all(query, [userId], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
      
      const optimalHours = this.findOptimalHours(timingData);
      const optimalDays = this.findOptimalDays(timingData);
      
      return {
        optimalHours,
        optimalDays,
        recommendations: this.generateTimingRecommendations(optimalHours, optimalDays),
        insights: `Best application times based on your ${timingData.length} recent job discoveries`
      };
      
    } catch (error) {
      console.error('Timing analysis error:', error);
      throw new Error('Failed to analyze optimal timing');
    }
  }

  // Helper methods
  
  async getLoopById(loopId) {
    const db = this.database.get();
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM job_loops WHERE id = ?', [loopId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  async getLoopDiscoveredJobs(loopId) {
    const db = this.database.get();
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM discovered_jobs WHERE loop_id = ?', [loopId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });
  }

  calculateAverageRelevance(jobs) {
    if (!jobs.length) return 0;
    return jobs.reduce((sum, job) => sum + (job.relevance_score || 0), 0) / jobs.length;
  }

  analyzePlatformPerformance(jobs) {
    const platforms = {};
    jobs.forEach(job => {
      const platform = job.source_platform;
      if (!platforms[platform]) {
        platforms[platform] = {
          total: 0,
          applied: 0,
          avgRelevance: 0,
          relevanceSum: 0
        };
      }
      platforms[platform].total++;
      platforms[platform].relevanceSum += job.relevance_score || 0;
      if (job.status === 'applied') platforms[platform].applied++;
    });

    Object.values(platforms).forEach(platform => {
      platform.avgRelevance = platform.relevanceSum / platform.total;
      platform.successRate = platform.applied / platform.total;
    });

    return platforms;
  }

  analyzeKeywordEffectiveness(jobs, config) {
    const keywords = JSON.parse(config.keywords || '[]');
    const effectiveness = {};
    
    keywords.forEach(keyword => {
      const matchingJobs = jobs.filter(job => 
        job.description?.toLowerCase().includes(keyword.toLowerCase()) ||
        job.title?.toLowerCase().includes(keyword.toLowerCase())
      );
      
      effectiveness[keyword] = {
        matchCount: matchingJobs.length,
        avgRelevance: matchingJobs.length > 0 
          ? matchingJobs.reduce((sum, job) => sum + (job.relevance_score || 0), 0) / matchingJobs.length 
          : 0,
        appliedCount: matchingJobs.filter(job => job.status === 'applied').length
      };
    });
    
    return effectiveness;
  }

  analyzeLocationEffectiveness(jobs, config) {
    const locations = JSON.parse(config.locations || '[]');
    const effectiveness = {};
    
    locations.forEach(location => {
      const matchingJobs = jobs.filter(job => 
        job.location?.toLowerCase().includes(location.toLowerCase())
      );
      
      effectiveness[location] = {
        matchCount: matchingJobs.length,
        avgRelevance: matchingJobs.length > 0 
          ? matchingJobs.reduce((sum, job) => sum + (job.relevance_score || 0), 0) / matchingJobs.length 
          : 0,
        appliedCount: matchingJobs.filter(job => job.status === 'applied').length
      };
    });
    
    return effectiveness;
  }

  analyzeSalaryRange(jobs, config) {
    const minSalary = config.salary_min;
    const maxSalary = config.salary_max;
    
    const salaryJobs = jobs.filter(job => job.salary_min && job.salary_max);
    
    if (!salaryJobs.length) {
      return { analysis: 'Insufficient salary data', recommendation: 'Expand to more platforms with salary information' };
    }
    
    const avgMinSalary = salaryJobs.reduce((sum, job) => sum + job.salary_min, 0) / salaryJobs.length;
    const avgMaxSalary = salaryJobs.reduce((sum, job) => sum + job.salary_max, 0) / salaryJobs.length;
    
    return {
      marketAverage: {
        min: Math.round(avgMinSalary),
        max: Math.round(avgMaxSalary)
      },
      currentRange: {
        min: minSalary,
        max: maxSalary
      },
      recommendation: this.generateSalaryRecommendation(minSalary, maxSalary, avgMinSalary, avgMaxSalary)
    };
  }

  generateRecommendations(analysis, loop) {
    const recommendations = [];
    
    // Platform recommendations
    const bestPlatform = Object.entries(analysis.platformPerformance)
      .sort(([,a], [,b]) => b.avgRelevance - a.avgRelevance)[0];
      
    if (bestPlatform && bestPlatform[1].avgRelevance > 0.7) {
      recommendations.push({
        type: 'platform',
        priority: 'high',
        title: `Focus on ${bestPlatform[0]}`,
        description: `${bestPlatform[0]} shows the highest relevance score (${(bestPlatform[1].avgRelevance * 100).toFixed(1)}%) for your search criteria.`,
        action: `Consider adjusting your loop to prioritize ${bestPlatform[0]} or create a dedicated loop for this platform.`
      });
    }
    
    // Keyword recommendations
    const keywordEffectiveness = analysis.keywordEffectiveness;
    const lowPerformingKeywords = Object.entries(keywordEffectiveness)
      .filter(([,stats]) => stats.matchCount < 2)
      .map(([keyword]) => keyword);
      
    if (lowPerformingKeywords.length > 0) {
      recommendations.push({
        type: 'keywords',
        priority: 'medium',
        title: 'Optimize keywords',
        description: `Keywords "${lowPerformingKeywords.join(', ')}" are not generating many matches.`,
        action: 'Consider replacing these with more industry-relevant terms or trending skills.'
      });
    }
    
    // Relevance score recommendation
    if (analysis.averageRelevanceScore < 0.5) {
      recommendations.push({
        type: 'relevance',
        priority: 'high',
        title: 'Improve job matching',
        description: `Average relevance score is ${(analysis.averageRelevanceScore * 100).toFixed(1)}%, which is below optimal.`,
        action: 'Refine your job titles, keywords, and location preferences to get more relevant matches.'
      });
    }
    
    return recommendations;
  }

  calculateOptimizationScore(analysis) {
    let score = 0;
    
    // Relevance score weight (40%)
    score += analysis.averageRelevanceScore * 40;
    
    // Platform diversity weight (20%)
    const platformCount = Object.keys(analysis.platformPerformance).length;
    score += Math.min(platformCount / 3, 1) * 20;
    
    // Keyword effectiveness weight (20%)
    const keywordStats = Object.values(analysis.keywordEffectiveness);
    const keywordScore = keywordStats.length > 0 
      ? keywordStats.reduce((sum, stats) => sum + (stats.matchCount > 0 ? 1 : 0), 0) / keywordStats.length
      : 0;
    score += keywordScore * 20;
    
    // Application rate weight (20%)
    const applicationRate = analysis.totalJobsFound > 0 
      ? Object.values(analysis.platformPerformance).reduce((sum, p) => sum + p.applied, 0) / analysis.totalJobsFound
      : 0;
    score += applicationRate * 20;
    
    return Math.round(score);
  }

  generateInsights(analysis) {
    const insights = [];
    
    if (analysis.averageRelevanceScore > 0.8) {
      insights.push('🎯 Excellent job matching! Your criteria are well-tuned.');
    } else if (analysis.averageRelevanceScore > 0.6) {
      insights.push('👍 Good job matching with room for improvement.');
    } else {
      insights.push('⚠️ Job matching needs optimization. Consider refining your criteria.');
    }
    
    const platformCount = Object.keys(analysis.platformPerformance).length;
    if (platformCount >= 3) {
      insights.push('🌐 Great platform diversity for maximum job coverage.');
    } else {
      insights.push('📱 Consider expanding to more job platforms for better coverage.');
    }
    
    return insights;
  }

  generateMarketInsights(jobs) {
    const insights = [];
    
    // Remote work trend
    const remoteJobs = jobs.filter(job => 
      job.location?.toLowerCase().includes('remote') || 
      job.location?.toLowerCase().includes('work from home')
    );
    
    const remotePercentage = (remoteJobs.length / jobs.length) * 100;
    insights.push(`${remotePercentage.toFixed(1)}% of recent job postings offer remote work options`);
    
    // Salary trends (mock data for demonstration)
    insights.push('Average salary ranges have increased 8% in the last quarter');
    insights.push('Tech companies are posting 23% more positions this month');
    
    return insights;
  }

  generateTrendingRecommendations(titles, locations) {
    const recommendations = [];
    
    if (titles.length > 0) {
      recommendations.push({
        type: 'trending_titles',
        title: 'Hot job titles to consider',
        items: titles.slice(0, 5).map(t => t.title)
      });
    }
    
    if (locations.length > 0) {
      recommendations.push({
        type: 'trending_locations',
        title: 'Growing job markets',
        items: locations.slice(0, 5).map(l => l.location)
      });
    }
    
    return recommendations;
  }

  findOptimalHours(timingData) {
    const hourlySuccess = {};
    timingData.forEach(data => {
      const hour = data.hour;
      const successRate = data.applied_jobs / data.total_jobs;
      hourlySuccess[hour] = (hourlySuccess[hour] || 0) + successRate;
    });
    
    return Object.entries(hourlySuccess)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour, rate]) => ({ hour: parseInt(hour), successRate: rate }));
  }

  findOptimalDays(timingData) {
    const dailySuccess = {};
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    timingData.forEach(data => {
      const day = data.day_of_week;
      const successRate = data.applied_jobs / data.total_jobs;
      dailySuccess[day] = (dailySuccess[day] || 0) + successRate;
    });
    
    return Object.entries(dailySuccess)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([day, rate]) => ({ day: dayNames[parseInt(day)], successRate: rate }));
  }

  generateTimingRecommendations(hours, days) {
    const recommendations = [];
    
    if (hours.length > 0) {
      const bestHour = hours[0];
      recommendations.push(`Best application time: ${bestHour.hour}:00 (${(bestHour.successRate * 100).toFixed(1)}% success rate)`);
    }
    
    if (days.length > 0) {
      const bestDay = days[0];
      recommendations.push(`Best application day: ${bestDay.day} (${(bestDay.successRate * 100).toFixed(1)}% success rate)`);
    }
    
    return recommendations;
  }

  generateSalaryRecommendation(userMin, userMax, marketMin, marketMax) {
    if (userMax < marketMin * 0.8) {
      return 'Consider increasing your salary range - market rates are higher than your current target';
    } else if (userMin > marketMax * 1.2) {
      return 'Your minimum salary target may be too high for the current market';
    } else {
      return 'Your salary range aligns well with market rates';
    }
  }
}

module.exports = JobSearchOptimizer;