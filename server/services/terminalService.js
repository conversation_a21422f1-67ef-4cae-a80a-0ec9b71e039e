const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

/**
 * Terminal Service for WebSocket-based terminal streaming
 */
class TerminalService {
  constructor() {
    this.terminals = new Map(); // terminalId -> terminal session
    this.containerTerminals = new Map(); // containerId -> terminalId
    this.wsClients = new Map(); // websocket -> client info
  }

  /**
   * Create a new terminal session
   */
  createTerminal(containerId, userId) {
    const terminalId = uuidv4();
    
    const terminal = {
      id: terminalId,
      containerId,
      userId,
      createdAt: new Date(),
      lastActivity: new Date(),
      status: 'active',
      history: [],
      clients: new Set() // WebSocket connections
    };

    this.terminals.set(terminalId, terminal);
    this.containerTerminals.set(containerId, terminalId);

    console.log(`📺 Created terminal ${terminalId} for container ${containerId}`);
    return terminalId;
  }

  /**
   * Connect WebSocket client to terminal
   */
  connectClient(ws, terminalId, userId) {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error('Terminal not found');
    }

    // Check access permissions
    if (terminal.userId !== userId) {
      throw new Error('Access denied to terminal');
    }

    // Add client to terminal
    terminal.clients.add(ws);
    
    // Store client info
    this.wsClients.set(ws, {
      terminalId,
      userId,
      connectedAt: new Date()
    });

    // Send terminal history to new client
    ws.send(JSON.stringify({
      type: 'terminal_history',
      terminalId,
      history: terminal.history
    }));

    // Send current status
    ws.send(JSON.stringify({
      type: 'terminal_status',
      terminalId,
      status: terminal.status,
      containerId: terminal.containerId
    }));

    console.log(`📱 Client connected to terminal ${terminalId}`);

    // Handle client disconnect
    ws.on('close', () => {
      this.disconnectClient(ws);
    });

    return terminalId;
  }

  /**
   * Disconnect WebSocket client
   */
  disconnectClient(ws) {
    const clientInfo = this.wsClients.get(ws);
    if (!clientInfo) return;

    const terminal = this.terminals.get(clientInfo.terminalId);
    if (terminal) {
      terminal.clients.delete(ws);
    }

    this.wsClients.delete(ws);
    console.log(`📱 Client disconnected from terminal ${clientInfo.terminalId}`);
  }

  /**
   * Send output to terminal clients
   */
  sendOutput(terminalId, data) {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) return;

    // Add to history
    const logEntry = {
      timestamp: new Date(),
      type: data.type || 'output',
      content: data.content || data,
      source: data.source || 'container'
    };

    terminal.history.push(logEntry);
    terminal.lastActivity = new Date();

    // Limit history size
    if (terminal.history.length > 1000) {
      terminal.history = terminal.history.slice(-1000);
    }

    // Broadcast to all connected clients
    const message = JSON.stringify({
      type: 'terminal_output',
      terminalId,
      data: logEntry
    });

    terminal.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  /**
   * Send input to container (if supported)
   */
  sendInput(terminalId, input, userId) {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error('Terminal not found');
    }

    if (terminal.userId !== userId) {
      throw new Error('Access denied');
    }

    // Log input
    this.sendOutput(terminalId, {
      type: 'input',
      content: input,
      source: 'user'
    });

    // For now, just echo back as containers are non-interactive
    // In a full implementation, this would send to the container's stdin
    console.log(`📝 Input sent to terminal ${terminalId}: ${input}`);
  }

  /**
   * Update terminal status
   */
  updateTerminalStatus(terminalId, status, metadata = {}) {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) return;

    terminal.status = status;
    terminal.lastActivity = new Date();

    // Broadcast status update
    const message = JSON.stringify({
      type: 'terminal_status',
      terminalId,
      status,
      metadata
    });

    terminal.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });

    console.log(`📺 Terminal ${terminalId} status updated: ${status}`);
  }

  /**
   * Get terminal info
   */
  getTerminal(terminalId) {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) return null;

    return {
      id: terminal.id,
      containerId: terminal.containerId,
      userId: terminal.userId,
      createdAt: terminal.createdAt,
      lastActivity: terminal.lastActivity,
      status: terminal.status,
      clientCount: terminal.clients.size,
      historyLength: terminal.history.length
    };
  }

  /**
   * Get terminal by container ID
   */
  getTerminalByContainer(containerId) {
    const terminalId = this.containerTerminals.get(containerId);
    return terminalId ? this.getTerminal(terminalId) : null;
  }

  /**
   * List terminals for user
   */
  listUserTerminals(userId) {
    const userTerminals = [];
    
    for (const terminal of this.terminals.values()) {
      if (terminal.userId === userId) {
        userTerminals.push({
          id: terminal.id,
          containerId: terminal.containerId,
          createdAt: terminal.createdAt,
          lastActivity: terminal.lastActivity,
          status: terminal.status,
          clientCount: terminal.clients.size
        });
      }
    }

    return userTerminals;
  }

  /**
   * Close terminal session
   */
  closeTerminal(terminalId, reason = 'manual') {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) return;

    // Notify all clients
    const message = JSON.stringify({
      type: 'terminal_closed',
      terminalId,
      reason
    });

    terminal.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
        client.close();
      }
    });

    // Remove from maps
    this.terminals.delete(terminalId);
    this.containerTerminals.delete(terminal.containerId);

    console.log(`📺 Terminal ${terminalId} closed (${reason})`);
  }

  /**
   * Handle WebSocket message
   */
  handleMessage(ws, message) {
    try {
      const data = JSON.parse(message);
      const clientInfo = this.wsClients.get(ws);

      if (!clientInfo) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Client not connected to terminal'
        }));
        return;
      }

      switch (data.type) {
        case 'terminal_input':
          this.sendInput(clientInfo.terminalId, data.input, clientInfo.userId);
          break;
          
        case 'terminal_resize':
          // Handle terminal resize (placeholder)
          console.log(`📺 Terminal ${clientInfo.terminalId} resized: ${data.cols}x${data.rows}`);
          break;
          
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong' }));
          break;
          
        default:
          console.warn('Unknown terminal message type:', data.type);
      }
    } catch (error) {
      console.error('Error handling terminal message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }));
    }
  }

  /**
   * Cleanup old terminals
   */
  cleanup() {
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [terminalId, terminal] of this.terminals.entries()) {
      if (now - terminal.lastActivity > maxAge) {
        this.closeTerminal(terminalId, 'timeout');
      }
    }

    console.log('🧹 Terminal cleanup completed');
  }

  /**
   * Get service statistics
   */
  getStats() {
    const stats = {
      totalTerminals: this.terminals.size,
      activeClients: this.wsClients.size,
      terminalsByStatus: {},
      oldestTerminal: null,
      newestTerminal: null
    };

    let oldest = null;
    let newest = null;

    for (const terminal of this.terminals.values()) {
      // Count by status
      stats.terminalsByStatus[terminal.status] = 
        (stats.terminalsByStatus[terminal.status] || 0) + 1;

      // Track oldest and newest
      if (!oldest || terminal.createdAt < oldest) {
        oldest = terminal.createdAt;
      }
      if (!newest || terminal.createdAt > newest) {
        newest = terminal.createdAt;
      }
    }

    stats.oldestTerminal = oldest;
    stats.newestTerminal = newest;

    return stats;
  }
}

module.exports = TerminalService;