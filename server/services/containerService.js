const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * Enhanced Container Service for managing Docker-based job execution environments
 * Now with advanced security features and integration with security services
 */
class ContainerService {
  constructor(securityService = null, monitoringService = null) {
    this.activeContainers = new Map(); // containerId -> containerInfo
    this.containerLogs = new Map(); // containerId -> log stream
    this.securityService = securityService;
    this.monitoringService = monitoringService;
    
    this.config = {
      baseImage: 'node:18-alpine',
      resourceLimits: {
        memory: '512m',
        cpus: '0.5'
      },
      timeout: 600000, // 10 minutes
      maxConcurrentContainers: 5,
      security: {
        enableVulnerabilityScanning: true,
        securityProfile: 'strict',
        networkPolicy: 'restricted',
        enforceResourceLimits: true,
        enableSeccomp: true,
        enableAppArmor: true
      }
    };
  }

  /**
   * Set security service reference
   */
  setSecurityService(securityService) {
    this.securityService = securityService;
  }

  /**
   * Set monitoring service reference
   */
  setMonitoringService(monitoringService) {
    this.monitoringService = monitoringService;
  }

  /**
   * Create and start a container for job execution with enhanced security
   */
  async createJobContainer(jobData, terminalCallback = null) {
    const containerId = uuidv4();
    const containerName = `cvleap-job-${containerId}`;
    
    try {
      // Check if we've reached concurrent container limit
      if (this.activeContainers.size >= this.config.maxConcurrentContainers) {
        throw new Error(`Maximum concurrent containers reached: ${this.config.maxConcurrentContainers}`);
      }

      // Perform security checks on base image
      await this.performSecurityChecks();

      // Prepare job execution script
      const jobScript = this.generateJobScript(jobData);
      const scriptPath = path.join('/tmp', `job-${containerId}.js`);
      await fs.writeFile(scriptPath, jobScript);

      // Build secure Docker arguments
      const dockerArgs = await this.buildSecureDockerArgs(containerId, containerName, scriptPath);

      // Start container with enhanced monitoring
      console.log(`🔒 Creating secure container: ${containerName}`);
      const containerProcess = spawn('docker', dockerArgs);
      
      const containerInfo = {
        id: containerId,
        name: containerName,
        process: containerProcess,
        jobData,
        startTime: new Date(),
        status: 'running',
        logs: [],
        terminalCallback,
        securityProfile: this.config.security.securityProfile,
        networkPolicy: this.config.security.networkPolicy,
        resourceUsage: {
          cpu: 0,
          memory: 0,
          diskIO: 0,
          networkIO: 0
        }
      };

      this.activeContainers.set(containerId, containerInfo);

      // Set up enhanced monitoring
      if (this.monitoringService) {
        this.monitoringService.trackJobExecution(containerId, containerInfo.startTime);
        this.monitoringService.setMetric('application.containers.active', this.activeContainers.size);
      }

      // Set up log streaming with security event monitoring
      this.setupEnhancedLogStreaming(containerId, containerProcess, terminalCallback);

      // Set up timeout with security considerations
      const timeoutId = setTimeout(() => {
        this.stopContainer(containerId, 'security_timeout');
      }, this.config.timeout);

      containerInfo.timeoutId = timeoutId;

      console.log(`🐳 Started secure container ${containerName} for job ${jobData.id}`);
      return containerId;

    } catch (error) {
      console.error(`Failed to create secure container for job ${jobData.id}:`, error);
      
      // Log security event if applicable
      if (this.securityService) {
        this.securityService.logSecurityEvent('CONTAINER_CREATION_FAILED', {
          jobId: jobData.id,
          error: error.message,
          timestamp: new Date()
        });
      }
      
      throw error;
    }
  }

  /**
   * Perform security checks before container creation
   */
  async performSecurityChecks() {
    if (!this.securityService) {
      console.warn('⚠️ Security service not available, skipping security checks');
      return;
    }

    // Check if base image is blocked
    if (this.securityService.isImageBlocked(this.config.baseImage)) {
      throw new Error(`Base image ${this.config.baseImage} is blocked due to security issues`);
    }

    // Perform vulnerability scan if enabled and image not trusted
    if (this.config.security.enableVulnerabilityScanning && 
        !this.securityService.isImageTrusted(this.config.baseImage)) {
      
      console.log(`🔍 Performing security scan on ${this.config.baseImage}...`);
      
      try {
        const scanResult = await this.securityService.scanContainerImage(this.config.baseImage);
        
        if (!scanResult.securityAssessment?.approved) {
          throw new Error(`Image security scan failed: ${scanResult.securityAssessment?.reason}`);
        }
        
        console.log(`✅ Security scan passed for ${this.config.baseImage}`);
      } catch (scanError) {
        console.error(`❌ Security scan failed:`, scanError);
        throw new Error(`Container security validation failed: ${scanError.message}`);
      }
    }
  }

  /**
   * Build secure Docker arguments with enhanced security features
   */
  async buildSecureDockerArgs(containerId, containerName, scriptPath) {
    const args = [
      'run',
      '--rm',
      '--name', containerName
    ];

    // Resource limits with enforcement
    if (this.config.security.enforceResourceLimits) {
      args.push(
        '--memory', this.config.resourceLimits.memory,
        '--cpus', this.config.resourceLimits.cpus,
        '--pids-limit', '100',
        '--ulimit', 'nofile=1024:1024',
        '--ulimit', 'nproc=50:50'
      );
    }

    // Security options
    args.push(
      '--user', '1000:1000', // Non-root user
      '--read-only', // Read-only filesystem
      '--tmpfs', '/tmp:rw,noexec,nosuid,size=100m',
      '--security-opt', 'no-new-privileges:true'
    );

    // Seccomp profile
    if (this.config.security.enableSeccomp && this.securityService) {
      const seccompProfile = this.securityService.generateSeccompProfile();
      if (seccompProfile) {
        // In production, write profile to file and reference it
        args.push('--security-opt', 'seccomp=unconfined'); // Fallback for demo
      }
    }

    // AppArmor profile
    if (this.config.security.enableAppArmor && this.securityService) {
      const apparmorProfile = this.securityService.generateAppArmorProfile();
      if (apparmorProfile) {
        args.push('--security-opt', `apparmor=${apparmorProfile}`);
      }
    }

    // Network isolation based on policy
    if (this.config.security.networkPolicy === 'none') {
      args.push('--network', 'none');
    } else if (this.config.security.networkPolicy === 'restricted') {
      // Create restricted network if needed
      await this.setupRestrictedNetwork(containerId);
      args.push('--network', `cvleap-restricted-${containerId}`);
    }

    // Capability dropping
    args.push(
      '--cap-drop', 'ALL',
      '--cap-add', 'CHOWN',
      '--cap-add', 'SETUID',
      '--cap-add', 'SETGID'
    );

    // Volume mounts
    args.push(
      '-v', `${scriptPath}:/app/job.js:ro`,
      '-w', '/app',
      this.config.baseImage,
      'node', 'job.js'
    );

    return args;
  }

  /**
   * Setup restricted network for container
   */
  async setupRestrictedNetwork(containerId) {
    const networkName = `cvleap-restricted-${containerId}`;
    
    try {
      // Create isolated bridge network
      const createNetworkArgs = [
        'network', 'create',
        '--driver', 'bridge',
        '--internal', // No external access by default
        '--subnet', '**********/16',
        networkName
      ];
      
      await this.executeDockerCommand(createNetworkArgs);
      console.log(`🌐 Created restricted network: ${networkName}`);
    } catch (error) {
      console.error(`Failed to create restricted network: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enhanced log streaming with security monitoring
   */
  setupEnhancedLogStreaming(containerId, containerProcess, terminalCallback) {
    const containerInfo = this.activeContainers.get(containerId);
    if (!containerInfo) return;

    containerProcess.stdout?.on('data', (data) => {
      const logLine = data.toString().trim();
      containerInfo.logs.push({
        type: 'stdout',
        message: logLine,
        timestamp: new Date()
      });

      // Monitor for security events in logs
      this.monitorSecurityEvents(containerId, logLine);

      if (terminalCallback) {
        terminalCallback({
          type: 'output',
          data: logLine,
          timestamp: new Date()
        });
      }
    });

    containerProcess.stderr?.on('data', (data) => {
      const logLine = data.toString().trim();
      containerInfo.logs.push({
        type: 'stderr',
        message: logLine,
        timestamp: new Date()
      });

      // Security monitoring for error patterns
      this.monitorSecurityEvents(containerId, logLine, 'error');

      if (terminalCallback) {
        terminalCallback({
          type: 'error',
          data: logLine,
          timestamp: new Date()
        });
      }
    });

    containerProcess.on('close', async (code) => {
      containerInfo.status = code === 0 ? 'completed' : 'failed';
      containerInfo.exitCode = code;
      containerInfo.endTime = new Date();
      
      if (containerInfo.timeoutId) {
        clearTimeout(containerInfo.timeoutId);
      }

      // Update monitoring metrics
      if (this.monitoringService) {
        const duration = containerInfo.endTime - containerInfo.startTime;
        this.monitoringService.trackJobCompletion(containerId, code === 0, duration);
        this.monitoringService.setMetric('application.containers.active', this.activeContainers.size - 1);
      }

      // Log security event for container termination
      if (this.securityService) {
        this.securityService.logSecurityEvent('CONTAINER_TERMINATED', {
          containerId,
          exitCode: code,
          duration: containerInfo.endTime - containerInfo.startTime,
          jobId: containerInfo.jobData.id
        });
      }
      
      if (terminalCallback) {
        terminalCallback({
          type: 'exit',
          code,
          timestamp: new Date()
        });
      }
      
      console.log(`🐳 Container ${containerInfo.name} exited with code ${code}`);
      
      // Cleanup after delay
      setTimeout(() => {
        this.cleanupContainer(containerId);
      }, 30000); // Keep logs for 30 seconds
    });

    // Enhanced error handling
    containerProcess.on('error', (error) => {
      console.error(`💥 Container process error for ${containerId}:`, error);
      
      if (this.securityService) {
        this.securityService.logSecurityEvent('CONTAINER_ERROR', {
          containerId,
          error: error.message,
          jobId: containerInfo.jobData.id
        });
      }

      if (terminalCallback) {
        terminalCallback({
          type: 'error',
          data: `Container process error: ${error.message}`,
          timestamp: new Date()
        });
      }
    });
  }

  /**
   * Monitor container logs for security events
   */
  monitorSecurityEvents(containerId, logLine, logType = 'info') {
    if (!this.securityService) return;

    const securityPatterns = [
      { pattern: /permission denied/i, severity: 'HIGH', type: 'PERMISSION_DENIED' },
      { pattern: /access denied/i, severity: 'HIGH', type: 'ACCESS_DENIED' },
      { pattern: /unauthorized/i, severity: 'MEDIUM', type: 'UNAUTHORIZED_ACCESS' },
      { pattern: /failed to connect/i, severity: 'MEDIUM', type: 'CONNECTION_FAILURE' },
      { pattern: /segmentation fault/i, severity: 'HIGH', type: 'SEGMENTATION_FAULT' },
      { pattern: /core dumped/i, severity: 'HIGH', type: 'CORE_DUMP' },
      { pattern: /out of memory/i, severity: 'MEDIUM', type: 'MEMORY_EXHAUSTION' }
    ];

    for (const { pattern, severity, type } of securityPatterns) {
      if (pattern.test(logLine)) {
        this.securityService.logSecurityEvent('CONTAINER_SECURITY_EVENT', {
          containerId,
          eventType: type,
          severity,
          logLine,
          logType,
          timestamp: new Date()
        });
        break;
      }
    }
  }

  /**
   * Enhanced container cleanup with security considerations
   */
  async cleanupContainer(containerId) {
    const containerInfo = this.activeContainers.get(containerId);
    if (!containerInfo) return;

    try {
      // Remove container (if still exists)
      await this.executeDockerCommand(['rm', '-f', containerInfo.name]).catch(() => {});

      // Remove restricted network if created
      if (this.config.security.networkPolicy === 'restricted') {
        await this.executeDockerCommand([
          'network', 'rm', `cvleap-restricted-${containerId}`
        ]).catch(() => {});
      }

      // Clean up temporary files securely
      const scriptPath = path.join('/tmp', `job-${containerId}.js`);
      await fs.unlink(scriptPath).catch(() => {});

      // Remove from tracking
      this.activeContainers.delete(containerId);
      
      console.log(`🧹 Cleaned up secure container: ${containerId}`);
    } catch (error) {
      console.error(`Cleanup error for container ${containerId}:`, error.message);
    }
  }

  /**
   * Execute Docker command with error handling
   */
  async executeDockerCommand(args) {
    return new Promise((resolve, reject) => {
      const process = spawn('docker', args);
      let output = '';
      let errorOutput = '';
      
      process.stdout?.on('data', (data) => {
        output += data.toString();
      });
      
      process.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(new Error(`Docker command failed: ${errorOutput}`));
        }
      });
    });
  }

  /**
   * Generate job execution script
   */
  generateJobScript(jobData) {
    return `
// Job execution script for ${jobData.id}
const jobData = ${JSON.stringify(jobData, null, 2)};

console.log('📋 Starting job execution...');
console.log('Job:', jobData.jobTitle, 'at', jobData.company);

// Simulate job application process
async function executeJob() {
  try {
    console.log('🔍 Analyzing job requirements...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('📝 Preparing application materials...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('🚀 Submitting application...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ Job application completed successfully');
    return { success: true, submissionId: 'sub_' + Math.random().toString(36).substr(2, 9) };
    
  } catch (error) {
    console.error('❌ Job execution failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Execute job and report results
executeJob()
  .then(result => {
    console.log('📊 Final result:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
`;
  }

  /**
   * Set up log streaming from container
   */
  setupLogStreaming(containerId, containerProcess, terminalCallback) {
    const containerInfo = this.activeContainers.get(containerId);
    
    containerProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      containerInfo.logs.push({
        timestamp: new Date(),
        type: 'stdout',
        content: logLine
      });
      
      // Stream to terminal if callback provided
      if (terminalCallback) {
        terminalCallback({
          type: 'output',
          content: logLine,
          timestamp: new Date()
        });
      }
    });

    containerProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      containerInfo.logs.push({
        timestamp: new Date(),
        type: 'stderr',
        content: logLine
      });
      
      if (terminalCallback) {
        terminalCallback({
          type: 'error',
          content: logLine,
          timestamp: new Date()
        });
      }
    });

    containerProcess.on('close', (code) => {
      containerInfo.status = code === 0 ? 'completed' : 'failed';
      containerInfo.exitCode = code;
      containerInfo.endTime = new Date();
      
      if (containerInfo.timeoutId) {
        clearTimeout(containerInfo.timeoutId);
      }
      
      if (terminalCallback) {
        terminalCallback({
          type: 'exit',
          code,
          timestamp: new Date()
        });
      }
      
      console.log(`🐳 Container ${containerInfo.name} exited with code ${code}`);
      
      // Cleanup after delay
      setTimeout(() => {
        this.activeContainers.delete(containerId);
      }, 30000); // Keep logs for 30 seconds
    });
  }

  /**
   * Stop a running container
   */
  async stopContainer(containerId, reason = 'manual') {
    const containerInfo = this.activeContainers.get(containerId);
    if (!containerInfo) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      // Stop the Docker container
      await this.executeDockerCommand(['stop', containerInfo.name]);
      
      containerInfo.status = 'stopped';
      containerInfo.stopReason = reason;
      
      console.log(`🛑 Stopped container ${containerInfo.name} (${reason})`);
      
    } catch (error) {
      console.error(`Failed to stop container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Get container status and logs
   */
  getContainerInfo(containerId) {
    const containerInfo = this.activeContainers.get(containerId);
    if (!containerInfo) {
      return null;
    }

    return {
      id: containerInfo.id,
      name: containerInfo.name,
      status: containerInfo.status,
      startTime: containerInfo.startTime,
      endTime: containerInfo.endTime,
      exitCode: containerInfo.exitCode,
      logs: containerInfo.logs,
      jobData: containerInfo.jobData
    };
  }

  /**
   * List all active containers
   */
  listActiveContainers() {
    const containers = [];
    for (const [id, info] of this.activeContainers.entries()) {
      containers.push({
        id,
        name: info.name,
        status: info.status,
        startTime: info.startTime,
        jobTitle: info.jobData.jobTitle,
        company: info.jobData.company
      });
    }
    return containers;
  }

  /**
   * Execute Docker command
   */
  executeDockerCommand(args) {
    return new Promise((resolve, reject) => {
      const process = spawn('docker', args);
      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`Docker command failed: ${error}`));
        }
      });
    });
  }

  /**
   * Health check for Docker availability
   */
  async healthCheck() {
    try {
      await this.executeDockerCommand(['version', '--format', '{{.Server.Version}}']);
      return {
        status: 'healthy',
        activeContainers: this.activeContainers.size,
        maxContainers: this.config.maxConcurrentContainers
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Cleanup old containers and resources
   */
  async cleanup() {
    console.log('🧹 Cleaning up container resources...');
    
    // Stop all active containers
    const stopPromises = [];
    for (const containerId of this.activeContainers.keys()) {
      stopPromises.push(this.stopContainer(containerId, 'cleanup'));
    }
    
    await Promise.allSettled(stopPromises);
    
    // Clean up temporary files
    try {
      const tempFiles = await fs.readdir('/tmp');
      for (const file of tempFiles) {
        if (file.startsWith('job-') && file.endsWith('.js')) {
          await fs.unlink(path.join('/tmp', file));
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup temp files:', error.message);
    }
    
    console.log('✅ Container cleanup completed');
  }
}

module.exports = ContainerService;