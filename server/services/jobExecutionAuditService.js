/**
 * Enhanced Audit Service for job execution system
 */
class JobExecutionAuditService {
  constructor() {
    // Use the existing audit logger singleton
    this.auditLogger = require('../utils/auditLogger');
    this.executionLogs = new Map(); // jobId -> execution events
    this.sessionLogs = new Map(); // sessionId -> session events
  }

  /**
   * Log job approval events
   */
  logJobApproval(action, userId, jobData, approvalData = {}) {
    const event = {
      category: 'JOB_APPROVAL',
      action,
      userId,
      jobId: jobData.id,
      jobTitle: jobData.jobTitle,
      company: jobData.company,
      platform: jobData.platform,
      approvalId: approvalData.approvalId,
      approverId: approvalData.approverId,
      modifications: approvalData.modifications || [],
      reason: approvalData.reason,
      metadata: {
        jobUrl: jobData.jobUrl,
        priority: jobData.priority,
        analysisResult: approvalData.analysisResult
      }
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      userId,
      {
        ...event,
        jobExecutionContext: true
      }
    );

    // Store in execution logs
    this.addExecutionEvent(jobData.id, event);
  }

  /**
   * Log container execution events
   */
  logContainerExecution(action, userId, containerId, jobData, containerData = {}) {
    const event = {
      category: 'CONTAINER_EXECUTION',
      action,
      userId,
      containerId,
      jobId: jobData.id,
      jobTitle: jobData.jobTitle,
      company: jobData.company,
      containerName: containerData.name,
      containerImage: containerData.image,
      exitCode: containerData.exitCode,
      executionTime: containerData.executionTime,
      resourceUsage: containerData.resourceUsage,
      metadata: {
        startTime: containerData.startTime,
        endTime: containerData.endTime,
        logs: containerData.logs ? containerData.logs.length : 0
      }
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      userId,
      {
        ...event,
        containerExecutionContext: true,
        securityLevel: 'high' // Container execution is security-sensitive
      }
    );

    this.addExecutionEvent(jobData.id, event);
  }

  /**
   * Log terminal access events
   */
  logTerminalAccess(action, userId, terminalId, containerId, accessData = {}) {
    const event = {
      category: 'TERMINAL_ACCESS',
      action,
      userId,
      terminalId,
      containerId,
      clientIp: accessData.clientIp,
      userAgent: accessData.userAgent,
      sessionDuration: accessData.sessionDuration,
      commandsExecuted: accessData.commandsExecuted,
      dataTransferred: accessData.dataTransferred
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      userId,
      {
        ...event,
        terminalAccessContext: true,
        securityLevel: 'high' // Terminal access is security-sensitive
      }
    );

    // Store in session logs
    this.addSessionEvent(terminalId, event);
  }

  /**
   * Log human intervention events
   */
  logHumanIntervention(action, userId, jobId, interventionData = {}) {
    const event = {
      category: 'HUMAN_INTERVENTION',
      action,
      userId,
      jobId,
      interventionType: interventionData.type,
      interventionReason: interventionData.reason,
      previousState: interventionData.previousState,
      newState: interventionData.newState,
      modifications: interventionData.modifications,
      duration: interventionData.duration,
      outcome: interventionData.outcome
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      userId,
      {
        ...event,
        humanInterventionContext: true,
        complianceRelevant: true
      }
    );

    this.addExecutionEvent(jobId, event);
  }

  /**
   * Log system automation events
   */
  logSystemAutomation(action, jobId, automationData = {}) {
    const event = {
      category: 'SYSTEM_AUTOMATION',
      action,
      userId: 'system',
      jobId,
      automationType: automationData.type,
      automationEngine: automationData.engine,
      success: automationData.success,
      errorCode: automationData.errorCode,
      executionTime: automationData.executionTime,
      metadata: {
        browserVersion: automationData.browserVersion,
        platformVersion: automationData.platformVersion,
        retryCount: automationData.retryCount
      }
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      'system',
      {
        ...event,
        systemAutomationContext: true
      }
    );

    this.addExecutionEvent(jobId, event);
  }

  /**
   * Log security events
   */
  logSecurityEvent(action, userId, securityData = {}) {
    const event = {
      category: 'SECURITY',
      action,
      userId,
      securityLevel: securityData.level || 'medium',
      threatType: securityData.threatType,
      sourceIp: securityData.sourceIp,
      userAgent: securityData.userAgent,
      blocked: securityData.blocked,
      reason: securityData.reason
    };

    this.auditLogger.logEvent(
      event.category,
      action,
      userId,
      {
        ...event,
        securityEvent: true,
        severity: securityData.level || 'medium'
      }
    );
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics(jobId, metrics) {
    const event = {
      category: 'PERFORMANCE',
      action: 'METRICS_RECORDED',
      userId: 'system',
      jobId,
      metrics: {
        executionTime: metrics.executionTime,
        queueTime: metrics.queueTime,
        containerStartTime: metrics.containerStartTime,
        memoryUsage: metrics.memoryUsage,
        cpuUsage: metrics.cpuUsage,
        networkUsage: metrics.networkUsage,
        success: metrics.success
      }
    };

    this.auditLogger.logEvent(
      event.category,
      event.action,
      'system',
      {
        ...event,
        performanceMetrics: true
      }
    );

    this.addExecutionEvent(jobId, event);
  }

  /**
   * Add event to execution logs
   */
  addExecutionEvent(jobId, event) {
    if (!this.executionLogs.has(jobId)) {
      this.executionLogs.set(jobId, []);
    }
    
    const events = this.executionLogs.get(jobId);
    events.push({
      ...event,
      timestamp: new Date(),
      sequence: events.length + 1
    });

    // Limit log size per job
    if (events.length > 1000) {
      events.splice(0, events.length - 1000);
    }
  }

  /**
   * Add event to session logs
   */
  addSessionEvent(sessionId, event) {
    if (!this.sessionLogs.has(sessionId)) {
      this.sessionLogs.set(sessionId, []);
    }
    
    const events = this.sessionLogs.get(sessionId);
    events.push({
      ...event,
      timestamp: new Date(),
      sequence: events.length + 1
    });

    // Limit log size per session
    if (events.length > 500) {
      events.splice(0, events.length - 500);
    }
  }

  /**
   * Get execution audit trail for job
   */
  getJobAuditTrail(jobId, filters = {}) {
    const events = this.executionLogs.get(jobId) || [];
    
    let filteredEvents = events;

    if (filters.category) {
      filteredEvents = filteredEvents.filter(e => e.category === filters.category);
    }

    if (filters.action) {
      filteredEvents = filteredEvents.filter(e => e.action === filters.action);
    }

    if (filters.userId) {
      filteredEvents = filteredEvents.filter(e => e.userId === filters.userId);
    }

    if (filters.startTime) {
      filteredEvents = filteredEvents.filter(e => e.timestamp >= filters.startTime);
    }

    if (filters.endTime) {
      filteredEvents = filteredEvents.filter(e => e.timestamp <= filters.endTime);
    }

    return filteredEvents.sort((a, b) => a.sequence - b.sequence);
  }

  /**
   * Get session audit trail
   */
  getSessionAuditTrail(sessionId, filters = {}) {
    const events = this.sessionLogs.get(sessionId) || [];
    
    let filteredEvents = events;

    if (filters.action) {
      filteredEvents = filteredEvents.filter(e => e.action === filters.action);
    }

    return filteredEvents.sort((a, b) => a.sequence - b.sequence);
  }

  /**
   * Generate compliance report
   */
  generateComplianceReport(startDate, endDate, options = {}) {
    const report = {
      period: { startDate, endDate },
      summary: {
        totalJobs: 0,
        approvedJobs: 0,
        rejectedJobs: 0,
        humanInterventions: 0,
        securityEvents: 0,
        containerExecutions: 0
      },
      events: [],
      riskAnalysis: {},
      recommendations: []
    };

    // Collect events from all jobs
    for (const [jobId, events] of this.executionLogs.entries()) {
      const jobEvents = events.filter(e => 
        e.timestamp >= startDate && e.timestamp <= endDate
      );

      report.events.push(...jobEvents);

      // Update summary
      if (jobEvents.length > 0) {
        report.summary.totalJobs++;
      }

      jobEvents.forEach(event => {
        switch (event.category) {
          case 'JOB_APPROVAL':
            if (event.action === 'APPROVED') report.summary.approvedJobs++;
            if (event.action === 'REJECTED') report.summary.rejectedJobs++;
            break;
          case 'HUMAN_INTERVENTION':
            report.summary.humanInterventions++;
            break;
          case 'SECURITY':
            report.summary.securityEvents++;
            break;
          case 'CONTAINER_EXECUTION':
            if (event.action === 'STARTED') report.summary.containerExecutions++;
            break;
        }
      });
    }

    // Generate risk analysis
    report.riskAnalysis = this.analyzeRisks(report.events);

    // Generate recommendations
    report.recommendations = this.generateRecommendations(report);

    return report;
  }

  /**
   * Analyze risks from audit events
   */
  analyzeRisks(events) {
    const risks = {
      high: [],
      medium: [],
      low: [],
      summary: {
        totalRisks: 0,
        highRiskCount: 0,
        securityIncidents: 0,
        failedExecutions: 0
      }
    };

    events.forEach(event => {
      // Analyze security events
      if (event.category === 'SECURITY') {
        risks.summary.securityIncidents++;
        if (event.securityLevel === 'high') {
          risks.high.push({
            type: 'security',
            description: `Security event: ${event.action}`,
            event
          });
        }
      }

      // Analyze failed executions
      if (event.category === 'CONTAINER_EXECUTION' && event.action === 'FAILED') {
        risks.summary.failedExecutions++;
        risks.medium.push({
          type: 'execution_failure',
          description: `Container execution failed: ${event.containerId}`,
          event
        });
      }

      // Analyze unauthorized access attempts
      if (event.category === 'TERMINAL_ACCESS' && event.action === 'UNAUTHORIZED_ACCESS') {
        risks.high.push({
          type: 'unauthorized_access',
          description: `Unauthorized terminal access attempt`,
          event
        });
      }
    });

    risks.summary.totalRisks = risks.high.length + risks.medium.length + risks.low.length;
    risks.summary.highRiskCount = risks.high.length;

    return risks;
  }

  /**
   * Generate recommendations based on audit data
   */
  generateRecommendations(report) {
    const recommendations = [];

    // Security recommendations
    if (report.riskAnalysis.summary.securityIncidents > 0) {
      recommendations.push({
        type: 'security',
        priority: 'high',
        description: 'Review security incidents and strengthen access controls',
        details: `${report.riskAnalysis.summary.securityIncidents} security incidents detected`
      });
    }

    // Performance recommendations
    const failureRate = report.summary.totalJobs > 0 ? 
      (report.riskAnalysis.summary.failedExecutions / report.summary.totalJobs) : 0;
    
    if (failureRate > 0.1) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        description: 'High failure rate detected, review execution environment',
        details: `${(failureRate * 100).toFixed(1)}% failure rate`
      });
    }

    // Compliance recommendations
    const approvalRate = report.summary.totalJobs > 0 ? 
      (report.summary.approvedJobs / report.summary.totalJobs) : 0;
    
    if (approvalRate < 0.8) {
      recommendations.push({
        type: 'compliance',
        priority: 'medium',
        description: 'Low approval rate may indicate process issues',
        details: `${(approvalRate * 100).toFixed(1)}% approval rate`
      });
    }

    return recommendations;
  }

  /**
   * Export audit data
   */
  exportAuditData(jobId, format = 'json') {
    const auditTrail = this.getJobAuditTrail(jobId);
    
    if (format === 'csv') {
      return this.convertToCSV(auditTrail);
    }
    
    return JSON.stringify(auditTrail, null, 2);
  }

  /**
   * Convert audit data to CSV format
   */
  convertToCSV(events) {
    if (events.length === 0) return '';

    const headers = ['timestamp', 'category', 'action', 'userId', 'jobId', 'details'];
    const rows = events.map(event => [
      event.timestamp.toISOString(),
      event.category,
      event.action,
      event.userId || '',
      event.jobId || '',
      JSON.stringify(event.metadata || {})
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  /**
   * Cleanup old audit logs
   */
  cleanup(retentionDays = 90) {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    let cleanedCount = 0;

    // Clean execution logs
    for (const [jobId, events] of this.executionLogs.entries()) {
      const filteredEvents = events.filter(e => e.timestamp > cutoffDate);
      if (filteredEvents.length !== events.length) {
        this.executionLogs.set(jobId, filteredEvents);
        cleanedCount += events.length - filteredEvents.length;
      }
      
      // Remove empty job logs
      if (filteredEvents.length === 0) {
        this.executionLogs.delete(jobId);
      }
    }

    // Clean session logs
    for (const [sessionId, events] of this.sessionLogs.entries()) {
      const filteredEvents = events.filter(e => e.timestamp > cutoffDate);
      if (filteredEvents.length !== events.length) {
        this.sessionLogs.set(sessionId, filteredEvents);
        cleanedCount += events.length - filteredEvents.length;
      }
      
      // Remove empty session logs
      if (filteredEvents.length === 0) {
        this.sessionLogs.delete(sessionId);
      }
    }

    console.log(`🧹 Cleaned up ${cleanedCount} old audit log entries`);
    return cleanedCount;
  }

  /**
   * Get audit statistics
   */
  getStats() {
    const stats = {
      totalJobs: this.executionLogs.size,
      totalSessions: this.sessionLogs.size,
      totalEvents: 0,
      eventsByCategory: {},
      recentActivity: {
        last24h: 0,
        last7d: 0,
        last30d: 0
      }
    };

    const now = new Date();
    const day = 24 * 60 * 60 * 1000;

    // Count events and categorize
    for (const events of this.executionLogs.values()) {
      stats.totalEvents += events.length;
      
      events.forEach(event => {
        // Count by category
        stats.eventsByCategory[event.category] = 
          (stats.eventsByCategory[event.category] || 0) + 1;
        
        // Count recent activity
        const age = now - event.timestamp;
        if (age <= day) stats.recentActivity.last24h++;
        if (age <= 7 * day) stats.recentActivity.last7d++;
        if (age <= 30 * day) stats.recentActivity.last30d++;
      });
    }

    return stats;
  }
}

module.exports = JobExecutionAuditService;