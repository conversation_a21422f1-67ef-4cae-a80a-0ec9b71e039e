const database = require('../database');

/**
 * Recruiter Discovery Service for finding and managing recruiter contacts
 * Includes email pattern generation and contact management
 */
class RecruiterDiscoveryService {
  constructor() {
    this.db = database.get();
  }

  /**
   * Discover recruiters for a given company
   */
  async discoverRecruiters(userId, companyName, companyDomain) {
    try {
      const discoveredContacts = [];
      
      // Generate potential recruiter email patterns
      const emailPatterns = this.generateEmailPatterns(companyName, companyDomain);
      
      // In production, this would integrate with LinkedIn API or other sources
      // For now, we'll generate mock recruiter data based on patterns
      const mockRecruiters = this.generateMockRecruiters(companyName, emailPatterns);
      
      // Save discovered contacts to database
      for (const recruiter of mockRecruiters) {
        try {
          const contactId = await this.saveRecruiterContact(userId, recruiter);
          discoveredContacts.push({
            id: contactId,
            ...recruiter
          });
        } catch (error) {
          // Skip if contact already exists (unique constraint)
          if (!error.message.includes('UNIQUE constraint failed')) {
            console.error('Error saving recruiter contact:', error);
          }
        }
      }
      
      return {
        success: true,
        discovered: discoveredContacts.length,
        contacts: discoveredContacts,
        patterns: emailPatterns
      };
      
    } catch (error) {
      console.error('Recruiter discovery error:', error);
      throw error;
    }
  }

  /**
   * Generate email patterns for a company domain
   */
  generateEmailPatterns(companyName, companyDomain) {
    const patterns = [];
    const normalizedDomain = companyDomain.replace(/^(www\.|https?:\/\/)/, '');
    
    // Common recruiter titles/departments
    const recruiterRoles = [
      'recruiter', 'recruitment', 'talent', 'hr', 'hiring',
      'people', 'careers', 'jobs', 'staffing'
    ];
    
    // Common email patterns
    const emailPatterns = [
      // Generic recruitment emails
      `recruiting@${normalizedDomain}`,
      `recruitment@${normalizedDomain}`,
      `talent@${normalizedDomain}`,
      `hr@${normalizedDomain}`,
      `careers@${normalizedDomain}`,
      `jobs@${normalizedDomain}`,
      
      // Individual recruiter patterns
      ...recruiterRoles.map(role => `${role}@${normalizedDomain}`),
      ...recruiterRoles.map(role => `${role}.team@${normalizedDomain}`),
      
      // Common name patterns (these would be more sophisticated in production)
      `sarah.smith@${normalizedDomain}`,
      `john.doe@${normalizedDomain}`,
      `mike.johnson@${normalizedDomain}`,
      `jennifer.brown@${normalizedDomain}`,
      `david.wilson@${normalizedDomain}`
    ];
    
    return emailPatterns.map(email => ({
      email,
      pattern: this.categorizeEmailPattern(email),
      confidence: this.calculatePatternConfidence(email)
    }));
  }

  /**
   * Generate mock recruiter data (in production, this would come from LinkedIn API)
   */
  generateMockRecruiters(companyName, emailPatterns) {
    const recruiters = [];
    
    // Sample recruiter names and titles
    const sampleRecruiters = [
      { name: 'Sarah Smith', title: 'Senior Technical Recruiter' },
      { name: 'John Rodriguez', title: 'Talent Acquisition Manager' },
      { name: 'Jennifer Chen', title: 'HR Business Partner' },
      { name: 'Michael Johnson', title: 'Recruiting Coordinator' },
      { name: 'Emily Davis', title: 'People Operations Specialist' },
      { name: 'David Wilson', title: 'Director of Talent Acquisition' },
      { name: 'Lisa Brown', title: 'Senior Recruiter' },
      { name: 'James Miller', title: 'Technical Talent Sourcer' }
    ];
    
    // Select a subset of patterns and create recruiter profiles
    const selectedPatterns = emailPatterns.slice(0, Math.min(8, emailPatterns.length));
    
    selectedPatterns.forEach((pattern, index) => {
      if (index < sampleRecruiters.length) {
        const recruiter = sampleRecruiters[index];
        recruiters.push({
          email: pattern.email,
          name: recruiter.name,
          company: companyName,
          title: recruiter.title,
          verification_status: 'unverified',
          discovery_method: 'pattern_generation',
          discovery_data: JSON.stringify({
            pattern: pattern.pattern,
            confidence: pattern.confidence,
            generated_at: new Date().toISOString()
          })
        });
      }
    });
    
    return recruiters;
  }

  /**
   * Save recruiter contact to database
   */
  async saveRecruiterContact(userId, recruiterData) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO recruiter_contacts (
          user_id, email, name, company, title, verification_status,
          discovery_method, discovery_data, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;
      
      const params = [
        userId,
        recruiterData.email,
        recruiterData.name,
        recruiterData.company,
        recruiterData.title,
        recruiterData.verification_status || 'unverified',
        recruiterData.discovery_method || 'manual',
        recruiterData.discovery_data || '{}'
      ];
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  /**
   * Get recruiter contacts for user
   */
  async getRecruiterContacts(userId, filters = {}) {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM recruiter_contacts WHERE user_id = ?';
      const params = [userId];
      
      // Apply filters
      if (filters.company) {
        query += ' AND company LIKE ?';
        params.push(`%${filters.company}%`);
      }
      
      if (filters.verification_status) {
        query += ' AND verification_status = ?';
        params.push(filters.verification_status);
      }
      
      if (filters.response_status) {
        query += ' AND response_status = ?';
        params.push(filters.response_status);
      }
      
      // Add ordering
      query += ' ORDER BY created_at DESC';
      
      // Add limit if specified
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }
      
      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Update recruiter contact
   */
  async updateRecruiterContact(userId, contactId, updateData) {
    return new Promise((resolve, reject) => {
      const fields = [];
      const params = [];
      
      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        if (['name', 'company', 'title', 'verification_status', 'response_status', 'notes'].includes(key)) {
          fields.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      });
      
      if (fields.length === 0) {
        reject(new Error('No valid fields to update'));
        return;
      }
      
      fields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(userId, contactId);
      
      const query = `
        UPDATE recruiter_contacts 
        SET ${fields.join(', ')}
        WHERE user_id = ? AND id = ?
      `;
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Delete recruiter contact
   */
  async deleteRecruiterContact(userId, contactId) {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM recruiter_contacts WHERE user_id = ? AND id = ?';
      
      this.db.run(query, [userId, contactId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Bulk import recruiter contacts
   */
  async bulkImportContacts(userId, contacts) {
    const results = {
      success: 0,
      failed: 0,
      errors: []
    };
    
    for (const contact of contacts) {
      try {
        await this.saveRecruiterContact(userId, contact);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          contact: contact.email,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Get contact statistics
   */
  async getContactStatistics(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          COUNT(*) as total_contacts,
          COUNT(CASE WHEN verification_status = 'verified' THEN 1 END) as verified_contacts,
          COUNT(CASE WHEN response_status = 'responded' THEN 1 END) as responded_contacts,
          COUNT(CASE WHEN last_contacted IS NOT NULL THEN 1 END) as contacted_contacts,
          COUNT(DISTINCT company) as unique_companies
        FROM recruiter_contacts 
        WHERE user_id = ?
      `;
      
      this.db.get(query, [userId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          const stats = {
            ...row,
            response_rate: row.contacted_contacts > 0 ? 
              ((row.responded_contacts / row.contacted_contacts) * 100).toFixed(2) : 0,
            verification_rate: row.total_contacts > 0 ? 
              ((row.verified_contacts / row.total_contacts) * 100).toFixed(2) : 0
          };
          resolve(stats);
        }
      });
    });
  }

  /**
   * Search for potential recruiter companies based on job searches
   */
  async suggestCompaniesForDiscovery(userId) {
    return new Promise((resolve, reject) => {
      // Get companies from user's job applications/searches
      const query = `
        SELECT DISTINCT company, COUNT(*) as job_count
        FROM jobs 
        WHERE user_id = ?
        GROUP BY company
        ORDER BY job_count DESC
        LIMIT 20
      `;
      
      this.db.all(query, [userId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          // Generate domain suggestions for each company
          const suggestions = rows.map(row => ({
            company: row.company,
            job_count: row.job_count,
            suggested_domain: this.suggestCompanyDomain(row.company),
            potential_contacts: this.estimateContactCount(row.company)
          }));
          
          resolve(suggestions);
        }
      });
    });
  }

  /**
   * Utility functions
   */
  categorizeEmailPattern(email) {
    if (email.includes('recruiting') || email.includes('recruitment')) {
      return 'recruitment_team';
    } else if (email.includes('talent')) {
      return 'talent_team';
    } else if (email.includes('hr') || email.includes('people')) {
      return 'hr_team';
    } else if (email.includes('careers') || email.includes('jobs')) {
      return 'careers_team';
    } else {
      return 'individual_recruiter';
    }
  }

  calculatePatternConfidence(email) {
    // Simple confidence scoring based on email pattern
    if (email.includes('recruiting') || email.includes('recruitment') || email.includes('talent')) {
      return 0.9;
    } else if (email.includes('hr') || email.includes('careers')) {
      return 0.8;
    } else if (email.includes('jobs') || email.includes('people')) {
      return 0.7;
    } else {
      return 0.6;
    }
  }

  suggestCompanyDomain(companyName) {
    // Simple domain suggestion logic
    const normalized = companyName.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .replace(/inc$|corp$|ltd$|llc$|company$/, '');
    
    return `${normalized}.com`;
  }

  estimateContactCount(companyName) {
    // Estimate potential contact count based on company size indicators
    const indicators = companyName.toLowerCase();
    
    if (indicators.includes('google') || indicators.includes('microsoft') || indicators.includes('amazon')) {
      return '50-100';
    } else if (indicators.includes('startup') || indicators.includes('small')) {
      return '1-5';
    } else {
      return '10-25';
    }
  }
}

module.exports = RecruiterDiscoveryService;