const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

/**
 * Enhanced Sandbox Security Service
 * Provides advanced container isolation, network namespaces, and resource monitoring
 */
class SandboxService {
  constructor() {
    this.activeSandboxes = new Map();
    this.resourceMonitor = new Map();
    this.config = {
      maxConcurrentSandboxes: 10,
      resourceLimits: {
        memory: '512m',
        cpus: '0.5',
        diskIO: '10m', // 10MB/s
        networkBandwidth: '1m' // 1MB/s
      },
      securityProfiles: {
        strict: {
          seccomp: true,
          apparmor: true,
          capabilities: ['CAP_SETUID', 'CAP_SETGID'],
          readOnlyRootfs: true,
          noNewPrivileges: true
        },
        moderate: {
          seccomp: true,
          apparmor: false,
          capabilities: ['CAP_SETUID', 'CAP_SETGID', 'CAP_NET_BIND_SERVICE'],
          readOnlyRootfs: true,
          noNewPrivileges: true
        }
      },
      networkPolicies: {
        none: { networkMode: 'none' }, // Complete isolation
        restricted: { 
          networkMode: 'bridge',
          allowedHosts: ['linkedin.com', 'indeed.com', 'glassdoor.com'],
          blockedPorts: ['22', '23', '135', '445']
        }
      }
    };
  }

  /**
   * Create enhanced sandbox with advanced security features
   */
  async createSecureSandbox(jobData, securityProfile = 'strict', networkPolicy = 'none') {
    const sandboxId = crypto.randomUUID();
    const sandboxName = `cvleap-secure-${sandboxId}`;
    
    try {
      // Validate resource availability
      await this.validateResourceAvailability();
      
      // Generate security configuration
      const securityConfig = this.generateSecurityConfig(securityProfile);
      const networkConfig = this.generateNetworkConfig(networkPolicy);
      
      // Create isolated network namespace if needed
      if (networkPolicy === 'restricted') {
        await this.createRestrictedNetwork(sandboxId);
      }
      
      // Prepare sandbox environment
      const sandboxPath = await this.prepareSandboxEnvironment(sandboxId, jobData);
      
      // Build Docker command with enhanced security
      const dockerArgs = [
        'run',
        '--detach',
        '--name', sandboxName,
        ...this.buildSecurityArgs(securityConfig),
        ...this.buildResourceArgs(),
        ...this.buildNetworkArgs(networkConfig, sandboxId),
        ...this.buildVolumeArgs(sandboxPath),
        'node:18-alpine',
        'node', '/app/job.js'
      ];

      console.log(`🔒 Creating secure sandbox: ${sandboxName}`);
      
      const containerProcess = spawn('docker', dockerArgs);
      
      // Initialize sandbox tracking
      const sandboxInfo = {
        id: sandboxId,
        name: sandboxName,
        process: containerProcess,
        startTime: new Date(),
        status: 'starting',
        securityProfile,
        networkPolicy,
        resourceUsage: {
          cpu: 0,
          memory: 0,
          diskIO: 0,
          networkIO: 0
        }
      };
      
      this.activeSandboxes.set(sandboxId, sandboxInfo);
      
      // Start resource monitoring
      this.startResourceMonitoring(sandboxId);
      
      // Handle container events
      await this.handleContainerEvents(containerProcess, sandboxInfo);
      
      return sandboxId;
      
    } catch (error) {
      console.error(`❌ Failed to create secure sandbox: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate security configuration based on profile
   */
  generateSecurityConfig(profileName) {
    const profile = this.config.securityProfiles[profileName];
    if (!profile) {
      throw new Error(`Unknown security profile: ${profileName}`);
    }
    return profile;
  }

  /**
   * Generate network configuration
   */
  generateNetworkConfig(policyName) {
    const policy = this.config.networkPolicies[policyName];
    if (!policy) {
      throw new Error(`Unknown network policy: ${policyName}`);
    }
    return policy;
  }

  /**
   * Build security arguments for Docker
   */
  buildSecurityArgs(securityConfig) {
    const args = [];
    
    // User namespace isolation
    args.push('--user', '1000:1000');
    
    // Read-only root filesystem
    if (securityConfig.readOnlyRootfs) {
      args.push('--read-only');
      args.push('--tmpfs', '/tmp:rw,noexec,nosuid,size=100m');
    }
    
    // No new privileges
    if (securityConfig.noNewPrivileges) {
      args.push('--security-opt', 'no-new-privileges:true');
    }
    
    // Seccomp profile
    if (securityConfig.seccomp) {
      args.push('--security-opt', 'seccomp=unconfined'); // In production, use custom profile
    }
    
    // AppArmor profile
    if (securityConfig.apparmor) {
      args.push('--security-opt', 'apparmor=docker-default');
    }
    
    // Drop all capabilities and add only required ones
    args.push('--cap-drop', 'ALL');
    if (securityConfig.capabilities) {
      securityConfig.capabilities.forEach(cap => {
        args.push('--cap-add', cap);
      });
    }
    
    return args;
  }

  /**
   * Build resource limit arguments
   */
  buildResourceArgs() {
    const limits = this.config.resourceLimits;
    return [
      '--memory', limits.memory,
      '--cpus', limits.cpus,
      '--pids-limit', '100',
      '--ulimit', 'nofile=1024:1024',
      '--ulimit', 'nproc=50:50'
    ];
  }

  /**
   * Build network arguments
   */
  buildNetworkArgs(networkConfig, sandboxId) {
    const args = [];
    
    if (networkConfig.networkMode === 'none') {
      args.push('--network', 'none');
    } else if (networkConfig.networkMode === 'bridge') {
      args.push('--network', `cvleap-restricted-${sandboxId}`);
    }
    
    return args;
  }

  /**
   * Build volume mount arguments
   */
  buildVolumeArgs(sandboxPath) {
    return [
      '-v', `${sandboxPath}/job.js:/app/job.js:ro`,
      '-v', `${sandboxPath}/temp:/app/temp:rw`,
      '-w', '/app'
    ];
  }

  /**
   * Create restricted network for sandbox
   */
  async createRestrictedNetwork(sandboxId) {
    const networkName = `cvleap-restricted-${sandboxId}`;
    
    // Create isolated bridge network
    const createNetworkArgs = [
      'network', 'create',
      '--driver', 'bridge',
      '--internal', // No external access by default
      '--subnet', '**********/16',
      networkName
    ];
    
    return new Promise((resolve, reject) => {
      const process = spawn('docker', createNetworkArgs);
      process.on('close', (code) => {
        if (code === 0) {
          console.log(`🌐 Created restricted network: ${networkName}`);
          resolve();
        } else {
          reject(new Error(`Failed to create restricted network: ${code}`));
        }
      });
    });
  }

  /**
   * Prepare sandbox environment with job script
   */
  async prepareSandboxEnvironment(sandboxId, jobData) {
    const sandboxPath = path.join('/tmp', `sandbox-${sandboxId}`);
    
    // Create sandbox directory
    await fs.mkdir(sandboxPath, { recursive: true });
    await fs.mkdir(path.join(sandboxPath, 'temp'), { recursive: true });
    
    // Generate secure job script
    const jobScript = this.generateSecureJobScript(jobData);
    await fs.writeFile(path.join(sandboxPath, 'job.js'), jobScript);
    
    // Set strict permissions
    await fs.chmod(sandboxPath, 0o755);
    await fs.chmod(path.join(sandboxPath, 'job.js'), 0o644);
    
    return sandboxPath;
  }

  /**
   * Generate secure job execution script
   */
  generateSecureJobScript(jobData) {
    return `
const { Worker, isMainThread, parentPort } = require('worker_threads');

// Security wrapper for job execution
class SecureJobRunner {
  constructor() {
    this.startTime = Date.now();
    this.maxExecutionTime = 600000; // 10 minutes
  }

  async executeJob() {
    try {
      // Set execution timeout
      const timeout = setTimeout(() => {
        console.error('Job execution timeout');
        process.exit(1);
      }, this.maxExecutionTime);

      console.log('🚀 Starting secure job execution...');
      console.log('Job Data:', ${JSON.stringify(jobData, null, 2)});
      
      // Simulate job execution
      const result = await this.processJobApplication(${JSON.stringify(jobData)});
      
      clearTimeout(timeout);
      console.log('✅ Job completed successfully');
      console.log('Result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Job execution failed:', error.message);
      throw error;
    }
  }

  async processJobApplication(jobData) {
    // Mock job processing - replace with actual browser automation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'completed',
          applied: true,
          platform: jobData.platform,
          company: jobData.company,
          position: jobData.position,
          timestamp: new Date().toISOString()
        });
      }, Math.random() * 5000 + 2000); // 2-7 seconds
    });
  }
}

// Execute job
const runner = new SecureJobRunner();
runner.executeJob()
  .then(result => {
    console.log('Final result:', JSON.stringify(result, null, 2));
    process.exit(0);
  })
  .catch(error => {
    console.error('Execution error:', error);
    process.exit(1);
  });
`;
  }

  /**
   * Start resource monitoring for sandbox
   */
  startResourceMonitoring(sandboxId) {
    const monitorInterval = setInterval(async () => {
      try {
        const stats = await this.getContainerStats(sandboxId);
        const sandboxInfo = this.activeSandboxes.get(sandboxId);
        
        if (sandboxInfo && stats) {
          sandboxInfo.resourceUsage = stats;
          
          // Check for resource violations
          await this.checkResourceViolations(sandboxId, stats);
        }
      } catch (error) {
        console.error(`Resource monitoring error for ${sandboxId}:`, error.message);
      }
    }, 5000); // Monitor every 5 seconds
    
    this.resourceMonitor.set(sandboxId, monitorInterval);
  }

  /**
   * Get container resource statistics
   */
  async getContainerStats(sandboxId) {
    const sandboxInfo = this.activeSandboxes.get(sandboxId);
    if (!sandboxInfo) return null;

    return new Promise((resolve) => {
      const statsProcess = spawn('docker', ['stats', '--no-stream', '--format', 'json', sandboxInfo.name]);
      let output = '';
      
      statsProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      statsProcess.on('close', () => {
        try {
          const stats = JSON.parse(output);
          resolve({
            cpu: parseFloat(stats.CPUPerc?.replace('%', '') || '0'),
            memory: parseFloat(stats.MemPerc?.replace('%', '') || '0'),
            diskIO: stats.BlockIO || '0B',
            networkIO: stats.NetIO || '0B'
          });
        } catch (error) {
          resolve(null);
        }
      });
    });
  }

  /**
   * Check for resource violations and take action
   */
  async checkResourceViolations(sandboxId, stats) {
    const violations = [];
    
    if (stats.cpu > 80) {
      violations.push({ type: 'cpu', value: stats.cpu, limit: 80 });
    }
    
    if (stats.memory > 90) {
      violations.push({ type: 'memory', value: stats.memory, limit: 90 });
    }
    
    if (violations.length > 0) {
      console.warn(`⚠️ Resource violations detected for sandbox ${sandboxId}:`, violations);
      
      // Implement violation response (throttling, termination, etc.)
      await this.handleResourceViolations(sandboxId, violations);
    }
  }

  /**
   * Handle resource violations
   */
  async handleResourceViolations(sandboxId, violations) {
    const sandboxInfo = this.activeSandboxes.get(sandboxId);
    if (!sandboxInfo) return;

    // For severe violations, terminate the sandbox
    const severeViolation = violations.some(v => 
      (v.type === 'cpu' && v.value > 95) || 
      (v.type === 'memory' && v.value > 95)
    );
    
    if (severeViolation) {
      console.log(`🛑 Terminating sandbox ${sandboxId} due to severe resource violations`);
      await this.terminateSandbox(sandboxId);
    }
  }

  /**
   * Handle container process events
   */
  async handleContainerEvents(containerProcess, sandboxInfo) {
    return new Promise((resolve, reject) => {
      containerProcess.stdout?.on('data', (data) => {
        console.log(`📝 [${sandboxInfo.name}]:`, data.toString().trim());
      });

      containerProcess.stderr?.on('data', (data) => {
        console.error(`❌ [${sandboxInfo.name}]:`, data.toString().trim());
      });

      containerProcess.on('close', async (code) => {
        sandboxInfo.status = code === 0 ? 'completed' : 'failed';
        sandboxInfo.exitCode = code;
        sandboxInfo.endTime = new Date();
        
        console.log(`🏁 Sandbox ${sandboxInfo.name} finished with code ${code}`);
        
        // Cleanup resources
        await this.cleanupSandbox(sandboxInfo.id);
        
        if (code === 0) {
          resolve(sandboxInfo);
        } else {
          reject(new Error(`Sandbox execution failed with code ${code}`));
        }
      });

      containerProcess.on('error', (error) => {
        console.error(`💥 Container process error:`, error);
        reject(error);
      });
    });
  }

  /**
   * Validate resource availability before creating sandbox
   */
  async validateResourceAvailability() {
    const activeSandboxCount = this.activeSandboxes.size;
    
    if (activeSandboxCount >= this.config.maxConcurrentSandboxes) {
      throw new Error(`Maximum concurrent sandboxes reached: ${activeSandboxCount}`);
    }
    
    // Additional system resource checks could go here
    return true;
  }

  /**
   * Terminate sandbox forcefully
   */
  async terminateSandbox(sandboxId) {
    const sandboxInfo = this.activeSandboxes.get(sandboxId);
    if (!sandboxInfo) return;

    try {
      // Stop container
      await this.executeDockerCommand(['stop', '-t', '5', sandboxInfo.name]);
      console.log(`⏹️ Terminated sandbox: ${sandboxInfo.name}`);
    } catch (error) {
      console.error(`Failed to terminate sandbox ${sandboxId}:`, error.message);
    }
  }

  /**
   * Cleanup sandbox resources
   */
  async cleanupSandbox(sandboxId) {
    const sandboxInfo = this.activeSandboxes.get(sandboxId);
    if (!sandboxInfo) return;

    try {
      // Stop resource monitoring
      const monitorInterval = this.resourceMonitor.get(sandboxId);
      if (monitorInterval) {
        clearInterval(monitorInterval);
        this.resourceMonitor.delete(sandboxId);
      }

      // Remove container (if still exists)
      await this.executeDockerCommand(['rm', '-f', sandboxInfo.name]).catch(() => {});

      // Remove restricted network (if created)
      if (sandboxInfo.networkPolicy === 'restricted') {
        await this.executeDockerCommand([
          'network', 'rm', `cvleap-restricted-${sandboxId}`
        ]).catch(() => {});
      }

      // Remove sandbox files
      const sandboxPath = path.join('/tmp', `sandbox-${sandboxId}`);
      await fs.rmdir(sandboxPath, { recursive: true }).catch(() => {});

      // Remove from tracking
      this.activeSandboxes.delete(sandboxId);
      
      console.log(`🧹 Cleaned up sandbox: ${sandboxId}`);
    } catch (error) {
      console.error(`Cleanup error for sandbox ${sandboxId}:`, error.message);
    }
  }

  /**
   * Execute Docker command with error handling
   */
  async executeDockerCommand(args) {
    return new Promise((resolve, reject) => {
      const process = spawn('docker', args);
      let output = '';
      let errorOutput = '';
      
      process.stdout?.on('data', (data) => {
        output += data.toString();
      });
      
      process.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(new Error(`Docker command failed: ${errorOutput}`));
        }
      });
    });
  }

  /**
   * Get sandbox information
   */
  getSandboxInfo(sandboxId) {
    return this.activeSandboxes.get(sandboxId);
  }

  /**
   * List all active sandboxes
   */
  listActiveSandboxes() {
    return Array.from(this.activeSandboxes.values()).map(sandbox => ({
      id: sandbox.id,
      name: sandbox.name,
      status: sandbox.status,
      startTime: sandbox.startTime,
      securityProfile: sandbox.securityProfile,
      networkPolicy: sandbox.networkPolicy,
      resourceUsage: sandbox.resourceUsage
    }));
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      activeSandboxes: this.activeSandboxes.size,
      maxConcurrentSandboxes: this.config.maxConcurrentSandboxes,
      resourceMonitors: this.resourceMonitor.size,
      lastCheck: new Date().toISOString()
    };
  }
}

module.exports = SandboxService;