const { BlobServiceClient } = require('@azure/storage-blob');
const { Storage } = require('@google-cloud/storage');
const sharp = require('sharp');
const crypto = require('crypto');
const path = require('path');
const fs = require('fs').promises;

/**
 * Cloud Storage Service for handling file uploads to Azure Blob Storage and Google Cloud Storage
 * Supports multiple cloud providers with automatic failover and comprehensive error handling
 */
class CloudStorageService {
  constructor() {
    this.provider = process.env.CLOUD_STORAGE_PROVIDER || 'azure';
    this.initializeClients();
    
    // Storage configuration
    this.config = {
      azure: {
        connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING,
        containerName: process.env.AZURE_CONTAINER_NAME || 'cvleap-uploads',
        cdnUrl: process.env.AZURE_CDN_URL
      },
      gcp: {
        projectId: process.env.GCP_PROJECT_ID,
        keyFilename: process.env.GCP_KEY_FILE,
        bucketName: process.env.GCP_BUCKET_NAME || 'cvleap-uploads',
        cdnUrl: process.env.GCP_CDN_URL
      }
    };

    // File type configurations
    this.fileTypes = {
      images: {
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
        maxSize: 10 * 1024 * 1024, // 10MB
        sizes: {
          thumbnail: { width: 150, height: 150 },
          medium: { width: 400, height: 400 },
          large: { width: 800, height: 800 }
        }
      },
      documents: {
        allowedMimeTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        maxSize: 25 * 1024 * 1024 // 25MB
      }
    };
  }

  /**
   * Initialize cloud storage clients
   */
  initializeClients() {
    try {
      // Initialize Azure Blob Storage
      if (this.config.azure.connectionString) {
        this.azureClient = BlobServiceClient.fromConnectionString(this.config.azure.connectionString);
        console.log('✅ Azure Blob Storage client initialized');
      }

      // Initialize Google Cloud Storage
      if (this.config.gcp.projectId && this.config.gcp.keyFilename) {
        this.gcpClient = new Storage({
          projectId: this.config.gcp.projectId,
          keyFilename: this.config.gcp.keyFilename
        });
        console.log('✅ Google Cloud Storage client initialized');
      }

      if (!this.azureClient && !this.gcpClient) {
        console.warn('⚠️  No cloud storage providers configured');
      }
    } catch (error) {
      console.error('❌ Failed to initialize cloud storage clients:', error);
    }
  }

  /**
   * Upload profile picture with multiple sizes
   * @param {Buffer} fileBuffer - File buffer
   * @param {Object} fileInfo - File information
   * @param {string} userId - User ID
   * @returns {Object} Upload result with URLs
   */
  async uploadProfilePicture(fileBuffer, fileInfo, userId) {
    try {
      const { originalname, mimetype } = fileInfo;
      
      // Validate file type
      if (!this.fileTypes.images.allowedMimeTypes.includes(mimetype)) {
        throw new Error(`Invalid file type: ${mimetype}`);
      }

      // Validate file size
      if (fileBuffer.length > this.fileTypes.images.maxSize) {
        throw new Error(`File too large: ${fileBuffer.length} bytes`);
      }

      // Generate unique filename
      const fileExtension = path.extname(originalname);
      const baseFilename = `profile-pictures/${userId}/${crypto.randomUUID()}`;

      // Process and upload different sizes
      const uploadResults = {};
      const sizes = this.fileTypes.images.sizes;

      for (const [sizeName, dimensions] of Object.entries(sizes)) {
        const processedBuffer = await this.processImage(fileBuffer, dimensions);
        const filename = `${baseFilename}-${sizeName}${fileExtension}`;
        
        const uploadResult = await this.uploadFile(processedBuffer, filename, mimetype, {
          userId,
          type: 'profile-picture',
          size: sizeName,
          originalName: originalname
        });

        uploadResults[sizeName] = uploadResult;
      }

      // Upload original size
      const originalFilename = `${baseFilename}-original${fileExtension}`;
      uploadResults.original = await this.uploadFile(fileBuffer, originalFilename, mimetype, {
        userId,
        type: 'profile-picture',
        size: 'original',
        originalName: originalname
      });

      return {
        success: true,
        urls: uploadResults,
        metadata: {
          originalName: originalname,
          mimeType: mimetype,
          fileSize: fileBuffer.length,
          uploadedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Profile picture upload failed:', error);
      throw new Error(`Profile picture upload failed: ${error.message}`);
    }
  }

  /**
   * Upload resume document with processing
   * @param {Buffer} fileBuffer - File buffer
   * @param {Object} fileInfo - File information
   * @param {string} userId - User ID
   * @returns {Object} Upload result with processing info
   */
  async uploadResume(fileBuffer, fileInfo, userId) {
    try {
      const { originalname, mimetype } = fileInfo;
      
      // Validate file type
      if (!this.fileTypes.documents.allowedMimeTypes.includes(mimetype)) {
        throw new Error(`Invalid file type: ${mimetype}`);
      }

      // Validate file size
      if (fileBuffer.length > this.fileTypes.documents.maxSize) {
        throw new Error(`File too large: ${fileBuffer.length} bytes`);
      }

      // Generate unique filename
      const fileExtension = path.extname(originalname);
      const filename = `resumes/${userId}/${crypto.randomUUID()}${fileExtension}`;

      // Upload original file
      const uploadResult = await this.uploadFile(fileBuffer, filename, mimetype, {
        userId,
        type: 'resume',
        originalName: originalname
      });

      // Generate thumbnail for PDF files
      let thumbnailUrl = null;
      if (mimetype === 'application/pdf') {
        try {
          thumbnailUrl = await this.generatePdfThumbnail(fileBuffer, filename, userId);
        } catch (error) {
          console.warn('Failed to generate PDF thumbnail:', error.message);
        }
      }

      return {
        success: true,
        url: uploadResult.url,
        secureUrl: uploadResult.secureUrl,
        thumbnailUrl,
        metadata: {
          originalName: originalname,
          mimeType: mimetype,
          fileSize: fileBuffer.length,
          uploadedAt: new Date().toISOString(),
          filename: filename
        }
      };

    } catch (error) {
      console.error('Resume upload failed:', error);
      throw new Error(`Resume upload failed: ${error.message}`);
    }
  }

  /**
   * Process image with Sharp
   * @param {Buffer} buffer - Image buffer
   * @param {Object} dimensions - Target dimensions
   * @returns {Buffer} Processed image buffer
   */
  async processImage(buffer, dimensions) {
    try {
      return await sharp(buffer)
        .resize(dimensions.width, dimensions.height, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 85, progressive: true })
        .toBuffer();
    } catch (error) {
      console.error('Image processing failed:', error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Generate PDF thumbnail using Puppeteer
   * @param {Buffer} pdfBuffer - PDF buffer
   * @param {string} filename - Original filename
   * @param {string} userId - User ID
   * @returns {string} Thumbnail URL
   */
  async generatePdfThumbnail(pdfBuffer, filename, userId) {
    try {
      const puppeteer = require('puppeteer');
      const pdfParse = require('pdf-parse');

      // Parse PDF to get first page text (for preview)
      const pdfData = await pdfParse(pdfBuffer);
      
      // Create a simple HTML representation for thumbnail
      const html = `
        <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; background: white; }
              .preview { font-size: 12px; line-height: 1.4; }
            </style>
          </head>
          <body>
            <div class="preview">
              ${pdfData.text.substring(0, 500).replace(/\n/g, '<br>')}...
            </div>
          </body>
        </html>
      `;

      // Generate thumbnail image
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      await page.setContent(html);
      await page.setViewport({ width: 400, height: 600 });
      
      const thumbnailBuffer = await page.screenshot({
        type: 'png',
        fullPage: false,
        clip: { x: 0, y: 0, width: 400, height: 600 }
      });
      
      await browser.close();

      // Upload thumbnail
      const thumbnailFilename = filename.replace(/\.[^/.]+$/, '-thumbnail.png');
      const thumbnailResult = await this.uploadFile(thumbnailBuffer, thumbnailFilename, 'image/png', {
        userId,
        type: 'resume-thumbnail',
        originalName: filename
      });

      return thumbnailResult.url;

    } catch (error) {
      console.error('PDF thumbnail generation failed:', error);
      throw error;
    }
  }

  /**
   * Upload file to cloud storage
   * @param {Buffer} buffer - File buffer
   * @param {string} filename - Target filename
   * @param {string} mimeType - MIME type
   * @param {Object} metadata - File metadata
   * @returns {Object} Upload result
   */
  async uploadFile(buffer, filename, mimeType, metadata = {}) {
    try {
      let result;

      if (this.provider === 'azure' && this.azureClient) {
        result = await this.uploadToAzure(buffer, filename, mimeType, metadata);
      } else if (this.provider === 'gcp' && this.gcpClient) {
        result = await this.uploadToGCP(buffer, filename, mimeType, metadata);
      } else {
        throw new Error('No cloud storage provider available');
      }

      return result;

    } catch (error) {
      console.error('File upload failed:', error);
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  /**
   * Upload to Azure Blob Storage
   * @param {Buffer} buffer - File buffer
   * @param {string} filename - Target filename
   * @param {string} mimeType - MIME type
   * @param {Object} metadata - File metadata
   * @returns {Object} Upload result
   */
  async uploadToAzure(buffer, filename, mimeType, metadata) {
    try {
      const containerClient = this.azureClient.getContainerClient(this.config.azure.containerName);
      
      // Ensure container exists
      await containerClient.createIfNotExists({
        access: 'blob'
      });

      const blockBlobClient = containerClient.getBlockBlobClient(filename);
      
      // Upload with metadata
      await blockBlobClient.upload(buffer, buffer.length, {
        blobHTTPHeaders: {
          blobContentType: mimeType
        },
        metadata: {
          ...metadata,
          uploadedAt: new Date().toISOString()
        }
      });

      const url = blockBlobClient.url;
      const cdnUrl = this.config.azure.cdnUrl 
        ? `${this.config.azure.cdnUrl}/${filename}`
        : url;

      return {
        url: cdnUrl,
        secureUrl: url,
        filename,
        provider: 'azure'
      };

    } catch (error) {
      console.error('Azure upload failed:', error);
      throw error;
    }
  }

  /**
   * Upload to Google Cloud Storage
   * @param {Buffer} buffer - File buffer
   * @param {string} filename - Target filename
   * @param {string} mimeType - MIME type
   * @param {Object} metadata - File metadata
   * @returns {Object} Upload result
   */
  async uploadToGCP(buffer, filename, mimeType, metadata) {
    try {
      const bucket = this.gcpClient.bucket(this.config.gcp.bucketName);
      const file = bucket.file(filename);

      await file.save(buffer, {
        metadata: {
          contentType: mimeType,
          metadata: {
            ...metadata,
            uploadedAt: new Date().toISOString()
          }
        }
      });

      // Make file publicly readable
      await file.makePublic();

      const url = `https://storage.googleapis.com/${this.config.gcp.bucketName}/${filename}`;
      const cdnUrl = this.config.gcp.cdnUrl 
        ? `${this.config.gcp.cdnUrl}/${filename}`
        : url;

      return {
        url: cdnUrl,
        secureUrl: url,
        filename,
        provider: 'gcp'
      };

    } catch (error) {
      console.error('GCP upload failed:', error);
      throw error;
    }
  }

  /**
   * Delete file from cloud storage
   * @param {string} filename - File to delete
   * @returns {boolean} Success status
   */
  async deleteFile(filename) {
    try {
      if (this.provider === 'azure' && this.azureClient) {
        const containerClient = this.azureClient.getContainerClient(this.config.azure.containerName);
        const blockBlobClient = containerClient.getBlockBlobClient(filename);
        await blockBlobClient.deleteIfExists();
      } else if (this.provider === 'gcp' && this.gcpClient) {
        const bucket = this.gcpClient.bucket(this.config.gcp.bucketName);
        await bucket.file(filename).delete();
      }

      return true;
    } catch (error) {
      console.error('File deletion failed:', error);
      return false;
    }
  }

  /**
   * Generate signed URL for secure access
   * @param {string} filename - File name
   * @param {number} expiresIn - Expiration time in seconds
   * @returns {string} Signed URL
   */
  async generateSignedUrl(filename, expiresIn = 3600) {
    try {
      if (this.provider === 'azure' && this.azureClient) {
        const containerClient = this.azureClient.getContainerClient(this.config.azure.containerName);
        const blockBlobClient = containerClient.getBlockBlobClient(filename);
        
        const expiresOn = new Date(Date.now() + expiresIn * 1000);
        return await blockBlobClient.generateSasUrl({
          permissions: 'r',
          expiresOn
        });
      } else if (this.provider === 'gcp' && this.gcpClient) {
        const bucket = this.gcpClient.bucket(this.config.gcp.bucketName);
        const file = bucket.file(filename);
        
        const [signedUrl] = await file.getSignedUrl({
          action: 'read',
          expires: Date.now() + expiresIn * 1000
        });
        
        return signedUrl;
      }

      throw new Error('No cloud storage provider available');
    } catch (error) {
      console.error('Signed URL generation failed:', error);
      throw error;
    }
  }

  /**
   * Get storage statistics
   * @returns {Object} Storage statistics
   */
  async getStorageStats() {
    try {
      const stats = {
        provider: this.provider,
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {}
      };

      // Implementation would depend on the provider
      // This is a placeholder for storage analytics

      return stats;
    } catch (error) {
      console.error('Failed to get storage stats:', error);
      return null;
    }
  }
}

module.exports = new CloudStorageService();
