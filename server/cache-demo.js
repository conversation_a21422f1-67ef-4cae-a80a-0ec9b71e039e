/**
 * Enhanced Cache Service Demo
 * Demonstrates the new features and capabilities
 */

const cacheService = require('./cacheService');

console.log('🚀 Enhanced Cache Service Demo\n');

// Clear cache and start fresh
cacheService.clear();

// Configure cache with custom limits
console.log('📝 Configuring cache with custom limits...');
cacheService.configure({
  maxCacheSize: 100,
  maxMemoryBytes: 1024 * 1024, // 1MB
  defaultTTL: 60000 // 1 minute
});

console.log('Current configuration:', cacheService.getConfiguration());
console.log('');

// Add some test data with different access patterns
console.log('📊 Adding test data with varied access patterns...');

// Add hot keys (frequently accessed)
cacheService.set('user:123', { name: '<PERSON>', role: 'admin' });
cacheService.set('user:456', { name: '<PERSON>', role: 'user' });

// Add warm keys (moderately accessed)
cacheService.set('config:app', { theme: 'dark', lang: 'en' });
cacheService.set('stats:daily', { views: 1500, users: 250 });

// Add cold keys (rarely accessed)
cacheService.set('archive:old', { data: 'legacy' });
cacheService.set('temp:cache', { value: 'temporary' });

// Simulate access patterns
console.log('🔥 Simulating hot key access patterns...');
for (let i = 0; i < 15; i++) {
  cacheService.get('user:123'); // Very hot
}

for (let i = 0; i < 8; i++) {
  cacheService.get('user:456'); // Hot
}

for (let i = 0; i < 3; i++) {
  cacheService.get('config:app'); // Warm
  cacheService.get('stats:daily'); // Warm
}

// Access cold keys once
cacheService.get('archive:old');
cacheService.get('temp:cache');

console.log('');

// Demonstrate smart TTL
console.log('🧠 Using Smart TTL based on access patterns...');
cacheService.setWithSmartTTL('user:123', { name: 'John Doe Updated', role: 'super-admin' }, 2);
console.log('Smart TTL applied to frequently accessed key');
console.log('');

// Show comprehensive statistics
console.log('📈 Comprehensive Cache Statistics:');
const stats = cacheService.getStats();

console.log(`
Cache Overview:
- Entries: ${stats.size}/${stats.maxSize}
- Hit Rate: ${stats.hitRate}
- Total Requests: ${stats.totalRequests}
- Memory Usage: ${stats.memoryUsage.total.kilobytes} KB (${stats.efficiency.memoryFillRatio})

Performance Metrics:
- Total Operations: ${stats.performance.totalOperations}
- Average Operation Time: ${stats.performance.avgOperationTime}
- Total Operation Time: ${stats.performance.totalOperationTime}

Eviction Statistics:
- Total Evictions: ${stats.evictionStats.totalEvictions}
- LRU Evictions: ${stats.evictionStats.lruEvictions}
- Expired Evictions: ${stats.evictionStats.expiredEvictions}
- Memory Evictions: ${stats.evictionStats.memoryEvictions}
- Last Eviction: ${stats.evictionStats.lastEvictionAge}

Access Patterns:
- Hot Keys: ${stats.accessPatterns.frequencyTiers.hot} (${stats.accessPatterns.frequencyTiers.percentages.hot})
- Warm Keys: ${stats.accessPatterns.frequencyTiers.warm} (${stats.accessPatterns.frequencyTiers.percentages.warm})
- Cold Keys: ${stats.accessPatterns.frequencyTiers.cold} (${stats.accessPatterns.frequencyTiers.percentages.cold})
- Average Access Count: ${stats.averageAccessCount}
`);

console.log('🔥 Top Hot Keys:');
stats.efficiency.hotKeys.forEach((hotKey, index) => {
  console.log(`${index + 1}. ${hotKey.key}`);
  console.log(`   Access Count: ${hotKey.accessCount}`);
  console.log(`   Access Share: ${hotKey.accessShare}`);
  console.log(`   Last Accessed: ${hotKey.lastAccessed}`);
  console.log(`   Efficiency: ${hotKey.efficiency}`);
  console.log('');
});

console.log('💾 Memory Usage Breakdown:');
console.log(`- Total: ${stats.memoryUsage.total.bytes} bytes`);
console.log(`- Keys: ${stats.memoryUsage.breakdown.keys}`);
console.log(`- Values: ${stats.memoryUsage.breakdown.values}`);
console.log(`- Metadata: ${stats.memoryUsage.breakdown.metadata}`);
console.log(`- Average Entry Size: ${stats.memoryUsage.averageEntrySize}`);
console.log('');

// Test memory-based eviction
console.log('🧹 Testing memory-based eviction...');
const initialSize = stats.size;

// Add many large entries to trigger memory eviction
for (let i = 0; i < 50; i++) {
  cacheService.set(`large:${i}`, 'x'.repeat(1000)); // 1KB each
}

const finalStats = cacheService.getStats();
console.log(`Entries before: ${initialSize}, after: ${finalStats.size}`);
console.log(`Memory evictions triggered: ${finalStats.evictionStats.memoryEvictions}`);
console.log('');

// Demonstrate pattern invalidation
console.log('🗑️  Testing pattern-based invalidation...');
const removed = cacheService.invalidatePattern('^large:');
console.log(`Removed ${removed} entries matching pattern '^large:'`);
console.log('');

// Final statistics
const endStats = cacheService.getStats();
console.log('📊 Final Cache State:');
console.log(`- Entries: ${endStats.size}`);
console.log(`- Memory Usage: ${endStats.memoryUsage.total.kilobytes} KB`);
console.log(`- Hit Rate: ${endStats.hitRate}`);
console.log(`- Total Evictions: ${endStats.evictionStats.totalEvictions}`);

console.log('\n✨ Enhanced Cache Service Demo Complete!');