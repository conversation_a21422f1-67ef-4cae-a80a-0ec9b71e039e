const fs = require('fs').promises;
const path = require('path');
const database = require('./database');
const cacheService = require('./cacheService');

class AnalyticsService {
  constructor() {
    this.db = database.get();
    this.cacheTimeout = 300000; // 5 minutes
  }

  // Track various events for analytics
  async trackEvent(userId, eventType, eventData = {}, metadata = {}) {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO analytics_events (user_id, event_type, event_data, metadata) VALUES (?, ?, ?, ?)',
        [userId, eventType, JSON.stringify(eventData), JSON.stringify(metadata)],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  // Record application metrics
  async recordApplicationMetric(userId, applicationId, metricType, metricValue) {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO application_metrics (user_id, application_id, metric_type, metric_value) VALUES (?, ?, ?, ?)',
        [userId, applicationId, metricType, metricValue],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  // Update resume performance metrics
  async updateResumePerformance(resumeId, updates) {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(resumeId);

    return new Promise((resolve, reject) => {
      this.db.run(
        `UPDATE resume_performance SET ${fields}, last_updated = CURRENT_TIMESTAMP WHERE resume_id = ?`,
        values,
        function(err) {
          if (err) {
            // If no row exists, create one
            database.get().run(
              'INSERT INTO resume_performance (resume_id, views, downloads, applications, responses, interviews, offers, ats_score) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
              [resumeId, updates.views || 0, updates.downloads || 0, updates.applications || 0, updates.responses || 0, updates.interviews || 0, updates.offers || 0, updates.ats_score || null],
              function(insertErr) {
                if (insertErr) {
                  reject(insertErr);
                } else {
                  resolve(this.lastID);
                }
              }
            );
          } else {
            resolve(this.changes);
          }
        }
      );
    });
  }

  // Get comprehensive analytics dashboard data
  async getDashboardAnalytics(userId, timeRange = '30 days') {
    const cacheKey = `dashboard_${userId}`;
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const timeFilter = this.getTimeFilter(timeRange);
      const [
        applicationStats,
        jobMarketInsights,
        resumePerformance,
        successPatterns,
        weeklyTrends,
        recentEvents
      ] = await Promise.all([
        this.getApplicationStatistics(userId, timeFilter),
        this.getJobMarketInsights(userId),
        this.getResumePerformanceMetrics(userId),
        this.getSuccessPatterns(userId),
        this.getWeeklyTrends(userId),
        this.getRecentEvents(userId, 10)
      ]);

      return {
        applicationStats,
        jobMarketInsights,
        resumePerformance,
        successPatterns,
        weeklyTrends,
        recentEvents,
        timeRange,
        generatedAt: new Date().toISOString()
      };
    }, this.cacheTimeout);
  }

  // Get application statistics and success metrics
  async getApplicationStatistics(userId, timeFilter) {
    const cacheKey = `app_stats_${userId}`;
    
    return await cacheService.getOrSet(cacheKey, async () => {
      try {
        const applications = await this.getUserApplications(userId);
        const filteredApps = applications.filter(app => {
          if (!timeFilter) return true;
          const appDate = new Date(app.applied_at);
          return appDate >= new Date(new Date().toISOString().slice(0, 10) + timeFilter.slice(10));
        });

        const stats = {
          totalApplications: filteredApps.length,
          pendingApplications: filteredApps.filter(app => app.status === 'pending').length,
          successfulApplications: filteredApps.filter(app => app.status === 'accepted').length,
          rejectedApplications: filteredApps.filter(app => app.status === 'rejected').length,
          interviewsScheduled: filteredApps.filter(app => app.status === 'interview').length,
          successRate: 0,
          averageResponseTime: 0,
          monthlyApplications: this.getMonthlyBreakdown(filteredApps),
          statusDistribution: this.getStatusDistribution(filteredApps)
        };

        if (stats.totalApplications > 0) {
          stats.successRate = ((stats.successfulApplications + stats.interviewsScheduled) / stats.totalApplications) * 100;
        }

        const responseTimes = filteredApps
          .filter(app => app.updated_at && app.applied_at)
          .map(app => new Date(app.updated_at) - new Date(app.applied_at));
        
        if (responseTimes.length > 0) {
          stats.averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
          stats.averageResponseTime = Math.round(stats.averageResponseTime / (1000 * 60 * 60 * 24));
        }

        return stats;
      } catch (error) {
        console.error('Error getting application statistics:', error);
        return this.getDefaultApplicationStats();
      }
    }, 120000);
  }

  // Get job market insights and trends
  async getJobMarketInsights(userId) {
    try {
      const userJobs = await this.getUserJobs(userId);
      const jobTitles = userJobs.map(job => job.title).filter(Boolean);
      const companies = userJobs.map(job => job.company).filter(Boolean);

      const insights = {
        topInDemandSkills: this.generateSkillsTrends(jobTitles),
        salaryTrends: this.generateSalaryTrends(jobTitles),
        competitionLevel: this.calculateCompetitionLevel(jobTitles),
        recommendedActions: this.generateRecommendations(jobTitles, companies),
        marketOutlook: this.generateMarketOutlook(jobTitles),
        emergingOpportunities: this.generateEmergingOpportunities(),
        locationInsights: this.generateLocationInsights(companies)
      };

      return insights;
    } catch (error) {
      console.error('Error getting job market insights:', error);
      return this.getDefaultMarketInsights();
    }
  }

  // Get resume performance metrics
  async getResumePerformanceMetrics(userId) {
    try {
      const resumes = await this.getUserResumes(userId);
      const applications = await this.getUserApplications(userId);

      const metrics = {
        totalResumes: resumes.length,
        activeResumes: resumes.filter(r => r.is_active).length,
        averageATSScore: 0,
        keywordOptimization: 0,
        resumeVariations: resumes.length,
        performanceByResume: [],
        improvementSuggestions: []
      };

      for (const resume of resumes) {
        const resumeApps = applications.filter(app => app.resume_id === resume.id);
        const performance = {
          resumeId: resume.id,
          title: resume.title,
          applications: resumeApps.length,
          successRate: resumeApps.length > 0 ? 
            (resumeApps.filter(app => ['accepted', 'interview'].includes(app.status)).length / resumeApps.length) * 100 : 0,
          lastUsed: resumeApps.length > 0 ? 
            new Date(Math.max(...resumeApps.map(app => new Date(app.applied_at)))).toISOString() : null,
          views: resume.views || 0,
          downloads: resume.downloads || 0,
          responses: resume.responses || 0,
          interviews: resume.interviews || 0,
          offers: resume.offers || 0,
          ats_score: resume.ats_score || null,
          response_rate: resumeApps.length > 0 ? (resume.responses * 1.0 / resumeApps.length) * 100 : 0,
          interview_rate: resumeApps.length > 0 ? (resume.interviews * 1.0 / resumeApps.length) * 100 : 0
        };
        metrics.performanceByResume.push(performance);
      }

      if (metrics.performanceByResume.length > 0) {
        metrics.averageATSScore = 85 + Math.random() * 10;
        metrics.keywordOptimization = 75 + Math.random() * 20;
      }

      metrics.improvementSuggestions = this.generateResumeSuggestions(metrics);
      return metrics;
    } catch (error) {
      console.error('Error getting resume performance metrics:', error);
      return this.getDefaultResumeMetrics();
    }
  }

  // Identify success patterns using ML-like analysis
  async getSuccessPatterns(userId) {
    try {
      const applications = await this.getUserApplications(userId);
      const successfulApps = applications.filter(app => ['accepted', 'interview'].includes(app.status));

      const patterns = {
        bestApplicationTimes: this.analyzeBestTimes(successfulApps),
        successfulCompanyTypes: this.analyzeCompanyTypes(successfulApps),
        effectiveKeywords: this.analyzeKeywords(successfulApps),
        optimalApplicationFrequency: this.analyzeFrequency(applications),
        industrySuccessRates: this.analyzeIndustrySuccess(applications),
        seasonalTrends: this.analyzeSeasonalTrends(applications),
        predictiveInsights: this.generatePredictiveInsights(applications),
        timingPatterns: this.analyzeTimingPatterns(applications),
        companyPatterns: this.analyzeCompanyPatterns(applications),
        skillPatterns: this.analyzeSkillPatterns(applications),
        locationPatterns: this.analyzeLocationPatterns(applications)
      };

      for (const pattern of Object.values(patterns).filter(p => p)) {
        await this.storePattern(userId, pattern);
      }

      return patterns;
    } catch (error) {
      console.error('Error getting success patterns:', error);
      return this.getDefaultSuccessPatterns();
    }
  }

  // Get weekly application trends
  async getWeeklyTrends(userId) {
    try {
      const applications = await this.getUserApplications(userId);
      const last12Weeks = this.getLast12Weeks();

      const trends = last12Weeks.map(week => {
        const weekApps = applications.filter(app => {
          const appDate = new Date(app.applied_at);
          return appDate >= week.start && appDate <= week.end;
        });

        return {
          week: week.label,
          applications: weekApps.length,
          responses: weekApps.filter(app => app.status !== 'pending').length,
          interviews: weekApps.filter(app => app.status === 'interview').length,
          offers: weekApps.filter(app => app.status === 'accepted').length
        };
      });

      return {
        weeklyData: trends,
        totalTrend: this.calculateTrend(trends.map(t => t.applications)),
        responseTrend: this.calculateTrend(trends.map(t => t.responses)),
        conversionRate: this.calculateAverageConversion(trends)
      };
    } catch (error) {
      console.error('Error getting weekly trends:', error);
      return this.getDefaultWeeklyTrends();
    }
  }

  // Generate predictive analytics for application success
  async getPredictiveAnalytics(userId, jobData) {
    try {
      const userApplications = await this.getUserApplications(userId);
      const userResumes = await this.getUserResumes(userId);

      const factors = {
        companySize: this.getCompanySizeScore(jobData.company),
        industryMatch: this.getIndustryMatchScore(jobData, userApplications),
        skillsAlignment: this.getSkillsAlignmentScore(jobData, userResumes),
        timingOptimality: this.getTimingScore(new Date()),
        competitionLevel: this.getCompetitionScore(jobData),
        locationFactor: this.getLocationScore(jobData.location)
      };

      const successProbability = this.calculateSuccessProbability(factors);
      const confidenceScore = this.calculateConfidenceScore(factors);

      return {
        successProbability: Math.round(successProbability * 100) / 100,
        confidenceScore: Math.round(confidenceScore * 100) / 100,
        factors: factors,
        recommendations: this.generateApplicationRecommendations(factors, successProbability),
        optimalApplicationTime: this.getOptimalApplicationTime(factors),
        estimatedResponseTime: this.estimateResponseTime(factors)
      };
    } catch (error) {
      console.error('Error generating predictive analytics:', error);
      return this.getDefaultPredictiveAnalytics();
    }
  }

  // Generate optimization recommendations based on analytics
  async generateOptimizationRecommendations(userId) {
    try {
      const analytics = await this.getDashboardAnalytics(userId);
      const recommendations = [];

      if (analytics.applicationStats.successRate < 10) {
        recommendations.push({
          type: 'resume_optimization',
          priority: 'high',
          title: 'Improve Resume Performance',
          description: 'Your application success rate is below average. Consider optimizing your resume content and targeting.',
          actionItems: [
            'Run ATS compatibility analysis',
            'Update resume with industry-specific keywords',
            'Quantify achievements with specific metrics'
          ]
        });
      }

      const avgResponseRate = analytics.resumePerformance.performanceByResume.reduce((sum, resume) => 
        sum + (resume.response_rate || 0), 0) / analytics.resumePerformance.performanceByResume.length || 0;

      if (avgResponseRate < 15) {
        recommendations.push({
          type: 'targeting_optimization',
          priority: 'medium',
          title: 'Improve Job Targeting',
          description: 'Your response rate suggests better job targeting could improve results.',
          actionItems: [
            'Focus on roles that match 80%+ of your skills',
            'Research company culture fit before applying',
            'Customize applications for each opportunity'
          ]
        });
      }

      const emailPerf = analytics.emailMetrics?.find(e => e.campaign_type === 'follow_up');
      if (emailPerf && emailPerf.reply_rate < 5) {
        recommendations.push({
          type: 'communication_optimization',
          priority: 'medium',
          title: 'Enhance Follow-up Strategy',
          description: 'Your follow-up emails have low response rates.',
          actionItems: [
            'Personalize follow-up messages',
            'Time follow-ups strategically',
            'Add value in each communication'
          ]
        });
      }

      return recommendations;
    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw new Error('Failed to generate optimization recommendations');
    }
  }

  // Database helper methods
  async getUserApplications(userId) {
    return new Promise((resolve, reject) => {
      this.db.all(
        'SELECT * FROM applications WHERE user_id = ? ORDER BY applied_at DESC',
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  async getUserJobs(userId) {
    return new Promise((resolve, reject) => {
      this.db.all(
        'SELECT * FROM jobs WHERE user_id = ? ORDER BY created_at DESC',
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  async getUserResumes(userId) {
    return new Promise((resolve, reject) => {
      this.db.all(
        'SELECT * FROM resumes WHERE user_id = ? ORDER BY updated_at DESC',
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  async getRecentEvents(userId, limit = 10) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          event_type,
          event_data,
          metadata,
          created_at
        FROM analytics_events 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?
      `;
      
      this.db.all(query, [userId, limit], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const events = rows.map(row => ({
            ...row,
            event_data: JSON.parse(row.event_data || '{}'),
            metadata: JSON.parse(row.metadata || '{}')
          }));
          resolve(events);
        }
      });
    });
  }

  // Cache management
  invalidateUserCache(userId) {
    cacheService.invalidateUser(userId);
  }

  // Default data methods for error cases
  getDefaultApplicationStats() {
    return {
      totalApplications: 0,
      pendingApplications: 0,
      successfulApplications: 0,
      rejectedApplications: 0,
      interviewsScheduled: 0,
      totalJobsSaved: 0,
      successRateSegments: 0,
      averageResponseTime: 0,
      monthlyApplications: [],
      statusDistribution: {}
    };
  }

  getDefaultMarketInsights() {
    return {
      topInDemandSkills: [],
      salaryTrends: {},
      competitionLevel: 'medium',
      recommendedActions: [],
      marketOutlook: 'stable',
      emergingOpportunities: [],
      locationInsights: {}
    };
  }

  getDefaultResumeMetrics() {
    return {
      totalResumes: 0,
      activeResumes: 0,
      averageATSScore: 0,
      keywordOptimization: 0,
      resumeVariations: 0,
      performanceByResume: [],
      improvementSuggestions: []
    };
  }

  getDefaultSuccessPatterns() {
    return {
      bestApplicationTimes: {},
      successfulCompanyTypes: [],
      effectiveKeywords: [],
      optimalApplicationFrequency: 'weekly',
      industrySuccessRates: {},
      seasonalTrends: {},
      predictiveInsights: {},
      timingPatterns: null,
      companyPatterns: null,
      skillPatterns: null,
      locationPatterns: null
    };
  }

  getDefaultWeeklyTrends() {
    return {
      weeklyData: [],
      totalTrend: 'stable',
      responseTrend: 'stable',
      conversionRate: 0
    };
  }

  getDefaultPredictiveAnalytics() {
    return {
      successProbability: 0.5,
      confidenceScore: 0.6,
      factors: {},
      recommendations: [],
      optimalApplicationTime: new Date(),
      estimatedResponseTime: 7
    };
  }

  // Additional analysis helper methods
  getMonthlyBreakdown(applications) {
    const months = {};
    applications.forEach(app => {
      const month = new Date(app.applied_at).toISOString().substring(0, 7);
      months[month] = (months[month] || 0) + 1;
    });
    return months;
  }

  getStatusDistribution(applications) {
    const distribution = {};
    applications.forEach(app => {
      distribution[app.status] = (distribution[app.status] || 0) + 1;
    });
    return distribution;
  }

  getLast12Weeks() {
    const weeks = [];
    const now = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - (i * 7));
      const weekStart = new Date(weekEnd);
      weekStart.setDate(weekEnd.getDate() - 6);
      
      weeks.push({
        label: `Week ${12 - i}`,
        start: weekStart,
        end: weekEnd
      });
    }
    
    return weeks;
  }

  calculateTrend(data) {
    if (data.length < 2) return 'stable';
    const recent = data.slice(-4).reduce((sum, val) => sum + val, 0) / 4;
    const older = data.slice(0, 4).reduce((sum, val) => sum + val, 0) / 4;
    
    if (recent > older * 1.1) return 'increasing';
    if (recent < older * 0.9) return 'decreasing';
    return 'stable';
  }

  calculateAverageConversion(trends) {
    const totalApps = trends.reduce((sum, t) => sum + t.applications, 0);
    const totalResponses = trends.reduce((sum, t) => sum + t.responses, 0);
    return totalApps > 0 ? (totalResponses / totalApps) * 100 : 0;
  }

  generateSkillsTrends(jobTitles) {
    const skillsMap = {
      'Software Engineer': ['JavaScript', 'Python', 'React', 'Node.js', 'Azure'],
      'Data Scientist': ['Python', 'Machine Learning', 'SQL', 'TensorFlow', 'R'],
      'Product Manager': ['Strategy', 'Analytics', 'Agile', 'Leadership', 'Communication'],
      'Designer': ['Figma', 'Adobe Creative Suite', 'User Research', 'Prototyping', 'CSS']
    };

    const allSkills = jobTitles.flatMap(title => {
      for (const [key, skills] of Object.entries(skillsMap)) {
        if (title.toLowerCase().includes(key.toLowerCase())) {
          return skills;
        }
      }
      return ['Communication', 'Problem Solving', 'Teamwork'];
    });

    const skillCounts = {};
    allSkills.forEach(skill => {
      skillCounts[skill] = (skillCounts[skill] || 0) + 1;
    });

    return Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([skill, count]) => ({ skill, demand: count, growth: Math.random() * 20 + 5 }));
  }

  generateSalaryTrends(jobTitles) {
    // Simulated salary trends
    return jobTitles.reduce((trends, title) => {
      trends[title] = {
        average: 80000 + Math.random() * 50000,
        min: 60000 + Math.random() * 30000,
        max: 100000 + Math.random() * 80000,
        trend: Math.random() > 0.5 ? 'increasing' : 'stable'
      };
      return trends;
    }, {});
  }

  calculateCompetitionLevel(jobTitles) {
    // Simulated competition level
    const count = jobTitles.length;
    if (count > 20) return 'high';
    if (count > 10) return 'medium';
    return 'low';
  }

  generateRecommendations(jobTitles, companies) {
    return [
      'Tailor your resume to highlight relevant skills',
      'Network with professionals at target companies',
      'Consider certifications in high-demand skills'
    ];
  }

  generateMarketOutlook(jobTitles) {
    return Math.random() > 0.5 ? 'positive' : 'stable';
  }

  generateEmergingOpportunities() {
    return [
      'Remote tech roles',
      'AI/ML development positions',
      'Sustainability-focused jobs'
    ];
  }

  generateLocationInsights(companies) {
    const locations = companies.map(c => c.split(',')[1]?.trim() || 'Unknown');
    const counts = locations.reduce((acc, loc) => {
      acc[loc] = (acc[loc] || 0) + 1;
      return acc;
    }, {});
    return counts;
  }

  generateResumeSuggestions(metrics) {
    const suggestions = [];
    if (metrics.averageATSScore < 80) {
      suggestions.push('Optimize resume for ATS: add more industry-specific keywords');
    }
    if (metrics.keywordOptimization < 70) {
      suggestions.push('Improve keyword density for better job matching');
    }
    if (metrics.resumeVariations < 2) {
      suggestions.push('Create multiple resume versions for different role types');
    }
    return suggestions;
  }

  analyzeBestTimes(applications) {
    const times = applications.reduce((acc, app) => {
      const hour = new Date(app.applied_at).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {});
    const bestHour = Object.keys(times).reduce((a, b) => times[a] > times[b] ? a : b, 0);
    return { bestHour: parseInt(bestHour), frequency: times[bestHour] };
  }

  analyzeCompanyTypes(applications) {
    const types = applications.reduce((acc, app) => {
      const type = this.estimateCompanySize(app.company);
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    return Object.entries(types).map(([type, count]) => ({ type, frequency: count }));
  }

  analyzeKeywords(applications) {
    const allKeywords = applications.flatMap(app => app.resume_data?.skills || []);
    const counts = allKeywords.reduce((acc, keyword) => {
      acc[keyword] = (acc[keyword] || 0) + 1;
      return acc;
    }, {});
    return Object.entries(counts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([keyword, count]) => ({ keyword, frequency: count }));
  }

  analyzeFrequency(applications) {
    const weekly = applications.reduce((acc, app) => {
      const week = new Date(app.applied_at).toISOString().slice(0, 10);
      acc[week] = (acc[week] || 0) + 1;
      return acc;
    }, {});
    const avg = Object.values(weekly).reduce((sum, val) => sum + val, 0) / Object.keys(weekly).length;
    return avg > 3 ? 'daily' : avg > 1 ? 'weekly' : 'monthly';
  }

  analyzeIndustrySuccess(applications) {
    const industries = applications.reduce((acc, app) => {
      const industry = app.job_title?.split(' ')[0] || 'Unknown';
      acc[industry] = acc[industry] || { total: 0, success: 0 };
      acc[industry].total++;
      if (['accepted', 'interview'].includes(app.status)) acc[industry].success++;
      return acc;
    }, {});
    return Object.entries(industries).reduce((acc, [industry, data]) => {
      acc[industry] = data.total > 0 ? (data.success / data.total) * 100 : 0;
      return acc;
    }, {});
  }

  analyzeSeasonalTrends(applications) {
    const seasons = applications.reduce((acc, app) => {
      const month = new Date(app.applied_at).getMonth();
      const season = month < 3 ? 'Winter' : month < 6 ? 'Spring' : month < 9 ? 'Summer' : 'Fall';
      acc[season] = (acc[season] || 0) + 1;
      return acc;
    }, {});
    return seasons;
  }

  generatePredictiveInsights(applications) {
    return {
      nextMonthPrediction: applications.length > 0 ? 'likely to increase' : 'stable',
      recommendedFocus: applications.length > 5 ? 'targeted applications' : 'volume increase'
    };
  }

  analyzeTimingPatterns(applications) {
    const days = applications.map(app => new Date(app.applied_at).getDay());
    const dayCounts = days.reduce((acc, day) => {
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {});

    const bestDay = Object.keys(dayCounts).reduce((a, b) => 
      dayCounts[a] > dayCounts[b] ? a : b
    );

    const successRate = (dayCounts[bestDay] / applications.length) * 100;

    if (successRate > 30) {
      return {
        type: 'timing',
        data: { bestDay: parseInt(bestDay), successRate },
        description: `Applications on ${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][bestDay]} have ${successRate.toFixed(1)}% success rate`
      };
    }
    return null;
  }

  analyzeCompanyPatterns(applications) {
    const companySizes = applications.map(app => this.estimateCompanySize(app.company));
    const sizeCounts = companySizes.reduce((acc, size) => {
      acc[size] = (acc[size] || 0) + 1;
      return acc;
    }, {});

    const bestSize = Object.keys(sizeCounts).reduce((a, b) => 
      sizeCounts[a] > sizeCounts[b] ? a : b
    );

    const successRate = (sizeCounts[bestSize] / applications.length) * 100;

    if (successRate > 40) {
      return {
        type: 'company_size',
        data: { bestSize, successRate },
        description: `${bestSize} companies show ${successRate.toFixed(1)}% success rate`
      };
    }
    return null;
  }

  analyzeSkillPatterns(applications) {
    const allSkills = applications.flatMap(app => app.resume_data.skills || []);
    const skillCounts = allSkills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {});

    const topSkills = Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([skill, count]) => ({
        skill,
        frequency: (count / applications.length) * 100
      }));

    if (topSkills.length > 0) {
      return {
        type: 'skills',
        data: { topSkills },
        description: `Top performing skills: ${topSkills.map(s => s.skill).join(', ')}`
      };
    }
    return null;
  }

  analyzeLocationPatterns(applications) {
    const locations = applications.map(app => app.location).filter(Boolean);
    const locationCounts = locations.reduce((acc, location) => {
      acc[location] = (acc[location] || 0) + 1;
      return acc;
    }, {});

    const bestLocation = Object.keys(locationCounts).reduce((a, b) => 
      locationCounts[a] > locationCounts[b] ? a : b
    );

    const successRate = (locationCounts[bestLocation] / applications.length) * 100;

    if (successRate > 30 && locations.length > 2) {
      return {
        type: 'location',
        data: { bestLocation, successRate },
        description: `${bestLocation} shows ${successRate.toFixed(1)}% success rate`
      };
    }
    return null;
  }

  estimateCompanySize(companyName) {
    const knownLarge = ['google', 'microsoft', 'apple', 'amazon', 'facebook', 'meta'];
    const knownMedium = ['startup', 'inc', 'corp', 'corporation'];
    
    const name = companyName.toLowerCase();
    
    if (knownLarge.some(large => name.includes(large))) {
      return 'large';
    } else if (knownMedium.some(medium => name.includes(medium))) {
      return 'medium';
    } else {
      return 'small';
    }
  }

  async storePattern(userId, pattern) {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO job_search_patterns (user_id, pattern_type, pattern_data, success_rate) VALUES (?, ?, ?, ?)',
        [userId, pattern.type, JSON.stringify(pattern.data), pattern.data.successRate || 0],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  getTimeFilter(timeRange) {
    const ranges = {
      '7 days': "AND created_at >= date('now', '-7 days')",
      '30 days': "AND created_at >= date('now', '-30 days')",
      '90 days': "AND created_at >= date('now', '-90 days')",
      '6 months': "AND created_at >= date('now', '-6 months')",
      '1 year': "AND created_at >= date('now', '-1 year')",
      'all': ''
    };
    
    return ranges[timeRange] || ranges['30 days'];
  }

  // Placeholder methods for various scoring functions
  getCompanySizeScore(company) { return 70 + Math.random() * 20; }
  getIndustryMatchScore(jobData, applications) { return 75 + Math.random() * 20; }
  getSkillsAlignmentScore(jobData, resumes) { return 80 + Math.random() * 15; }
  getTimingScore(date) { return 60 + Math.random() * 30; }
  getCompetitionScore(jobData) { return 50 + Math.random() * 40; }
  getLocationScore(location) { return 65 + Math.random() * 25; }

  calculateSuccessProbability(factors) {
    const weights = {
      companySize: 0.15,
      industryMatch: 0.25,
      skillsAlignment: 0.30,
      timingOptimality: 0.10,
      competitionLevel: 0.15,
      locationFactor: 0.05
    };

    let probability = 0;
    for (const [factor, score] of Object.entries(factors)) {
      probability += (score / 100) * (weights[factor] || 0);
    }

    return Math.max(0.1, Math.min(0.95, probability));
  }

  calculateConfidenceScore(factors) {
    const scores = Object.values(factors);
    const avg = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - avg, 2), 0) / scores.length;
    return Math.max(0.3, Math.min(0.95, (100 - Math.sqrt(variance)) / 100));
  }

  generateApplicationRecommendations(factors, probability) {
    const recommendations = [];
    
    if (factors.skillsAlignment < 70) {
      recommendations.push('Consider updating your resume to better match the required skills');
    }
    
    if (factors.timingOptimality < 60) {
      recommendations.push('Wait for a more optimal application time (Tuesday-Thursday, 9-11 AM)');
    }
    
    if (probability < 0.4) {
      recommendations.push('This position may be highly competitive - consider similar roles with better fit');
    }
    
    return recommendations;
  }

  getOptimalApplicationTime(factors) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0);
    return tomorrow;
  }

  estimateResponseTime(factors) {
    const baseTime = 7;
    const sizeMultiplier = factors.companySize > 80 ? 1.5 : 1;
    const competitionMultiplier = factors.competitionLevel > 70 ? 1.3 : 1;
    
    return Math.round(baseTime * sizeMultiplier * competitionMultiplier);
  }

  // AI-Powered Analytics Features
  async generateAIInsights(userId, resumeData = null, jobApplications = [], options = {}) {
    const AIService = require('./aiService');
    const aiService = new AIService();

    try {
      const prompt = `
Analyze this user's job search data and provide intelligent insights:

User ID: ${userId}
Resume Data: ${resumeData ? JSON.stringify(resumeData, null, 2) : 'Not provided'}
Job Applications: ${JSON.stringify(jobApplications.slice(0, 10), null, 2)} // Latest 10

Provide comprehensive AI-powered insights:

{
  "performanceAnalysis": {
    "overallScore": 85,
    "strengths": ["Strong technical skills", "Consistent application frequency"],
    "improvementAreas": ["Interview conversion rate", "Application targeting"],
    "benchmarkComparison": "Above average for industry"
  },
  "trendAnalysis": {
    "applicationTrend": "increasing",
    "responseTrend": "stable", 
    "successPrediction": "positive outlook",
    "seasonalFactors": ["Q4 hiring typically slows", "Tech hiring picks up in January"]
  },
  "personalizedRecommendations": [
    {
      "category": "strategy",
      "priority": "high",
      "recommendation": "Focus on mid-size companies (100-500 employees)",
      "reasoning": "Your profile shows 40% higher success rate with this segment",
      "expectedImpact": "25% improvement in response rate"
    },
    {
      "category": "skills",
      "priority": "medium", 
      "recommendation": "Add cloud certification (AWS/Azure)",
      "reasoning": "Appears in 70% of target job requirements",
      "expectedImpact": "15% increase in interview requests"
    }
  ],
  "marketIntelligence": {
    "demandForecast": "high demand expected for your skills in next 6 months",
    "salaryTrends": "Increasing 8% annually in your field",
    "competitionLevel": "moderate - 50 applicants per position average",
    "emergingOpportunities": ["Remote-first companies", "AI/ML adjacent roles"]
  },
  "optimizationSuggestions": [
    "Apply Tuesday-Thursday 9-11 AM for 23% higher open rates",
    "Tailor resume for each application - current generic approach limiting success",
    "Follow up 1 week after application for 18% higher response rate"
  ]
}`;

      const result = await aiService.callWithFallback(prompt, {
        maxTokens: 3000,
        temperature: 0.4,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('AI insights generation error:', error);
      // Return fallback insights
      return this.getDefaultAIInsights();
    }
  }

  async predictApplicationSuccess(resumeData, jobData, userHistory = [], options = {}) {
    const AIService = require('./aiService');
    const aiService = new AIService();

    try {
      const prompt = `
Predict the success probability for this job application:

Resume Data: ${JSON.stringify(resumeData, null, 2)}
Job Data: ${JSON.stringify(jobData, null, 2)}
User History: ${JSON.stringify(userHistory.slice(0, 5), null, 2)} // Recent applications

Provide detailed success prediction:

{
  "successPrediction": {
    "probability": 0.72,
    "confidenceLevel": 0.85,
    "riskLevel": "low",
    "expectedOutcome": "likely positive response"
  },
  "factorAnalysis": {
    "skillsMatch": {
      "score": 85,
      "matchingSkills": ["JavaScript", "React", "Node.js"],
      "missingSkills": ["TypeScript", "GraphQL"],
      "impact": "high"
    },
    "experienceAlignment": {
      "score": 78,
      "relevantYears": 4,
      "industryMatch": "excellent",
      "roleProgression": "appropriate",
      "impact": "medium"
    },
    "companyFit": {
      "score": 70,
      "companySizeMatch": "good",
      "cultureAlignment": "estimated good",
      "locationFactor": "remote friendly",
      "impact": "medium"
    }
  },
  "improvementSuggestions": [
    {
      "area": "skills",
      "suggestion": "Highlight TypeScript experience if any",
      "potentialGain": "+8% success probability"
    },
    {
      "area": "presentation",
      "suggestion": "Quantify achievements in current role descriptions",
      "potentialGain": "+12% success probability"
    }
  ],
  "timingRecommendation": {
    "optimalApplicationTime": "Tuesday 10 AM",
    "urgency": "moderate",
    "reasoning": "Position posted 3 days ago, ideal application window"
  },
  "benchmarkComparison": {
    "vsAverageCandidate": "+15% higher probability",
    "vsYourHistory": "consistent with previous successful applications",
    "industryAverage": "above average for this role level"
  }
}`;

      const result = await aiService.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Success prediction error:', error);
      return this.getDefaultSuccessPrediction();
    }
  }

  async generateMarketTrends(industryData = [], skillsData = [], options = {}) {
    const AIService = require('./aiService');
    const aiService = new AIService();

    try {
      const prompt = `
Analyze current job market trends based on this data:

Industry Data: ${JSON.stringify(industryData, null, 2)}
Skills Data: ${JSON.stringify(skillsData, null, 2)}

Provide comprehensive market analysis:

{
  "marketOverview": {
    "currentState": "strong demand in tech sector",
    "outlook": "positive growth expected",
    "keyDrivers": ["AI adoption", "Digital transformation", "Remote work normalization"]
  },
  "industryTrends": [
    {
      "industry": "Technology", 
      "growthRate": "12% annually",
      "demandLevel": "high",
      "averageSalary": "$95,000",
      "topRoles": ["Software Engineer", "Data Scientist", "Product Manager"]
    }
  ],
  "skillsTrends": [
    {
      "skill": "AI/Machine Learning",
      "demandGrowth": "85% increase",
      "averageSalaryBoost": "+$15,000",
      "adoptionRate": "rapidly increasing",
      "futureProjection": "critical skill for next 5 years"
    }
  ],
  "emergingOpportunities": [
    {
      "area": "AI Ethics",
      "description": "New roles in responsible AI development",
      "growthPotential": "300% in 2 years",
      "requiredSkills": ["AI/ML", "Ethics", "Policy"]
    }
  ],
  "regionalInsights": [
    {
      "region": "Remote",
      "growth": "40% increase in remote positions",
      "averageSalary": "10% higher than local equivalent",
      "topIndustries": ["Tech", "Finance", "Consulting"]
    }
  ],
  "predictions": {
    "nextQuarter": "Increased hiring in Q1 2024",
    "yearEnd": "15% growth in tech positions",
    "longTerm": "AI and data roles to dominate market"
  }
}`;

      const result = await aiService.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.4,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Market trends analysis error:', error);
      return this.getDefaultMarketTrends();
    }
  }

  async generatePersonalizedStrategy(userData, goals = {}, options = {}) {
    const AIService = require('./aiService');
    const aiService = new AIService();

    try {
      const prompt = `
Create a personalized job search strategy for this user:

User Data: ${JSON.stringify(userData, null, 2)}
Goals: ${JSON.stringify(goals, null, 2)}

Provide comprehensive personalized strategy:

{
  "strategy": {
    "primaryApproach": "targeted applications with strong networking",
    "timeframe": "3-6 months",
    "applicationVolume": "8-12 applications per week",
    "successMetrics": ["40% response rate", "3+ interviews per month"]
  },
  "actionPlan": {
    "immediate": [
      {
        "action": "Update resume with quantified achievements",
        "timeline": "1 week",
        "priority": "high",
        "effort": "medium"
      },
      {
        "action": "Optimize LinkedIn profile",
        "timeline": "3 days", 
        "priority": "high",
        "effort": "low"
      }
    ],
    "shortTerm": [
      {
        "action": "Complete AWS certification",
        "timeline": "1 month",
        "priority": "medium",
        "effort": "high"
      }
    ],
    "longTerm": [
      {
        "action": "Build portfolio project showcasing AI skills",
        "timeline": "3 months",
        "priority": "medium",
        "effort": "high"
      }
    ]
  },
  "targetCompanies": [
    {
      "company": "TechCorp Inc",
      "reasoning": "Strong culture fit + actively hiring",
      "probability": "high",
      "applicationStrategy": "Direct application + LinkedIn outreach"
    }
  ],
  "skillDevelopment": [
    {
      "skill": "TypeScript",
      "priority": "high",
      "timeToLearn": "2 weeks",
      "resources": ["Official docs", "Practice projects"],
      "impact": "15% increase in relevant positions"
    }
  ],
  "networkingStrategy": {
    "approach": "Technical communities + professional meetups",
    "targets": ["Senior engineers at target companies", "Tech recruiters"],
    "frequency": "2-3 interactions per week",
    "platforms": ["LinkedIn", "Twitter", "GitHub"]
  },
  "monitoringKPIs": [
    "Application response rate > 30%",
    "Interview conversion rate > 15%", 
    "Network growth 10+ connections/week"
  ]
}`;

      const result = await aiService.callWithFallback(prompt, {
        maxTokens: 3000,
        temperature: 0.5,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Personalized strategy generation error:', error);
      return this.getDefaultPersonalizedStrategy();
    }
  }

  // Fallback methods for AI failures
  getDefaultAIInsights() {
    return {
      performanceAnalysis: {
        overallScore: 75,
        strengths: ["Consistent application activity"],
        improvementAreas: ["Application targeting"],
        benchmarkComparison: "Average for industry"
      },
      trendAnalysis: {
        applicationTrend: "stable",
        responseTrend: "stable", 
        successPrediction: "neutral outlook",
        seasonalFactors: []
      },
      personalizedRecommendations: [],
      marketIntelligence: {
        demandForecast: "stable demand",
        salaryTrends: "stable",
        competitionLevel: "moderate",
        emergingOpportunities: []
      },
      optimizationSuggestions: []
    };
  }

  getDefaultSuccessPrediction() {
    return {
      successPrediction: {
        probability: 0.5,
        confidenceLevel: 0.6,
        riskLevel: "medium",
        expectedOutcome: "uncertain"
      },
      factorAnalysis: {},
      improvementSuggestions: [],
      timingRecommendation: {},
      benchmarkComparison: {}
    };
  }

  getDefaultMarketTrends() {
    return {
      marketOverview: {
        currentState: "stable market conditions",
        outlook: "neutral",
        keyDrivers: []
      },
      industryTrends: [],
      skillsTrends: [],
      emergingOpportunities: [],
      regionalInsights: [],
      predictions: {}
    };
  }

  getDefaultPersonalizedStrategy() {
    return {
      strategy: {
        primaryApproach: "broad application strategy",
        timeframe: "6+ months",
        applicationVolume: "5-10 applications per week",
        successMetrics: []
      },
      actionPlan: {
        immediate: [],
        shortTerm: [],
        longTerm: []
      },
      targetCompanies: [],
      skillDevelopment: [],
      networkingStrategy: {},
      monitoringKPIs: []
    };
  }
}

module.exports = AnalyticsService;