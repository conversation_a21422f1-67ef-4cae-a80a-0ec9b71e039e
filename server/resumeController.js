const database = require('./database');

class ResumeController {
  async getResumes(req, res) {
    try {
      const userId = req.user.userId;
      const resumes = await this.getUserResumes(userId);
      res.json(resumes);
    } catch (error) {
      console.error('Get resumes error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async getResume(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const resume = await this.getResumeById(id, userId);
      if (!resume) {
        return res.status(404).json({ error: 'Resume not found' });
      }

      res.json({
        id: resume.id,
        title: resume.title,
        data: JSON.parse(resume.data),
        created_at: resume.created_at,
        updated_at: resume.updated_at
      });
    } catch (error) {
      console.error('Get resume error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async createResume(req, res) {
    try {
      const { title, data } = req.body;
      const userId = req.user.userId;

      if (!title || !data) {
        return res.status(400).json({ error: 'Title and data are required' });
      }

      const resumeId = await this.createResumeRecord(userId, title, JSON.stringify(data));
      
      res.status(201).json({
        message: 'Resume created successfully',
        id: resumeId,
        title,
        data
      });
    } catch (error) {
      console.error('Create resume error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async updateResume(req, res) {
    try {
      const { id } = req.params;
      const { title, data } = req.body;
      const userId = req.user.userId;

      if (!title || !data) {
        return res.status(400).json({ error: 'Title and data are required' });
      }

      const resume = await this.getResumeById(id, userId);
      if (!resume) {
        return res.status(404).json({ error: 'Resume not found' });
      }

      await this.updateResumeRecord(id, title, JSON.stringify(data));
      
      res.json({
        message: 'Resume updated successfully',
        id,
        title,
        data
      });
    } catch (error) {
      console.error('Update resume error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async deleteResume(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const resume = await this.getResumeById(id, userId);
      if (!resume) {
        return res.status(404).json({ error: 'Resume not found' });
      }

      await this.deleteResumeRecord(id);
      
      res.json({ message: 'Resume deleted successfully' });
    } catch (error) {
      console.error('Delete resume error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  getUserResumes(userId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        'SELECT id, title, created_at, updated_at FROM resumes WHERE user_id = ? AND is_active = 1 ORDER BY updated_at DESC',
        [userId],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        }
      );
    });
  }

  getResumeById(id, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resumes WHERE id = ? AND user_id = ? AND is_active = 1',
        [id, userId],
        (err, row) => {
          if (err) {
            reject(err);
          } else {
            resolve(row);
          }
        }
      );
    });
  }

  createResumeRecord(userId, title, data) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'INSERT INTO resumes (user_id, title, data) VALUES (?, ?, ?)',
        [userId, title, data],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  updateResumeRecord(id, title, data) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE resumes SET title = ?, data = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [title, data, id],
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  deleteResumeRecord(id) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE resumes SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id],
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }
}

module.exports = ResumeController;