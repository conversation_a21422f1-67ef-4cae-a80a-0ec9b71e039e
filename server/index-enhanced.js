require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

// Import enhanced controllers and middleware
const { setupDatabase } = require('./database/setup');
const authRoutes = require('./routes/auth');
const resumeRoutes = require('./routes/resumes');
const aiRoutes = require('./routes/ai');
const uploadRoutes = require('./routes/uploads');
const analyticsRoutes = require('./routes/analytics');
const templateRoutes = require('./routes/templates');
const paymentRoutes = require('./routes/payments');

// Import middleware
const { authenticate } = require('./middleware/auth');
const { errorHandler } = require('./middleware/errorHandler');
const { sanitizeInput } = require('./middleware/validation');

// Import legacy components for compatibility
const { AuthController, authenticateToken } = require('./auth');
const ResumeController = require('./resumeController');
const AIController = require('./aiController');
const JobController = require('./jobController');
const JobApplicationController = require('./jobApplicationController');
const EnhancedJobApplicationController = require('./enhancedJobApplicationController');
const AnalyticsController = require('./analyticsController');
const AutomationController = require('./automationController');
const SecurityMiddleware = require('./securityMiddleware');
const HealthController = require('./healthController');
const cacheService = require('./cacheService');
const notificationService = require('./notificationService');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize legacy components for backward compatibility
const security = new SecurityMiddleware();
const authController = new AuthController();
const resumeController = new ResumeController();
const aiController = new AIController();
const jobController = new JobController();
const jobApplicationController = new JobApplicationController();
const enhancedJobApplicationController = new EnhancedJobApplicationController();
const analyticsController = new AnalyticsController();
const automationController = new AutomationController();
const healthController = new HealthController();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

app.use(compression());
app.use(morgan('combined'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request sanitization
app.use(sanitizeInput);

// Health check endpoints
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Legacy health checks for backward compatibility
app.get('/health/status', healthController.systemStatus.bind(healthController));
app.get('/health/ready', healthController.readiness.bind(healthController));
app.get('/health/live', healthController.liveness.bind(healthController));
app.get('/metrics', healthController.metrics.bind(healthController));

// Modern API Routes
app.use('/api/auth', authRoutes);
app.use('/api/resumes', authenticate, resumeRoutes);
app.use('/api/ai', authenticate, aiRoutes);
app.use('/api/uploads', authenticate, uploadRoutes);
app.use('/api/analytics', authenticate, analyticsRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/payments', authenticate, paymentRoutes);

// Legacy routes for backward compatibility
app.get('/api/notifications/stats', authenticateToken, (req, res) => {
  try {
    const stats = notificationService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get notification stats' });
  }
});

// Legacy job routes
app.get('/api/jobs', authenticateToken, jobController.getJobs.bind(jobController));
app.get('/api/jobs/:id', authenticateToken, jobController.getJob.bind(jobController));
app.post('/api/jobs', authenticateToken, jobController.createJob.bind(jobController));
app.put('/api/jobs/:id', authenticateToken, jobController.updateJob.bind(jobController));
app.delete('/api/jobs/:id', authenticateToken, jobController.deleteJob.bind(jobController));

// Enhanced Job Application Routes (v2 API)
app.post('/api/v2/applications/queue', authenticateToken, enhancedJobApplicationController.queueApplication.bind(enhancedJobApplicationController));
app.post('/api/v2/applications/bulk-queue', authenticateToken, enhancedJobApplicationController.bulkQueueApplications.bind(enhancedJobApplicationController));
app.get('/api/v2/applications/stats', authenticateToken, enhancedJobApplicationController.getApplicationStats.bind(enhancedJobApplicationController));
app.get('/api/v2/applications/queue-status', authenticateToken, enhancedJobApplicationController.getQueueStatus.bind(enhancedJobApplicationController));
app.delete('/api/v2/applications/:id/cancel', authenticateToken, enhancedJobApplicationController.cancelApplication.bind(enhancedJobApplicationController));
app.post('/api/v2/applications/optimal-timing', authenticateToken, enhancedJobApplicationController.getOptimalTiming.bind(enhancedJobApplicationController));
app.put('/api/v2/applications/settings', authenticateToken, enhancedJobApplicationController.updateAutomationSettings.bind(enhancedJobApplicationController));
app.get('/api/v2/applications/insights', authenticateToken, enhancedJobApplicationController.getAutomationInsights.bind(enhancedJobApplicationController));
app.post('/api/v2/applications/test', authenticateToken, enhancedJobApplicationController.testAutomation.bind(enhancedJobApplicationController));
app.get('/api/v2/applications/health', authenticateToken, enhancedJobApplicationController.getHealthStatus.bind(enhancedJobApplicationController));

// Legacy job application routes
app.post('/api/applications/queue', authenticateToken, jobApplicationController.queueApplication.bind(jobApplicationController));
app.get('/api/applications/stats', authenticateToken, jobApplicationController.getApplicationStats.bind(jobApplicationController));
app.get('/api/applications/timing', authenticateToken, jobApplicationController.getOptimalTiming.bind(jobApplicationController));
app.get('/api/applications/queue-status', authenticateToken, jobApplicationController.getQueueStatus.bind(jobApplicationController));
app.delete('/api/applications/:id/cancel', authenticateToken, jobApplicationController.cancelApplication.bind(jobApplicationController));
app.post('/api/applications/bulk-queue', authenticateToken, jobApplicationController.bulkQueueApplications.bind(jobApplicationController));
app.put('/api/applications/optimization-settings', authenticateToken, jobApplicationController.updateOptimizationSettings.bind(jobApplicationController));

// Legacy analytics routes with caching
app.post('/api/analytics/track-event', authenticateToken, analyticsController.trackEvent.bind(analyticsController));
app.put('/api/analytics/resume-metrics/:resumeId', authenticateToken, analyticsController.updateResumeMetrics.bind(analyticsController));
app.get('/api/analytics/recommendations', authenticateToken, analyticsController.getOptimizationRecommendations.bind(analyticsController));
app.get('/api/analytics/metrics/:metricType', authenticateToken, analyticsController.getDetailedMetrics.bind(analyticsController));
app.post('/api/analytics/generate-report', authenticateToken, analyticsController.generateReport.bind(analyticsController));
app.post('/api/analytics/predict', authenticateToken, analyticsController.getPredictiveAnalytics.bind(analyticsController));
app.get('/api/analytics/application-metrics', authenticateToken, analyticsController.getApplicationMetrics.bind(analyticsController));
app.get('/api/analytics/job-market', authenticateToken, analyticsController.getJobMarketInsights.bind(analyticsController));
app.get('/api/analytics/resume-performance', authenticateToken, analyticsController.getResumePerformance.bind(analyticsController));
app.get('/api/analytics/weekly-trends', authenticateToken, analyticsController.getWeeklyTrends.bind(analyticsController));

// Legacy automation routes
app.post('/api/automation/optimize-timing', authenticateToken, automationController.optimizeApplicationTiming.bind(automationController));
app.get('/api/automation/geographic-opportunities', authenticateToken, automationController.analyzeGeographicOpportunities.bind(automationController));
app.post('/api/automation/culture-fit', authenticateToken, automationController.analyzeCompanyCultureFit.bind(automationController));
app.get('/api/automation/anti-detection', authenticateToken, automationController.getAntiDetectionStrategy.bind(automationController));
app.get('/api/automation/insights', authenticateToken, automationController.getApplicationInsights.bind(automationController));
app.post('/api/automation/optimize-strategy', authenticateToken, automationController.optimizeApplicationStrategy.bind(automationController));

// Legacy resume data endpoint
app.get('/api/resume', (req, res) => {
  const resumeData = {
    name: 'John Doe',
    title: 'Software Engineer',
    summary: 'Experienced developer with a focus on full stack applications.',
    skills: ['JavaScript', 'Node.js', 'React'],
    experience: [
      {
        company: 'ABC Corp',
        role: 'Frontend Developer',
        start: '2020',
        end: '2022',
      },
    ],
  };
  res.json(resumeData);
});

// Error handling middleware
app.use(errorHandler);

// Initialize database and start server
async function startServer() {
  try {
    await setupDatabase();
    console.log('Database connection established');
    
    const server = app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
      console.log(`Client URL: ${process.env.CLIENT_URL || 'http://localhost:5173'}`);
    });

    // Initialize WebSocket for notifications
    notificationService.initialize(server);

    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('Shutting down gracefully...');
      server.close(() => {
        console.log('HTTP server closed.');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();