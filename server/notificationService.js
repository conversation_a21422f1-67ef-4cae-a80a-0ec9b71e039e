const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

class NotificationService {
  constructor() {
    this.clients = new Map(); // userId -> WebSocket connection
    this.notificationQueue = new Map(); // userId -> notifications array
    this.wss = null;
    this.collaborationHandlers = new Map(); // sessionId -> collaboration metadata
  }

  // Initialize WebSocket server
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws',
      verifyClient: (info) => {
        // Basic validation
        return true;
      }
    });

    this.wss.on('connection', (ws, req) => {
      this.handleConnection(ws, req);
    });

    console.log('WebSocket notification service initialized');
  }

  // Handle new WebSocket connection
  handleConnection(ws, req) {
    let userId = null;
    let collaborationService = null;

    // Import collaboration service dynamically to avoid circular dependency
    try {
      collaborationService = require('./collaborationService');
    } catch (error) {
      console.warn('Collaboration service not available:', error.message);
    }

    // Authentication and message handling
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'auth' && data.token) {
          const decoded = jwt.verify(data.token, process.env.JWT_SECRET);
          userId = decoded.userId;
          
          // Store connection
          this.clients.set(userId, ws);
          
          // Send authentication success
          ws.send(JSON.stringify({
            type: 'auth_success',
            userId: userId,
            timestamp: new Date().toISOString()
          }));

          // Send any queued notifications
          this.sendQueuedNotifications(userId);
          
          console.log(`WebSocket client authenticated: User ${userId}`);
          
        } else if (data.type === 'ping') {
          // Handle ping/pong for keepalive
          ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
          
        } else if (data.type === 'join_editing' && collaborationService) {
          // Handle collaborative editing session join
          if (userId && data.resumeId && data.sessionId) {
            collaborationService.joinEditingSession(ws, data.resumeId, userId, data.sessionId);
            this.collaborationHandlers.set(data.sessionId, {
              userId,
              resumeId: data.resumeId,
              ws
            });
          }
          
        } else if (data.type === 'editing_operation' && collaborationService) {
          // Handle real-time editing operations
          if (data.sessionId && data.operation) {
            collaborationService.handleOperation(data.sessionId, data.operation);
          }
          
        } else if (data.type === 'cursor_update' && collaborationService) {
          // Handle cursor position updates
          if (data.sessionId && data.cursor) {
            collaborationService.updateCursor(data.sessionId, data.cursor, data.selection);
          }
          
        } else if (data.type === 'leave_editing' && collaborationService) {
          // Handle leaving editing session
          if (data.sessionId) {
            collaborationService.leaveEditingSession(data.sessionId);
            this.collaborationHandlers.delete(data.sessionId);
          }
          
        } else if (data.type === 'join_terminal') {
          // Handle terminal session join for job execution monitoring
          if (userId && data.terminalId) {
            // Store terminal connection mapping
            this.terminalConnections = this.terminalConnections || new Map();
            this.terminalConnections.set(data.terminalId, {
              userId,
              ws,
              connectedAt: new Date()
            });
            
            ws.send(JSON.stringify({
              type: 'terminal_joined',
              terminalId: data.terminalId,
              timestamp: new Date().toISOString()
            }));
          }
          
        } else if (data.type === 'leave_terminal') {
          // Handle leaving terminal session
          if (data.terminalId) {
            this.terminalConnections = this.terminalConnections || new Map();
            this.terminalConnections.delete(data.terminalId);
          }
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format or authentication failed'
        }));
      }
    });

    // Handle connection close
    ws.on('close', () => {
      if (userId) {
        this.clients.delete(userId);
        console.log(`WebSocket client disconnected: User ${userId}`);
        
        // Clean up collaboration sessions
        if (collaborationService) {
          for (const [sessionId, handler] of this.collaborationHandlers.entries()) {
            if (handler.userId === userId) {
              collaborationService.leaveEditingSession(sessionId);
              this.collaborationHandlers.delete(sessionId);
            }
          }
        }
      }
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      if (userId) {
        this.clients.delete(userId);
        
        // Clean up collaboration sessions on error
        if (collaborationService) {
          for (const [sessionId, handler] of this.collaborationHandlers.entries()) {
            if (handler.userId === userId) {
              collaborationService.leaveEditingSession(sessionId);
              this.collaborationHandlers.delete(sessionId);
            }
          }
        }
      }
    });

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'Connected to CVLeap notifications. Please authenticate with your token.',
      timestamp: new Date().toISOString()
    }));
  }

  // Send notification to specific user
  sendNotification(userId, notification) {
    const ws = this.clients.get(userId);
    
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify({
          type: 'notification',
          ...notification,
          timestamp: new Date().toISOString()
        }));
        return true;
      } catch (error) {
        console.error('Failed to send notification:', error);
        this.clients.delete(userId);
        return false;
      }
    } else {
      // Queue notification for later delivery
      this.queueNotification(userId, notification);
      return false;
    }
  }

  // Queue notification for offline users
  queueNotification(userId, notification) {
    if (!this.notificationQueue.has(userId)) {
      this.notificationQueue.set(userId, []);
    }
    
    const queue = this.notificationQueue.get(userId);
    queue.push({
      ...notification,
      queuedAt: new Date().toISOString()
    });

    // Limit queue size to prevent memory issues
    if (queue.length > 50) {
      queue.splice(0, queue.length - 50);
    }
  }

  // Send queued notifications when user connects
  sendQueuedNotifications(userId) {
    const queue = this.notificationQueue.get(userId);
    if (!queue || queue.length === 0) return;

    const ws = this.clients.get(userId);
    if (!ws || ws.readyState !== WebSocket.OPEN) return;

    // Send each queued notification
    queue.forEach(notification => {
      try {
        ws.send(JSON.stringify({
          type: 'notification',
          ...notification,
          queued: true,
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        console.error('Failed to send queued notification:', error);
      }
    });

    // Clear the queue
    this.notificationQueue.delete(userId);
  }

  // Broadcast to all connected users
  broadcast(notification) {
    let sentCount = 0;
    
    for (const [userId, ws] of this.clients.entries()) {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify({
            type: 'broadcast',
            ...notification,
            timestamp: new Date().toISOString()
          }));
          sentCount++;
        } catch (error) {
          console.error('Failed to broadcast to user:', userId, error);
          this.clients.delete(userId);
        }
      }
    }
    
    return sentCount;
  }

  // Send application status updates
  notifyApplicationStatus(userId, application) {
    this.sendNotification(userId, {
      category: 'application_update',
      title: 'Application Status Update',
      message: `Your application to ${application.company} for ${application.jobTitle} has been updated to: ${application.status}`,
      data: {
        applicationId: application.id,
        company: application.company,
        jobTitle: application.jobTitle,
        status: application.status
      },
      priority: 'high'
    });
  }

  // Send AI processing notifications
  notifyAIProcessing(userId, type, status) {
    let message = '';
    let priority = 'medium';

    switch (type) {
      case 'resume_enhancement':
        message = status === 'started' ? 'AI is enhancing your resume...' : 'Resume enhancement completed!';
        break;
      case 'cover_letter':
        message = status === 'started' ? 'Generating cover letter...' : 'Cover letter generated!';
        break;
      case 'ats_analysis':
        message = status === 'started' ? 'Analyzing ATS compatibility...' : 'ATS analysis completed!';
        break;
      default:
        message = `AI ${type} ${status}`;
    }

    if (status === 'completed') {
      priority = 'high';
    }

    this.sendNotification(userId, {
      category: 'ai_processing',
      title: 'AI Processing Update',
      message: message,
      data: { type, status },
      priority: priority
    });
  }

  // Send job alerts
  notifyJobAlert(userId, job) {
    this.sendNotification(userId, {
      category: 'job_alert',
      title: 'New Job Match Found',
      message: `New ${job.title} position at ${job.company} matches your criteria`,
      data: {
        jobId: job.id,
        title: job.title,
        company: job.company,
        location: job.location
      },
      priority: 'medium'
    });
  }

  // Send system notifications
  notifySystemUpdate(message, priority = 'low') {
    this.broadcast({
      category: 'system',
      title: 'System Update',
      message: message,
      priority: priority
    });
  }

  // Collaboration notifications
  notifyCollaborationInvite(userId, inviterName, organizationName, resumeTitle) {
    this.sendNotification(userId, {
      category: 'collaboration',
      title: 'Collaboration Invitation',
      message: `${inviterName} invited you to collaborate on "${resumeTitle}" in ${organizationName}`,
      data: {
        inviterName,
        organizationName,
        resumeTitle
      },
      priority: 'high'
    });
  }

  notifyNewComment(userId, commenterName, resumeTitle, sectionName) {
    this.sendNotification(userId, {
      category: 'comment',
      title: 'New Comment',
      message: `${commenterName} commented on ${sectionName} in "${resumeTitle}"`,
      data: {
        commenterName,
        resumeTitle,
        sectionName
      },
      priority: 'medium'
    });
  }

  notifyReviewCompleted(userId, reviewerName, resumeTitle, rating) {
    this.sendNotification(userId, {
      category: 'review',
      title: 'Review Completed',
      message: `${reviewerName} completed their review of "${resumeTitle}" with a ${rating}/5 rating`,
      data: {
        reviewerName,
        resumeTitle,
        rating
      },
      priority: 'high'
    });
  }

  notifyTeamMemberJoined(organizationId, newMemberName, inviterName) {
    // Notify all organization members
    this.broadcastToOrganization(organizationId, {
      category: 'team',
      title: 'New Team Member',
      message: `${newMemberName} joined the team (invited by ${inviterName})`,
      data: {
        newMemberName,
        inviterName
      },
      priority: 'medium'
    });
  }

  notifyEditingConflict(userId, resumeTitle, conflictType) {
    this.sendNotification(userId, {
      category: 'editing_conflict',
      title: 'Editing Conflict',
      message: `Conflict detected in "${resumeTitle}": ${conflictType}`,
      data: {
        resumeTitle,
        conflictType
      },
      priority: 'high'
    });
  }

  // Broadcast to organization members
  async broadcastToOrganization(organizationId, notification) {
    try {
      const database = require('./database');
      
      // Get all organization members
      database.get().all(
        `SELECT u.id FROM users u 
         JOIN organization_members om ON u.id = om.user_id 
         WHERE om.organization_id = ? AND om.status = 'active'`,
        [organizationId],
        (err, rows) => {
          if (err) {
            console.error('Error getting organization members:', err);
            return;
          }
          
          // Send notification to each member
          rows.forEach(row => {
            this.sendNotification(row.id, notification);
          });
        }
      );
    } catch (error) {
      console.error('Error broadcasting to organization:', error);
    }
  }

  // Alias for sendNotification for consistency with other services
  sendToUser(userId, notification) {
    return this.sendNotification(userId, notification);
  }

  // Get connection statistics
  getStats() {
    return {
      connectedClients: this.clients.size,
      queuedNotifications: Array.from(this.notificationQueue.values())
        .reduce((total, queue) => total + queue.length, 0),
      clientsList: Array.from(this.clients.keys())
    };
  }

  // Clean up disconnected clients
  cleanup() {
    for (const [userId, ws] of this.clients.entries()) {
      if (ws.readyState !== WebSocket.OPEN) {
        this.clients.delete(userId);
      }
    }
  }

  // Graceful shutdown
  shutdown() {
    if (this.wss) {
      this.wss.clients.forEach((ws) => {
        ws.send(JSON.stringify({
          type: 'system',
          message: 'Server is shutting down',
          timestamp: new Date().toISOString()
        }));
        ws.close();
      });
      
      this.wss.close();
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

module.exports = notificationService;