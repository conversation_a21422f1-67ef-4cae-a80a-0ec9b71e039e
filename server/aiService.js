const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const MultiModelAIClient = require('./multiModelAIClient');

class AIService {
  constructor() {
    try {
      // Initialize MultiModelAIClient
      this.aiClient = new MultiModelAIClient();
      const availableModels = this.aiClient.getAvailableModels();
      
      if (availableModels.length === 0) {
        console.warn('No AI models configured. AI features will not work.');
        this.aiClient = null;
      } else {
        console.log(`AI Service initialized with models: ${availableModels.map(m => m.name).join(', ')}`);
      }
    } catch (error) {
      console.error('Failed to initialize AI Service:', error);
      this.aiClient = null;
    }
    
    // Fallback initialization for individual providers
    if (!this.aiClient) {
      // Initialize OpenAI
      const openaiKey = process.env.OPENAI_API_KEY;
      this.openai = (openaiKey && openaiKey !== 'your_openai_api_key_here') 
        ? new OpenAI({ apiKey: openaiKey }) 
        : null;

      // Initialize Anthropic Claude
      const anthropicKey = process.env.ANTHROPIC_API_KEY;
      this.anthropic = (anthropicKey && anthropicKey !== 'your_anthropic_api_key_here') 
        ? new Anthropic({ apiKey: anthropicKey }) 
        : null;

      // Initialize Google Gemini
      const geminiKey = process.env.GEMINI_API_KEY;
      this.gemini = (geminiKey && geminiKey !== 'your_gemini_api_key_here') 
        ? new GoogleGenerativeAI(geminiKey) 
        : null;

      // Define AI provider preferences and fallback chain
      this.providers = [
        { name: 'openai', client: this.openai, model: 'gpt-4o' },
        { name: 'claude', client: this.anthropic, model: 'claude-3-5-sonnet-20241022' },
        { name: 'gemini', client: this.gemini, model: 'gemini-1.5-pro' }
      ].filter(provider => provider.client !== null);

      if (this.providers.length === 0 && !this.aiClient) {
        console.warn('No AI providers configured. AI features will not work.');
      } else if (this.providers.length > 0) {
        console.log(`Fallback AI providers available: ${this.providers.map(p => p.name).join(', ')}`);
      }
    }
  }

  async callWithFallback(prompt, options = {}) {
    const { temperature = 0.7, maxTokens = 1500, preferredProvider = null } = options;
    
    // Reorder providers if a preferred one is specified
    let providers = [...this.providers];
    if (preferredProvider) {
      const preferred = providers.find(p => p.name === preferredProvider);
      if (preferred) {
        providers = [preferred, ...providers.filter(p => p.name !== preferredProvider)];
      }
    }

    for (const provider of providers) {
      try {
        console.log(`Attempting AI call with ${provider.name}...`);
        
        if (provider.name === 'openai') {
          const response = await provider.client.chat.completions.create({
            model: provider.model,
            messages: [{ role: 'user', content: prompt }],
            temperature,
            max_tokens: maxTokens,
          });
          return response.choices[0].message.content;
        }
        
        if (provider.name === 'claude') {
          const response = await provider.client.messages.create({
            model: provider.model,
            max_tokens: maxTokens,
            temperature,
            messages: [{ role: 'user', content: prompt }],
          });
          return response.content[0].text;
        }
        
        if (provider.name === 'gemini') {
          const model = provider.client.getGenerativeModel({ 
            model: provider.model,
            generationConfig: {
              temperature,
              maxOutputTokens: maxTokens,
            }
          });
          const result = await model.generateContent(prompt);
          return result.response.text();
        }
        
      } catch (error) {
        console.error(`${provider.name} failed:`, error.message);
        if (provider === providers[providers.length - 1]) {
          throw new Error(`All AI providers failed. Last error: ${error.message}`);
        }
        continue;
      }
    }
  }

  async enhanceResumeContent(resumeData, targetJob = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
As a professional resume writer, enhance the following resume content to be more compelling and ATS-friendly.
${targetJob ? `The target job is: ${targetJob}` : ''}

Current Resume:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end})`).join('; ')}

Please provide:
1. An enhanced professional summary (2-3 sentences)
2. Improved skill descriptions with relevant keywords
3. Enhanced experience descriptions with action verbs and quantifiable achievements
4. Additional relevant skills that might be missing

Format the response as JSON with the following structure:
{
  "enhancedSummary": "...",
  "enhancedSkills": ["skill1", "skill2", ...],
  "enhancedExperience": [{"company": "...", "role": "...", "start": "...", "end": "...", "description": "..."}],
  "suggestedSkills": ["skill1", "skill2", ...]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.7,
          maxTokens: 1500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 1500,
          preferredProvider: 'openai'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('AI enhancement error:', error);
      throw new Error('Failed to enhance resume content');
    }
  }

  async generateCoverLetter(resumeData, jobDescription, companyName, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Create a professional cover letter based on the following information:

Resume Data:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company}`).join(', ')}

Job Description: ${jobDescription}
Company Name: ${companyName}

Create a compelling cover letter that:
1. Shows enthusiasm for the role and company
2. Highlights relevant experience and skills
3. Demonstrates value to the company
4. Maintains a professional tone
5. Is 3-4 paragraphs long

Return only the cover letter text without any additional formatting or explanations.
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'claude-3-5-sonnet-20241022',
          temperature: 0.8,
          maxTokens: 800,
        });
        return {
          content: result.content.trim(),
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.8, 
          maxTokens: 800,
          preferredProvider: 'claude'
        });
        return content.trim();
      }
    } catch (error) {
      console.error('Cover letter generation error:', error);
      throw new Error('Failed to generate cover letter');
    }
  }

  async analyzeATSCompatibility(resumeData, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Analyze the following resume for ATS (Applicant Tracking System) compatibility and provide a detailed scoring and recommendations:

Resume:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end})`).join('; ')}

Evaluate on these criteria:
1. Keyword density and relevance
2. Format and structure
3. Use of action verbs
4. Quantifiable achievements
5. Industry-specific terminology

Provide a JSON response with:
{
  "overallScore": 85,
  "scores": {
    "keywords": 80,
    "format": 90,
    "actionVerbs": 75,
    "achievements": 80,
    "industryTerms": 85
  },
  "recommendations": [
    "Add more quantifiable achievements",
    "Include industry-specific keywords",
    "Use stronger action verbs"
  ],
  "missingKeywords": ["keyword1", "keyword2", ...],
  "strengthAreas": ["Good use of technical skills", "Clear job progression"]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gemini-1.5-pro',
          temperature: 0.3,
          maxTokens: 1000,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.3, 
          maxTokens: 1000,
          preferredProvider: 'gemini'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('ATS analysis error:', error);
      throw new Error('Failed to analyze ATS compatibility');
    }
  }

  async suggestSkills(currentSkills, jobTitle, industry = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Suggest relevant skills for a ${jobTitle} role${industry ? ` in the ${industry} industry` : ''}.

Current skills: ${currentSkills.join(', ')}

Provide suggestions for:
1. Technical skills that are commonly required
2. Soft skills that are valuable
3. Emerging technologies/tools in this field
4. Certifications that would be beneficial

Return a JSON response with:
{
  "technicalSkills": ["skill1", "skill2", ...],
  "softSkills": ["skill1", "skill2", ...],
  "emergingTech": ["tech1", "tech2", ...],
  "certifications": ["cert1", "cert2", ...]
}

Only suggest skills that are not already in the current skills list.
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'claude-3-5-sonnet-20241022',
          temperature: 0.6,
          maxTokens: 800,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.6, 
          maxTokens: 800,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Skill suggestion error:', error);
      throw new Error('Failed to suggest skills');
    }
  }

  async simulateInterview(resumeData, jobDescription, industry = '', difficulty = 'medium', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Conduct a mock interview simulation for the following candidate:

Candidate Profile:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end})`).join('; ')}

Job Description: ${jobDescription}
Industry: ${industry}
Difficulty Level: ${difficulty}

Generate a realistic interview scenario with:
1. 5-7 relevant interview questions (mix of behavioral, technical, and situational)
2. Evaluation criteria for each question
3. Sample strong answers based on candidate's background
4. Areas for improvement
5. Overall interview preparedness score

Format the response as JSON:
{
  "questions": [
    {
      "question": "...",
      "type": "behavioral|technical|situational",
      "evaluationCriteria": ["criteria1", "criteria2", ...],
      "sampleAnswer": "...",
      "improvementTips": ["tip1", "tip2", ...]
    }
  ],
  "overallScore": 75,
  "strengths": ["strength1", "strength2", ...],
  "areasForImprovement": ["area1", "area2", ...],
  "preparationTips": ["tip1", "tip2", ...]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'claude-3-5-sonnet-20241022',
          temperature: 0.7,
          maxTokens: 2000,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 2000,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Interview simulation error:', error);
      throw new Error('Failed to simulate interview');
    }
  }

  async predictJobSuccessScore(resumeData, jobDescription, companyData = {}, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Analyze the compatibility between the candidate and job opportunity to predict application success probability:

Candidate Profile:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end}): ${exp.description || 'No description'}`).join('\n')}

Job Opportunity:
Description: ${jobDescription}
Company: ${companyData.name || 'Not specified'}
Industry: ${companyData.industry || 'Not specified'}
Company Size: ${companyData.size || 'Not specified'}

Analyze and provide:
1. Overall success probability (0-100%)
2. Skill match percentage
3. Experience relevance score
4. Cultural fit assessment
5. Competitiveness analysis
6. Specific recommendations for improvement

Format as JSON:
{
  "successProbability": 78,
  "skillMatch": 85,
  "experienceRelevance": 72,
  "culturalFit": 80,
  "competitiveness": 75,
  "recommendations": [
    {
      "category": "skills|experience|presentation",
      "suggestion": "...",
      "impact": "high|medium|low",
      "timeToImplement": "immediate|1-2 weeks|1-3 months"
    }
  ],
  "strengthAreas": ["area1", "area2", ...],
  "improvementAreas": ["area1", "area2", ...],
  "marketOutlook": "favorable|competitive|challenging",
  "confidenceLevel": 85
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gemini-1.5-pro',
          temperature: 0.4,
          maxTokens: 1500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.4, 
          maxTokens: 1500,
          preferredProvider: 'gemini'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Job success prediction error:', error);
      throw new Error('Failed to predict job success score');
    }
  }

  async generateIndustrySpecificPrompt(industry, jobRole, promptType = 'enhancement', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Create industry-specific prompts and guidelines for ${industry} professionals in ${jobRole} roles.

Generate specialized content for:
1. Resume enhancement strategies specific to ${industry}
2. Key skills and competencies valued in ${industry}
3. Industry-specific terminology and keywords
4. Common interview questions for ${jobRole} in ${industry}
5. Career progression paths
6. Salary expectations and negotiation points

Focus on ${promptType} specifically.

Return as JSON:
{
  "industryKeywords": ["keyword1", "keyword2", ...],
  "essentialSkills": ["skill1", "skill2", ...],
  "commonInterviewQuestions": ["question1", "question2", ...],
  "careerProgressionPath": ["level1", "level2", "level3", ...],
  "salaryBenchmarks": {
    "entry": "...",
    "mid": "...",
    "senior": "..."
  },
  "industryTrends": ["trend1", "trend2", ...],
  "professionalCertifications": ["cert1", "cert2", ...],
  "networkingOpportunities": ["opportunity1", "opportunity2", ...]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.6,
          maxTokens: 1200,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.6, 
          maxTokens: 1200,
          preferredProvider: 'openai'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Industry-specific prompt generation error:', error);
      throw new Error('Failed to generate industry-specific content');
    }
  }

  async generateResumeVariations(resumeData, targetJob, variationCount = 3, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Create ${variationCount} different variations of the resume optimized for A/B testing:

Original Resume:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills.join(', ')}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end})`).join('; ')}

Target Job: ${targetJob}

Create variations with different approaches:
1. Variation A: Technical/Skills-focused approach
2. Variation B: Achievement/Results-focused approach  
3. Variation C: Leadership/Impact-focused approach

Each variation should have:
- Modified professional summary
- Reordered and re-emphasized experience descriptions
- Adjusted skills presentation
- Different keyword optimization strategy

Format as JSON:
{
  "variations": [
    {
      "id": "A",
      "approach": "technical-focused",
      "enhancedSummary": "...",
      "enhancedSkills": ["skill1", "skill2", ...],
      "enhancedExperience": [{"company": "...", "role": "...", "start": "...", "end": "...", "description": "..."}],
      "keywordFocus": ["keyword1", "keyword2", ...],
      "targetAudience": "..."
    }
  ],
  "testingRecommendations": {
    "metricsToTrack": ["metric1", "metric2", ...],
    "testDuration": "...",
    "expectedOutcomes": ["outcome1", "outcome2", ...]
  }
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'claude-3-5-sonnet-20241022',
          temperature: 0.8,
          maxTokens: 2500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.8, 
          maxTokens: 2500,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Resume variation generation error:', error);
      throw new Error('Failed to generate resume variations');
    }
  }

  // Career Coaching Features
  async analyzeCareerPath(resumeData, careerGoals, options = {}) {
    const prompt = `
As a professional career coach, analyze the following resume and career goals to provide detailed career path recommendations.

Current Resume:
Name: ${resumeData.name}
Title: ${resumeData.title}
Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end})`).join('; ')}
Skills: ${resumeData.skills.join(', ')}
Education: ${resumeData.education?.map(edu => `${edu.degree} from ${edu.institution}`).join('; ') || 'Not specified'}

Career Goals:
Target Role: ${careerGoals.targetRole || 'Not specified'}
Target Industry: ${careerGoals.targetIndustry || 'Not specified'}
Timeline: ${careerGoals.timeline || 'Not specified'}
Salary Expectations: ${careerGoals.salaryRange || 'Not specified'}

Provide a comprehensive analysis including:
1. Career progression assessment (current level, strengths, weaknesses)
2. Recommended next career steps with timelines
3. Skills needed for target roles
4. Industry transition advice (if applicable)
5. Salary growth projections
6. Potential roadblocks and mitigation strategies

Format as JSON:
{
  "currentAssessment": {
    "careerLevel": "...",
    "strengths": ["..."],
    "weaknesses": ["..."],
    "marketPosition": "..."
  },
  "recommendations": [
    {
      "step": "...",
      "timeline": "...",
      "description": "...",
      "requiredActions": ["..."]
    }
  ],
  "skillGaps": ["..."],
  "industryAdvice": "...",
  "salaryProjection": {
    "current": "...",
    "sixMonths": "...",
    "oneYear": "...",
    "twoYears": "..."
  },
  "risks": ["..."],
  "mitigationStrategies": ["..."]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 2500,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Career path analysis error:', error);
      throw new Error('Failed to analyze career path');
    }
  }

  async analyzeSkillGaps(currentSkills, targetRole, industry = '', options = {}) {
    const prompt = `
As a career development expert, analyze the skill gap between current skills and target role requirements.

Current Skills: ${currentSkills.join(', ')}
Target Role: ${targetRole}
Industry: ${industry || 'General'}

Provide a detailed skill gap analysis including:
1. Skills that match the target role
2. Missing critical skills
3. Skills to develop further
4. Learning recommendations with priorities
5. Estimated time to acquire missing skills
6. Alternative paths if skills gap is too large

Format as JSON:
{
  "matchingSkills": ["..."],
  "criticalGaps": [
    {
      "skill": "...",
      "importance": "high|medium|low",
      "learningTime": "...",
      "resources": ["..."]
    }
  ],
  "skillsToImprove": ["..."],
  "learningPlan": [
    {
      "phase": "...",
      "duration": "...",
      "skills": ["..."],
      "approach": "..."
    }
  ],
  "alternativePaths": ["..."],
  "overallGapScore": 0.85,
  "recommendations": ["..."]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2000,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 2000,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Skill gap analysis error:', error);
      throw new Error('Failed to analyze skill gaps');
    }
  }

  async generateInterviewQuestions(jobDescription, resumeData, difficulty = 'medium', options = {}) {
    const prompt = `
As an interview coach, generate relevant interview questions based on the job description and candidate's background.

Job Description: ${jobDescription}
Candidate Background: 
- Current Role: ${resumeData.title}
- Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company}`).join('; ')}
- Skills: ${resumeData.skills.join(', ')}

Difficulty Level: ${difficulty}

Generate questions in these categories:
1. General/Behavioral questions
2. Technical/Skills questions  
3. Experience-based questions
4. Company/Role-specific questions
5. Challenging/Problem-solving questions

Format as JSON:
{
  "behavioral": [
    {
      "question": "...",
      "purpose": "...",
      "tips": "..."
    }
  ],
  "technical": [...],
  "experienceBased": [...],
  "roleSpecific": [...],
  "challenging": [...],
  "preparationAdvice": "...",
  "overallStrategy": "..."
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.8,
          maxTokens: 2500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.8, 
          maxTokens: 2500,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Interview questions generation error:', error);
      throw new Error('Failed to generate interview questions');
    }
  }

  async generateSalaryNegotiationAdvice(resumeData, jobOffer, marketData = {}, options = {}) {
    const prompt = `
As a salary negotiation expert, provide strategic advice for negotiating the following job offer.

Candidate Profile:
- Current Role: ${resumeData.title}
- Experience Level: ${resumeData.experience?.length || 0} positions
- Skills: ${resumeData.skills.join(', ')}

Job Offer Details:
- Position: ${jobOffer.position || 'Not specified'}
- Offered Salary: ${jobOffer.salary || 'Not specified'}
- Benefits: ${jobOffer.benefits || 'Not specified'}
- Company: ${jobOffer.company || 'Not specified'}

Market Data:
- Industry Average: ${marketData.industryAverage || 'Not available'}
- Location Factor: ${marketData.locationFactor || 'Not available'}

Provide comprehensive negotiation strategy including:
1. Market position assessment
2. Negotiation leverage points
3. Counter-offer strategy
4. Scripts and talking points
5. Alternative compensation options
6. Timeline recommendations

Format as JSON:
{
  "marketAssessment": {
    "positionStrength": "strong|moderate|weak",
    "salaryGap": "...",
    "marketValue": "..."
  },
  "leveragePoints": ["..."],
  "negotiationStrategy": {
    "approach": "...",
    "timing": "...",
    "keyPoints": ["..."]
  },
  "scripts": {
    "opening": "...",
    "counterOffer": "...",
    "closing": "..."
  },
  "alternatives": ["..."],
  "timeline": "...",
  "riskAssessment": "..."
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 2500,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Salary negotiation advice error:', error);
      throw new Error('Failed to generate salary negotiation advice');
    }
  }

  async optimizeLinkedInProfile(resumeData, targetAudience = 'recruiters', options = {}) {
    const prompt = `
As a personal branding expert, optimize this LinkedIn profile for maximum impact.

Current Profile Data:
- Name: ${resumeData.name}
- Current Title: ${resumeData.title}
- Summary: ${resumeData.summary}
- Experience: ${resumeData.experience.map(exp => `${exp.role} at ${exp.company}`).join('; ')}
- Skills: ${resumeData.skills.join(', ')}

Target Audience: ${targetAudience}

Provide optimization recommendations for:
1. Headline optimization
2. Summary/About section enhancement
3. Experience descriptions improvement
4. Skills section optimization
5. Content strategy recommendations
6. Network building advice

Format as JSON:
{
  "headline": {
    "current": "...",
    "optimized": "...",
    "reasoning": "..."
  },
  "summary": {
    "optimized": "...",
    "keyElements": ["..."],
    "callToAction": "..."
  },
  "experience": [
    {
      "position": "...",
      "optimizedDescription": "...",
      "keywords": ["..."]
    }
  ],
  "skills": {
    "prioritize": ["..."],
    "add": ["..."],
    "remove": ["..."]
  },
  "contentStrategy": {
    "postTypes": ["..."],
    "frequency": "...",
    "topics": ["..."]
  },
  "networkingAdvice": ["..."]
}
    `;

    try {
      if (this.aiClient) {
        const result = await this.aiClient.generateWithFallback(prompt, {
          preferredModel: options.preferredModel || 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2500,
        });
        const content = result.content;
        const parsed = JSON.parse(content);
        return {
          ...parsed,
          _metadata: {
            modelUsed: result.modelUsed,
            attempt: result.attempt
          }
        };
      } else {
        const content = await this.callWithFallback(prompt, { 
          temperature: 0.7, 
          maxTokens: 2500,
          preferredProvider: 'claude'
        });
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('LinkedIn optimization error:', error);
      throw new Error('Failed to optimize LinkedIn profile');
    }
  }

  getModelStatus() {
    if (!this.aiClient && this.providers.length === 0) {
      return { available: false, models: [] };
    }
    
    if (this.aiClient) {
      return {
        available: true,
        models: this.aiClient.getModelStatus()
      };
    } else {
      return {
        available: true,
        models: this.providers.map(p => ({ name: p.name, model: p.model }))
      };
    }
  }

  getAvailableModels() {
    if (!this.aiClient && this.providers.length === 0) {
      return [];
    }
    
    if (this.aiClient) {
      return this.aiClient.getAvailableModels();
    } else {
      return this.providers.map(p => ({ name: p.name, model: p.model }));
    }
  }

  // Advanced Resume Analysis Engine
  async advancedResumeScoring(resumeData, targetJob = '', industry = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
As an expert ATS and resume analysis system, provide a comprehensive scoring and analysis of this resume:

Resume Data:
Name: ${resumeData.name}
Title: ${resumeData.title}
Summary: ${resumeData.summary}
Skills: ${resumeData.skills ? resumeData.skills.join(', ') : 'Not specified'}
Experience: ${resumeData.experience ? resumeData.experience.map(exp => `${exp.role} at ${exp.company} (${exp.start} - ${exp.end}): ${exp.description || ''}`).join('\n') : 'Not specified'}
Education: ${resumeData.education ? resumeData.education.map(edu => `${edu.degree} at ${edu.institution} (${edu.year})`).join('\n') : 'Not specified'}

${targetJob ? `Target Job: ${targetJob}` : ''}
${industry ? `Target Industry: ${industry}` : ''}

Provide a detailed analysis with scores (0-100) and specific recommendations:

{
  "overallScore": 85,
  "categoryScores": {
    "atsCompatibility": 90,
    "contentQuality": 85,
    "keywordOptimization": 80,
    "professionalPresentation": 88,
    "relevanceToTarget": 82
  },
  "strengths": [
    "Strong quantifiable achievements in previous roles",
    "Relevant technical skills for target position"
  ],
  "weaknesses": [
    "Missing industry-specific keywords",
    "Professional summary could be more compelling"
  ],
  "recommendations": [
    {
      "category": "content",
      "priority": "high",
      "suggestion": "Add specific metrics to achievement descriptions",
      "example": "Instead of 'Improved sales', use 'Increased sales by 25% over 6 months'"
    },
    {
      "category": "keywords",
      "priority": "medium", 
      "suggestion": "Include relevant industry buzzwords",
      "example": "Add terms like 'data-driven', 'stakeholder management'"
    }
  ],
  "missingElements": [
    "Professional certifications",
    "Volunteer experience",
    "Languages"
  ],
  "atsOptimization": {
    "score": 90,
    "issues": [],
    "suggestions": [
      "Use standard section headers",
      "Avoid images and graphics"
    ]
  }
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 3000,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Advanced resume scoring error:', error);
      throw new Error('Failed to analyze resume');
    }
  }

  async detectResumeWeaknesses(resumeData, targetJob = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Analyze this resume for weaknesses and gaps that could hurt job application success:

Resume:
${JSON.stringify(resumeData, null, 2)}
${targetJob ? `Target Job: ${targetJob}` : ''}

Identify specific weaknesses and provide actionable solutions:

{
  "criticalWeaknesses": [
    {
      "category": "content",
      "issue": "Vague achievement descriptions",
      "severity": "high",
      "impact": "Recruiters cannot assess actual performance",
      "solution": "Add specific metrics and quantifiable results"
    }
  ],
  "minorWeaknesses": [
    {
      "category": "formatting",
      "issue": "Inconsistent date formats",
      "severity": "low",
      "impact": "Appears unprofessional",
      "solution": "Use consistent MM/YYYY format throughout"
    }
  ],
  "gapAnalysis": {
    "missingSkills": ["Python", "Data Analysis"],
    "missingExperience": ["Team leadership", "Budget management"],
    "missingCredentials": ["PMP Certification", "MBA"]
  },
  "improvementPriority": [
    "Add quantifiable achievements",
    "Include relevant keywords",
    "Improve professional summary"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Weakness detection error:', error);
      throw new Error('Failed to detect resume weaknesses');
    }
  }

  async amplifyResumeStrengths(resumeData, targetJob = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Identify and amplify the strengths in this resume to make them more compelling:

Resume:
${JSON.stringify(resumeData, null, 2)}
${targetJob ? `Target Job: ${targetJob}` : ''}

Provide enhanced versions of existing strengths:

{
  "identifiedStrengths": [
    {
      "original": "Managed a team",
      "category": "leadership",
      "strength": "Team management experience",
      "enhanced": "Led cross-functional team of 8 professionals, resulting in 30% improvement in project delivery time",
      "impactScore": 85
    }
  ],
  "hiddenStrengths": [
    {
      "strength": "Problem-solving abilities",
      "evidence": "Resolved customer complaints consistently",
      "enhancement": "Developed systematic approach to customer issue resolution, achieving 95% satisfaction rate"
    }
  ],
  "enhancedSummary": "Results-driven professional with proven track record of leading high-performing teams and delivering exceptional customer outcomes",
  "powerWords": ["spearheaded", "optimized", "transformed", "exceeded"],
  "quantificationSuggestions": [
    {
      "section": "experience",
      "suggestion": "Add percentage improvements, dollar amounts, team sizes, timeframes",
      "examples": ["Increased revenue by $X", "Reduced costs by Y%", "Managed team of Z people"]
    }
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.4,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Strength amplification error:', error);
      throw new Error('Failed to amplify resume strengths');
    }
  }

  // Real-time AI Assistant
  async chatAssistant(message, context = {}, conversationHistory = [], options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const { resumeData, currentSection, userGoals } = context;
    
    const prompt = `
You are an expert resume and career coach AI assistant. Provide helpful, specific advice based on the user's message.

Context:
${resumeData ? `Current Resume: ${JSON.stringify(resumeData, null, 2)}` : 'No resume loaded'}
${currentSection ? `Current Section: ${currentSection}` : ''}
${userGoals ? `User Goals: ${userGoals}` : ''}

Conversation History:
${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

User Message: ${message}

Provide a helpful response that:
1. Directly addresses their question
2. Offers specific, actionable advice
3. References their resume data when relevant
4. Suggests next steps

Response format:
{
  "response": "Your helpful response here",
  "suggestions": ["Specific action item 1", "Specific action item 2"],
  "relevantTools": ["enhance-resume", "generate-cover-letter"],
  "nextSteps": "What they should do next"
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 1500,
        temperature: 0.7,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Chat assistant error:', error);
      throw new Error('Failed to process chat message');
    }
  }

  async generateRealTimeSuggestions(currentText, sectionType, targetJob = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Provide real-time suggestions to improve this ${sectionType} section:

Current Text: "${currentText}"
${targetJob ? `Target Job: ${targetJob}` : ''}
Section Type: ${sectionType}

Provide immediate, actionable suggestions:

{
  "suggestions": [
    {
      "type": "improvement",
      "text": "Consider adding a specific metric here",
      "reason": "Quantifiable achievements are more impactful"
    },
    {
      "type": "keyword",
      "text": "Add the keyword 'strategic planning'",
      "reason": "Common requirement for this role type"
    }
  ],
  "strengthScore": 75,
  "improvements": [
    "Be more specific about the impact",
    "Use stronger action verbs"
  ],
  "alternativeVersions": [
    "Alternative wording option 1",
    "Alternative wording option 2"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 1000,
        temperature: 0.5,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Real-time suggestions error:', error);
      throw new Error('Failed to generate suggestions');
    }
  }

  // Advanced Document Processing
  async parseResumeContent(rawText, format = 'unknown', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Parse this raw resume text into structured data. The original format was: ${format}

Raw Text:
${rawText}

Extract and structure the information into this exact JSON format:

{
  "personalInfo": {
    "name": "Full Name",
    "email": "<EMAIL>",
    "phone": "+**********",
    "location": "City, State",
    "linkedin": "linkedin.com/in/profile",
    "website": "website.com"
  },
  "professionalSummary": "Professional summary text",
  "experience": [
    {
      "role": "Job Title",
      "company": "Company Name",
      "location": "City, State",
      "start": "MM/YYYY",
      "end": "MM/YYYY",
      "description": "Job description and achievements",
      "achievements": ["Achievement 1", "Achievement 2"]
    }
  ],
  "education": [
    {
      "degree": "Degree Type",
      "institution": "School Name",
      "location": "City, State",
      "year": "YYYY",
      "gpa": "3.8",
      "honors": ["Honor 1", "Honor 2"]
    }
  ],
  "skills": {
    "technical": ["Skill 1", "Skill 2"],
    "soft": ["Skill 1", "Skill 2"],
    "certifications": ["Cert 1", "Cert 2"]
  },
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description",
      "technologies": ["Tech 1", "Tech 2"],
      "date": "MM/YYYY"
    }
  ],
  "additionalSections": {
    "languages": ["Language 1", "Language 2"],
    "volunteer": ["Experience 1"],
    "awards": ["Award 1"]
  }
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 3000,
        temperature: 0.2,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Resume parsing error:', error);
      throw new Error('Failed to parse resume content');
    }
  }

  async intelligentDataExtraction(rawText, extractionType = 'comprehensive', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Perform intelligent data extraction from this text for ${extractionType} analysis:

Text:
${rawText}

Extract key information and provide insights:

{
  "extractedData": {
    "keyEntities": [
      {
        "type": "company",
        "value": "Company Name",
        "context": "Where it appears in text"
      }
    ],
    "skills": ["Extracted skill 1", "Extracted skill 2"],
    "achievements": ["Quantified achievement 1"],
    "dates": ["2020-2023", "Jan 2021"],
    "locations": ["City, State"],
    "technologies": ["Technology 1"]
  },
  "insights": {
    "experienceLevel": "Mid-level",
    "industryFocus": "Technology",
    "careerProgression": "Upward trajectory",
    "strengthAreas": ["Leadership", "Technical Skills"]
  },
  "qualityMetrics": {
    "completeness": 85,
    "clarity": 90,
    "professionalism": 88
  },
  "recommendations": [
    "Add more quantifiable achievements",
    "Include specific technologies used"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Data extraction error:', error);
      throw new Error('Failed to extract data');
    }
  }

  async detectDuplicateContent(resumeData1, resumeData2, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Compare these two resume datasets and identify duplicate or similar content:

Resume 1:
${JSON.stringify(resumeData1, null, 2)}

Resume 2:
${JSON.stringify(resumeData2, null, 2)}

Analyze for duplicates and similarities:

{
  "duplicateDetection": {
    "overallSimilarity": 75,
    "duplicateSections": [
      {
        "section": "experience",
        "similarity": 90,
        "duplicateItems": ["Software Engineer role at Tech Corp"]
      }
    ],
    "similarContent": [
      {
        "type": "skill",
        "item1": "JavaScript Programming",
        "item2": "JavaScript Development",
        "similarity": 95
      }
    ]
  },
  "mergeRecommendations": [
    {
      "action": "merge",
      "section": "skills",
      "reason": "Complementary skill sets",
      "suggestion": "Combine technical and soft skills"
    }
  ],
  "uniqueElements": {
    "resume1": ["Unique experience 1"],
    "resume2": ["Unique certification 1"]
  }
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2000,
        temperature: 0.2,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Duplicate detection error:', error);
      throw new Error('Failed to detect duplicates');
    }
  }

  async compareResumeVersions(oldVersion, newVersion, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Compare these two resume versions and provide detailed analysis of changes:

Old Version:
${JSON.stringify(oldVersion, null, 2)}

New Version:
${JSON.stringify(newVersion, null, 2)}

Provide comprehensive version comparison:

{
  "versionComparison": {
    "changesDetected": 12,
    "improvementScore": 85,
    "sections": {
      "personalInfo": {
        "changes": ["Updated phone number"],
        "impact": "low"
      },
      "experience": {
        "changes": ["Added new role", "Enhanced descriptions"],
        "impact": "high"
      }
    }
  },
  "improvements": [
    {
      "section": "experience",
      "change": "Added quantifiable achievements",
      "impact": "high",
      "benefit": "More compelling to recruiters"
    }
  ],
  "regressions": [
    {
      "section": "skills",
      "change": "Removed relevant skill",
      "impact": "medium",
      "concern": "May miss ATS keywords"
    }
  ],
  "recommendations": [
    "Consider re-adding the removed skill",
    "Maintain the improved achievement descriptions"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Version comparison error:', error);
      throw new Error('Failed to compare resume versions');
    }
  }

  // AI Template Selection and Design Optimization
  async recommendOptimalTemplate(resumeData, targetJob = '', industry = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Analyze this resume and recommend the optimal template based on industry, role, and content:

Resume Data:
${JSON.stringify(resumeData, null, 2)}
${targetJob ? `Target Job: ${targetJob}` : ''}
${industry ? `Industry: ${industry}` : ''}

Available Template Types:
- Professional: Clean, corporate design for traditional industries
- Creative: Modern design with visual elements for creative fields  
- Technical: Code-friendly layout for developers and engineers
- Academic: Publication-focused layout for researchers and academics
- Executive: Premium design for senior leadership roles

Provide template recommendation:

{
  "recommendedTemplate": {
    "type": "professional",
    "name": "Corporate Executive",
    "reasoning": "Best suited for traditional corporate environment and senior role level",
    "atsScore": 95,
    "visualImpact": 88
  },
  "alternativeTemplates": [
    {
      "type": "technical",
      "name": "Developer Pro",
      "reasoning": "Good for technical roles but less formal",
      "atsScore": 92,
      "visualImpact": 85
    }
  ],
  "designRecommendations": {
    "colorScheme": "navy and gray - professional and trustworthy",
    "fontSelection": "Calibri - ATS-friendly and professional",
    "layout": "Two-column with emphasis on experience section",
    "sections": ["Header", "Summary", "Experience", "Skills", "Education"]
  },
  "customizationSuggestions": [
    "Emphasize leadership experience in layout",
    "Use larger font for role titles",
    "Add subtle accent color for section headers"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2000,
        temperature: 0.4,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Template recommendation error:', error);
      throw new Error('Failed to recommend template');
    }
  }

  async optimizeResumeLayout(resumeData, templateType = 'professional', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Optimize the layout and visual presentation for this resume using ${templateType} template:

Resume Data:
${JSON.stringify(resumeData, null, 2)}

Provide layout optimization recommendations:

{
  "layoutOptimization": {
    "sectionOrder": ["Header", "Professional Summary", "Core Competencies", "Professional Experience", "Education"],
    "sectionWeights": {
      "experience": 45,
      "skills": 25,
      "education": 15,
      "summary": 15
    }
  },
  "visualEnhancements": {
    "typography": {
      "primaryFont": "Calibri",
      "headingFont": "Calibri Bold",
      "bodySize": "11pt",
      "headingSize": "14pt"
    },
    "spacing": {
      "sectionGaps": "12pt",
      "lineSpacing": "1.15",
      "margins": "0.75 inch all sides"
    },
    "colorPalette": {
      "primary": "#2C3E50",
      "accent": "#3498DB",
      "text": "#000000"
    }
  },
  "atsOptimization": {
    "score": 95,
    "recommendations": [
      "Use standard section headers",
      "Maintain left-aligned text",
      "Avoid text in headers/footers"
    ]
  },
  "readabilityImprovements": [
    "Use bullet points for achievements",
    "Bold key accomplishments",
    "Ensure consistent date formatting"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2000,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Layout optimization error:', error);
      throw new Error('Failed to optimize layout');
    }
  }

  // Multi-language and Cultural Adaptation
  async translateResume(resumeData, targetLanguage, targetRegion = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Translate this resume to ${targetLanguage} while maintaining professional context and cultural appropriateness:

Resume Data:
${JSON.stringify(resumeData, null, 2)}
Target Language: ${targetLanguage}
${targetRegion ? `Target Region: ${targetRegion}` : ''}

Provide professional translation with cultural adaptation:

{
  "translatedResume": {
    "personalInfo": {
      "name": "Translated name (if culturally appropriate)",
      "title": "Professional title in target language"
    },
    "professionalSummary": "Translated professional summary",
    "experience": [
      {
        "role": "Translated role title",
        "company": "Company name (translated if needed)",
        "description": "Translated job description"
      }
    ],
    "skills": ["Translated skill 1", "Translated skill 2"],
    "education": [
      {
        "degree": "Translated degree name",
        "institution": "Translated institution name"
      }
    ]
  },
  "culturalAdaptations": [
    {
      "section": "personalInfo",
      "adaptation": "Added photo requirement for German market",
      "reasoning": "Standard practice in German-speaking countries"
    }
  ],
  "formatAdjustments": [
    "Date format changed to DD.MM.YYYY for European standards",
    "Added nationality field as required in target region"
  ],
  "linguisticNotes": [
    "Used formal register appropriate for professional context",
    "Maintained technical terminology accuracy"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 3000,
        temperature: 0.3,
        preferredProvider: 'claude', // Claude often better for translations
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Resume translation error:', error);
      throw new Error('Failed to translate resume');
    }
  }

  async adaptForRegionalMarket(resumeData, targetRegion, targetRole = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Adapt this resume for the ${targetRegion} job market and cultural expectations:

Resume Data:
${JSON.stringify(resumeData, null, 2)}
Target Region: ${targetRegion}
${targetRole ? `Target Role: ${targetRole}` : ''}

Provide regional market adaptation:

{
  "regionalAdaptation": {
    "requiredFields": [
      {
        "field": "photo",
        "required": true,
        "reasoning": "Standard practice in German job market"
      },
      {
        "field": "nationality",
        "required": true,
        "reasoning": "Work authorization clarity"
      }
    ],
    "formatPreferences": {
      "dateFormat": "DD.MM.YYYY",
      "lengthPreference": "2 pages maximum",
      "sectionOrder": ["Personal Info", "Experience", "Education", "Skills"]
    },
    "culturalConsiderations": [
      "Include detailed education section - highly valued in German market",
      "Emphasize certifications and formal qualifications",
      "Use formal language and conservative presentation"
    ]
  },
  "contentAdjustments": [
    {
      "section": "experience",
      "adjustment": "Add company size and industry details",
      "reasoning": "European employers value context about company scale"
    }
  ],
  "marketSpecificTips": [
    "Include language proficiency levels using CEFR standards",
    "Mention willingness to relocate if applicable",
    "Add references availability statement"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Regional adaptation error:', error);
      throw new Error('Failed to adapt for regional market');
    }
  }

  // Achievement Quantification Assistant
  async quantifyAchievements(achievements, jobContext = '', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
As a professional resume coach, help quantify the following achievements with specific metrics and impact statements.

${jobContext ? `Job Context: ${jobContext}` : ''}

Current Achievements:
${Array.isArray(achievements) ? achievements.map((a, i) => `${i + 1}. ${a}`).join('\n') : achievements}

For each achievement, provide:
1. Quantified version with specific numbers, percentages, timeframes
2. Impact statement showing business value
3. Action-oriented language with strong verbs
4. Industry-appropriate metrics when possible

Guidelines:
- Use specific numbers whenever possible (percentages, dollar amounts, timeframes)
- Include scope (team size, project scale, customer base)
- Highlight measurable impact (revenue, efficiency, cost savings)
- Use industry-standard KPIs where relevant

Format as JSON:
{
  "quantifiedAchievements": [
    {
      "original": "Improved sales",
      "quantified": "Increased quarterly sales by 25% ($2.3M) through implementation of new CRM system",
      "metrics": ["25% increase", "$2.3M revenue", "quarterly timeframe"],
      "impact": "Generated significant revenue growth and improved customer relationship management",
      "actionVerb": "Increased",
      "suggestions": ["Consider adding team size or market context"]
    }
  ],
  "overallScore": 85,
  "recommendations": [
    "Add more specific timeframes",
    "Include team or project scope where applicable"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2000,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Achievement quantification error:', error);
      throw new Error('Failed to quantify achievements');
    }
  }

  // Enhanced Content Personalization for Company Culture
  async personalizeForCompanyCulture(resumeData, companyInfo, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Personalize resume content to match the specific company culture and values.

Resume Data:
${JSON.stringify(resumeData, null, 2)}

Company Information:
${JSON.stringify(companyInfo, null, 2)}

Analyze the company culture and provide personalized content suggestions:

1. Tone and Language Adjustments
2. Value Alignment Opportunities
3. Cultural Fit Enhancements
4. Industry-Specific Terminology
5. Communication Style Matching

Provide specific content modifications:

{
  "personalizedContent": {
    "summary": "Tailored professional summary reflecting company values",
    "keywordAdjustments": ["value-aligned keywords to emphasize"],
    "toneRecommendations": {
      "current": "formal",
      "recommended": "collaborative",
      "reasoning": "Company emphasizes teamwork and innovation"
    },
    "culturalAlignments": [
      {
        "companyValue": "Innovation",
        "resumeAlignment": "Highlight creative problem-solving experience",
        "suggestedContent": "Led innovative solutions that..."
      }
    ]
  },
  "modifications": [
    {
      "section": "experience",
      "original": "Managed team projects",
      "personalized": "Collaborated with cross-functional teams to drive innovation",
      "reasoning": "Aligns with company's collaborative culture"
    }
  ],
  "fitScore": 88,
  "recommendations": [
    "Emphasize collaborative achievements",
    "Use company-specific terminology where appropriate"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.4,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Content personalization error:', error);
      throw new Error('Failed to personalize content for company culture');
    }
  }

  // Industry-Specific Section Recommendations
  async recommendResumeSections(industry, jobRole, experienceLevel = 'mid', options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Provide industry-specific resume section recommendations for optimal impact.

Industry: ${industry}
Job Role: ${jobRole}
Experience Level: ${experienceLevel}

Analyze industry standards and provide section recommendations:

Standard Sections: Header, Summary, Experience, Education, Skills
Optional Sections: Certifications, Projects, Publications, Volunteer Work, Awards, Languages, References

Consider:
1. Industry expectations and norms
2. ATS compatibility
3. Recruiter preferences for this field
4. Role-specific requirements
5. Experience level appropriateness

{
  "recommendedSections": [
    {
      "name": "Professional Summary",
      "priority": "essential",
      "reasoning": "Critical for ATS and recruiter screening",
      "industrySpecific": true,
      "order": 1,
      "tips": ["Keep to 2-3 sentences", "Include key industry keywords"]
    },
    {
      "name": "Technical Skills",
      "priority": "high", 
      "reasoning": "Tech industry values specific skill sets",
      "industrySpecific": true,
      "order": 3,
      "tips": ["Categorize by skill type", "Include proficiency levels"]
    }
  ],
  "sectionsToAvoid": [
    {
      "name": "Objective",
      "reasoning": "Outdated in modern resume standards",
      "alternative": "Professional Summary"
    }
  ],
  "industryInsights": {
    "keyPriorities": ["Technical skills demonstration", "Project outcomes"],
    "commonMistakes": ["Too much detail in job descriptions", "Missing quantifiable results"],
    "atsConsiderations": ["Use standard section headers", "Include relevant keywords"]
  },
  "customization": {
    "sectionOrder": [1, 2, 3, 4, 5],
    "emphasisAreas": ["Technical skills", "Project experience", "Quantifiable results"]
  }
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2000,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Section recommendations error:', error);
      throw new Error('Failed to generate section recommendations');
    }
  }

  // Keyword Density Analysis and Optimization
  async analyzeKeywordDensity(resumeContent, jobDescription, options = {}) {
    if (!this.aiClient && this.providers.length === 0) {
      throw new Error('AI service not configured');
    }

    const prompt = `
Analyze keyword density in resume content against job requirements and provide optimization recommendations.

Resume Content:
${typeof resumeContent === 'string' ? resumeContent : JSON.stringify(resumeContent, null, 2)}

Job Description:
${jobDescription}

Perform comprehensive keyword analysis:

1. Extract key terms from job description
2. Analyze current keyword usage in resume
3. Calculate keyword density percentages
4. Identify over-optimization risks
5. Suggest natural keyword integration
6. Balance keyword density for ATS optimization

{
  "keywordAnalysis": {
    "extractedKeywords": [
      {
        "keyword": "project management",
        "importance": "high",
        "currentCount": 3,
        "recommendedCount": "2-4",
        "density": 2.1,
        "status": "optimal"
      },
      {
        "keyword": "leadership",
        "importance": "medium", 
        "currentCount": 1,
        "recommendedCount": "2-3",
        "density": 0.7,
        "status": "under-used"
      }
    ],
    "overallDensity": 8.5,
    "optimalRange": "7-12%",
    "riskAssessment": "low"
  },
  "optimization": {
    "addKeywords": [
      {
        "keyword": "strategic planning",
        "suggestion": "Add to experience descriptions naturally",
        "priority": "high",
        "context": "Mention in project management contexts"
      }
    ],
    "reduceKeywords": [
      {
        "keyword": "responsible for",
        "currentCount": 5,
        "suggestion": "Replace with action verbs",
        "priority": "medium"
      }
    ],
    "naturalIntegration": [
      "Integrate 'cross-functional collaboration' in team project descriptions",
      "Add 'data-driven decision making' to analytics-related achievements"
    ]
  },
  "atsScore": 92,
  "recommendations": [
    "Maintain current keyword density",
    "Focus on natural language integration",
    "Avoid keyword stuffing in any single section"
  ]
}`;

    try {
      const result = await this.callWithFallback(prompt, {
        maxTokens: 2500,
        temperature: 0.3,
        ...options
      });

      if (result && result.content) {
        const content = result.content.replace(/```json\n?|\n?```/g, '').trim();
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Keyword density analysis error:', error);
      throw new Error('Failed to analyze keyword density');
    }
  }
}

module.exports = AIService;