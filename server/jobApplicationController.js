const JobApplicationService = require('./jobApplicationService');

class JobApplicationController {
  constructor() {
    this.applicationService = new JobApplicationService();
  }

  // Queue an automated job application
  async queueApplication(req, res) {
    try {
      const {
        jobId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        coverLetter,
        customAnswers,
        priority,
        scheduledFor
      } = req.body;

      const userId = req.user.userId;

      if (!jobId || !resumeId || !jobUrl || !jobTitle || !company) {
        return res.status(400).json({
          error: 'Missing required fields: jobId, resumeId, jobUrl, jobTitle, company'
        });
      }

      const applicationData = {
        userId,
        jobId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        coverLetter,
        customAnswers,
        priority: priority || 'normal',
        scheduledFor: scheduledFor ? new Date(scheduledFor) : new Date()
      };

      const queuedApplication = await this.applicationService.queueApplication(applicationData);

      res.json({
        message: 'Application queued successfully',
        application: {
          id: queuedApplication.id,
          jobTitle: queuedApplication.jobTitle,
          company: queuedApplication.company,
          status: queuedApplication.status,
          scheduledFor: queuedApplication.scheduledFor,
          priority: queuedApplication.priority
        }
      });
    } catch (error) {
      console.error('Queue application error:', error);
      res.status(500).json({ error: 'Failed to queue application' });
    }
  }

  // Get application statistics for user
  async getApplicationStats(req, res) {
    try {
      const userId = req.user.userId;
      const stats = await this.applicationService.getApplicationStats(userId);

      res.json({
        message: 'Application statistics retrieved successfully',
        stats
      });
    } catch (error) {
      console.error('Get application stats error:', error);
      res.status(500).json({ error: 'Failed to get application statistics' });
    }
  }

  // Get optimal timing for job application
  async getOptimalTiming(req, res) {
    try {
      const { jobTitle, company, description } = req.body;

      if (!jobTitle || !company) {
        return res.status(400).json({
          error: 'Job title and company are required'
        });
      }

      const jobData = { title: jobTitle, company, description };
      const timing = this.applicationService.getOptimalApplicationTiming(jobData);

      res.json({
        message: 'Optimal timing calculated successfully',
        timing
      });
    } catch (error) {
      console.error('Get optimal timing error:', error);
      res.status(500).json({ error: 'Failed to calculate optimal timing' });
    }
  }

  // Get application queue status
  async getQueueStatus(req, res) {
    try {
      const queueStatus = this.applicationService.getQueueStatus();

      res.json({
        message: 'Queue status retrieved successfully',
        queueStatus
      });
    } catch (error) {
      console.error('Get queue status error:', error);
      res.status(500).json({ error: 'Failed to get queue status' });
    }
  }

  // Cancel a queued application
  async cancelApplication(req, res) {
    try {
      const { applicationId } = req.params;
      const userId = req.user.userId;

      // Find application in queue
      const application = this.applicationService.applicationQueue.find(
        app => app.id === applicationId && app.userId === userId
      );

      if (!application) {
        return res.status(404).json({ error: 'Application not found' });
      }

      if (application.status === 'processing') {
        return res.status(400).json({ 
          error: 'Cannot cancel application that is currently being processed' 
        });
      }

      // Remove from queue
      this.applicationService.removeFromQueue(applicationId);

      res.json({
        message: 'Application cancelled successfully',
        applicationId
      });
    } catch (error) {
      console.error('Cancel application error:', error);
      res.status(500).json({ error: 'Failed to cancel application' });
    }
  }

  // Bulk queue applications for multiple jobs
  async bulkQueueApplications(req, res) {
    try {
      const { applications } = req.body;
      const userId = req.user.userId;

      if (!Array.isArray(applications) || applications.length === 0) {
        return res.status(400).json({
          error: 'Applications array is required and must not be empty'
        });
      }

      const results = [];
      const errors = [];

      for (let i = 0; i < applications.length; i++) {
        try {
          const appData = { ...applications[i], userId };
          const queuedApp = await this.applicationService.queueApplication(appData);
          
          results.push({
            index: i,
            id: queuedApp.id,
            jobTitle: queuedApp.jobTitle,
            company: queuedApp.company,
            status: 'queued'
          });
        } catch (error) {
          errors.push({
            index: i,
            error: error.message,
            jobTitle: applications[i].jobTitle,
            company: applications[i].company
          });
        }
      }

      res.json({
        message: `Bulk queue completed: ${results.length} queued, ${errors.length} failed`,
        queued: results,
        errors: errors,
        summary: {
          total: applications.length,
          successful: results.length,
          failed: errors.length
        }
      });
    } catch (error) {
      console.error('Bulk queue applications error:', error);
      res.status(500).json({ error: 'Failed to bulk queue applications' });
    }
  }

  // Update application timing optimization settings
  async updateOptimizationSettings(req, res) {
    try {
      const { 
        maxConcurrentApplications,
        delayBetweenApplications,
        maxRetries,
        timeoutPerApplication
      } = req.body;

      // Update service configuration
      const config = this.applicationService.automationConfig;
      
      if (maxConcurrentApplications) {
        config.maxConcurrentApplications = Math.max(1, Math.min(10, maxConcurrentApplications));
      }
      
      if (delayBetweenApplications) {
        config.delayBetweenApplications = Math.max(1000, Math.min(60000, delayBetweenApplications));
      }
      
      if (maxRetries) {
        config.maxRetries = Math.max(1, Math.min(5, maxRetries));
      }
      
      if (timeoutPerApplication) {
        config.timeoutPerApplication = Math.max(60000, Math.min(1800000, timeoutPerApplication));
      }

      res.json({
        message: 'Optimization settings updated successfully',
        config: this.applicationService.automationConfig
      });
    } catch (error) {
      console.error('Update optimization settings error:', error);
      res.status(500).json({ error: 'Failed to update optimization settings' });
    }
  }
}

module.exports = JobApplicationController;