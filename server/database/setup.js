const { Pool } = require('pg');
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const SecureDatabaseInit = require('./secureInit');

/**
 * Enhanced Database Setup with Security and Migration Support
 * Supports both PostgreSQL and SQLite with comprehensive security features
 */
class DatabaseSetup {
  constructor() {
    this.client = null;
    this.usePostgreSQL = false;
    this.environment = process.env.NODE_ENV || 'development';
    this.isProduction = this.environment === 'production';

    // Database configuration
    this.config = {
      postgresql: {
        connectionString: process.env.DATABASE_URL,
        ssl: this.isProduction ? { rejectUnauthorized: false } : false,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      },
      sqlite: {
        path: process.env.DB_PATH || path.join(__dirname, '../database.sqlite'),
        options: {
          verbose: !this.isProduction
        }
      }
    };
  }

  /**
   * Initialize database with security features
   */
  async initDatabase() {
    console.log('🚀 Initializing CVLeap database...');

    try {
      // Attempt PostgreSQL connection first
      if (this.config.postgresql.connectionString &&
          this.config.postgresql.connectionString !== 'postgresql://username:password@localhost:5432/cvleap') {

        await this.initializePostgreSQL();
      } else {
        await this.initializeSQLite();
      }

      // Run security initialization (skip if disabled)
      if (process.env.DISABLE_SECURE_INIT !== 'true') {
        await this.runSecureInitialization();
      } else {
        console.log('⚠️  Secure initialization disabled via DISABLE_SECURE_INIT');
      }

      // Run migrations (skip if disabled)
      if (process.env.DISABLE_MIGRATIONS !== 'true') {
        await this.runMigrations();
      } else {
        console.log('⚠️  Database migrations disabled via DISABLE_MIGRATIONS');
      }

      console.log('✅ Database initialization completed successfully');

      return {
        success: true,
        database: this.usePostgreSQL ? 'PostgreSQL' : 'SQLite',
        securityEnabled: true
      };

    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  /**
   * Initialize PostgreSQL connection
   */
  async initializePostgreSQL() {
    try {
      console.log('🐘 Connecting to PostgreSQL...');

      this.client = new Pool(this.config.postgresql);

      // Test connection
      const testResult = await this.client.query('SELECT NOW()');
      console.log('✅ PostgreSQL connection established');

      this.usePostgreSQL = true;

      // Set application context
      await this.client.query(`
        SET application_name = 'CVLeap-${this.environment}';
        SET timezone = 'UTC';
      `);

    } catch (error) {
      console.warn('⚠️  PostgreSQL connection failed, falling back to SQLite:', error.message);
      await this.initializeSQLite();
    }
  }

  /**
   * Initialize SQLite connection
   */
  async initializeSQLite() {
    try {
      console.log('📁 Connecting to SQLite...');

      // Ensure database directory exists
      const dbDir = path.dirname(this.config.sqlite.path);
      await fs.mkdir(dbDir, { recursive: true });

      this.client = new sqlite3.Database(
        this.config.sqlite.path,
        sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
        (err) => {
          if (err) {
            throw err;
          }
        }
      );

      // Enable foreign keys and other security features
      await this.executeSQLite('PRAGMA foreign_keys = ON');
      await this.executeSQLite('PRAGMA journal_mode = WAL');
      await this.executeSQLite('PRAGMA synchronous = NORMAL');

      console.log('✅ SQLite connection established');
      this.usePostgreSQL = false;

    } catch (error) {
      throw new Error(`SQLite initialization failed: ${error.message}`);
    }
  }

  /**
   * Run secure database initialization
   */
  async runSecureInitialization() {
    console.log('🔐 Running secure database initialization...');

    try {
      const secureInit = new SecureDatabaseInit(this.client);
      const result = await secureInit.initializeSecureDatabase();

      console.log('✅ Secure initialization completed');
      return result;

    } catch (error) {
      console.error('❌ Secure initialization failed:', error);
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  async runMigrations() {
    console.log('📋 Running database migrations...');

    try {
      const migrationsDir = path.join(__dirname, 'migrations');

      // Check if migrations directory exists
      try {
        await fs.access(migrationsDir);
      } catch (error) {
        console.log('📁 Creating migrations directory...');
        await fs.mkdir(migrationsDir, { recursive: true });
      }

      // Get list of migration files
      const migrationFiles = await fs.readdir(migrationsDir);
      const sqlMigrations = migrationFiles
        .filter(file => file.endsWith('.sql'))
        .sort();

      if (sqlMigrations.length === 0) {
        console.log('📋 No migrations to run');
        return;
      }

      // Create migrations table if it doesn't exist
      await this.createMigrationsTable();

      // Get applied migrations
      const appliedMigrations = await this.getAppliedMigrations();

      // Run pending migrations
      for (const migrationFile of sqlMigrations) {
        const migrationName = path.basename(migrationFile, '.sql');

        if (!appliedMigrations.includes(migrationName)) {
          await this.runMigration(migrationFile, migrationName);
        } else {
          console.log(`⏭️  Skipping already applied migration: ${migrationName}`);
        }
      }

      console.log('✅ All migrations completed');

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Create migrations tracking table
   */
  async createMigrationsTable() {
    const sql = this.usePostgreSQL ? `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id SERIAL PRIMARY KEY,
        version VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(64)
      )
    ` : `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version TEXT NOT NULL UNIQUE,
        description TEXT,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        checksum TEXT
      )
    `;

    await this.executeSQL(sql);
  }

  /**
   * Get list of applied migrations
   */
  async getAppliedMigrations() {
    try {
      const result = await this.executeSQL('SELECT version FROM schema_migrations ORDER BY applied_at');

      if (this.usePostgreSQL) {
        return result.rows.map(row => row.version);
      } else {
        return result.map(row => row.version);
      }
    } catch (error) {
      // Table doesn't exist yet
      return [];
    }
  }

  /**
   * Run a single migration
   */
  async runMigration(migrationFile, migrationName) {
    console.log(`🔄 Running migration: ${migrationName}`);

    try {
      const migrationPath = path.join(__dirname, 'migrations', migrationFile);
      const migrationSQL = await fs.readFile(migrationPath, 'utf8');

      // Calculate checksum
      const checksum = crypto.createHash('sha256').update(migrationSQL).digest('hex');

      // Execute migration
      await this.executeSQL(migrationSQL);

      // Record migration
      await this.executeSQL(
        'INSERT INTO schema_migrations (version, description, checksum) VALUES (?, ?, ?)',
        [migrationName, `Migration: ${migrationName}`, checksum]
      );

      console.log(`✅ Migration completed: ${migrationName}`);

    } catch (error) {
      console.error(`❌ Migration failed: ${migrationName}`, error);
      throw error;
    }
  }

  /**
   * Execute SQL with proper error handling
   */
  async executeSQL(sql, params = []) {
    try {
      if (this.usePostgreSQL) {
        return await this.client.query(sql, params);
      } else {
        return await this.executeSQLite(sql, params);
      }
    } catch (error) {
      console.error('SQL execution error:', error);
      throw error;
    }
  }

  /**
   * Execute SQLite query with promise wrapper
   */
  async executeSQLite(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (sql.trim().toUpperCase().startsWith('SELECT')) {
        this.client.all(sql, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      } else {
        this.client.run(sql, params, function(err) {
          if (err) reject(err);
          else resolve({ lastID: this.lastID, changes: this.changes });
        });
      }
    });
  }

  /**
   * Get database client
   */
  getClient() {
    return this.client;
  }

  /**
   * Get database type
   */
  getDatabaseType() {
    return this.usePostgreSQL ? 'postgresql' : 'sqlite';
  }

  /**
   * Check if database is ready
   */
  async isReady() {
    try {
      if (this.usePostgreSQL) {
        await this.client.query('SELECT 1');
      } else {
        await this.executeSQLite('SELECT 1');
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus() {
    try {
      const startTime = Date.now();

      if (this.usePostgreSQL) {
        const result = await this.client.query(`
          SELECT
            current_database() as database_name,
            version() as version,
            current_timestamp as server_time
        `);

        return {
          status: 'healthy',
          database: 'PostgreSQL',
          responseTime: Date.now() - startTime,
          details: result.rows[0]
        };
      } else {
        await this.executeSQLite('SELECT 1');

        return {
          status: 'healthy',
          database: 'SQLite',
          responseTime: Date.now() - startTime,
          details: {
            database_name: 'cvleap.sqlite',
            version: 'SQLite 3.x'
          }
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        database: this.usePostgreSQL ? 'PostgreSQL' : 'SQLite',
        error: error.message
      };
    }
  }

  /**
   * Close database connection
   */
  async close() {
    try {
      if (this.usePostgreSQL && this.client) {
        await this.client.end();
      } else if (this.client) {
        this.client.close();
      }
      console.log('📴 Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }

  // Enhanced adapter methods for secure database operations
  async createUser(email, passwordHash, name, options = {}) {
    try {
      const sql = this.usePostgreSQL ? `
        INSERT INTO users (
          email, password_hash, name, first_name, last_name,
          user_role, account_status, email_verified
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
      ` : `
        INSERT INTO users (
          email, password_hash, name, first_name, last_name,
          user_role, account_status, email_verified
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        email,
        passwordHash,
        name,
        options.firstName || name.split(' ')[0],
        options.lastName || name.split(' ').slice(1).join(' ') || '',
        options.role || 'user',
        options.status || 'pending_verification',
        options.emailVerified || false
      ];

      const result = await this.executeSQL(sql, params);

      if (this.usePostgreSQL) {
        return result.rows[0].id;
      } else {
        return result.lastID;
      }
    } catch (error) {
      throw new Error(`User creation failed: ${error.message}`);
    }
  }

  async getUserByEmail(email) {
    try {
      const sql = 'SELECT * FROM users WHERE email = ?';
      const result = await this.executeSQL(sql, [email]);

      if (this.usePostgreSQL) {
        return result.rows[0] || null;
      } else {
        return result[0] || null;
      }
    } catch (error) {
      throw new Error(`User lookup failed: ${error.message}`);
    }
  }

  async getUserById(userId) {
    try {
      const sql = 'SELECT * FROM users WHERE id = ?';
      const result = await this.executeSQL(sql, [userId]);

      if (this.usePostgreSQL) {
        return result.rows[0] || null;
      } else {
        return result[0] || null;
      }
    } catch (error) {
      throw new Error(`User lookup failed: ${error.message}`);
    }
  }

  async createResume(userId, title, data, options = {}) {
    try {
      const sql = this.usePostgreSQL ? `
        INSERT INTO resumes (
          user_id, title, data, template_id, is_encrypted
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      ` : `
        INSERT INTO resumes (
          user_id, title, data, template_id, is_encrypted
        ) VALUES (?, ?, ?, ?, ?)
      `;

      const params = [
        userId,
        title,
        typeof data === 'string' ? data : JSON.stringify(data),
        options.templateId || 'default',
        options.isEncrypted || false
      ];

      const result = await this.executeSQL(sql, params);

      if (this.usePostgreSQL) {
        return result.rows[0].id;
      } else {
        return result.lastID;
      }
    } catch (error) {
      throw new Error(`Resume creation failed: ${error.message}`);
    }
  }

  async getResumesByUserId(userId) {
    try {
      const sql = 'SELECT * FROM resumes WHERE user_id = ? AND is_active = true ORDER BY created_at DESC';
      const result = await this.executeSQL(sql, [userId]);

      if (this.usePostgreSQL) {
        return result.rows;
      } else {
        return result;
      }
    } catch (error) {
      throw new Error(`Resume lookup failed: ${error.message}`);
    }
  }

  async createJob(userId, jobData) {
    try {
      const sql = this.usePostgreSQL ? `
        INSERT INTO jobs (
          user_id, title, company, location, description, url,
          salary_min, salary_max, job_type, status, external_id, source_platform
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING id
      ` : `
        INSERT INTO jobs (
          user_id, title, company, location, description, url,
          salary_min, salary_max, job_type, status, external_id, source_platform
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        userId,
        jobData.title,
        jobData.company,
        jobData.location,
        jobData.description,
        jobData.url,
        jobData.salaryMin,
        jobData.salaryMax,
        jobData.jobType,
        jobData.status || 'saved',
        jobData.externalId,
        jobData.sourcePlatform
      ];

      const result = await this.executeSQL(sql, params);

      if (this.usePostgreSQL) {
        return result.rows[0].id;
      } else {
        return result.lastID;
      }
    } catch (error) {
      throw new Error(`Job creation failed: ${error.message}`);
    }
  }

  async logAuditEvent(eventData) {
    try {
      const sql = this.usePostgreSQL ? `
        INSERT INTO audit_logs (
          user_id, action, resource_type, resource_id,
          new_values, ip_address, user_agent, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
      ` : `
        INSERT INTO audit_logs (
          user_id, action, resource_type, resource_id,
          new_values, ip_address, user_agent, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        eventData.userId,
        eventData.action,
        eventData.resourceType,
        eventData.resourceId,
        JSON.stringify(eventData.newValues || {}),
        eventData.ipAddress,
        eventData.userAgent,
        JSON.stringify(eventData.metadata || {})
      ];

      const result = await this.executeSQL(sql, params);

      if (this.usePostgreSQL) {
        return result.rows[0].id;
      } else {
        return result.lastID;
      }
    } catch (error) {
      console.error('Audit logging failed:', error);
      // Don't throw error for audit logging failures
    }
  }
}

const setupDatabase = async () => {
  const dbSetup = new DatabaseSetup();
  await dbSetup.initDatabase();
  return dbSetup;
};

module.exports = { DatabaseSetup, setupDatabase };