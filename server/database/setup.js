const { PrismaClient } = require('@prisma/client');
const database = require('../database'); // Keep SQLite as fallback

class DatabaseSetup {
  constructor() {
    this.prisma = null;
    this.usePrisma = false;
    this.initDatabase();
  }

  async initDatabase() {
    try {
      // Try to initialize PostgreSQL with Prisma
      if (process.env.DATABASE_URL && process.env.DATABASE_URL !== 'postgresql://username:password@localhost:5432/cvleap') {
        // Check if Prisma client is available before attempting to use it
        try {
          this.prisma = new PrismaClient();
          await this.prisma.$connect();
          console.log('Connected to PostgreSQL database');
          this.usePrisma = true;
          
          // Initialize Prisma tables if needed
          await this.ensurePrismaTables();
        } catch (prismaError) {
          console.log('Prisma client initialization failed:', prismaError.message);
          if (prismaError.message.includes('Prisma schema could not be found') || 
              prismaError.message.includes('PrismaClient is unable to be run in the browser') ||
              prismaError.message.includes('Query engine library not found')) {
            console.log('Prisma binaries not available, falling back to SQLite');
            this.usePrisma = false;
          } else {
            throw prismaError;
          }
        }
      } else {
        console.log('Using SQLite fallback database');
        this.usePrisma = false;
      }
    } catch (error) {
      console.log('PostgreSQL connection failed, falling back to SQLite:', error.message);
      this.usePrisma = false;
    }
  }

  async ensurePrismaTables() {
    try {
      // Check if tables exist and create if needed
      // This would typically be handled by Prisma migrations
      console.log('PostgreSQL tables initialized');
    } catch (error) {
      console.error('Error initializing PostgreSQL tables:', error);
    }
  }

  getClient() {
    if (this.usePrisma && this.prisma) {
      return this.prisma;
    }
    return database; // Fallback to SQLite
  }

  async close() {
    if (this.prisma) {
      await this.prisma.$disconnect();
    }
    if (database.close) {
      database.close();
    }
  }

  // Adapter methods to maintain compatibility
  async createUser(email, passwordHash, name) {
    if (this.usePrisma) {
      const result = await this.prisma.user.create({
        data: {
          email,
          password_hash: passwordHash,
          first_name: name,
        }
      });
      return result.id;
    }
    // Fallback to SQLite
    return new Promise((resolve, reject) => {
      database.get().run(
        'INSERT INTO users (email, password, name) VALUES (?, ?, ?)',
        [email, passwordHash, name],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
  }

  async getUserByEmail(email) {
    if (this.usePrisma) {
      return await this.prisma.user.findUnique({
        where: { email }
      });
    }
    // Fallback to SQLite
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM users WHERE email = ?',
        [email],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async createResume(userId, title, data) {
    if (this.usePrisma) {
      const result = await this.prisma.resume.create({
        data: {
          user_id: userId,
          title,
          content: typeof data === 'string' ? JSON.parse(data) : data,
        }
      });
      return result.id;
    }
    // Fallback to SQLite
    return new Promise((resolve, reject) => {
      database.get().run(
        'INSERT INTO resumes (user_id, title, data) VALUES (?, ?, ?)',
        [userId, title, data],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
  }

  async getResumesByUserId(userId) {
    if (this.usePrisma) {
      return await this.prisma.resume.findMany({
        where: { user_id: userId }
      });
    }
    // Fallback to SQLite
    return new Promise((resolve, reject) => {
      database.get().all(
        'SELECT * FROM resumes WHERE user_id = ?',
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }
}

const setupDatabase = async () => {
  const dbSetup = new DatabaseSetup();
  await dbSetup.initDatabase();
  return dbSetup;
};

module.exports = { DatabaseSetup, setupDatabase };