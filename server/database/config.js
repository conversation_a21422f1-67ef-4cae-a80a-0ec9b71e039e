/**
 * Production-Ready Database Configuration
 * Handles environment-specific database settings with security hardening
 */

const path = require('path');

/**
 * Database configuration for different environments
 */
const config = {
  development: {
    database: {
      type: process.env.DB_TYPE || 'sqlite',
      postgresql: {
        connectionString: process.env.DATABASE_URL || 'postgresql://cvleap:cvleap@localhost:5432/cvleap_dev',
        ssl: false,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
        statement_timeout: 30000,
        query_timeout: 30000
      },
      sqlite: {
        path: process.env.DB_PATH || path.join(__dirname, '../database_dev.sqlite'),
        options: {
          verbose: true,
          busyTimeout: 30000
        }
      }
    },
    security: {
      bcryptRounds: 12,
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      apiKeyExpiry: 30 * 24 * 60 * 60 * 1000, // 30 days
      maxFailedLogins: 10,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      enableAuditLogging: true,
      enableRLS: false // Disabled for development ease
    },
    admin: {
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      name: process.env.ADMIN_NAME || 'Development Admin',
      autoCreate: true
    }
  },

  test: {
    database: {
      type: 'sqlite',
      postgresql: {
        connectionString: process.env.TEST_DATABASE_URL || 'postgresql://cvleap:cvleap@localhost:5432/cvleap_test',
        ssl: false,
        max: 5,
        idleTimeoutMillis: 10000,
        connectionTimeoutMillis: 1000
      },
      sqlite: {
        path: ':memory:', // In-memory database for tests
        options: {
          verbose: false
        }
      }
    },
    security: {
      bcryptRounds: 4, // Faster for tests
      sessionTimeout: 60 * 60 * 1000, // 1 hour
      apiKeyExpiry: 24 * 60 * 60 * 1000, // 1 day
      maxFailedLogins: 3,
      lockoutDuration: 5 * 60 * 1000, // 5 minutes
      enableAuditLogging: false,
      enableRLS: false
    },
    admin: {
      email: '<EMAIL>',
      name: 'Test Admin',
      autoCreate: true
    }
  },

  staging: {
    database: {
      type: 'postgresql',
      postgresql: {
        connectionString: process.env.DATABASE_URL,
        ssl: {
          rejectUnauthorized: false
        },
        max: 15,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 5000,
        statement_timeout: 60000,
        query_timeout: 60000
      },
      sqlite: {
        path: path.join(__dirname, '../database_staging.sqlite'),
        options: {
          verbose: false
        }
      }
    },
    security: {
      bcryptRounds: 13,
      sessionTimeout: 12 * 60 * 60 * 1000, // 12 hours
      apiKeyExpiry: 90 * 24 * 60 * 60 * 1000, // 90 days
      maxFailedLogins: 5,
      lockoutDuration: 30 * 60 * 1000, // 30 minutes
      enableAuditLogging: true,
      enableRLS: true
    },
    admin: {
      email: process.env.ADMIN_EMAIL,
      name: process.env.ADMIN_NAME || 'Staging Admin',
      autoCreate: false // Manual creation in staging
    }
  },

  production: {
    database: {
      type: 'postgresql',
      postgresql: {
        connectionString: process.env.DATABASE_URL,
        ssl: {
          rejectUnauthorized: true,
          ca: process.env.DB_SSL_CA,
          cert: process.env.DB_SSL_CERT,
          key: process.env.DB_SSL_KEY
        },
        max: 25,
        min: 5,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000,
        statement_timeout: 120000,
        query_timeout: 120000,
        application_name: 'CVLeap-Production'
      },
      sqlite: {
        // SQLite not recommended for production
        path: path.join(__dirname, '../database_prod.sqlite'),
        options: {
          verbose: false
        }
      }
    },
    security: {
      bcryptRounds: 14,
      sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
      apiKeyExpiry: 365 * 24 * 60 * 60 * 1000, // 1 year
      maxFailedLogins: 3,
      lockoutDuration: 60 * 60 * 1000, // 1 hour
      enableAuditLogging: true,
      enableRLS: true,
      requireTwoFactor: true,
      enforcePasswordPolicy: true
    },
    admin: {
      email: process.env.ADMIN_EMAIL,
      name: process.env.ADMIN_NAME || 'Production Admin',
      autoCreate: false // Never auto-create in production
    }
  }
};

/**
 * Get configuration for current environment
 */
function getConfig() {
  const env = process.env.NODE_ENV || 'development';
  
  if (!config[env]) {
    throw new Error(`Invalid environment: ${env}`);
  }
  
  return {
    ...config[env],
    environment: env,
    isProduction: env === 'production',
    isTest: env === 'test',
    isDevelopment: env === 'development'
  };
}

/**
 * Validate configuration for current environment
 */
function validateConfig() {
  const currentConfig = getConfig();
  const errors = [];
  
  // Validate required environment variables
  const requiredVars = ['ENCRYPTION_MASTER_KEY'];
  
  if (currentConfig.isProduction) {
    requiredVars.push(
      'DATABASE_URL',
      'ADMIN_EMAIL',
      'ADMIN_PASSWORD',
      'JWT_SECRET'
    );
  }
  
  for (const envVar of requiredVars) {
    if (!process.env[envVar]) {
      errors.push(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // Validate database URL format
  if (currentConfig.database.type === 'postgresql' && process.env.DATABASE_URL) {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl.startsWith('postgresql://') && !dbUrl.startsWith('postgres://')) {
      errors.push('Invalid PostgreSQL connection string format');
    }
  }
  
  // Validate admin email format
  if (currentConfig.admin.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(currentConfig.admin.email)) {
      errors.push('Invalid admin email format');
    }
  }
  
  // Validate security settings
  if (currentConfig.security.bcryptRounds < 10) {
    errors.push('bcrypt rounds too low for security');
  }
  
  if (currentConfig.isProduction) {
    if (currentConfig.security.bcryptRounds < 12) {
      errors.push('bcrypt rounds too low for production');
    }
    
    if (!currentConfig.security.enableAuditLogging) {
      errors.push('Audit logging must be enabled in production');
    }
    
    if (!currentConfig.security.enableRLS) {
      errors.push('Row Level Security must be enabled in production');
    }
  }
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
  
  return true;
}

/**
 * Get database connection configuration
 */
function getDatabaseConfig() {
  const config = getConfig();
  
  if (config.database.type === 'postgresql') {
    return {
      type: 'postgresql',
      ...config.database.postgresql
    };
  } else {
    return {
      type: 'sqlite',
      ...config.database.sqlite
    };
  }
}

/**
 * Get security configuration
 */
function getSecurityConfig() {
  const config = getConfig();
  return config.security;
}

/**
 * Get admin configuration
 */
function getAdminConfig() {
  const config = getConfig();
  return config.admin;
}

/**
 * Check if feature is enabled
 */
function isFeatureEnabled(feature) {
  const config = getConfig();
  
  switch (feature) {
    case 'audit_logging':
      return config.security.enableAuditLogging;
    case 'row_level_security':
      return config.security.enableRLS;
    case 'two_factor':
      return config.security.requireTwoFactor;
    case 'password_policy':
      return config.security.enforcePasswordPolicy;
    default:
      return false;
  }
}

/**
 * Get connection pool configuration
 */
function getPoolConfig() {
  const dbConfig = getDatabaseConfig();
  
  if (dbConfig.type === 'postgresql') {
    return {
      max: dbConfig.max,
      min: dbConfig.min || 0,
      idleTimeoutMillis: dbConfig.idleTimeoutMillis,
      connectionTimeoutMillis: dbConfig.connectionTimeoutMillis
    };
  }
  
  return null;
}

module.exports = {
  getConfig,
  validateConfig,
  getDatabaseConfig,
  getSecurityConfig,
  getAdminConfig,
  isFeatureEnabled,
  getPoolConfig
};
