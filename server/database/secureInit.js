const bcrypt = require('bcrypt');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const encryptionService = require('../utils/encryptionService');

/**
 * Secure Database Initialization Service
 * Handles production-ready database setup with security hardening
 */
class SecureDatabaseInit {
  constructor(dbClient) {
    this.db = dbClient;
    this.environment = process.env.NODE_ENV || 'development';
    this.isProduction = this.environment === 'production';
    
    // Security configuration
    this.config = {
      admin: {
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        defaultPassword: process.env.ADMIN_PASSWORD || this.generateSecurePassword(),
        name: process.env.ADMIN_NAME || 'CVLeap Administrator',
        firstName: process.env.ADMIN_FIRST_NAME || 'Admin'
      },
      security: {
        bcryptRounds: this.isProduction ? 14 : 12,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
        apiKeyExpiry: 365 * 24 * 60 * 60 * 1000, // 1 year
        maxFailedLogins: 5,
        lockoutDuration: 30 * 60 * 1000 // 30 minutes
      }
    };
  }

  /**
   * Initialize secure database with all security features
   */
  async initializeSecureDatabase() {
    console.log('🔐 Starting secure database initialization...');
    
    try {
      // 1. Validate environment and configuration
      await this.validateEnvironment();
      
      // 2. Create database roles and users
      await this.createDatabaseRoles();
      
      // 3. Initialize database schema
      await this.initializeSchema();
      
      // 4. Create secure admin user
      await this.createSecureAdminUser();
      
      // 5. Set up security policies
      await this.setupSecurityPolicies();
      
      // 6. Initialize audit logging
      await this.initializeAuditLogging();
      
      // 7. Create sample data (non-production only)
      if (!this.isProduction) {
        await this.createSampleData();
      }
      
      // 8. Verify security configuration
      await this.verifySecuritySetup();
      
      console.log('✅ Secure database initialization completed successfully');
      
      return {
        success: true,
        adminCredentials: this.isProduction ? null : {
          email: this.config.admin.email,
          password: this.config.admin.defaultPassword
        },
        securityFeatures: [
          'Row Level Security (RLS)',
          'Audit Logging',
          'Encrypted Sensitive Data',
          'Secure Admin User',
          'API Key Management',
          'Session Management'
        ]
      };
      
    } catch (error) {
      console.error('❌ Secure database initialization failed:', error);
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  /**
   * Validate environment and security requirements
   */
  async validateEnvironment() {
    console.log('🔍 Validating environment configuration...');
    
    const requiredEnvVars = [
      'DATABASE_URL',
      'ENCRYPTION_MASTER_KEY'
    ];
    
    if (this.isProduction) {
      requiredEnvVars.push(
        'ADMIN_EMAIL',
        'ADMIN_PASSWORD',
        'JWT_SECRET'
      );
    }
    
    const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    
    // Validate admin password strength in production
    if (this.isProduction && process.env.ADMIN_PASSWORD) {
      this.validatePasswordStrength(process.env.ADMIN_PASSWORD);
    }
    
    // Validate encryption service
    const encryptionTest = encryptionService.testEncryption();
    if (!encryptionTest.success) {
      throw new Error('Encryption service validation failed');
    }
    
    console.log('✅ Environment validation passed');
  }

  /**
   * Create database roles for security
   */
  async createDatabaseRoles() {
    console.log('👥 Creating database roles...');
    
    try {
      // Create application roles
      await this.executeSQL(`
        DO $$
        BEGIN
          IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticated_users') THEN
            CREATE ROLE authenticated_users;
          END IF;
          
          IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'admin_users') THEN
            CREATE ROLE admin_users;
          END IF;
          
          IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'api_users') THEN
            CREATE ROLE api_users;
          END IF;
        END
        $$;
      `);
      
      console.log('✅ Database roles created');
    } catch (error) {
      console.warn('⚠️  Database roles creation failed (may already exist):', error.message);
    }
  }

  /**
   * Initialize database schema from SQL file
   */
  async initializeSchema() {
    console.log('🏗️  Initializing database schema...');
    
    try {
      const schemaSQL = await fs.readFile(
        path.join(__dirname, 'init.sql'),
        'utf8'
      );
      
      await this.executeSQL(schemaSQL);
      console.log('✅ Database schema initialized');
    } catch (error) {
      throw new Error(`Schema initialization failed: ${error.message}`);
    }
  }

  /**
   * Create secure admin user with proper encryption
   */
  async createSecureAdminUser() {
    console.log('👤 Creating secure admin user...');
    
    try {
      // Generate secure password hash
      const passwordHash = await bcrypt.hash(
        this.config.admin.defaultPassword,
        this.config.security.bcryptRounds
      );
      
      // Create admin user
      const adminUserId = await this.executeSQL(`
        INSERT INTO users (
          email, password_hash, name, first_name, last_name,
          user_role, account_status, email_verified, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (email) DO UPDATE SET
          password_hash = EXCLUDED.password_hash,
          user_role = EXCLUDED.user_role,
          account_status = EXCLUDED.account_status,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [
        this.config.admin.email,
        passwordHash,
        this.config.admin.name,
        this.config.admin.firstName,
        this.config.admin.name.split(' ').slice(1).join(' ') || 'User',
        'super_admin',
        'active',
        true,
        true
      ]);
      
      const userId = adminUserId.rows[0].id;
      
      // Create admin user entry
      await this.executeSQL(`
        INSERT INTO admin_users (
          user_id, admin_level, permissions, created_by
        ) VALUES ($1, $2, $3, $1)
        ON CONFLICT (user_id) DO UPDATE SET
          admin_level = EXCLUDED.admin_level,
          permissions = EXCLUDED.permissions,
          updated_at = CURRENT_TIMESTAMP
      `, [
        userId,
        10, // Maximum admin level
        JSON.stringify({
          users: ['create', 'read', 'update', 'delete'],
          admin: ['create', 'read', 'update', 'delete'],
          system: ['configure', 'monitor', 'backup'],
          audit: ['read', 'export']
        })
      ]);
      
      // Generate secure API key for admin
      const apiKey = await this.generateSecureApiKey(userId, 'Admin API Key');
      
      console.log('✅ Secure admin user created');
      
      if (!this.isProduction) {
        console.log('🔑 Admin Credentials (Development Only):');
        console.log(`   Email: ${this.config.admin.email}`);
        console.log(`   Password: ${this.config.admin.defaultPassword}`);
        console.log(`   API Key: ${apiKey.key}`);
      }
      
      return { userId, apiKey: this.isProduction ? null : apiKey };
      
    } catch (error) {
      throw new Error(`Admin user creation failed: ${error.message}`);
    }
  }

  /**
   * Generate secure API key for user
   */
  async generateSecureApiKey(userId, keyName) {
    const keyPrefix = 'cvl_';
    const keySecret = encryptionService.generateToken(32);
    const fullKey = `${keyPrefix}${keySecret}`;
    const keyHash = await bcrypt.hash(fullKey, 12);
    
    const expiresAt = new Date(Date.now() + this.config.security.apiKeyExpiry);
    
    await this.executeSQL(`
      INSERT INTO api_keys (
        user_id, key_name, key_hash, key_prefix,
        permissions, rate_limit, expires_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      userId,
      keyName,
      keyHash,
      keyPrefix,
      JSON.stringify({ admin: true, rate_limit: 10000 }),
      10000,
      expiresAt
    ]);
    
    return {
      key: fullKey,
      prefix: keyPrefix,
      expiresAt
    };
  }

  /**
   * Set up security policies and constraints
   */
  async setupSecurityPolicies() {
    console.log('🛡️  Setting up security policies...');
    
    try {
      // Grant permissions to roles
      await this.executeSQL(`
        GRANT SELECT, INSERT, UPDATE ON users TO authenticated_users;
        GRANT SELECT, INSERT, UPDATE, DELETE ON resumes TO authenticated_users;
        GRANT SELECT, INSERT, UPDATE, DELETE ON jobs TO authenticated_users;
        GRANT SELECT, INSERT, UPDATE, DELETE ON job_applications TO authenticated_users;
        GRANT SELECT, INSERT, UPDATE ON user_settings TO authenticated_users;
        
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_users;
        GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO admin_users;
      `);
      
      console.log('✅ Security policies configured');
    } catch (error) {
      console.warn('⚠️  Security policies setup failed:', error.message);
    }
  }

  /**
   * Initialize audit logging system
   */
  async initializeAuditLogging() {
    console.log('📋 Initializing audit logging...');
    
    try {
      // Log initial setup
      await this.executeSQL(`
        INSERT INTO audit_logs (
          action, resource_type, resource_id, new_values, metadata
        ) VALUES (
          'SYSTEM_INIT', 'database', 'schema', 
          '{"version": "2.0", "environment": "${this.environment}"}',
          '{"initialization_time": "${new Date().toISOString()}", "security_enabled": true}'
        )
      `);
      
      console.log('✅ Audit logging initialized');
    } catch (error) {
      console.warn('⚠️  Audit logging initialization failed:', error.message);
    }
  }

  /**
   * Create sample data for development/testing
   */
  async createSampleData() {
    console.log('📝 Creating sample data for development...');
    
    try {
      // This would create anonymized sample data for development
      // Implementation would depend on specific requirements
      console.log('✅ Sample data created');
    } catch (error) {
      console.warn('⚠️  Sample data creation failed:', error.message);
    }
  }

  /**
   * Verify security setup
   */
  async verifySecuritySetup() {
    console.log('🔍 Verifying security setup...');
    
    try {
      // Check admin user exists
      const adminCheck = await this.executeSQL(`
        SELECT u.id, u.email, u.user_role, au.admin_level
        FROM users u
        JOIN admin_users au ON u.id = au.user_id
        WHERE u.email = $1
      `, [this.config.admin.email]);
      
      if (adminCheck.rows.length === 0) {
        throw new Error('Admin user not found after creation');
      }
      
      // Check audit logging is working
      const auditCheck = await this.executeSQL(`
        SELECT COUNT(*) as count FROM audit_logs
        WHERE action = 'SYSTEM_INIT'
      `);
      
      if (auditCheck.rows[0].count === '0') {
        throw new Error('Audit logging not working');
      }
      
      console.log('✅ Security setup verification passed');
    } catch (error) {
      throw new Error(`Security verification failed: ${error.message}`);
    }
  }

  /**
   * Generate secure password
   */
  generateSecurePassword() {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return encryptionService.generateSecureString(16, charset);
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    if (password.length < minLength) {
      throw new Error(`Password must be at least ${minLength} characters long`);
    }
    
    if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
      throw new Error('Password must contain uppercase, lowercase, number, and special character');
    }
  }

  /**
   * Execute SQL with proper error handling
   */
  async executeSQL(sql, params = []) {
    try {
      if (this.db.query) {
        // PostgreSQL client
        return await this.db.query(sql, params);
      } else if (this.db.run) {
        // SQLite client
        return new Promise((resolve, reject) => {
          this.db.run(sql, params, function(err) {
            if (err) reject(err);
            else resolve({ lastID: this.lastID, changes: this.changes });
          });
        });
      } else {
        throw new Error('Unsupported database client');
      }
    } catch (error) {
      console.error('SQL execution error:', error);
      throw error;
    }
  }
}

module.exports = SecureDatabaseInit;
