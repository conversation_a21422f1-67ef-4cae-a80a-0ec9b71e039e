-- Migration: 003_monitoring_tables
-- Description: Add tables for monitoring, error tracking, and alerting
-- Version: 3.0
-- Created: 2024-01-15

BEGIN;

-- Record this migration
INSERT INTO schema_migrations (version, description, checksum)
VALUES (
    '003_monitoring_tables',
    'Add tables for monitoring, error tracking, and alerting',
    'sha256_checksum_placeholder_003'
) ON CONFLICT (version) DO NOTHING;

-- Create error_logs table for comprehensive error tracking
CREATE TABLE IF NOT EXISTS error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    fingerprint VARCHAR(32) NOT NULL,
    message TEXT NOT NULL,
    stack TEXT,
    name VARCHAR(255) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'medium',
    context JSONB DEFAULT '{}',
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    ip_address INET,
    url VARCHAR(500),
    method VARCHAR(10),
    user_agent TEXT,
    correlation_id VARCHAR(32),
    resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for error_logs table
CREATE INDEX IF NOT EXISTS idx_error_logs_fingerprint ON error_logs(fingerprint);
CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON error_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_resolved ON error_logs(resolved);
CREATE INDEX IF NOT EXISTS idx_error_logs_correlation_id ON error_logs(correlation_id);

-- Create performance_metrics table for application performance tracking
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(50),
    metric_type VARCHAR(50) NOT NULL DEFAULT 'gauge', -- gauge, counter, histogram
    tags JSONB DEFAULT '{}',
    endpoint VARCHAR(255),
    method VARCHAR(10),
    status_code INTEGER,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    correlation_id VARCHAR(32),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance_metrics table
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_created_at ON performance_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON performance_metrics(endpoint);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_id ON performance_metrics(user_id);

-- Create system_health table for health check results
CREATE TABLE IF NOT EXISTS system_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    check_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL, -- healthy, unhealthy, warning
    response_time INTEGER, -- in milliseconds
    details JSONB DEFAULT '{}',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for system_health table
CREATE INDEX IF NOT EXISTS idx_system_health_check_name ON system_health(check_name);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health(status);
CREATE INDEX IF NOT EXISTS idx_system_health_created_at ON system_health(created_at);

-- Create alerts table for alert management
CREATE TABLE IF NOT EXISTS alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_type VARCHAR(255) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'warning', -- info, warning, critical
    title VARCHAR(500) NOT NULL,
    description TEXT,
    alert_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'active', -- active, acknowledged, resolved
    acknowledged_by UUID REFERENCES users(id) ON DELETE SET NULL,
    acknowledged_at TIMESTAMP,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    notification_sent BOOLEAN DEFAULT false,
    notification_channels TEXT[], -- email, slack, sms, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for alerts table
CREATE INDEX IF NOT EXISTS idx_alerts_type ON alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts(severity);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status);
CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_alerts_acknowledged_by ON alerts(acknowledged_by);

-- Create application_metrics table for business metrics
CREATE TABLE IF NOT EXISTS application_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_category VARCHAR(100) NOT NULL, -- users, jobs, resumes, applications, etc.
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(50),
    dimensions JSONB DEFAULT '{}', -- additional dimensions for grouping
    aggregation_period VARCHAR(20) DEFAULT 'hour', -- minute, hour, day, week, month
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for application_metrics table
CREATE INDEX IF NOT EXISTS idx_application_metrics_category ON application_metrics(metric_category);
CREATE INDEX IF NOT EXISTS idx_application_metrics_name ON application_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_application_metrics_period_start ON application_metrics(period_start);
CREATE INDEX IF NOT EXISTS idx_application_metrics_period_end ON application_metrics(period_end);
CREATE INDEX IF NOT EXISTS idx_application_metrics_aggregation_period ON application_metrics(aggregation_period);

-- Create uptime_checks table for external service monitoring
CREATE TABLE IF NOT EXISTS uptime_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_name VARCHAR(255) NOT NULL,
    service_url VARCHAR(500) NOT NULL,
    check_type VARCHAR(50) NOT NULL DEFAULT 'http', -- http, tcp, ping
    status VARCHAR(20) NOT NULL, -- up, down, degraded
    response_time INTEGER, -- in milliseconds
    status_code INTEGER,
    error_message TEXT,
    check_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for uptime_checks table
CREATE INDEX IF NOT EXISTS idx_uptime_checks_service_name ON uptime_checks(service_name);
CREATE INDEX IF NOT EXISTS idx_uptime_checks_status ON uptime_checks(status);
CREATE INDEX IF NOT EXISTS idx_uptime_checks_created_at ON uptime_checks(created_at);

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_error_logs_updated_at ON error_logs;
CREATE TRIGGER update_error_logs_updated_at 
    BEFORE UPDATE ON error_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_alerts_updated_at ON alerts;
CREATE TRIGGER update_alerts_updated_at 
    BEFORE UPDATE ON alerts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create functions for monitoring and alerting

-- Function to get error statistics
CREATE OR REPLACE FUNCTION get_error_statistics(hours_back INTEGER DEFAULT 24)
RETURNS TABLE (
    total_errors BIGINT,
    unique_errors BIGINT,
    critical_errors BIGINT,
    high_errors BIGINT,
    medium_errors BIGINT,
    low_errors BIGINT,
    error_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_errors,
        COUNT(DISTINCT fingerprint) as unique_errors,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_errors,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_errors,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_errors,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_errors,
        ROUND(COUNT(*)::DECIMAL / GREATEST(hours_back, 1), 2) as error_rate
    FROM error_logs
    WHERE created_at > NOW() - INTERVAL '1 hour' * hours_back;
END;
$$ LANGUAGE plpgsql;

-- Function to get top errors by frequency
CREATE OR REPLACE FUNCTION get_top_errors(limit_count INTEGER DEFAULT 10, hours_back INTEGER DEFAULT 24)
RETURNS TABLE (
    fingerprint VARCHAR,
    message TEXT,
    name VARCHAR,
    severity VARCHAR,
    error_count BIGINT,
    last_occurrence TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        el.fingerprint,
        el.message,
        el.name,
        el.severity,
        COUNT(*) as error_count,
        MAX(el.created_at) as last_occurrence
    FROM error_logs el
    WHERE el.created_at > NOW() - INTERVAL '1 hour' * hours_back
    GROUP BY el.fingerprint, el.message, el.name, el.severity
    ORDER BY error_count DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old monitoring data
CREATE OR REPLACE FUNCTION cleanup_monitoring_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up error logs older than 90 days
    DELETE FROM error_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up performance metrics older than 30 days
    DELETE FROM performance_metrics 
    WHERE created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up system health checks older than 7 days
    DELETE FROM system_health 
    WHERE created_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up resolved alerts older than 30 days
    DELETE FROM alerts 
    WHERE status = 'resolved' AND resolved_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up uptime checks older than 30 days
    DELETE FROM uptime_checks 
    WHERE created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get system health summary
CREATE OR REPLACE FUNCTION get_system_health_summary()
RETURNS TABLE (
    check_name VARCHAR,
    current_status VARCHAR,
    last_check TIMESTAMP,
    avg_response_time DECIMAL,
    uptime_percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_checks AS (
        SELECT DISTINCT ON (sh.check_name)
            sh.check_name,
            sh.status,
            sh.response_time,
            sh.created_at
        FROM system_health sh
        ORDER BY sh.check_name, sh.created_at DESC
    ),
    health_stats AS (
        SELECT 
            sh.check_name,
            COUNT(*) as total_checks,
            COUNT(CASE WHEN sh.status = 'healthy' THEN 1 END) as healthy_checks,
            AVG(sh.response_time) as avg_response_time
        FROM system_health sh
        WHERE sh.created_at > NOW() - INTERVAL '24 hours'
        GROUP BY sh.check_name
    )
    SELECT 
        lc.check_name,
        lc.status as current_status,
        lc.created_at as last_check,
        ROUND(hs.avg_response_time, 2) as avg_response_time,
        ROUND((hs.healthy_checks::DECIMAL / hs.total_checks) * 100, 2) as uptime_percentage
    FROM latest_checks lc
    LEFT JOIN health_stats hs ON lc.check_name = hs.check_name
    ORDER BY lc.check_name;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions for the new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON error_logs TO authenticated_users;
GRANT SELECT, INSERT ON performance_metrics TO authenticated_users;
GRANT SELECT ON system_health TO authenticated_users;
GRANT SELECT ON alerts TO authenticated_users;
GRANT SELECT ON application_metrics TO authenticated_users;
GRANT SELECT ON uptime_checks TO authenticated_users;

GRANT ALL PRIVILEGES ON error_logs TO admin_users;
GRANT ALL PRIVILEGES ON performance_metrics TO admin_users;
GRANT ALL PRIVILEGES ON system_health TO admin_users;
GRANT ALL PRIVILEGES ON alerts TO admin_users;
GRANT ALL PRIVILEGES ON application_metrics TO admin_users;
GRANT ALL PRIVILEGES ON uptime_checks TO admin_users;

-- Log migration completion
INSERT INTO audit_logs (action, resource_type, resource_id, new_values, metadata)
VALUES (
    'MIGRATION_APPLIED',
    'schema',
    '003_monitoring_tables',
    '{"version": "003", "description": "Add tables for monitoring, error tracking, and alerting"}',
    '{"applied_at": "' || CURRENT_TIMESTAMP || '", "tables_created": ["error_logs", "performance_metrics", "system_health", "alerts", "application_metrics", "uptime_checks"]}'
);

COMMIT;
