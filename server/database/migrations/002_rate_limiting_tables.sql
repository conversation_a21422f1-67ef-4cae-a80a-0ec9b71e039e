-- Migration: 002_rate_limiting_tables
-- Description: Add tables for rate limiting and security monitoring
-- Version: 2.0
-- Created: 2024-01-15

BEGIN;

-- Record this migration
INSERT INTO schema_migrations (version, description, checksum)
VALUES (
    '002_rate_limiting_tables',
    'Add tables for rate limiting and security monitoring',
    'sha256_checksum_placeholder_002'
) ON CONFLICT (version) DO NOTHING;

-- Create rate limits table for persistent rate limiting
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) NOT NULL UNIQUE,
    count INTEGER NOT NULL DEFAULT 0,
    reset_time BIGINT NOT NULL,
    created_at BIGINT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for rate limits table
CREATE INDEX IF NOT EXISTS idx_rate_limits_key ON rate_limits(key);
CREATE INDEX IF NOT EXISTS idx_rate_limits_reset_time ON rate_limits(reset_time);
CREATE INDEX IF NOT EXISTS idx_rate_limits_created_at ON rate_limits(created_at);

-- Create security violations table for tracking security events
CREATE TABLE IF NOT EXISTS security_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    violation_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'medium',
    ip_address INET,
    user_agent TEXT,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    request_path VARCHAR(500),
    request_method VARCHAR(10),
    request_data JSONB,
    violation_data JSONB,
    blocked BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for security violations table
CREATE INDEX IF NOT EXISTS idx_security_violations_type ON security_violations(violation_type);
CREATE INDEX IF NOT EXISTS idx_security_violations_severity ON security_violations(severity);
CREATE INDEX IF NOT EXISTS idx_security_violations_ip ON security_violations(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_violations_user_id ON security_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_security_violations_created_at ON security_violations(created_at);
CREATE INDEX IF NOT EXISTS idx_security_violations_blocked ON security_violations(blocked);

-- Create blocked IPs table for IP-based blocking
CREATE TABLE IF NOT EXISTS blocked_ips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address INET NOT NULL UNIQUE,
    reason VARCHAR(255) NOT NULL,
    blocked_by UUID REFERENCES users(id) ON DELETE SET NULL,
    blocked_until TIMESTAMP,
    permanent BOOLEAN DEFAULT false,
    violation_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for blocked IPs table
CREATE INDEX IF NOT EXISTS idx_blocked_ips_ip ON blocked_ips(ip_address);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_blocked_until ON blocked_ips(blocked_until);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_permanent ON blocked_ips(permanent);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_created_at ON blocked_ips(created_at);

-- Create API usage statistics table
CREATE TABLE IF NOT EXISTS api_usage_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time INTEGER, -- in milliseconds
    request_size INTEGER, -- in bytes
    response_size INTEGER, -- in bytes
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for API usage stats
CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_api_key_id ON api_usage_stats(api_key_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage_stats(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_status_code ON api_usage_stats(status_code);
CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage_stats(created_at);

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_rate_limits_updated_at ON rate_limits;
CREATE TRIGGER update_rate_limits_updated_at 
    BEFORE UPDATE ON rate_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_blocked_ips_updated_at ON blocked_ips;
CREATE TRIGGER update_blocked_ips_updated_at 
    BEFORE UPDATE ON blocked_ips 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean up expired rate limits
CREATE OR REPLACE FUNCTION cleanup_expired_rate_limits()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM rate_limits 
    WHERE reset_time <= EXTRACT(EPOCH FROM NOW()) * 1000;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old security violations
CREATE OR REPLACE FUNCTION cleanup_old_security_violations()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Keep security violations for 90 days
    DELETE FROM security_violations 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old API usage stats
CREATE OR REPLACE FUNCTION cleanup_old_api_usage_stats()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Keep API usage stats for 30 days
    DELETE FROM api_usage_stats 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically block IPs with too many violations
CREATE OR REPLACE FUNCTION auto_block_violating_ips()
RETURNS INTEGER AS $$
DECLARE
    blocked_count INTEGER := 0;
    violation_record RECORD;
BEGIN
    -- Find IPs with more than 50 violations in the last hour
    FOR violation_record IN
        SELECT 
            ip_address,
            COUNT(*) as violation_count
        FROM security_violations 
        WHERE created_at > NOW() - INTERVAL '1 hour'
            AND ip_address IS NOT NULL
        GROUP BY ip_address
        HAVING COUNT(*) > 50
    LOOP
        -- Block the IP for 24 hours if not already blocked
        INSERT INTO blocked_ips (ip_address, reason, blocked_until, violation_count)
        VALUES (
            violation_record.ip_address,
            'Automatic block due to excessive violations',
            NOW() + INTERVAL '24 hours',
            violation_record.violation_count
        )
        ON CONFLICT (ip_address) DO UPDATE SET
            blocked_until = GREATEST(blocked_ips.blocked_until, NOW() + INTERVAL '24 hours'),
            violation_count = blocked_ips.violation_count + violation_record.violation_count,
            updated_at = NOW();
        
        blocked_count := blocked_count + 1;
    END LOOP;
    
    RETURN blocked_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to check if IP is blocked
CREATE OR REPLACE FUNCTION is_ip_blocked(check_ip INET)
RETURNS BOOLEAN AS $$
DECLARE
    blocked_record RECORD;
BEGIN
    SELECT * INTO blocked_record
    FROM blocked_ips 
    WHERE ip_address = check_ip
        AND (permanent = true OR blocked_until > NOW());
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions for the new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON rate_limits TO authenticated_users;
GRANT SELECT, INSERT ON security_violations TO authenticated_users;
GRANT SELECT ON blocked_ips TO authenticated_users;
GRANT SELECT, INSERT ON api_usage_stats TO authenticated_users;

GRANT ALL PRIVILEGES ON rate_limits TO admin_users;
GRANT ALL PRIVILEGES ON security_violations TO admin_users;
GRANT ALL PRIVILEGES ON blocked_ips TO admin_users;
GRANT ALL PRIVILEGES ON api_usage_stats TO admin_users;

-- Log migration completion
INSERT INTO audit_logs (action, resource_type, resource_id, new_values, metadata)
VALUES (
    'MIGRATION_APPLIED',
    'schema',
    '002_rate_limiting_tables',
    '{"version": "002", "description": "Add tables for rate limiting and security monitoring"}',
    '{"applied_at": "' || CURRENT_TIMESTAMP || '", "tables_created": ["rate_limits", "security_violations", "blocked_ips", "api_usage_stats"]}'
);

COMMIT;
