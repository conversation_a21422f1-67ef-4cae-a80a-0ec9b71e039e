/**
 * Centralized configuration management service
 * Handles environment variables, feature flags, and application settings
 * with validation, type conversion, and default values
 */

const path = require('path');
const fs = require('fs');

/**
 * Configuration schema for validation and type conversion
 */
const configSchema = {
  // Server configuration
  server: {
    port: { type: 'number', default: 3000, env: 'PORT' },
    host: { type: 'string', default: 'localhost', env: 'HOST' },
    nodeEnv: { type: 'string', default: 'development', env: 'NODE_ENV' },
    clientUrl: { type: 'string', default: 'http://localhost:5173', env: 'CLIENT_URL' },
    trustProxy: { type: 'boolean', default: true, env: 'TRUST_PROXY' }
  },

  // Database configuration
  database: {
    type: { type: 'string', default: 'sqlite', env: 'DB_TYPE' },
    url: { type: 'string', default: null, env: 'DATABASE_URL' },
    path: { type: 'string', default: './database.sqlite', env: 'DB_PATH' },
    host: { type: 'string', default: 'localhost', env: 'DB_HOST' },
    port: { type: 'number', default: 5432, env: 'DB_PORT' },
    name: { type: 'string', default: 'cvleap', env: 'DB_NAME' },
    username: { type: 'string', default: null, env: 'DB_USERNAME' },
    password: { type: 'string', default: null, env: 'DB_PASSWORD' },
    ssl: { type: 'boolean', default: false, env: 'DB_SSL' },
    poolMin: { type: 'number', default: 2, env: 'DB_POOL_MIN' },
    poolMax: { type: 'number', default: 10, env: 'DB_POOL_MAX' }
  },

  // Redis configuration
  redis: {
    url: { type: 'string', default: null, env: 'REDIS_URL' },
    host: { type: 'string', default: 'localhost', env: 'REDIS_HOST' },
    port: { type: 'number', default: 6379, env: 'REDIS_PORT' },
    password: { type: 'string', default: null, env: 'REDIS_PASSWORD' },
    db: { type: 'number', default: 0, env: 'REDIS_DB' },
    enabled: { type: 'boolean', default: false, env: 'REDIS_ENABLED' }
  },

  // Security configuration
  security: {
    jwtSecret: { type: 'string', default: null, env: 'JWT_SECRET', required: true },
    jwtExpiration: { type: 'string', default: '24h', env: 'JWT_EXPIRATION' },
    bcryptRounds: { type: 'number', default: 12, env: 'BCRYPT_ROUNDS' },
    sessionSecret: { type: 'string', default: null, env: 'SESSION_SECRET' },
    corsOrigin: { type: 'string', default: '*', env: 'CORS_ORIGIN' },
    enableHttps: { type: 'boolean', default: false, env: 'ENABLE_HTTPS' },
    sslKeyPath: { type: 'string', default: null, env: 'SSL_KEY_PATH' },
    sslCertPath: { type: 'string', default: null, env: 'SSL_CERT_PATH' }
  },

  // Rate limiting configuration
  rateLimit: {
    enabled: { type: 'boolean', default: true, env: 'RATE_LIMIT_ENABLED' },
    windowMs: { type: 'number', default: 900000, env: 'RATE_LIMIT_WINDOW_MS' }, // 15 minutes
    maxRequests: { type: 'number', default: 100, env: 'RATE_LIMIT_MAX_REQUESTS' },
    authWindowMs: { type: 'number', default: 900000, env: 'AUTH_RATE_LIMIT_WINDOW_MS' },
    authMaxRequests: { type: 'number', default: 5, env: 'AUTH_RATE_LIMIT_MAX_REQUESTS' },
    aiWindowMs: { type: 'number', default: 60000, env: 'AI_RATE_LIMIT_WINDOW_MS' }, // 1 minute
    aiMaxRequests: { type: 'number', default: 10, env: 'AI_RATE_LIMIT_MAX_REQUESTS' }
  },

  // AI services configuration
  ai: {
    openaiApiKey: { type: 'string', default: null, env: 'OPENAI_API_KEY' },
    anthropicApiKey: { type: 'string', default: null, env: 'ANTHROPIC_API_KEY' },
    googleApiKey: { type: 'string', default: null, env: 'GOOGLE_AI_API_KEY' },
    defaultModel: { type: 'string', default: 'gpt-4', env: 'AI_DEFAULT_MODEL' },
    maxTokens: { type: 'number', default: 2000, env: 'AI_MAX_TOKENS' },
    temperature: { type: 'number', default: 0.7, env: 'AI_TEMPERATURE' },
    timeout: { type: 'number', default: 30000, env: 'AI_TIMEOUT' }
  },

  // File upload configuration
  upload: {
    maxFileSize: { type: 'number', default: ********, env: 'UPLOAD_MAX_FILE_SIZE' }, // 10MB
    allowedTypes: { type: 'array', default: ['pdf', 'doc', 'docx', 'txt'], env: 'UPLOAD_ALLOWED_TYPES' },
    uploadDir: { type: 'string', default: './uploads', env: 'UPLOAD_DIR' },
    azureStorageAccount: { type: 'string', default: null, env: 'AZURE_STORAGE_ACCOUNT' },
    azureStorageKey: { type: 'string', default: null, env: 'AZURE_STORAGE_KEY' },
    azureContainerName: { type: 'string', default: 'uploads', env: 'AZURE_CONTAINER_NAME' }
  },

  // Email configuration
  email: {
    provider: { type: 'string', default: 'sendgrid', env: 'EMAIL_PROVIDER' },
    sendgridApiKey: { type: 'string', default: null, env: 'SENDGRID_API_KEY' },
    fromEmail: { type: 'string', default: '<EMAIL>', env: 'EMAIL_FROM' },
    fromName: { type: 'string', default: 'CVleap', env: 'EMAIL_FROM_NAME' },
    smtpHost: { type: 'string', default: null, env: 'SMTP_HOST' },
    smtpPort: { type: 'number', default: 587, env: 'SMTP_PORT' },
    smtpUser: { type: 'string', default: null, env: 'SMTP_USER' },
    smtpPassword: { type: 'string', default: null, env: 'SMTP_PASSWORD' }
  },

  // Payment configuration
  payment: {
    stripePublicKey: { type: 'string', default: null, env: 'STRIPE_PUBLIC_KEY' },
    stripeSecretKey: { type: 'string', default: null, env: 'STRIPE_SECRET_KEY' },
    stripeWebhookSecret: { type: 'string', default: null, env: 'STRIPE_WEBHOOK_SECRET' },
    currency: { type: 'string', default: 'usd', env: 'PAYMENT_CURRENCY' }
  },

  // Logging configuration
  logging: {
    level: { type: 'string', default: 'info', env: 'LOG_LEVEL' },
    enableConsole: { type: 'boolean', default: true, env: 'LOG_ENABLE_CONSOLE' },
    enableFile: { type: 'boolean', default: false, env: 'LOG_ENABLE_FILE' },
    logDir: { type: 'string', default: './logs', env: 'LOG_DIR' },
    maxFileSize: { type: 'number', default: ********, env: 'LOG_MAX_FILE_SIZE' }, // 10MB
    maxFiles: { type: 'number', default: 5, env: 'LOG_MAX_FILES' }
  },

  // Cache configuration
  cache: {
    enabled: { type: 'boolean', default: true, env: 'CACHE_ENABLED' },
    defaultTtl: { type: 'number', default: 300000, env: 'CACHE_DEFAULT_TTL' }, // 5 minutes
    maxSize: { type: 'number', default: 1000, env: 'CACHE_MAX_SIZE' },
    cleanupInterval: { type: 'number', default: 60000, env: 'CACHE_CLEANUP_INTERVAL' } // 1 minute
  },

  // Feature flags
  features: {
    enableAnalytics: { type: 'boolean', default: true, env: 'FEATURE_ANALYTICS' },
    enableNotifications: { type: 'boolean', default: true, env: 'FEATURE_NOTIFICATIONS' },
    enableFileUpload: { type: 'boolean', default: true, env: 'FEATURE_FILE_UPLOAD' },
    enablePayments: { type: 'boolean', default: false, env: 'FEATURE_PAYMENTS' },
    enableAiServices: { type: 'boolean', default: true, env: 'FEATURE_AI_SERVICES' },
    aiAssistant: { type: 'boolean', default: true, env: 'FEATURE_AI_ASSISTANT' },
    collaboration: { type: 'boolean', default: true, env: 'FEATURE_COLLABORATION' },
    automation: { type: 'boolean', default: true, env: 'FEATURE_AUTOMATION' },
    multiLanguage: { type: 'boolean', default: false, env: 'FEATURE_MULTI_LANGUAGE' },
    darkMode: { type: 'boolean', default: true, env: 'FEATURE_DARK_MODE' },
    advancedTemplates: { type: 'boolean', default: true, env: 'FEATURE_ADVANCED_TEMPLATES' },
    maintenanceMode: { type: 'boolean', default: false, env: 'MAINTENANCE_MODE' },
    debugMode: { type: 'boolean', default: false, env: 'DEBUG_MODE' }
  },

  // Monitoring configuration
  monitoring: {
    enabled: { type: 'boolean', default: true, env: 'MONITORING_ENABLED' },
    metricsPath: { type: 'string', default: '/metrics', env: 'METRICS_PATH' },
    healthPath: { type: 'string', default: '/health', env: 'HEALTH_PATH' },
    prometheusEnabled: { type: 'boolean', default: false, env: 'PROMETHEUS_ENABLED' },
    alertWebhook: { type: 'string', default: null, env: 'ALERT_WEBHOOK_URL' },
    uptimeRobotApiKey: { type: 'string', default: null, env: 'UPTIME_ROBOT_API_KEY' }
  },

  // Performance configuration
  performance: {
    enableCompression: { type: 'boolean', default: true, env: 'ENABLE_COMPRESSION' },
    enableCaching: { type: 'boolean', default: true, env: 'ENABLE_CACHING' },
    maxRequestSize: { type: 'string', default: '10mb', env: 'MAX_REQUEST_SIZE' },
    timeout: { type: 'number', default: 30000, env: 'REQUEST_TIMEOUT' },
    keepAliveTimeout: { type: 'number', default: 5000, env: 'KEEP_ALIVE_TIMEOUT' }
  }
};

/**
 * Configuration management class
 */
class ConfigManager {
  constructor() {
    this.config = {};
    this.initialized = false;
    this.watchers = new Map();
    this.fileWatchers = new Map();
    this.environment = process.env.NODE_ENV || 'development';
    this.loadConfig();
    this.setupFileWatching();
  }

  /**
   * Load configuration from environment variables and defaults
   */
  loadConfig() {
    this.config = this.buildConfigFromSchema(configSchema);
    this.applyEnvironmentSpecificConfig();
    this.validateRequiredConfig();
    this.initialized = true;
  }

  /**
   * Build configuration object from schema
   * @param {Object} schema - Configuration schema
   * @returns {Object} Built configuration
   */
  buildConfigFromSchema(schema) {
    const config = {};
    
    for (const [section, fields] of Object.entries(schema)) {
      config[section] = {};
      
      for (const [field, definition] of Object.entries(fields)) {
        const envValue = process.env[definition.env];
        let value = envValue !== undefined ? envValue : definition.default;
        
        // Type conversion
        value = this.convertType(value, definition.type);
        
        config[section][field] = value;
      }
    }
    
    return config;
  }

  /**
   * Convert value to specified type
   * @param {*} value - Value to convert
   * @param {string} type - Target type
   * @returns {*} Converted value
   */
  convertType(value, type) {
    if (value === null || value === undefined) return value;
    
    switch (type) {
      case 'number':
        return Number(value);
      case 'boolean':
        return value === 'true' || value === true;
      case 'array':
        return typeof value === 'string' ? value.split(',').map(s => s.trim()) : value;
      case 'json':
        return typeof value === 'string' ? JSON.parse(value) : value;
      default:
        return String(value);
    }
  }

  /**
   * Validate required configuration values
   */
  validateRequiredConfig() {
    const errors = [];
    
    this.traverseSchema(configSchema, (section, field, definition, value) => {
      // Only enforce required fields in production or if explicitly required
      if (definition.required && (value === null || value === undefined)) {
        if (this.environment === 'production' || definition.alwaysRequired) {
          errors.push(`Required configuration missing: ${section}.${field} (env: ${definition.env})`);
        }
      }
    });
    
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * Traverse configuration schema
   * @param {Object} schema - Schema to traverse
   * @param {Function} callback - Callback function
   */
  traverseSchema(schema, callback) {
    for (const [section, fields] of Object.entries(schema)) {
      for (const [field, definition] of Object.entries(fields)) {
        const value = this.config[section]?.[field];
        callback(section, field, definition, value);
      }
    }
  }

  /**
   * Get configuration value
   * @param {string} path - Configuration path (e.g., 'server.port')
   * @param {*} defaultValue - Default value if not found
   * @returns {*} Configuration value
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let value = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }

  /**
   * Set configuration value (runtime only)
   * @param {string} path - Configuration path
   * @param {*} value - Value to set
   */
  set(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = this.config;
    
    for (const key of keys) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {};
      }
      target = target[key];
    }
    
    const oldValue = target[lastKey];
    target[lastKey] = value;
    
    // Notify watchers
    this.notifyWatchers(path, value, oldValue);
  }

  /**
   * Check if feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} True if enabled
   */
  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`, false);
  }

  /**
   * Check if application is in development mode
   * @returns {boolean} True if in development
   */
  isDevelopment() {
    return this.get('server.nodeEnv') === 'development';
  }

  /**
   * Check if application is in production mode
   * @returns {boolean} True if in production
   */
  isProduction() {
    return this.get('server.nodeEnv') === 'production';
  }

  /**
   * Check if maintenance mode is enabled
   * @returns {boolean} True if in maintenance mode
   */
  isMaintenanceMode() {
    return this.get('features.maintenanceMode', false);
  }

  /**
   * Get database configuration for specific type
   * @returns {Object} Database configuration
   */
  getDatabaseConfig() {
    const dbType = this.get('database.type');
    const config = { ...this.config.database };
    
    // Return appropriate config based on type
    if (dbType === 'postgres' && config.url) {
      return { url: config.url, ssl: config.ssl };
    }
    
    return config;
  }

  /**
   * Get AI service configuration
   * @param {string} provider - AI provider name
   * @returns {Object} AI configuration
   */
  getAiConfig(provider = null) {
    const aiConfig = { ...this.config.ai };
    
    if (provider) {
      const providerKey = `${provider}ApiKey`;
      return {
        apiKey: aiConfig[providerKey],
        maxTokens: aiConfig.maxTokens,
        temperature: aiConfig.temperature,
        timeout: aiConfig.timeout
      };
    }
    
    return aiConfig;
  }

  /**
   * Watch for configuration changes
   * @param {string} path - Configuration path to watch
   * @param {Function} callback - Callback function
   * @returns {Function} Unwatch function
   */
  watch(path, callback) {
    if (!this.watchers.has(path)) {
      this.watchers.set(path, new Set());
    }
    
    this.watchers.get(path).add(callback);
    
    // Return unwatch function
    return () => {
      const pathWatchers = this.watchers.get(path);
      if (pathWatchers) {
        pathWatchers.delete(callback);
        if (pathWatchers.size === 0) {
          this.watchers.delete(path);
        }
      }
    };
  }

  /**
   * Notify watchers of configuration changes
   * @param {string} path - Configuration path
   * @param {*} newValue - New value
   * @param {*} oldValue - Old value
   */
  notifyWatchers(path, newValue, oldValue) {
    const watchers = this.watchers.get(path);
    if (watchers) {
      watchers.forEach(callback => {
        try {
          callback(newValue, oldValue, path);
        } catch (error) {
          console.error(`Error in config watcher for ${path}:`, error);
        }
      });
    }
  }

  /**
   * Load configuration from file
   * @param {string} filePath - Path to configuration file
   */
  loadFromFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        const fileConfig = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        this.mergeConfig(fileConfig);
      }
    } catch (error) {
      console.warn(`Failed to load configuration from ${filePath}:`, error.message);
    }
  }

  /**
   * Merge configuration object with current config
   * @param {Object} newConfig - Configuration to merge
   */
  mergeConfig(newConfig) {
    const deepMerge = (target, source) => {
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key]) target[key] = {};
          deepMerge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    };
    
    deepMerge(this.config, newConfig);
  }

  /**
   * Export current configuration (sanitized)
   * @param {boolean} includeSecrets - Whether to include sensitive data
   * @returns {Object} Configuration object
   */
  exportConfig(includeSecrets = false) {
    const sensitiveKeys = ['password', 'secret', 'key', 'token'];
    
    const sanitize = (obj) => {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null) {
          result[key] = sanitize(value);
        } else if (!includeSecrets && sensitiveKeys.some(s => key.toLowerCase().includes(s))) {
          result[key] = value ? '[HIDDEN]' : null;
        } else {
          result[key] = value;
        }
      }
      return result;
    };
    
    return sanitize(this.config);
  }

  /**
   * Validate configuration against schema
   * @returns {Array} Array of validation errors
   */
  validate() {
    const errors = [];
    
    this.traverseSchema(configSchema, (section, field, definition, value) => {
      // Check required fields
      if (definition.required && (value === null || value === undefined)) {
        errors.push(`Required field missing: ${section}.${field}`);
      }
      
      // Check types
      if (value !== null && value !== undefined) {
        const expectedType = definition.type;
        const actualType = Array.isArray(value) ? 'array' : typeof value;
        
        if (expectedType !== actualType) {
          errors.push(`Type mismatch for ${section}.${field}: expected ${expectedType}, got ${actualType}`);
        }
      }
      
      // Check custom validation if present
      if (definition.validate && typeof definition.validate === 'function') {
        try {
          definition.validate(value);
        } catch (error) {
          errors.push(`Validation failed for ${section}.${field}: ${error.message}`);
        }
      }
    });
    
    return errors;
  }

  /**
   * Apply environment-specific configuration overrides
   */
  applyEnvironmentSpecificConfig() {
    const envOverrides = this.getEnvironmentSpecificConfig();
    this.mergeConfig(envOverrides);
  }

  /**
   * Get environment-specific configuration overrides
   * @returns {Object} Environment-specific configuration
   */
  getEnvironmentSpecificConfig() {
    switch (this.environment) {
      case 'production':
        return {
          logging: {
            level: 'warn',
            enableConsole: false,
            enableFile: true
          },
          security: {
            corsOrigin: process.env.CORS_ORIGINS?.split(',') || false
          },
          cache: {
            enabled: true,
            defaultTtl: 600000 // 10 minutes in production
          }
        };
      
      case 'staging':
        return {
          logging: {
            level: 'info',
            enableConsole: true,
            enableFile: true
          },
          cache: {
            defaultTtl: 300000 // 5 minutes in staging
          }
        };
      
      case 'test':
        return {
          database: {
            type: 'sqlite',
            path: ':memory:'
          },
          logging: {
            level: 'error',
            enableConsole: false,
            enableFile: false
          },
          cache: {
            enabled: false
          }
        };
      
      default: // development
        return {
          logging: {
            level: 'debug',
            enableConsole: true,
            enableFile: false
          },
          security: {
            corsOrigin: ['http://localhost:3001', 'http://localhost:5173']
          }
        };
    }
  }

  /**
   * Setup file watching for configuration changes
   */
  setupFileWatching() {
    const configFiles = [
      path.join(process.cwd(), '.env'),
      path.join(process.cwd(), '.env.local'),
      path.join(process.cwd(), `config/${this.environment}.json`)
    ];

    configFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        try {
          const watcher = fs.watch(filePath, (eventType) => {
            if (eventType === 'change') {
              this.handleFileChange(filePath);
            }
          });
          this.fileWatchers.set(filePath, watcher);
        } catch (error) {
          console.warn(`Failed to watch configuration file ${filePath}:`, error.message);
        }
      }
    });
  }

  /**
   * Handle configuration file changes
   * @param {string} filePath - Path to changed file
   */
  handleFileChange(filePath) {
    try {
      // Debounce file changes (wait 100ms for multiple rapid changes)
      if (this.fileChangeTimeout) {
        clearTimeout(this.fileChangeTimeout);
      }

      this.fileChangeTimeout = setTimeout(() => {
        console.log(`Configuration file changed: ${filePath}`);
        
        if (filePath.endsWith('.json')) {
          this.reloadFromFile(filePath);
        } else if (filePath.endsWith('.env') || filePath.endsWith('.env.local')) {
          // For .env files, we'd need to restart the process or use a different approach
          // since process.env is read-only after initial load
          console.warn('Environment file changed. Application restart may be required.');
        }
      }, 100);
    } catch (error) {
      console.error(`Error handling file change for ${filePath}:`, error);
    }
  }

  /**
   * Reload configuration from a specific file
   * @param {string} filePath - Path to configuration file
   */
  reloadFromFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        const fileConfig = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const oldConfig = JSON.parse(JSON.stringify(this.config));
        
        this.mergeConfig(fileConfig);
        this.notifyConfigChanges(oldConfig, this.config);
      }
    } catch (error) {
      console.error(`Failed to reload configuration from ${filePath}:`, error.message);
    }
  }

  /**
   * Notify watchers of configuration changes after comparing old and new config
   * @param {Object} oldConfig - Previous configuration
   * @param {Object} newConfig - New configuration
   */
  notifyConfigChanges(oldConfig, newConfig) {
    const notifyPath = (path, oldVal, newVal) => {
      if (oldVal !== newVal) {
        this.notifyWatchers(path, newVal, oldVal);
      }
    };

    const traverse = (obj1, obj2, currentPath = '') => {
      const allKeys = new Set([...Object.keys(obj1 || {}), ...Object.keys(obj2 || {})]);
      
      for (const key of allKeys) {
        const newPath = currentPath ? `${currentPath}.${key}` : key;
        const oldVal = obj1?.[key];
        const newVal = obj2?.[key];
        
        if (typeof oldVal === 'object' && typeof newVal === 'object' && 
            oldVal !== null && newVal !== null && 
            !Array.isArray(oldVal) && !Array.isArray(newVal)) {
          traverse(oldVal, newVal, newPath);
        } else {
          notifyPath(newPath, oldVal, newVal);
        }
      }
    };

    traverse(oldConfig, newConfig);
  }

  /**
   * Get Redis configuration
   * @returns {Object} Redis configuration
   */
  getRedisConfig() {
    return this.config.redis;
  }

  /**
   * Get security configuration
   * @returns {Object} Security configuration
   */
  getSecurityConfig() {
    return this.config.security;
  }

  /**
   * Get monitoring configuration
   * @returns {Object} Monitoring configuration
   */
  getMonitoringConfig() {
    return this.config.monitoring;
  }

  /**
   * Get performance configuration
   * @returns {Object} Performance configuration
   */
  getPerformanceConfig() {
    return this.config.performance;
  }

  /**
   * Check if environment is test
   * @returns {boolean} True if in test environment
   */
  isTest() {
    return this.environment === 'test';
  }

  /**
   * Check if environment is staging
   * @returns {boolean} True if in staging environment
   */
  isStaging() {
    return this.environment === 'staging';
  }

  /**
   * Get current environment
   * @returns {string} Current environment name
   */
  getEnvironment() {
    return this.environment;
  }

  /**
   * Cleanup watchers and resources
   */
  cleanup() {
    // Close file watchers
    for (const [filePath, watcher] of this.fileWatchers) {
      try {
        watcher.close();
      } catch (error) {
        console.warn(`Failed to close file watcher for ${filePath}:`, error.message);
      }
    }
    this.fileWatchers.clear();

    // Clear timeout
    if (this.fileChangeTimeout) {
      clearTimeout(this.fileChangeTimeout);
    }

    // Clear all watchers
    this.watchers.clear();
  }
}

// Create singleton instance
const configManager = new ConfigManager();

module.exports = {
  ConfigManager,
  config: configManager,
  configSchema
};