const path = require('path');
const fs = require('fs');

/**
 * Production-grade configuration management with environment-specific settings
 */
class ConfigManager {
  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.config = this.loadConfiguration();
    this.secrets = this.loadSecrets();
    
    // Validate required configurations
    this.validateConfiguration();
  }

  /**
   * Load configuration from environment and files
   */
  loadConfiguration() {
    const config = {
      // Application settings
      app: {
        name: process.env.APP_NAME || 'cvleap',
        version: process.env.APP_VERSION || '1.0.0',
        environment: this.environment,
        port: parseInt(process.env.PORT) || 3000,
        host: process.env.HOST || '0.0.0.0',
        clientUrl: process.env.CLIENT_URL || 'http://localhost:3001'
      },

      // Database configuration
      database: {
        url: process.env.DATABASE_URL,
        type: process.env.DB_TYPE || 'sqlite',
        sqlite: {
          path: process.env.DB_PATH || path.join(__dirname, '..', 'database.sqlite')
        },
        postgresql: {
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT) || 5432,
          database: process.env.DB_NAME || 'cvleap',
          username: process.env.DB_USER || 'cvleap_user',
          password: process.env.DB_PASSWORD || process.env.POSTGRES_PASSWORD,
          ssl: process.env.DB_SSL === 'true',
          pool: {
            min: parseInt(process.env.DB_POOL_MIN) || 2,
            max: parseInt(process.env.DB_POOL_MAX) || 10,
            acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 30000,
            idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 600000
          }
        }
      },

      // Redis configuration
      redis: {
        url: process.env.REDIS_URL,
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        ttl: parseInt(process.env.CACHE_TTL) || 3600,
        maxConnections: parseInt(process.env.REDIS_MAX_CONNECTIONS) || 10
      },

      // Security configuration
      security: {
        jwt: {
          secret: process.env.JWT_SECRET,
          refreshSecret: process.env.JWT_REFRESH_SECRET,
          accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
          refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || '7d',
          issuer: process.env.JWT_ISSUER || 'cvleap-server',
          audience: process.env.JWT_AUDIENCE || 'cvleap-client'
        },
        encryption: {
          masterKey: process.env.ENCRYPTION_MASTER_KEY,
          algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm'
        },
        cors: {
          origin: this.parseCorsOrigins(),
          credentials: process.env.CORS_CREDENTIALS === 'true',
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        },
        rateLimit: {
          windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15 minutes
          max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
          skipSuccessfulRequests: false,
          skipFailedRequests: false
        },
        session: {
          maxAge: parseInt(process.env.SESSION_MAX_AGE) || 86400000, // 24 hours
          cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL) || 3600000 // 1 hour
        }
      },

      // AI service configuration
      ai: {
        openai: {
          apiKey: process.env.OPENAI_API_KEY,
          model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
          maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 1000,
          temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7
        },
        anthropic: {
          apiKey: process.env.ANTHROPIC_API_KEY,
          model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
          maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS) || 1000
        },
        google: {
          apiKey: process.env.GOOGLE_AI_API_KEY,
          model: process.env.GOOGLE_AI_MODEL || 'gemini-pro'
        },
        rateLimit: {
          windowMs: parseInt(process.env.AI_RATE_LIMIT_WINDOW) || 60000, // 1 minute
          max: parseInt(process.env.AI_RATE_LIMIT_MAX) || 10
        }
      },

      // File upload configuration
      upload: {
        maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10 * 1024 * 1024, // 10MB
        allowedTypes: this.parseUploadTypes(),
        directory: process.env.UPLOAD_DIR || path.join(__dirname, '..', 'uploads'),
        azure: {
          accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME,
          accountKey: process.env.AZURE_STORAGE_ACCOUNT_KEY,
          containerName: process.env.AZURE_CONTAINER_NAME || 'cvleap-uploads'
        }
      },

      // Email configuration
      email: {
        provider: process.env.EMAIL_PROVIDER || 'sendgrid',
        sendgrid: {
          apiKey: process.env.SENDGRID_API_KEY,
          fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
          fromName: process.env.SENDGRID_FROM_NAME || 'CVleap'
        },
        smtp: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT) || 587,
          secure: process.env.SMTP_SECURE === 'true',
          user: process.env.SMTP_USER,
          password: process.env.SMTP_PASSWORD
        }
      },

      // Payment configuration
      payment: {
        stripe: {
          secretKey: process.env.STRIPE_SECRET_KEY,
          publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
          webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
          currency: process.env.STRIPE_CURRENCY || 'usd'
        }
      },

      // Logging configuration
      logging: {
        level: process.env.LOG_LEVEL || (this.environment === 'production' ? 'info' : 'debug'),
        enableConsole: process.env.LOG_CONSOLE !== 'false',
        enableFile: process.env.LOG_FILE === 'true',
        enableAudit: process.env.LOG_AUDIT !== 'false',
        directory: process.env.LOG_DIR || path.join(__dirname, '..', 'logs'),
        maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE) || 50 * 1024 * 1024, // 50MB
        maxFiles: parseInt(process.env.LOG_MAX_FILES) || 10,
        auditWebhook: process.env.AUDIT_WEBHOOK_URL
      },

      // Monitoring configuration
      monitoring: {
        enabled: process.env.MONITORING_ENABLED !== 'false',
        metricsPath: process.env.METRICS_PATH || '/metrics',
        healthPath: process.env.HEALTH_PATH || '/health',
        prometheusEnabled: process.env.PROMETHEUS_ENABLED === 'true',
        alertWebhook: process.env.ALERT_WEBHOOK_URL,
        uptimeRobotApiKey: process.env.UPTIME_ROBOT_API_KEY
      },

      // Feature flags
      features: {
        aiAssistant: process.env.FEATURE_AI_ASSISTANT !== 'false',
        collaboration: process.env.FEATURE_COLLABORATION !== 'false',
        automation: process.env.FEATURE_AUTOMATION !== 'false',
        analytics: process.env.FEATURE_ANALYTICS !== 'false',
        payments: process.env.FEATURE_PAYMENTS === 'true',
        multiLanguage: process.env.FEATURE_MULTI_LANGUAGE === 'true',
        darkMode: process.env.FEATURE_DARK_MODE !== 'false',
        advancedTemplates: process.env.FEATURE_ADVANCED_TEMPLATES !== 'false'
      },

      // Performance configuration
      performance: {
        enableCompression: process.env.ENABLE_COMPRESSION !== 'false',
        enableCaching: process.env.ENABLE_CACHING !== 'false',
        maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
        timeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000,
        keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT) || 5000
      },

      // Environment-specific overrides
      ...this.getEnvironmentSpecificConfig()
    };

    return config;
  }

  /**
   * Load sensitive secrets separately
   */
  loadSecrets() {
    // In production, these should come from secure secret management
    // like Kubernetes secrets, HashiCorp Vault, AWS Secrets Manager, etc.
    return {
      jwtSecret: process.env.JWT_SECRET,
      jwtRefreshSecret: process.env.JWT_REFRESH_SECRET,
      encryptionMasterKey: process.env.ENCRYPTION_MASTER_KEY,
      databasePassword: process.env.DB_PASSWORD || process.env.POSTGRES_PASSWORD,
      redisPassword: process.env.REDIS_PASSWORD,
      openaiApiKey: process.env.OPENAI_API_KEY,
      anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      googleAiApiKey: process.env.GOOGLE_AI_API_KEY,
      sendgridApiKey: process.env.SENDGRID_API_KEY,
      stripeSecretKey: process.env.STRIPE_SECRET_KEY,
      azureStorageKey: process.env.AZURE_STORAGE_ACCOUNT_KEY
    };
  }

  /**
   * Parse CORS origins from environment variable
   */
  parseCorsOrigins() {
    const origins = process.env.CORS_ORIGINS;
    if (!origins) {
      return this.environment === 'production' ? false : ['http://localhost:3001'];
    }
    return origins.split(',').map(origin => origin.trim());
  }

  /**
   * Parse allowed upload file types
   */
  parseUploadTypes() {
    const types = process.env.UPLOAD_ALLOWED_TYPES;
    if (!types) {
      return ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
    }
    return types.split(',').map(type => type.trim());
  }

  /**
   * Get environment-specific configuration overrides
   */
  getEnvironmentSpecificConfig() {
    switch (this.environment) {
      case 'production':
        return {
          logging: {
            level: 'warn',
            enableConsole: false,
            enableFile: true,
            enableAudit: true
          },
          security: {
            rateLimit: {
              max: 1000 // Higher limit for production
            }
          }
        };
      
      case 'staging':
        return {
          logging: {
            level: 'info',
            enableConsole: true,
            enableFile: true
          }
        };
      
      case 'test':
        return {
          database: {
            type: 'sqlite',
            sqlite: {
              path: ':memory:'
            }
          },
          logging: {
            level: 'error',
            enableConsole: false,
            enableFile: false,
            enableAudit: false
          }
        };
      
      default: // development
        return {
          logging: {
            level: 'debug',
            enableConsole: true,
            enableFile: false
          },
          security: {
            rateLimit: {
              max: 1000 // Relaxed for development
            }
          }
        };
    }
  }

  /**
   * Validate required configuration
   */
  validateConfiguration() {
    const required = [];

    // Check critical secrets for production
    if (this.environment === 'production') {
      if (!this.secrets.jwtSecret) required.push('JWT_SECRET');
      if (!this.secrets.encryptionMasterKey) required.push('ENCRYPTION_MASTER_KEY');
      
      // Check database configuration
      if (!this.config.database.url && this.config.database.type === 'postgresql') {
        if (!this.secrets.databasePassword) required.push('DB_PASSWORD or POSTGRES_PASSWORD');
      }
    }

    if (required.length > 0) {
      throw new Error(`Missing required environment variables: ${required.join(', ')}`);
    }
  }

  /**
   * Get configuration value by path
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let value = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }

  /**
   * Check if feature is enabled
   */
  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`, false);
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig() {
    return this.config.database;
  }

  /**
   * Get Redis configuration
   */
  getRedisConfig() {
    return this.config.redis;
  }

  /**
   * Get security configuration
   */
  getSecurityConfig() {
    return this.config.security;
  }

  /**
   * Export configuration for external tools
   */
  exportConfig() {
    // Return config without sensitive secrets
    const exportedConfig = JSON.parse(JSON.stringify(this.config));
    
    // Remove sensitive information
    if (exportedConfig.security?.jwt) {
      exportedConfig.security.jwt.secret = '[REDACTED]';
      exportedConfig.security.jwt.refreshSecret = '[REDACTED]';
    }
    
    return exportedConfig;
  }

  /**
   * Generate sample environment file
   */
  generateSampleEnv() {
    return `# CVleap Environment Configuration
# Copy this file to .env and fill in your values

# Application
NODE_ENV=development
APP_NAME=cvleap
APP_VERSION=1.0.0
PORT=3000
HOST=0.0.0.0
CLIENT_URL=http://localhost:3001

# Database
DATABASE_URL=postgresql://cvleap_user:password@localhost:5432/cvleap
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cvleap
DB_USER=cvleap_user
DB_PASSWORD=your_db_password
POSTGRES_PASSWORD=your_db_password

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_TTL=3600

# Security
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key
ENCRYPTION_MASTER_KEY=your_base64_encoded_encryption_key

# CORS
CORS_ORIGINS=http://localhost:3001,https://your-domain.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# AI Services
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key

# File Upload
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# Azure Storage (optional)
AZURE_STORAGE_ACCOUNT_NAME=your_storage_account
AZURE_STORAGE_ACCOUNT_KEY=your_storage_key

# Email
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Payment (optional)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Logging
LOG_LEVEL=info
LOG_CONSOLE=true
LOG_FILE=false
LOG_AUDIT=true

# Monitoring
MONITORING_ENABLED=true
PROMETHEUS_ENABLED=false

# Feature Flags
FEATURE_AI_ASSISTANT=true
FEATURE_COLLABORATION=true
FEATURE_AUTOMATION=true
FEATURE_ANALYTICS=true
FEATURE_PAYMENTS=false
`;
  }
}

// Create singleton instance
const configManager = new ConfigManager();

module.exports = configManager;