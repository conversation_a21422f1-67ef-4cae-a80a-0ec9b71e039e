const { Pool } = require('pg');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const configManager = require('../utils/configManager');
const metricsCollector = require('../utils/metricsCollector');
const auditLogger = require('../utils/auditLogger');

/**
 * Enhanced Database Manager with connection pooling and performance optimization
 */
class DatabaseManager {
  constructor() {
    this.config = configManager.getDatabaseConfig();
    this.pool = null;
    this.sqliteDb = null;
    this.connectionType = this.config.type || 'sqlite';
    this.queryCache = new Map();
    this.cacheMaxAge = 5 * 60 * 1000; // 5 minutes
    
    this.initializeConnection();
    this.setupMetrics();
  }

  /**
   * Initialize database connection based on configuration
   */
  initializeConnection() {
    if (this.connectionType === 'postgresql' && this.config.url) {
      this.initializePostgreSQL();
    } else {
      this.initializeSQLite();
    }
  }

  /**
   * Initialize PostgreSQL connection pool
   */
  initializePostgreSQL() {
    try {
      const poolConfig = {
        connectionString: this.config.url,
        min: this.config.postgresql.pool.min || 2,
        max: this.config.postgresql.pool.max || 10,
        acquireTimeoutMillis: this.config.postgresql.pool.acquireTimeoutMillis || 30000,
        idleTimeoutMillis: this.config.postgresql.pool.idleTimeoutMillis || 600000,
        connectionTimeoutMillis: 5000,
        statement_timeout: 30000,
        query_timeout: 30000,
        application_name: 'cvleap-server'
      };

      this.pool = new Pool(poolConfig);

      // Handle pool events
      this.pool.on('connect', (client) => {
        console.log('New PostgreSQL client connected');
        metricsCollector.incrementCounter('db_connections_total', { type: 'postgresql', event: 'connect' });
      });

      this.pool.on('remove', (client) => {
        console.log('PostgreSQL client removed');
        metricsCollector.incrementCounter('db_connections_total', { type: 'postgresql', event: 'remove' });
      });

      this.pool.on('error', (err, client) => {
        console.error('PostgreSQL pool error:', err);
        auditLogger.logSecurity('DATABASE_ERROR', null, 'high', { error: err.message });
        metricsCollector.incrementCounter('db_errors_total', { type: 'postgresql' });
      });

      console.log('PostgreSQL connection pool initialized');
      auditLogger.logAdmin('DATABASE_INITIALIZED', 'system', 'postgresql_pool', {
        minConnections: poolConfig.min,
        maxConnections: poolConfig.max
      });

    } catch (error) {
      console.error('Failed to initialize PostgreSQL pool:', error);
      throw error;
    }
  }

  /**
   * Initialize SQLite connection
   */
  initializeSQLite() {
    try {
      const dbPath = this.config.sqlite.path || path.join(__dirname, '..', 'database.sqlite');
      
      this.sqliteDb = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('SQLite connection failed:', err);
          throw err;
        } else {
          console.log('SQLite database connected');
          this.initializeSQLiteTables();
        }
      });

      // Configure SQLite for better performance
      this.sqliteDb.configure('busyTimeout', 30000);
      this.sqliteDb.run('PRAGMA journal_mode = WAL');
      this.sqliteDb.run('PRAGMA synchronous = NORMAL');
      this.sqliteDb.run('PRAGMA cache_size = 1000');
      this.sqliteDb.run('PRAGMA temp_store = MEMORY');
      this.sqliteDb.run('PRAGMA mmap_size = 268435456'); // 256MB

      auditLogger.logAdmin('DATABASE_INITIALIZED', 'system', 'sqlite', { path: dbPath });

    } catch (error) {
      console.error('Failed to initialize SQLite:', error);
      throw error;
    }
  }

  /**
   * Setup database metrics collection
   */
  setupMetrics() {
    // Collect pool metrics every 30 seconds
    setInterval(() => {
      if (this.pool) {
        metricsCollector.setGauge('db_pool_total_connections', this.pool.totalCount, { type: 'postgresql' });
        metricsCollector.setGauge('db_pool_idle_connections', this.pool.idleCount, { type: 'postgresql' });
        metricsCollector.setGauge('db_pool_waiting_requests', this.pool.waitingCount, { type: 'postgresql' });
      }
    }, 30000);
  }

  /**
   * Execute query with connection pooling and metrics
   */
  async query(sql, params = [], options = {}) {
    const startTime = Date.now();
    const queryType = this.getQueryType(sql);
    
    try {
      // Check cache for SELECT queries
      if (queryType === 'SELECT' && options.cache !== false) {
        const cached = this.getCachedQuery(sql, params);
        if (cached) {
          metricsCollector.incrementCounter('db_cache_hits_total', { type: this.connectionType });
          return cached;
        }
      }

      let result;
      
      if (this.connectionType === 'postgresql' && this.pool) {
        result = await this.executePostgreSQLQuery(sql, params);
      } else if (this.sqliteDb) {
        result = await this.executeSQLiteQuery(sql, params, queryType);
      } else {
        throw new Error('No database connection available');
      }

      // Cache SELECT results
      if (queryType === 'SELECT' && options.cache !== false) {
        this.setCachedQuery(sql, params, result);
        metricsCollector.incrementCounter('db_cache_misses_total', { type: this.connectionType });
      }

      // Record metrics
      const duration = Date.now() - startTime;
      metricsCollector.incrementCounter('db_queries_total', { 
        type: this.connectionType, 
        operation: queryType.toLowerCase() 
      });
      metricsCollector.recordHistogram('db_query_duration_ms', duration, { 
        type: this.connectionType, 
        operation: queryType.toLowerCase() 
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Record error metrics
      metricsCollector.incrementCounter('db_errors_total', { 
        type: this.connectionType, 
        operation: queryType.toLowerCase() 
      });
      metricsCollector.recordHistogram('db_query_duration_ms', duration, { 
        type: this.connectionType, 
        operation: queryType.toLowerCase(),
        status: 'error'
      });

      // Log security-relevant errors
      if (error.message.includes('permission') || error.message.includes('access')) {
        auditLogger.logSecurity('DATABASE_ACCESS_ERROR', null, 'medium', {
          query: sql.substring(0, 100),
          error: error.message
        });
      }

      throw error;
    }
  }

  /**
   * Execute PostgreSQL query
   */
  async executePostgreSQLQuery(sql, params) {
    const client = await this.pool.connect();
    
    try {
      const result = await client.query(sql, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount,
        command: result.command
      };
    } finally {
      client.release();
    }
  }

  /**
   * Execute SQLite query
   */
  async executeSQLiteQuery(sql, params, queryType) {
    return new Promise((resolve, reject) => {
      if (queryType === 'SELECT') {
        this.sqliteDb.all(sql, params, (err, rows) => {
          if (err) reject(err);
          else resolve({ rows, rowCount: rows.length });
        });
      } else {
        this.sqliteDb.run(sql, params, function(err) {
          if (err) reject(err);
          else resolve({ 
            rowCount: this.changes, 
            lastInsertId: this.lastID 
          });
        });
      }
    });
  }

  /**
   * Get query type from SQL
   */
  getQueryType(sql) {
    const trimmed = sql.trim().toUpperCase();
    if (trimmed.startsWith('SELECT')) return 'SELECT';
    if (trimmed.startsWith('INSERT')) return 'INSERT';
    if (trimmed.startsWith('UPDATE')) return 'UPDATE';
    if (trimmed.startsWith('DELETE')) return 'DELETE';
    if (trimmed.startsWith('CREATE')) return 'CREATE';
    if (trimmed.startsWith('DROP')) return 'DROP';
    if (trimmed.startsWith('ALTER')) return 'ALTER';
    return 'OTHER';
  }

  /**
   * Get cached query result
   */
  getCachedQuery(sql, params) {
    const key = this.getCacheKey(sql, params);
    const cached = this.queryCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheMaxAge) {
      return cached.result;
    }
    
    if (cached) {
      this.queryCache.delete(key);
    }
    
    return null;
  }

  /**
   * Cache query result
   */
  setCachedQuery(sql, params, result) {
    const key = this.getCacheKey(sql, params);
    this.queryCache.set(key, {
      result: JSON.parse(JSON.stringify(result)), // Deep clone
      timestamp: Date.now()
    });

    // Limit cache size
    if (this.queryCache.size > 1000) {
      const oldestKey = this.queryCache.keys().next().value;
      this.queryCache.delete(oldestKey);
    }
  }

  /**
   * Generate cache key
   */
  getCacheKey(sql, params) {
    return `${sql}|${JSON.stringify(params)}`;
  }

  /**
   * Clear query cache
   */
  clearCache() {
    this.queryCache.clear();
    metricsCollector.incrementCounter('db_cache_cleared_total', { type: this.connectionType });
  }

  /**
   * Transaction wrapper
   */
  async transaction(callback) {
    if (this.connectionType === 'postgresql' && this.pool) {
      return await this.postgresTransaction(callback);
    } else if (this.sqliteDb) {
      return await this.sqliteTransaction(callback);
    } else {
      throw new Error('No database connection available');
    }
  }

  /**
   * PostgreSQL transaction
   */
  async postgresTransaction(callback) {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const transactionDb = {
        query: async (sql, params) => {
          const result = await client.query(sql, params);
          return {
            rows: result.rows,
            rowCount: result.rowCount,
            command: result.command
          };
        }
      };
      
      const result = await callback(transactionDb);
      await client.query('COMMIT');
      
      return result;
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * SQLite transaction
   */
  async sqliteTransaction(callback) {
    return new Promise((resolve, reject) => {
      this.sqliteDb.serialize(() => {
        this.sqliteDb.run('BEGIN TRANSACTION');
        
        const transactionDb = {
          query: async (sql, params) => {
            return this.executeSQLiteQuery(sql, params, this.getQueryType(sql));
          }
        };
        
        callback(transactionDb)
          .then(result => {
            this.sqliteDb.run('COMMIT', (err) => {
              if (err) reject(err);
              else resolve(result);
            });
          })
          .catch(error => {
            this.sqliteDb.run('ROLLBACK', () => {
              reject(error);
            });
          });
      });
    });
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      await this.query('SELECT 1 as health_check');
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        type: this.connectionType,
        latency,
        connectionCount: this.pool ? this.pool.totalCount : 1,
        idleConnections: this.pool ? this.pool.idleCount : 0
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        type: this.connectionType,
        error: error.message
      };
    }
  }

  /**
   * Get connection statistics
   */
  getStats() {
    const stats = {
      type: this.connectionType,
      cacheSize: this.queryCache.size,
      cacheMaxAge: this.cacheMaxAge
    };

    if (this.pool) {
      stats.pool = {
        totalConnections: this.pool.totalCount,
        idleConnections: this.pool.idleCount,
        waitingRequests: this.pool.waitingCount,
        maxConnections: this.pool.options.max,
        minConnections: this.pool.options.min
      };
    }

    return stats;
  }

  /**
   * Initialize SQLite tables (fallback from original database.js)
   */
  initializeSQLiteTables() {
    // This would include all the table creation logic from the original database.js
    // For brevity, including just the essential structure
    const tables = [
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS resumes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        content TEXT,
        template TEXT DEFAULT 'modern',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`
    ];

    tables.forEach(sql => {
      this.sqliteDb.run(sql, (err) => {
        if (err) {
          console.error('Error creating table:', err);
        }
      });
    });
  }

  /**
   * Close all connections
   */
  async close() {
    try {
      if (this.pool) {
        await this.pool.end();
        console.log('PostgreSQL pool closed');
      }
      
      if (this.sqliteDb) {
        this.sqliteDb.close((err) => {
          if (err) {
            console.error('Error closing SQLite:', err);
          } else {
            console.log('SQLite database closed');
          }
        });
      }
      
      this.clearCache();
      
    } catch (error) {
      console.error('Error closing database connections:', error);
    }
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

// Export both the manager and a legacy-compatible interface
module.exports = databaseManager;

// Legacy compatibility
module.exports.get = () => databaseManager.sqliteDb;
module.exports.query = (sql, params) => databaseManager.query(sql, params);
module.exports.transaction = (callback) => databaseManager.transaction(callback);