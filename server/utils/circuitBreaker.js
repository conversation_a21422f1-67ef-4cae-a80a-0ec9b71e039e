const { logger } = require('./logger');
const { monitoring } = require('./monitoring');

/**
 * Circuit Breaker Pattern Implementation
 * Provides fault tolerance for external API calls
 */

class CircuitBreaker {
  constructor(name, options = {}) {
    this.name = name;
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 60000; // 1 minute
    this.expectedErrors = options.expectedErrors || [];
    
    // Circuit states
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
    
    // Statistics
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      timeouts: 0,
      circuitOpenCount: 0,
      lastReset: Date.now()
    };
    
    // Monitoring
    this.setupMonitoring();
    
    logger.info('Circuit breaker initialized', {
      name: this.name,
      failureThreshold: this.failureThreshold,
      recoveryTimeout: this.recoveryTimeout
    });
  }

  /**
   * Execute a function with circuit breaker protection
   * @param {Function} fn - Function to execute
   * @param {...any} args - Arguments to pass to the function
   * @returns {Promise} Function result or circuit breaker error
   */
  async execute(fn, ...args) {
    this.stats.totalRequests++;
    
    // Check circuit state
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttemptTime) {
        const error = new Error(`Circuit breaker is OPEN for ${this.name}`);
        error.code = 'CIRCUIT_BREAKER_OPEN';
        error.nextAttemptTime = this.nextAttemptTime;
        
        logger.warn('Circuit breaker blocked request', {
          name: this.name,
          state: this.state,
          nextAttemptTime: this.nextAttemptTime
        });
        
        throw error;
      } else {
        // Transition to HALF_OPEN for testing
        this.state = 'HALF_OPEN';
        logger.info('Circuit breaker transitioning to HALF_OPEN', {
          name: this.name
        });
      }
    }
    
    try {
      const startTime = Date.now();
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      
      // Success - reset failure count
      this.onSuccess(duration);
      
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  /**
   * Handle successful execution
   * @param {number} duration - Execution duration
   */
  onSuccess(duration) {
    this.stats.successfulRequests++;
    this.failureCount = 0;
    
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      logger.info('Circuit breaker closed after successful test', {
        name: this.name,
        duration
      });
    }
    
    // Log performance metric
    logger.logPerformance(`Circuit breaker success: ${this.name}`, duration, {
      circuitBreaker: this.name,
      state: this.state
    });
  }

  /**
   * Handle failed execution
   * @param {Error} error - Error that occurred
   */
  onFailure(error) {
    this.stats.failedRequests++;
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    // Check if error should be ignored
    if (this.shouldIgnoreError(error)) {
      logger.debug('Circuit breaker ignoring expected error', {
        name: this.name,
        error: error.message,
        code: error.code
      });
      return;
    }
    
    // Check if we should open the circuit
    if (this.failureCount >= this.failureThreshold) {
      this.openCircuit();
    }
    
    logger.warn('Circuit breaker recorded failure', {
      name: this.name,
      failureCount: this.failureCount,
      threshold: this.failureThreshold,
      state: this.state,
      error: error.message
    });
  }

  /**
   * Open the circuit breaker
   */
  openCircuit() {
    this.state = 'OPEN';
    this.nextAttemptTime = Date.now() + this.recoveryTimeout;
    this.stats.circuitOpenCount++;
    
    logger.error('Circuit breaker opened', {
      name: this.name,
      failureCount: this.failureCount,
      nextAttemptTime: this.nextAttemptTime
    });
    
    // Trigger monitoring alert
    monitoring.triggerAlert('circuit_breaker_opened', {
      level: 'warning',
      circuitBreaker: this.name,
      failureCount: this.failureCount,
      nextAttemptTime: this.nextAttemptTime
    });
  }

  /**
   * Check if error should be ignored
   * @param {Error} error - Error to check
   * @returns {boolean} True if error should be ignored
   */
  shouldIgnoreError(error) {
    return this.expectedErrors.some(expectedError => {
      if (typeof expectedError === 'string') {
        return error.message.includes(expectedError);
      }
      if (expectedError instanceof RegExp) {
        return expectedError.test(error.message);
      }
      if (typeof expectedError === 'function') {
        return expectedError(error);
      }
      return false;
    });
  }

  /**
   * Get current circuit breaker state
   * @returns {Object} Current state information
   */
  getState() {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      failureThreshold: this.failureThreshold,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime,
      stats: { ...this.stats }
    };
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
    this.stats.lastReset = Date.now();
    
    logger.info('Circuit breaker reset', { name: this.name });
  }

  /**
   * Setup monitoring for circuit breaker
   */
  setupMonitoring() {
    // Report metrics every monitoring period
    setInterval(() => {
      this.reportMetrics();
    }, this.monitoringPeriod);
  }

  /**
   * Report circuit breaker metrics
   */
  reportMetrics() {
    const metrics = {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      ...this.stats,
      successRate: this.stats.totalRequests > 0 
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2)
        : 0
    };
    
    logger.logPerformance('Circuit breaker metrics', 0, metrics);
    
    // Alert if success rate is low
    if (metrics.successRate < 50 && this.stats.totalRequests > 10) {
      monitoring.triggerAlert('circuit_breaker_low_success_rate', {
        level: 'warning',
        circuitBreaker: this.name,
        successRate: metrics.successRate,
        totalRequests: this.stats.totalRequests
      });
    }
  }
}

/**
 * Circuit Breaker Manager
 * Manages multiple circuit breakers for different services
 */
class CircuitBreakerManager {
  constructor() {
    this.breakers = new Map();
    this.defaultOptions = {
      failureThreshold: 5,
      recoveryTimeout: 60000,
      monitoringPeriod: 60000
    };
  }

  /**
   * Get or create a circuit breaker
   * @param {string} name - Circuit breaker name
   * @param {Object} options - Circuit breaker options
   * @returns {CircuitBreaker} Circuit breaker instance
   */
  getBreaker(name, options = {}) {
    if (!this.breakers.has(name)) {
      const breakerOptions = { ...this.defaultOptions, ...options };
      this.breakers.set(name, new CircuitBreaker(name, breakerOptions));
    }
    return this.breakers.get(name);
  }

  /**
   * Execute function with circuit breaker protection
   * @param {string} name - Circuit breaker name
   * @param {Function} fn - Function to execute
   * @param {Object} options - Circuit breaker options
   * @param {...any} args - Arguments to pass to function
   * @returns {Promise} Function result
   */
  async execute(name, fn, options = {}, ...args) {
    const breaker = this.getBreaker(name, options);
    return breaker.execute(fn, ...args);
  }

  /**
   * Get all circuit breaker states
   * @returns {Array} Array of circuit breaker states
   */
  getAllStates() {
    const states = [];
    for (const [name, breaker] of this.breakers) {
      states.push(breaker.getState());
    }
    return states;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll() {
    for (const [name, breaker] of this.breakers) {
      breaker.reset();
    }
    logger.info('All circuit breakers reset');
  }

  /**
   * Reset specific circuit breaker
   * @param {string} name - Circuit breaker name
   */
  reset(name) {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.reset();
    } else {
      throw new Error(`Circuit breaker '${name}' not found`);
    }
  }

  /**
   * Get circuit breaker statistics
   * @returns {Object} Aggregated statistics
   */
  getStatistics() {
    const stats = {
      totalBreakers: this.breakers.size,
      openBreakers: 0,
      halfOpenBreakers: 0,
      closedBreakers: 0,
      totalRequests: 0,
      totalFailures: 0,
      totalSuccesses: 0
    };

    for (const [name, breaker] of this.breakers) {
      const state = breaker.getState();
      
      switch (state.state) {
        case 'OPEN':
          stats.openBreakers++;
          break;
        case 'HALF_OPEN':
          stats.halfOpenBreakers++;
          break;
        case 'CLOSED':
          stats.closedBreakers++;
          break;
      }
      
      stats.totalRequests += state.stats.totalRequests;
      stats.totalFailures += state.stats.failedRequests;
      stats.totalSuccesses += state.stats.successfulRequests;
    }

    stats.overallSuccessRate = stats.totalRequests > 0
      ? (stats.totalSuccesses / stats.totalRequests * 100).toFixed(2)
      : 0;

    return stats;
  }
}

// Create singleton instance
const circuitBreakerManager = new CircuitBreakerManager();

// Pre-configure circuit breakers for known external APIs
const API_CIRCUIT_BREAKERS = {
  indeed: {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    expectedErrors: ['Rate limit exceeded', 'API key invalid']
  },
  linkedin: {
    failureThreshold: 5,
    recoveryTimeout: 60000,
    expectedErrors: ['Unauthorized', 'Forbidden']
  },
  glassdoor: {
    failureThreshold: 3,
    recoveryTimeout: 45000,
    expectedErrors: ['Partner ID invalid']
  },
  apollo: {
    failureThreshold: 4,
    recoveryTimeout: 30000,
    expectedErrors: ['Credits exhausted']
  },
  hunter: {
    failureThreshold: 3,
    recoveryTimeout: 60000,
    expectedErrors: ['Monthly quota exceeded']
  },
  clearbit: {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    expectedErrors: ['Person not found']
  }
};

// Initialize circuit breakers for external APIs
Object.entries(API_CIRCUIT_BREAKERS).forEach(([name, options]) => {
  circuitBreakerManager.getBreaker(name, options);
});

module.exports = {
  CircuitBreaker,
  CircuitBreakerManager,
  circuitBreakerManager,
  API_CIRCUIT_BREAKERS
};
