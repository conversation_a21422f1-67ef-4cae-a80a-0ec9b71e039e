/**
 * Enhanced logging system with multiple levels, structured output,
 * and configurable destinations for better debugging and monitoring
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * Log levels with numeric values for filtering
 */
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  TRACE: 4
};

/**
 * Logger class with configurable output and formatting
 */
class Logger {
  constructor(options = {}) {
    this.level = LOG_LEVELS[options.level?.toUpperCase()] ?? LOG_LEVELS.INFO;
    this.serviceName = options.serviceName || 'cvleap-server';
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile || false;
    this.logDir = options.logDir || path.join(__dirname, '..', 'logs');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    
    // Context for correlation IDs and async tracking
    this.context = new Map();
    
    // Ensure log directory exists if file logging is enabled
    if (this.enableFile) {
      this.ensureLogDirectory();
    }
  }

  /**
   * Generate a correlation ID for request tracking
   * @returns {string} Unique correlation ID
   */
  generateCorrelationId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Set context for the current operation (correlation ID, user info, etc.)
   * @param {Object} contextData - Context data to set
   */
  setContext(contextData) {
    Object.keys(contextData).forEach(key => {
      this.context.set(key, contextData[key]);
    });
  }

  /**
   * Get current context
   * @returns {Object} Current context object
   */
  getContext() {
    const context = {};
    this.context.forEach((value, key) => {
      context[key] = value;
    });
    return context;
  }

  /**
   * Clear context
   */
  clearContext() {
    this.context.clear();
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Format log message with timestamp and context
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   * @returns {Object} Formatted log object
   */
  formatMessage(level, message, meta = {}) {
    const context = this.getContext();
    return {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      service: this.serviceName,
      message,
      ...context,
      ...meta,
      pid: process.pid,
      environment: process.env.NODE_ENV || 'development'
    };
  }

  /**
   * Write log to console with appropriate styling
   * @param {Object} logObject - Formatted log object
   */
  writeToConsole(logObject) {
    const { level, timestamp, message, ...meta } = logObject;
    const timeStr = timestamp.substring(11, 19); // HH:MM:SS
    
    // Color coding for different log levels
    const colors = {
      ERROR: '\x1b[31m', // Red
      WARN: '\x1b[33m',  // Yellow
      INFO: '\x1b[36m',  // Cyan
      DEBUG: '\x1b[32m', // Green
      TRACE: '\x1b[35m'  // Magenta
    };
    
    const reset = '\x1b[0m';
    const color = colors[level] || reset;
    
    let output = `${color}[${timeStr}] ${level.padEnd(5)} ${message}${reset}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      output += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    console.log(output);
  }

  /**
   * Write log to file with rotation
   * @param {Object} logObject - Formatted log object
   */
  writeToFile(logObject) {
    const logFile = path.join(this.logDir, `${this.serviceName}.log`);
    const logLine = JSON.stringify(logObject) + '\n';
    
    try {
      // Check if file needs rotation
      if (fs.existsSync(logFile)) {
        const stats = fs.statSync(logFile);
        if (stats.size > this.maxFileSize) {
          this.rotateLogFile(logFile);
        }
      }
      
      fs.appendFileSync(logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  /**
   * Rotate log files when they get too large
   * @param {string} logFile - Path to current log file
   */
  rotateLogFile(logFile) {
    try {
      // Move existing files
      for (let i = this.maxFiles - 1; i > 0; i--) {
        const oldFile = `${logFile}.${i}`;
        const newFile = `${logFile}.${i + 1}`;
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest file
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }
      
      // Move current log to .1
      if (fs.existsSync(logFile)) {
        fs.renameSync(logFile, `${logFile}.1`);
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error.message);
    }
  }

  /**
   * Generic log method
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  log(level, message, meta = {}) {
    const levelValue = LOG_LEVELS[level.toUpperCase()];
    
    if (levelValue === undefined || levelValue > this.level) {
      return; // Skip if level is not enabled
    }
    
    const logObject = this.formatMessage(level, message, meta);
    
    if (this.enableConsole) {
      this.writeToConsole(logObject);
    }
    
    if (this.enableFile) {
      this.writeToFile(logObject);
    }
  }

  /**
   * Error level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  error(message, meta = {}) {
    this.log('ERROR', message, meta);
  }

  /**
   * Warning level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  warn(message, meta = {}) {
    this.log('WARN', message, meta);
  }

  /**
   * Info level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  info(message, meta = {}) {
    this.log('INFO', message, meta);
  }

  /**
   * Debug level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  debug(message, meta = {}) {
    this.log('DEBUG', message, meta);
  }

  /**
   * Trace level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  trace(message, meta = {}) {
    this.log('TRACE', message, meta);
  }

  /**
   * Log HTTP requests for monitoring
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {number} duration - Request duration in milliseconds
   */
  logRequest(req, res, duration) {
    const meta = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.userId || null,
      correlationId: req.correlationId || null
    };

    const level = res.statusCode >= 400 ? 'WARN' : 'INFO';
    this.log(level, `HTTP ${req.method} ${req.originalUrl}`, meta);
  }

  /**
   * Log database operations for monitoring
   * @param {string} operation - Database operation type
   * @param {string} table - Database table name
   * @param {number} duration - Operation duration in milliseconds
   * @param {Object} meta - Additional metadata
   */
  logDatabase(operation, table, duration, meta = {}) {
    this.debug(`Database ${operation} on ${table}`, {
      operation,
      table,
      duration: `${duration}ms`,
      ...meta
    });

    // Also log as a performance metric
    this.logMetric(`db_${operation.toLowerCase()}_duration`, duration, 'ms', {
      table,
      operation,
      ...meta
    });
  }

  /**
   * Log performance metrics and optionally integrate with metrics collector
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @param {string} unit - Metric unit
   * @param {Object} meta - Additional metadata
   */
  logMetric(metric, value, unit = '', meta = {}) {
    this.info(`Metric: ${metric}`, {
      metric,
      value,
      unit,
      ...meta
    });

    // Integrate with metrics collector if available
    try {
      const MetricsCollector = require('./metricsCollector');
      if (typeof MetricsCollector === 'function') {
        const collector = new MetricsCollector();
        if (typeof collector.recordHistogram === 'function') {
          collector.recordHistogram(metric, value, meta);
        }
      }
    } catch (error) {
      // Metrics collector integration is optional
      this.debug('Metrics collector not available for integration', { error: error.message });
    }
  }

  /**
   * Create request logging middleware for Express
   * @returns {Function} Express middleware function
   */
  createRequestMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      const correlationId = this.generateCorrelationId();
      
      // Set correlation ID on request for easy access
      req.correlationId = correlationId;
      
      // Set context for this request
      this.asyncLocalStorage.run(new Map(), () => {
        const store = this.asyncLocalStorage.getStore();
        store.set('correlationId', correlationId);
        store.set('userId', req.user?.userId || null);
        store.set('sessionId', req.sessionID || null);

        this.setContext({
          correlationId,
          userId: req.user?.userId || null,
          sessionId: req.sessionID || null
        });
        
        // Log request start
        this.debug(`Starting ${req.method} ${req.originalUrl}`, {
          method: req.method,
          url: req.originalUrl,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          correlationId
        });

        // Override res.end to log when request finishes
        const originalEnd = res.end;
        res.end = (...args) => {
          const duration = Date.now() - startTime;
          this.logRequest(req, res, duration);
          // Clear context after request is complete
          this.clearContext();
          originalEnd.apply(res, args);
        };

        next();
      });
    };
  }
}

// Create default logger instance
const defaultLogger = new Logger({
  level: process.env.LOG_LEVEL || 'INFO',
  enableConsole: true,
  enableFile: process.env.ENABLE_FILE_LOGGING === 'true',
  serviceName: 'cvleap-server'
});

module.exports = {
  Logger,
  logger: defaultLogger,
  LOG_LEVELS
};