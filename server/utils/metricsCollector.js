/**
 * Application Metrics Collection System for monitoring and observability
 */
class MetricsCollector {
  constructor() {
    this.metrics = new Map();
    this.counters = new Map();
    this.gauges = new Map();
    this.histograms = new Map();
    this.timers = new Map();
    this.startTime = Date.now();
    
    // Initialize default metrics
    this.initializeDefaultMetrics();
    
    // Start background collection
    this.startBackgroundCollection();
  }

  /**
   * Initialize default system metrics
   */
  initializeDefaultMetrics() {
    // HTTP request metrics
    this.counters.set('http_requests_total', { value: 0, labels: {} });
    this.histograms.set('http_request_duration_ms', { buckets: new Map(), labels: {} });
    this.counters.set('http_errors_total', { value: 0, labels: {} });
    
    // Database metrics
    this.counters.set('db_queries_total', { value: 0, labels: {} });
    this.histograms.set('db_query_duration_ms', { buckets: new Map(), labels: {} });
    this.counters.set('db_errors_total', { value: 0, labels: {} });
    
    // Cache metrics
    this.counters.set('cache_hits_total', { value: 0, labels: {} });
    this.counters.set('cache_misses_total', { value: 0, labels: {} });
    
    // Application metrics
    this.gauges.set('active_connections', { value: 0, labels: {} });
    this.gauges.set('memory_usage_bytes', { value: 0, labels: {} });
    this.gauges.set('cpu_usage_percent', { value: 0, labels: {} });
    
    // AI service metrics
    this.counters.set('ai_requests_total', { value: 0, labels: {} });
    this.histograms.set('ai_request_duration_ms', { buckets: new Map(), labels: {} });
    this.counters.set('ai_tokens_consumed', { value: 0, labels: {} });
    
    // Business metrics
    this.counters.set('user_registrations_total', { value: 0, labels: {} });
    this.counters.set('resume_generations_total', { value: 0, labels: {} });
    this.counters.set('job_applications_total', { value: 0, labels: {} });
  }

  /**
   * Increment a counter metric
   */
  incrementCounter(name, labels = {}, value = 1) {
    const key = this.getMetricKey(name, labels);
    
    if (!this.counters.has(key)) {
      this.counters.set(key, { value: 0, labels, lastUpdated: Date.now() });
    }
    
    const counter = this.counters.get(key);
    counter.value += value;
    counter.lastUpdated = Date.now();
  }

  /**
   * Set a gauge metric value
   */
  setGauge(name, value, labels = {}) {
    const key = this.getMetricKey(name, labels);
    this.gauges.set(key, { value, labels, lastUpdated: Date.now() });
  }

  /**
   * Record a histogram measurement
   */
  recordHistogram(name, value, labels = {}) {
    const key = this.getMetricKey(name, labels);
    
    if (!this.histograms.has(key)) {
      this.histograms.set(key, { 
        buckets: new Map(),
        sum: 0,
        count: 0,
        labels,
        lastUpdated: Date.now()
      });
    }
    
    const histogram = this.histograms.get(key);
    histogram.sum += value;
    histogram.count++;
    histogram.lastUpdated = Date.now();
    
    // Add to appropriate buckets (simplified bucketing)
    const buckets = [1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000];
    for (const bucket of buckets) {
      if (value <= bucket) {
        const bucketCount = histogram.buckets.get(bucket) || 0;
        histogram.buckets.set(bucket, bucketCount + 1);
      }
    }
  }

  /**
   * Start a timer for measuring duration
   */
  startTimer(name, labels = {}) {
    const timerId = `${name}_${Date.now()}_${Math.random()}`;
    this.timers.set(timerId, { 
      name, 
      labels, 
      startTime: Date.now() 
    });
    return timerId;
  }

  /**
   * End a timer and record the duration
   */
  endTimer(timerId) {
    const timer = this.timers.get(timerId);
    if (!timer) {
      return null;
    }
    
    const duration = Date.now() - timer.startTime;
    this.recordHistogram(timer.name, duration, timer.labels);
    this.timers.delete(timerId);
    
    return duration;
  }

  /**
   * Middleware for HTTP request metrics
   */
  httpMetricsMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      const labels = {
        method: req.method,
        route: req.route?.path || req.path,
        status_code: 'unknown'
      };

      // Increment request counter
      this.incrementCounter('http_requests_total', {
        method: req.method,
        route: labels.route
      });

      // Override res.end to capture metrics
      const originalEnd = res.end;
      res.end = (...args) => {
        const duration = Date.now() - startTime;
        labels.status_code = res.statusCode.toString();

        // Record response time
        this.recordHistogram('http_request_duration_ms', duration, labels);

        // Count errors
        if (res.statusCode >= 400) {
          this.incrementCounter('http_errors_total', labels);
        }

        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Database query metrics wrapper
   */
  wrapDatabaseQuery(queryFunction, queryType = 'unknown') {
    return async (...args) => {
      const timer = this.startTimer('db_query_duration_ms', { type: queryType });
      
      try {
        this.incrementCounter('db_queries_total', { type: queryType });
        const result = await queryFunction.apply(this, args);
        this.endTimer(timer);
        return result;
      } catch (error) {
        this.incrementCounter('db_errors_total', { type: queryType });
        this.endTimer(timer);
        throw error;
      }
    };
  }

  /**
   * AI service metrics wrapper
   */
  wrapAIService(serviceFunction, provider = 'unknown') {
    return async (...args) => {
      const timer = this.startTimer('ai_request_duration_ms', { provider });
      
      try {
        this.incrementCounter('ai_requests_total', { provider });
        const result = await serviceFunction.apply(this, args);
        
        // Estimate token consumption (simplified)
        if (result && result.usage) {
          this.incrementCounter('ai_tokens_consumed', { provider }, result.usage.total_tokens);
        }
        
        this.endTimer(timer);
        return result;
      } catch (error) {
        this.incrementCounter('ai_errors_total', { provider });
        this.endTimer(timer);
        throw error;
      }
    };
  }

  /**
   * Record business metrics
   */
  recordBusinessMetric(event, userId = null, metadata = {}) {
    const labels = { ...metadata };
    if (userId) {
      labels.user_id = userId;
    }

    switch (event) {
      case 'user_registration':
        this.incrementCounter('user_registrations_total', labels);
        break;
      case 'resume_generation':
        this.incrementCounter('resume_generations_total', labels);
        break;
      case 'job_application':
        this.incrementCounter('job_applications_total', labels);
        break;
      default:
        this.incrementCounter(`business_${event}_total`, labels);
    }
  }

  /**
   * Get metric key with labels
   */
  getMetricKey(name, labels = {}) {
    const labelStr = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}="${v}"`)
      .join(',');
    return labelStr ? `${name}{${labelStr}}` : name;
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    const process = require('process');
    
    // Memory usage
    const memUsage = process.memoryUsage();
    this.setGauge('memory_usage_bytes', memUsage.heapUsed, { type: 'heap_used' });
    this.setGauge('memory_usage_bytes', memUsage.heapTotal, { type: 'heap_total' });
    this.setGauge('memory_usage_bytes', memUsage.rss, { type: 'rss' });
    
    // CPU usage (simplified)
    const cpuUsage = process.cpuUsage();
    this.setGauge('cpu_usage_microseconds', cpuUsage.user, { type: 'user' });
    this.setGauge('cpu_usage_microseconds', cpuUsage.system, { type: 'system' });
    
    // Uptime
    this.setGauge('uptime_seconds', Math.floor((Date.now() - this.startTime) / 1000));
    
    // Event loop lag (simplified)
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const lag = Number(process.hrtime.bigint() - start) / 1000000;
      this.setGauge('event_loop_lag_ms', lag);
    });
  }

  /**
   * Start background metrics collection
   */
  startBackgroundCollection() {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);
    
    // Initial collection
    this.collectSystemMetrics();
  }

  /**
   * Export metrics in Prometheus format
   */
  exportPrometheus() {
    let output = '';
    
    // Export counters
    for (const [key, counter] of this.counters.entries()) {
      const [name] = key.split('{');
      output += `# TYPE ${name} counter\n`;
      output += `${key} ${counter.value}\n`;
    }
    
    // Export gauges
    for (const [key, gauge] of this.gauges.entries()) {
      const [name] = key.split('{');
      output += `# TYPE ${name} gauge\n`;
      output += `${key} ${gauge.value}\n`;
    }
    
    // Export histograms
    for (const [key, histogram] of this.histograms.entries()) {
      const [name] = key.split('{');
      output += `# TYPE ${name} histogram\n`;
      
      for (const [bucket, count] of histogram.buckets.entries()) {
        const bucketKey = key.replace('}', `,le="${bucket}"}`);
        output += `${bucketKey} ${count}\n`;
      }
      
      const countKey = key.replace('}', '_count}').replace(name, `${name}_count`);
      const sumKey = key.replace('}', '_sum}').replace(name, `${name}_sum`);
      output += `${countKey} ${histogram.count}\n`;
      output += `${sumKey} ${histogram.sum}\n`;
    }
    
    return output;
  }

  /**
   * Export metrics in JSON format
   */
  exportJSON() {
    return {
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      counters: Object.fromEntries(this.counters),
      gauges: Object.fromEntries(this.gauges),
      histograms: Object.fromEntries(
        Array.from(this.histograms.entries()).map(([key, hist]) => [
          key,
          {
            count: hist.count,
            sum: hist.sum,
            avg: hist.count > 0 ? hist.sum / hist.count : 0,
            buckets: Object.fromEntries(hist.buckets),
            labels: hist.labels
          }
        ])
      )
    };
  }

  /**
   * Get metrics summary
   */
  getSummary() {
    const totalRequests = Array.from(this.counters.entries())
      .filter(([key]) => key.startsWith('http_requests_total'))
      .reduce((sum, [, counter]) => sum + counter.value, 0);

    const totalErrors = Array.from(this.counters.entries())
      .filter(([key]) => key.startsWith('http_errors_total'))
      .reduce((sum, [, counter]) => sum + counter.value, 0);

    const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

    return {
      totalRequests,
      totalErrors,
      errorRate: Math.round(errorRate * 100) / 100,
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      memoryUsage: this.gauges.get('memory_usage_bytes{type="heap_used"}')?.value || 0,
      activeMetrics: this.counters.size + this.gauges.size + this.histograms.size
    };
  }

  /**
   * Reset all metrics (use with caution)
   */
  reset() {
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
    this.timers.clear();
    this.initializeDefaultMetrics();
  }
}

// Create singleton instance
const metricsCollector = new MetricsCollector();

module.exports = metricsCollector;