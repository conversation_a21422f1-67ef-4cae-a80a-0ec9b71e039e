/**
 * Graceful shutdown handler for production applications
 */
class GracefulShutdown {
  constructor() {
    this.isShuttingDown = false;
    this.connections = new Set();
    this.tasks = new Set();
    this.shutdownTimeout = parseInt(process.env.SHUTDOWN_TIMEOUT) || 30000; // 30 seconds
    this.cleanupHandlers = [];
    
    this.setupSignalHandlers();
  }

  /**
   * Setup signal handlers for graceful shutdown
   */
  setupSignalHandlers() {
    // Handle termination signals
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
    
    signals.forEach(signal => {
      process.on(signal, () => {
        console.log(`Received ${signal}, starting graceful shutdown...`);
        this.gracefulShutdown(signal);
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.gracefulShutdown('uncaughtException', 1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('unhandledRejection', 1);
    });
  }

  /**
   * Register a server to track connections
   */
  registerServer(server) {
    // Track connections
    server.on('connection', (connection) => {
      this.connections.add(connection);
      
      connection.on('close', () => {
        this.connections.delete(connection);
      });
    });

    // Handle server shutdown
    this.addCleanupHandler(async () => {
      console.log('Closing server...');
      
      return new Promise((resolve) => {
        server.close((err) => {
          if (err) {
            console.error('Error closing server:', err);
          } else {
            console.log('Server closed successfully');
          }
          resolve();
        });
      });
    });
  }

  /**
   * Register a database connection for cleanup
   */
  registerDatabase(database) {
    this.addCleanupHandler(async () => {
      console.log('Closing database connections...');
      
      return new Promise((resolve) => {
        if (database && typeof database.close === 'function') {
          database.close((err) => {
            if (err) {
              console.error('Error closing database:', err);
            } else {
              console.log('Database closed successfully');
            }
            resolve();
          });
        } else if (database && typeof database.end === 'function') {
          database.end().then(() => {
            console.log('Database pool closed successfully');
            resolve();
          }).catch((err) => {
            console.error('Error closing database pool:', err);
            resolve();
          });
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Register Redis client for cleanup
   */
  registerRedis(redisClient) {
    this.addCleanupHandler(async () => {
      console.log('Closing Redis connection...');
      
      return new Promise((resolve) => {
        if (redisClient && typeof redisClient.quit === 'function') {
          redisClient.quit().then(() => {
            console.log('Redis connection closed successfully');
            resolve();
          }).catch((err) => {
            console.error('Error closing Redis connection:', err);
            resolve();
          });
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Add a custom cleanup handler
   */
  addCleanupHandler(handler) {
    this.cleanupHandlers.push(handler);
  }

  /**
   * Track active task
   */
  trackTask(task) {
    this.tasks.add(task);
    
    // Auto-remove when task completes
    if (task && typeof task.then === 'function') {
      task.finally(() => {
        this.tasks.delete(task);
      });
    }
    
    return task;
  }

  /**
   * Wait for active tasks to complete
   */
  async waitForTasks() {
    if (this.tasks.size === 0) {
      return;
    }

    console.log(`Waiting for ${this.tasks.size} active tasks to complete...`);
    
    const taskArray = Array.from(this.tasks);
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        console.log(`Task completion timeout reached, proceeding with shutdown`);
        resolve();
      }, this.shutdownTimeout / 2);
    });

    try {
      await Promise.race([
        Promise.allSettled(taskArray),
        timeoutPromise
      ]);
      
      console.log('Active tasks completed or timed out');
    } catch (error) {
      console.error('Error waiting for tasks:', error);
    }
  }

  /**
   * Close all active connections
   */
  async closeConnections() {
    if (this.connections.size === 0) {
      return;
    }

    console.log(`Closing ${this.connections.size} active connections...`);
    
    // Set a timeout for closing connections
    const timeout = setTimeout(() => {
      console.log('Connection close timeout, forcing close...');
      this.connections.forEach(connection => {
        connection.destroy();
      });
    }, 10000); // 10 seconds

    // Close connections gracefully
    this.connections.forEach(connection => {
      connection.end();
    });

    // Wait for connections to close
    while (this.connections.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    clearTimeout(timeout);
    console.log('All connections closed');
  }

  /**
   * Run cleanup handlers
   */
  async runCleanupHandlers() {
    console.log(`Running ${this.cleanupHandlers.length} cleanup handlers...`);
    
    for (const handler of this.cleanupHandlers) {
      try {
        await handler();
      } catch (error) {
        console.error('Cleanup handler error:', error);
      }
    }
    
    console.log('Cleanup handlers completed');
  }

  /**
   * Log final metrics before shutdown
   */
  logFinalMetrics() {
    try {
      const metricsCollector = require('./metricsCollector');
      const auditLogger = require('./auditLogger');
      
      // Log final metrics
      const summary = metricsCollector.getSummary();
      console.log('Final Metrics:', JSON.stringify(summary, null, 2));
      
      // Log shutdown event
      auditLogger.logAdmin('APPLICATION_SHUTDOWN', 'system', 'graceful_shutdown', {
        uptime: summary.uptime,
        totalRequests: summary.totalRequests,
        errorRate: summary.errorRate
      });
      
    } catch (error) {
      console.error('Error logging final metrics:', error);
    }
  }

  /**
   * Main graceful shutdown process
   */
  async gracefulShutdown(signal, exitCode = 0) {
    if (this.isShuttingDown) {
      console.log('Shutdown already in progress...');
      return;
    }

    this.isShuttingDown = true;
    
    console.log(`Starting graceful shutdown (signal: ${signal})...`);
    const startTime = Date.now();

    // Set overall shutdown timeout
    const shutdownTimer = setTimeout(() => {
      console.error('Graceful shutdown timeout reached, forcing exit');
      this.logFinalMetrics();
      process.exit(1);
    }, this.shutdownTimeout);

    try {
      // 1. Stop accepting new requests/connections
      console.log('1. Stopping new requests...');
      
      // 2. Wait for active tasks to complete
      console.log('2. Waiting for active tasks...');
      await this.waitForTasks();
      
      // 3. Close active connections
      console.log('3. Closing active connections...');
      await this.closeConnections();
      
      // 4. Run cleanup handlers (database, cache, etc.)
      console.log('4. Running cleanup handlers...');
      await this.runCleanupHandlers();
      
      // 5. Log final metrics
      console.log('5. Logging final metrics...');
      this.logFinalMetrics();
      
      const shutdownTime = Date.now() - startTime;
      console.log(`Graceful shutdown completed in ${shutdownTime}ms`);
      
      clearTimeout(shutdownTimer);
      process.exit(exitCode);
      
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      clearTimeout(shutdownTimer);
      process.exit(1);
    }
  }

  /**
   * Check if application is shutting down
   */
  isShutdownInProgress() {
    return this.isShuttingDown;
  }

  /**
   * Middleware to reject requests during shutdown
   */
  rejectionMiddleware() {
    return (req, res, next) => {
      if (this.isShuttingDown) {
        res.status(503).json({
          error: 'Service temporarily unavailable',
          reason: 'Server is shutting down',
          retryAfter: 30
        });
        return;
      }
      next();
    };
  }

  /**
   * Health check that considers shutdown state
   */
  healthCheckMiddleware() {
    return (req, res, next) => {
      if (this.isShuttingDown) {
        res.status(503).json({
          status: 'shutting-down',
          timestamp: new Date().toISOString()
        });
        return;
      }
      next();
    };
  }
}

// Create singleton instance
const gracefulShutdown = new GracefulShutdown();

module.exports = gracefulShutdown;