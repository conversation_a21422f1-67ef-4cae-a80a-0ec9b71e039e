const { logger } = require('./logger');
const { circuitBreakerManager } = require('./circuitBreaker');
const { monitoring } = require('./monitoring');

/**
 * API Resilience Service
 * Provides comprehensive error handling, retries, and fallbacks for external APIs
 */

class ApiResilience {
  constructor() {
    this.retryDefaults = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      jitter: true
    };
    
    this.timeoutDefaults = {
      connect: 5000,
      request: 30000,
      response: 30000
    };
    
    this.fallbackStrategies = new Map();
    this.apiHealthStatus = new Map();
    
    this.initializeHealthMonitoring();
  }

  /**
   * Initialize API health monitoring
   */
  initializeHealthMonitoring() {
    // Check API health every 5 minutes
    setInterval(() => {
      this.checkApiHealth();
    }, 300000);
    
    logger.info('API resilience service initialized');
  }

  /**
   * Execute API call with full resilience features
   * @param {string} apiName - Name of the API
   * @param {Function} apiCall - Function that makes the API call
   * @param {Object} options - Resilience options
   * @returns {Promise} API response or fallback result
   */
  async executeWithResilience(apiName, apiCall, options = {}) {
    const config = {
      ...this.retryDefaults,
      ...this.timeoutDefaults,
      ...options
    };
    
    const startTime = Date.now();
    let lastError;
    
    try {
      // Execute with circuit breaker protection
      const result = await circuitBreakerManager.execute(
        apiName,
        async () => {
          return await this.executeWithRetry(apiCall, config);
        },
        config.circuitBreaker
      );
      
      // Update API health status
      this.updateApiHealth(apiName, true, Date.now() - startTime);
      
      return result;
      
    } catch (error) {
      lastError = error;
      
      // Update API health status
      this.updateApiHealth(apiName, false, Date.now() - startTime, error);
      
      // Log API failure
      logger.warn('API call failed after all retries', {
        apiName,
        error: error.message,
        duration: Date.now() - startTime,
        circuitBreakerOpen: error.code === 'CIRCUIT_BREAKER_OPEN'
      });
      
      // Try fallback strategy
      if (this.fallbackStrategies.has(apiName)) {
        try {
          const fallbackResult = await this.executeFallback(apiName, error, config);
          
          logger.info('Fallback strategy succeeded', {
            apiName,
            fallbackType: fallbackResult.fallbackType
          });
          
          return fallbackResult;
        } catch (fallbackError) {
          logger.error('Fallback strategy failed', {
            apiName,
            originalError: error.message,
            fallbackError: fallbackError.message
          });
        }
      }
      
      // If no fallback or fallback failed, throw original error
      throw this.createUserFriendlyError(apiName, lastError);
    }
  }

  /**
   * Execute API call with retry logic
   * @param {Function} apiCall - Function to execute
   * @param {Object} config - Retry configuration
   * @returns {Promise} API response
   */
  async executeWithRetry(apiCall, config) {
    let attempt = 0;
    let lastError;
    
    while (attempt <= config.maxRetries) {
      try {
        // Add timeout wrapper
        const result = await this.withTimeout(apiCall(), config.request);
        
        if (attempt > 0) {
          logger.info('API call succeeded after retry', {
            attempt,
            maxRetries: config.maxRetries
          });
        }
        
        return result;
        
      } catch (error) {
        lastError = error;
        attempt++;
        
        // Don't retry on certain errors
        if (this.shouldNotRetry(error)) {
          logger.debug('Not retrying due to error type', {
            error: error.message,
            code: error.code,
            status: error.status
          });
          throw error;
        }
        
        // Don't retry if we've reached max attempts
        if (attempt > config.maxRetries) {
          break;
        }
        
        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateRetryDelay(attempt, config);
        
        logger.debug('Retrying API call', {
          attempt,
          maxRetries: config.maxRetries,
          delay,
          error: error.message
        });
        
        await this.sleep(delay);
      }
    }
    
    throw lastError;
  }

  /**
   * Wrap function with timeout
   * @param {Promise} promise - Promise to wrap
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise} Promise with timeout
   */
  async withTimeout(promise, timeout) {
    return Promise.race([
      promise,
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Request timeout after ${timeout}ms`));
        }, timeout);
      })
    ]);
  }

  /**
   * Check if error should not be retried
   * @param {Error} error - Error to check
   * @returns {boolean} True if should not retry
   */
  shouldNotRetry(error) {
    // Don't retry on client errors (4xx) except rate limiting
    if (error.status >= 400 && error.status < 500 && error.status !== 429) {
      return true;
    }
    
    // Don't retry on authentication errors
    if (error.code === 'EAUTH' || error.message.includes('authentication')) {
      return true;
    }
    
    // Don't retry on circuit breaker open
    if (error.code === 'CIRCUIT_BREAKER_OPEN') {
      return true;
    }
    
    return false;
  }

  /**
   * Calculate retry delay with exponential backoff and jitter
   * @param {number} attempt - Current attempt number
   * @param {Object} config - Retry configuration
   * @returns {number} Delay in milliseconds
   */
  calculateRetryDelay(attempt, config) {
    let delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
    
    // Apply maximum delay
    delay = Math.min(delay, config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (config.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.floor(delay);
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Register fallback strategy for an API
   * @param {string} apiName - API name
   * @param {Function} fallbackFunction - Fallback function
   * @param {Object} options - Fallback options
   */
  registerFallback(apiName, fallbackFunction, options = {}) {
    this.fallbackStrategies.set(apiName, {
      function: fallbackFunction,
      type: options.type || 'custom',
      description: options.description || 'Custom fallback strategy',
      ...options
    });
    
    logger.info('Fallback strategy registered', {
      apiName,
      type: options.type,
      description: options.description
    });
  }

  /**
   * Execute fallback strategy
   * @param {string} apiName - API name
   * @param {Error} originalError - Original error
   * @param {Object} config - Configuration
   * @returns {Promise} Fallback result
   */
  async executeFallback(apiName, originalError, config) {
    const fallback = this.fallbackStrategies.get(apiName);
    
    if (!fallback) {
      throw new Error(`No fallback strategy registered for ${apiName}`);
    }
    
    try {
      const result = await fallback.function(originalError, config);
      
      return {
        ...result,
        fallbackType: fallback.type,
        fallbackDescription: fallback.description,
        originalError: originalError.message
      };
    } catch (error) {
      error.fallbackFailed = true;
      throw error;
    }
  }

  /**
   * Update API health status
   * @param {string} apiName - API name
   * @param {boolean} success - Whether call was successful
   * @param {number} responseTime - Response time in ms
   * @param {Error} error - Error if failed
   */
  updateApiHealth(apiName, success, responseTime, error = null) {
    const now = Date.now();
    
    if (!this.apiHealthStatus.has(apiName)) {
      this.apiHealthStatus.set(apiName, {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageResponseTime: 0,
        lastCallTime: now,
        lastSuccessTime: null,
        lastFailureTime: null,
        consecutiveFailures: 0,
        status: 'unknown'
      });
    }
    
    const health = this.apiHealthStatus.get(apiName);
    
    health.totalCalls++;
    health.lastCallTime = now;
    
    if (success) {
      health.successfulCalls++;
      health.lastSuccessTime = now;
      health.consecutiveFailures = 0;
      health.status = 'healthy';
    } else {
      health.failedCalls++;
      health.lastFailureTime = now;
      health.consecutiveFailures++;
      health.lastError = error?.message;
      
      // Update status based on consecutive failures
      if (health.consecutiveFailures >= 5) {
        health.status = 'unhealthy';
      } else if (health.consecutiveFailures >= 3) {
        health.status = 'degraded';
      }
    }
    
    // Update average response time
    health.averageResponseTime = (
      (health.averageResponseTime * (health.totalCalls - 1) + responseTime) / 
      health.totalCalls
    );
    
    // Calculate success rate
    health.successRate = (health.successfulCalls / health.totalCalls * 100).toFixed(2);
  }

  /**
   * Check health of all APIs
   */
  async checkApiHealth() {
    const healthReport = {};
    
    for (const [apiName, health] of this.apiHealthStatus) {
      healthReport[apiName] = {
        ...health,
        lastCallAge: Date.now() - health.lastCallTime
      };
      
      // Alert on unhealthy APIs
      if (health.status === 'unhealthy') {
        monitoring.triggerAlert('api_unhealthy', {
          level: 'critical',
          apiName,
          consecutiveFailures: health.consecutiveFailures,
          successRate: health.successRate,
          lastError: health.lastError
        });
      } else if (health.status === 'degraded') {
        monitoring.triggerAlert('api_degraded', {
          level: 'warning',
          apiName,
          consecutiveFailures: health.consecutiveFailures,
          successRate: health.successRate
        });
      }
    }
    
    logger.logPerformance('API health check', 0, healthReport);
  }

  /**
   * Create user-friendly error message
   * @param {string} apiName - API name
   * @param {Error} error - Original error
   * @returns {Error} User-friendly error
   */
  createUserFriendlyError(apiName, error) {
    const userError = new Error();
    userError.originalError = error;
    userError.apiName = apiName;
    
    if (error.code === 'CIRCUIT_BREAKER_OPEN') {
      userError.message = `The ${apiName} service is temporarily unavailable. Please try again later.`;
      userError.code = 'SERVICE_UNAVAILABLE';
      userError.retryAfter = error.nextAttemptTime;
    } else if (error.message.includes('timeout')) {
      userError.message = `The ${apiName} service is responding slowly. Please try again.`;
      userError.code = 'SERVICE_TIMEOUT';
    } else if (error.status === 429) {
      userError.message = `Too many requests to ${apiName}. Please wait before trying again.`;
      userError.code = 'RATE_LIMITED';
    } else if (error.status >= 500) {
      userError.message = `The ${apiName} service is experiencing issues. Please try again later.`;
      userError.code = 'SERVICE_ERROR';
    } else {
      userError.message = `Unable to connect to ${apiName} service. Please check your request and try again.`;
      userError.code = 'CONNECTION_ERROR';
    }
    
    return userError;
  }

  /**
   * Get API health status
   * @param {string} apiName - API name (optional)
   * @returns {Object} Health status
   */
  getApiHealth(apiName = null) {
    if (apiName) {
      return this.apiHealthStatus.get(apiName) || null;
    }
    
    const health = {};
    for (const [name, status] of this.apiHealthStatus) {
      health[name] = status;
    }
    return health;
  }

  /**
   * Get circuit breaker status
   * @returns {Array} Circuit breaker states
   */
  getCircuitBreakerStatus() {
    return circuitBreakerManager.getAllStates();
  }

  /**
   * Reset API health for specific API or all APIs
   * @param {string} apiName - API name (optional)
   */
  resetApiHealth(apiName = null) {
    if (apiName) {
      this.apiHealthStatus.delete(apiName);
      circuitBreakerManager.reset(apiName);
    } else {
      this.apiHealthStatus.clear();
      circuitBreakerManager.resetAll();
    }
    
    logger.info('API health reset', { apiName: apiName || 'all' });
  }
}

// Create singleton instance
const apiResilience = new ApiResilience();

// Register default fallback strategies
apiResilience.registerFallback('indeed', async (error, config) => {
  return {
    jobs: [],
    source: 'cache',
    message: 'Showing cached results due to Indeed API unavailability'
  };
}, { type: 'cache', description: 'Return cached job results' });

apiResilience.registerFallback('linkedin', async (error, config) => {
  return {
    profiles: [],
    source: 'alternative',
    message: 'LinkedIn data temporarily unavailable'
  };
}, { type: 'empty', description: 'Return empty results' });

module.exports = {
  ApiResilience,
  apiResilience
};
