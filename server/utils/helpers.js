/**
 * Comprehensive utility functions for common operations across the application
 * Includes data processing, validation, formatting, and helper functions
 */

const crypto = require('crypto');

/**
 * String manipulation utilities
 */
const stringUtils = {
  /**
   * Capitalize first letter of each word
   * @param {string} str - Input string
   * @returns {string} Capitalized string
   */
  titleCase: (str) => {
    if (!str || typeof str !== 'string') return '';
    return str.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
  },

  /**
   * Convert string to slug format (URL-friendly)
   * @param {string} str - Input string
   * @returns {string} Slugified string
   */
  slugify: (str) => {
    if (!str || typeof str !== 'string') return '';
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  /**
   * Truncate string to specified length with ellipsis
   * @param {string} str - Input string
   * @param {number} maxLength - Maximum length
   * @param {string} suffix - Suffix to add when truncated
   * @returns {string} Truncated string
   */
  truncate: (str, maxLength = 100, suffix = '...') => {
    if (!str || typeof str !== 'string') return '';
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
  },

  /**
   * Extract initials from a name
   * @param {string} name - Full name
   * @returns {string} Initials
   */
  getInitials: (name) => {
    if (!name || typeof name !== 'string') return '';
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 3);
  },

  /**
   * Clean and normalize text input
   * @param {string} text - Input text
   * @returns {string} Cleaned text
   */
  cleanText: (text) => {
    if (!text || typeof text !== 'string') return '';
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\-_.@]/g, '');
  }
};

/**
 * Array manipulation utilities
 */
const arrayUtils = {
  /**
   * Remove duplicates from array
   * @param {Array} arr - Input array
   * @param {string} key - Key to compare for objects
   * @returns {Array} Array without duplicates
   */
  removeDuplicates: (arr, key = null) => {
    if (!Array.isArray(arr)) return [];
    if (key) {
      const seen = new Set();
      return arr.filter(item => {
        const value = item[key];
        if (seen.has(value)) return false;
        seen.add(value);
        return true;
      });
    }
    return [...new Set(arr)];
  },

  /**
   * Chunk array into smaller arrays of specified size
   * @param {Array} arr - Input array
   * @param {number} size - Chunk size
   * @returns {Array} Array of chunks
   */
  chunk: (arr, size) => {
    if (!Array.isArray(arr) || size <= 0) return [];
    const chunks = [];
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size));
    }
    return chunks;
  },

  /**
   * Shuffle array randomly
   * @param {Array} arr - Input array
   * @returns {Array} Shuffled array
   */
  shuffle: (arr) => {
    if (!Array.isArray(arr)) return [];
    const shuffled = [...arr];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  /**
   * Group array items by specified key
   * @param {Array} arr - Input array
   * @param {string} key - Key to group by
   * @returns {Object} Grouped object
   */
  groupBy: (arr, key) => {
    if (!Array.isArray(arr)) return {};
    return arr.reduce((groups, item) => {
      const group = item[key];
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  },

  /**
   * Find items in array that match all criteria
   * @param {Array} arr - Input array
   * @param {Object} criteria - Search criteria
   * @returns {Array} Matching items
   */
  findWhere: (arr, criteria) => {
    if (!Array.isArray(arr) || !criteria) return [];
    return arr.filter(item => {
      return Object.keys(criteria).every(key => item[key] === criteria[key]);
    });
  }
};

/**
 * Object manipulation utilities
 */
const objectUtils = {
  /**
   * Deep clone an object
   * @param {*} obj - Object to clone
   * @returns {*} Cloned object
   */
  deepClone: (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => objectUtils.deepClone(item));
    if (typeof obj === 'object') {
      const cloned = {};
      Object.keys(obj).forEach(key => {
        cloned[key] = objectUtils.deepClone(obj[key]);
      });
      return cloned;
    }
    return obj;
  },

  /**
   * Merge objects deeply
   * @param {Object} target - Target object
   * @param {...Object} sources - Source objects
   * @returns {Object} Merged object
   */
  deepMerge: (target, ...sources) => {
    if (!sources.length) return target;
    const source = sources.shift();

    if (objectUtils.isObject(target) && objectUtils.isObject(source)) {
      for (const key in source) {
        if (objectUtils.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          objectUtils.deepMerge(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }

    return objectUtils.deepMerge(target, ...sources);
  },

  /**
   * Check if value is a plain object
   * @param {*} item - Item to check
   * @returns {boolean} True if plain object
   */
  isObject: (item) => {
    return item && typeof item === 'object' && !Array.isArray(item);
  },

  /**
   * Pick specified keys from object
   * @param {Object} obj - Source object
   * @param {Array} keys - Keys to pick
   * @returns {Object} Object with picked keys
   */
  pick: (obj, keys) => {
    if (!objectUtils.isObject(obj) || !Array.isArray(keys)) return {};
    return keys.reduce((result, key) => {
      if (obj.hasOwnProperty(key)) {
        result[key] = obj[key];
      }
      return result;
    }, {});
  },

  /**
   * Omit specified keys from object
   * @param {Object} obj - Source object
   * @param {Array} keys - Keys to omit
   * @returns {Object} Object without omitted keys
   */
  omit: (obj, keys) => {
    if (!objectUtils.isObject(obj) || !Array.isArray(keys)) return obj;
    const result = { ...obj };
    keys.forEach(key => delete result[key]);
    return result;
  },

  /**
   * Flatten nested object with dot notation
   * @param {Object} obj - Object to flatten
   * @param {string} prefix - Key prefix
   * @returns {Object} Flattened object
   */
  flatten: (obj, prefix = '') => {
    const flattened = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(flattened, objectUtils.flatten(obj[key], newKey));
        } else {
          flattened[newKey] = obj[key];
        }
      }
    }
    return flattened;
  }
};

/**
 * Date and time utilities
 */
const dateUtils = {
  /**
   * Format date to ISO string in local timezone
   * @param {Date} date - Date object
   * @returns {string} Formatted date string
   */
  formatISO: (date = new Date()) => {
    if (!(date instanceof Date)) return null;
    return date.toISOString();
  },

  /**
   * Add days to a date
   * @param {Date} date - Source date
   * @param {number} days - Number of days to add
   * @returns {Date} New date
   */
  addDays: (date, days) => {
    if (!(date instanceof Date) || typeof days !== 'number') return null;
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  },

  /**
   * Get date difference in days
   * @param {Date} date1 - First date
   * @param {Date} date2 - Second date
   * @returns {number} Difference in days
   */
  daysDifference: (date1, date2) => {
    if (!(date1 instanceof Date) || !(date2 instanceof Date)) return 0;
    const timeDiff = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  },

  /**
   * Check if date is within range
   * @param {Date} date - Date to check
   * @param {Date} start - Start date
   * @param {Date} end - End date
   * @returns {boolean} True if within range
   */
  isWithinRange: (date, start, end) => {
    if (!(date instanceof Date) || !(start instanceof Date) || !(end instanceof Date)) {
      return false;
    }
    return date >= start && date <= end;
  },

  /**
   * Format date for display
   * @param {Date} date - Date to format
   * @param {string} format - Format type ('short', 'long', 'relative')
   * @returns {string} Formatted date string
   */
  formatDisplay: (date, format = 'short') => {
    if (!(date instanceof Date)) return '';
    
    switch (format) {
      case 'long':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'relative':
        return dateUtils.timeAgo(date);
      default:
        return date.toLocaleDateString('en-US');
    }
  },

  /**
   * Get relative time string (time ago)
   * @param {Date} date - Date to compare
   * @returns {string} Relative time string
   */
  timeAgo: (date) => {
    if (!(date instanceof Date)) return '';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return date.toLocaleDateString();
  }
};

/**
 * Validation utilities
 */
const validationUtils = {
  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid email
   */
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return typeof email === 'string' && emailRegex.test(email);
  },

  /**
   * Validate URL format
   * @param {string} url - URL to validate
   * @returns {boolean} True if valid URL
   */
  isValidUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Validate password strength
   * @param {string} password - Password to validate
   * @returns {Object} Validation result with score and feedback
   */
  validatePassword: (password) => {
    if (typeof password !== 'string') {
      return { isValid: false, score: 0, feedback: ['Password must be a string'] };
    }

    const feedback = [];
    let score = 0;

    if (password.length < 8) feedback.push('Password must be at least 8 characters long');
    else score += 1;

    if (!/[a-z]/.test(password)) feedback.push('Password must contain lowercase letters');
    else score += 1;

    if (!/[A-Z]/.test(password)) feedback.push('Password must contain uppercase letters');
    else score += 1;

    if (!/\d/.test(password)) feedback.push('Password must contain numbers');
    else score += 1;

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) feedback.push('Password must contain special characters');
    else score += 1;

    const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    return {
      isValid: feedback.length === 0,
      score,
      strength: strengthLevels[Math.min(score, strengthLevels.length - 1)],
      feedback
    };
  },

  /**
   * Validate phone number format
   * @param {string} phone - Phone number to validate
   * @returns {boolean} True if valid phone number
   */
  isValidPhone: (phone) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return typeof phone === 'string' && phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
  }
};

/**
 * Encryption and hashing utilities
 */
const cryptoUtils = {
  /**
   * Generate random string
   * @param {number} length - Length of string
   * @param {string} charset - Character set to use
   * @returns {string} Random string
   */
  generateRandomString: (length = 32, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
  },

  /**
   * Generate UUID v4
   * @returns {string} UUID string
   */
  generateUUID: () => {
    return crypto.randomUUID();
  },

  /**
   * Hash string using SHA-256
   * @param {string} text - Text to hash
   * @returns {string} Hashed string
   */
  hash: (text) => {
    return crypto.createHash('sha256').update(text).digest('hex');
  },

  /**
   * Generate secure token
   * @param {number} bytes - Number of bytes for randomness
   * @returns {string} Secure token
   */
  generateToken: (bytes = 32) => {
    return crypto.randomBytes(bytes).toString('hex');
  }
};

/**
 * Performance and monitoring utilities
 */
const performanceUtils = {
  /**
   * Create a performance timer
   * @param {string} label - Timer label
   * @returns {Function} Timer stop function
   */
  createTimer: (label = 'timer') => {
    const startTime = process.hrtime.bigint();
    return () => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
      return { label, duration };
    };
  },

  /**
   * Measure async function execution time
   * @param {Function} fn - Async function to measure
   * @param {...any} args - Function arguments
   * @returns {Promise<Object>} Result with timing info
   */
  measureAsync: async (fn, ...args) => {
    const timer = performanceUtils.createTimer();
    try {
      const result = await fn(...args);
      const timing = timer();
      return { result, timing, error: null };
    } catch (error) {
      const timing = timer();
      return { result: null, timing, error };
    }
  },

  /**
   * Debounce function execution
   * @param {Function} func - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} Debounced function
   */
  debounce: (func, delay) => {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(null, args), delay);
    };
  },

  /**
   * Throttle function execution
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled function
   */
  throttle: (func, limit) => {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func.apply(null, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};

module.exports = {
  stringUtils,
  arrayUtils,
  objectUtils,
  dateUtils,
  validationUtils,
  cryptoUtils,
  performanceUtils
};