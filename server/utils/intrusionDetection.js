const { logger } = require('./logger');
const { monitoring } = require('./monitoring');
const { cacheManager } = require('./cacheManager');
const database = require('../database');

/**
 * Intrusion Detection System
 * Provides real-time threat detection and automated response
 */

class IntrusionDetectionSystem {
  constructor() {
    this.threatPatterns = {
      // SQL Injection patterns
      sqlInjection: [
        /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
        /((\%27)|(\')|((\%3D)|(=)))/i,
        /((\%3B)|(;))/i,
        /(\b(or|and)\b\s*(\%27|\')?\s*(\%3D|=)\s*(\%27|\')?\s*(\%27|\'))/i
      ],
      
      // XSS patterns
      xss: [
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /<iframe[^>]*>.*?<\/iframe>/gi,
        /eval\s*\(/gi,
        /expression\s*\(/gi
      ],
      
      // Command injection patterns
      commandInjection: [
        /(\||;|&|\$\(|\`)/,
        /(nc|netcat|wget|curl|ping|nslookup|dig)/i,
        /(rm|mv|cp|cat|ls|ps|kill|chmod|chown)/i
      ],
      
      // Path traversal patterns
      pathTraversal: [
        /\.\.\//,
        /\.\.\\/,
        /%2e%2e%2f/i,
        /%2e%2e%5c/i,
        /\.\.%2f/i,
        /\.\.%5c/i
      ],
      
      // LDAP injection patterns
      ldapInjection: [
        /(\(|\)|\*|\||&)/,
        /(\%28|\%29|\%2a|\%7c|\%26)/i
      ]
    };
    
    this.suspiciousUserAgents = [
      /sqlmap/i,
      /nikto/i,
      /nessus/i,
      /burp/i,
      /zap/i,
      /w3af/i,
      /acunetix/i,
      /netsparker/i,
      /appscan/i,
      /websecurify/i
    ];
    
    this.rateLimits = {
      requests: { window: 60000, max: 100 }, // 100 requests per minute
      failures: { window: 300000, max: 5 },  // 5 failures per 5 minutes
      patterns: { window: 3600000, max: 3 }  // 3 pattern matches per hour
    };
    
    this.blockedIPs = new Set();
    this.suspiciousIPs = new Map();
    
    this.initializeIDS();
  }

  /**
   * Initialize Intrusion Detection System
   */
  initializeIDS() {
    // Load blocked IPs from database
    this.loadBlockedIPs();
    
    // Schedule cleanup every hour
    setInterval(() => {
      this.cleanupSuspiciousIPs();
    }, 3600000);
    
    // Schedule threat intelligence updates every 6 hours
    setInterval(() => {
      this.updateThreatIntelligence();
    }, 21600000);
    
    logger.info('Intrusion Detection System initialized', {
      threatPatterns: Object.keys(this.threatPatterns).length,
      suspiciousUserAgents: this.suspiciousUserAgents.length
    });
  }

  /**
   * Analyze incoming request for threats
   * @param {Object} req - Express request object
   * @returns {Object} Analysis result
   */
  analyzeRequest(req) {
    const analysis = {
      threats: [],
      riskScore: 0,
      blocked: false,
      suspicious: false
    };

    const clientIP = this.getClientIP(req);
    const userAgent = req.get('User-Agent') || '';
    const requestData = this.extractRequestData(req);

    // Check if IP is already blocked
    if (this.blockedIPs.has(clientIP)) {
      analysis.blocked = true;
      analysis.riskScore = 100;
      analysis.threats.push({
        type: 'blocked_ip',
        severity: 'critical',
        message: 'Request from blocked IP address'
      });
      return analysis;
    }

    // Analyze request patterns
    this.analyzePatterns(requestData, analysis);
    
    // Analyze user agent
    this.analyzeUserAgent(userAgent, analysis);
    
    // Analyze request frequency
    this.analyzeRequestFrequency(clientIP, analysis);
    
    // Check geolocation anomalies
    this.analyzeGeolocation(clientIP, req, analysis);
    
    // Analyze request timing
    this.analyzeRequestTiming(clientIP, analysis);

    // Determine if request should be blocked
    if (analysis.riskScore >= 80) {
      analysis.blocked = true;
      this.handleHighRiskRequest(clientIP, req, analysis);
    } else if (analysis.riskScore >= 50) {
      analysis.suspicious = true;
      this.handleSuspiciousRequest(clientIP, req, analysis);
    }

    return analysis;
  }

  /**
   * Extract request data for analysis
   * @param {Object} req - Express request object
   * @returns {Object} Request data
   */
  extractRequestData(req) {
    return {
      url: req.originalUrl || req.url,
      method: req.method,
      headers: req.headers,
      query: req.query,
      body: req.body,
      params: req.params,
      cookies: req.cookies
    };
  }

  /**
   * Analyze request for malicious patterns
   * @param {Object} requestData - Request data
   * @param {Object} analysis - Analysis object to update
   */
  analyzePatterns(requestData, analysis) {
    const dataToAnalyze = [
      JSON.stringify(requestData.query),
      JSON.stringify(requestData.body),
      JSON.stringify(requestData.params),
      requestData.url
    ].join(' ');

    for (const [patternType, patterns] of Object.entries(this.threatPatterns)) {
      for (const pattern of patterns) {
        if (pattern.test(dataToAnalyze)) {
          const threat = {
            type: patternType,
            severity: this.getThreatSeverity(patternType),
            message: `${patternType} pattern detected`,
            pattern: pattern.toString()
          };
          
          analysis.threats.push(threat);
          analysis.riskScore += this.getThreatScore(patternType);
          
          logger.logSecurity('THREAT_PATTERN_DETECTED', {
            type: patternType,
            pattern: pattern.toString(),
            data: dataToAnalyze.substring(0, 200)
          });
        }
      }
    }
  }

  /**
   * Analyze user agent for suspicious tools
   * @param {string} userAgent - User agent string
   * @param {Object} analysis - Analysis object to update
   */
  analyzeUserAgent(userAgent, analysis) {
    for (const suspiciousUA of this.suspiciousUserAgents) {
      if (suspiciousUA.test(userAgent)) {
        const threat = {
          type: 'suspicious_user_agent',
          severity: 'high',
          message: 'Suspicious user agent detected',
          userAgent: userAgent
        };
        
        analysis.threats.push(threat);
        analysis.riskScore += 30;
        
        logger.logSecurity('SUSPICIOUS_USER_AGENT', {
          userAgent,
          pattern: suspiciousUA.toString()
        });
        break;
      }
    }

    // Check for missing or unusual user agents
    if (!userAgent || userAgent.length < 10) {
      analysis.threats.push({
        type: 'missing_user_agent',
        severity: 'medium',
        message: 'Missing or suspicious user agent'
      });
      analysis.riskScore += 10;
    }
  }

  /**
   * Analyze request frequency for rate limiting violations
   * @param {string} clientIP - Client IP address
   * @param {Object} analysis - Analysis object to update
   */
  async analyzeRequestFrequency(clientIP, analysis) {
    try {
      // Check request rate
      const requestCount = await cacheManager.incr(`ids:requests:${clientIP}`, 1, 'rate_limit');
      if (requestCount === 1) {
        await cacheManager.set(`ids:requests:${clientIP}`, 1, 'rate_limit', this.rateLimits.requests.window / 1000);
      }

      if (requestCount > this.rateLimits.requests.max) {
        analysis.threats.push({
          type: 'rate_limit_exceeded',
          severity: 'high',
          message: `Request rate limit exceeded: ${requestCount}/${this.rateLimits.requests.max}`
        });
        analysis.riskScore += 25;
      }

      // Check pattern detection rate
      const patternCount = await cacheManager.get(`ids:patterns:${clientIP}`, 'rate_limit') || 0;
      if (patternCount > this.rateLimits.patterns.max) {
        analysis.threats.push({
          type: 'pattern_rate_exceeded',
          severity: 'critical',
          message: `Pattern detection rate exceeded: ${patternCount}/${this.rateLimits.patterns.max}`
        });
        analysis.riskScore += 40;
      }
    } catch (error) {
      logger.error('Failed to analyze request frequency', { error: error.message, clientIP });
    }
  }

  /**
   * Analyze geolocation for anomalies
   * @param {string} clientIP - Client IP address
   * @param {Object} req - Express request object
   * @param {Object} analysis - Analysis object to update
   */
  async analyzeGeolocation(clientIP, req, analysis) {
    try {
      // Skip private IP addresses
      if (this.isPrivateIP(clientIP)) {
        return;
      }

      // Get geolocation data (would integrate with GeoIP service)
      const geoData = await this.getGeolocation(clientIP);
      
      if (geoData) {
        // Check for high-risk countries
        const highRiskCountries = ['CN', 'RU', 'KP', 'IR'];
        if (highRiskCountries.includes(geoData.country)) {
          analysis.threats.push({
            type: 'high_risk_country',
            severity: 'medium',
            message: `Request from high-risk country: ${geoData.country}`,
            country: geoData.country
          });
          analysis.riskScore += 15;
        }

        // Check for VPN/Proxy usage
        if (geoData.isProxy || geoData.isVPN) {
          analysis.threats.push({
            type: 'proxy_detected',
            severity: 'medium',
            message: 'Request through proxy/VPN detected'
          });
          analysis.riskScore += 10;
        }
      }
    } catch (error) {
      logger.error('Failed to analyze geolocation', { error: error.message, clientIP });
    }
  }

  /**
   * Analyze request timing patterns
   * @param {string} clientIP - Client IP address
   * @param {Object} analysis - Analysis object to update
   */
  async analyzeRequestTiming(clientIP, analysis) {
    try {
      const now = Date.now();
      const timingKey = `ids:timing:${clientIP}`;
      
      // Get previous request timestamps
      const timestamps = await cacheManager.get(timingKey, 'analytics') || [];
      timestamps.push(now);
      
      // Keep only last 10 timestamps
      const recentTimestamps = timestamps.slice(-10);
      await cacheManager.set(timingKey, recentTimestamps, 'analytics', 3600);

      if (recentTimestamps.length >= 5) {
        // Calculate request intervals
        const intervals = [];
        for (let i = 1; i < recentTimestamps.length; i++) {
          intervals.push(recentTimestamps[i] - recentTimestamps[i - 1]);
        }

        // Check for automated/bot-like behavior
        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
        const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
        
        // Very consistent timing suggests automation
        if (variance < 100 && avgInterval < 5000) {
          analysis.threats.push({
            type: 'automated_behavior',
            severity: 'medium',
            message: 'Automated request pattern detected',
            avgInterval,
            variance
          });
          analysis.riskScore += 20;
        }
      }
    } catch (error) {
      logger.error('Failed to analyze request timing', { error: error.message, clientIP });
    }
  }

  /**
   * Handle high-risk requests
   * @param {string} clientIP - Client IP address
   * @param {Object} req - Express request object
   * @param {Object} analysis - Analysis result
   */
  async handleHighRiskRequest(clientIP, req, analysis) {
    try {
      // Add to suspicious IPs
      this.suspiciousIPs.set(clientIP, {
        firstSeen: Date.now(),
        lastSeen: Date.now(),
        riskScore: analysis.riskScore,
        threats: analysis.threats.length,
        blocked: false
      });

      // Log security event
      logger.logSecurity('HIGH_RISK_REQUEST_BLOCKED', {
        clientIP,
        riskScore: analysis.riskScore,
        threats: analysis.threats,
        url: req.originalUrl,
        userAgent: req.get('User-Agent')
      });

      // Trigger monitoring alert
      monitoring.triggerAlert('high_risk_request', {
        level: 'critical',
        clientIP,
        riskScore: analysis.riskScore,
        threatsCount: analysis.threats.length
      });

      // Consider temporary IP blocking for repeated offenses
      const offenseCount = await this.getOffenseCount(clientIP);
      if (offenseCount >= 3) {
        await this.blockIP(clientIP, 'Repeated high-risk requests', 3600); // 1 hour block
      }
    } catch (error) {
      logger.error('Failed to handle high-risk request', { error: error.message, clientIP });
    }
  }

  /**
   * Handle suspicious requests
   * @param {string} clientIP - Client IP address
   * @param {Object} req - Express request object
   * @param {Object} analysis - Analysis result
   */
  async handleSuspiciousRequest(clientIP, req, analysis) {
    try {
      // Update suspicious IP tracking
      const existing = this.suspiciousIPs.get(clientIP);
      if (existing) {
        existing.lastSeen = Date.now();
        existing.riskScore = Math.max(existing.riskScore, analysis.riskScore);
        existing.threats += analysis.threats.length;
      } else {
        this.suspiciousIPs.set(clientIP, {
          firstSeen: Date.now(),
          lastSeen: Date.now(),
          riskScore: analysis.riskScore,
          threats: analysis.threats.length,
          blocked: false
        });
      }

      // Log security event
      logger.logSecurity('SUSPICIOUS_REQUEST_DETECTED', {
        clientIP,
        riskScore: analysis.riskScore,
        threats: analysis.threats,
        url: req.originalUrl
      });

      // Increment pattern count for rate limiting
      if (analysis.threats.length > 0) {
        await cacheManager.incr(`ids:patterns:${clientIP}`, 1, 'rate_limit');
      }
    } catch (error) {
      logger.error('Failed to handle suspicious request', { error: error.message, clientIP });
    }
  }

  /**
   * Block IP address
   * @param {string} ip - IP address to block
   * @param {string} reason - Reason for blocking
   * @param {number} duration - Block duration in seconds (0 = permanent)
   */
  async blockIP(ip, reason, duration = 0) {
    try {
      this.blockedIPs.add(ip);
      
      // Store in database
      const db = database.get();
      await db.run(`
        INSERT OR REPLACE INTO blocked_ips (ip, reason, blocked_at, expires_at, created_at)
        VALUES (?, ?, ?, ?, ?)
      `, [
        ip,
        reason,
        new Date().toISOString(),
        duration > 0 ? new Date(Date.now() + duration * 1000).toISOString() : null,
        new Date().toISOString()
      ]);

      // Cache the block
      if (duration > 0) {
        await cacheManager.set(`blocked_ip:${ip}`, true, 'security', duration);
      }

      logger.logSecurity('IP_BLOCKED', {
        ip,
        reason,
        duration: duration || 'permanent'
      });

      monitoring.triggerAlert('ip_blocked', {
        level: 'warning',
        ip,
        reason,
        duration
      });
    } catch (error) {
      logger.error('Failed to block IP', { error: error.message, ip });
    }
  }

  /**
   * Unblock IP address
   * @param {string} ip - IP address to unblock
   */
  async unblockIP(ip) {
    try {
      this.blockedIPs.delete(ip);
      
      // Remove from database
      const db = database.get();
      await db.run('DELETE FROM blocked_ips WHERE ip = ?', [ip]);
      
      // Remove from cache
      await cacheManager.del(`blocked_ip:${ip}`, 'security');

      logger.logSecurity('IP_UNBLOCKED', { ip });
    } catch (error) {
      logger.error('Failed to unblock IP', { error: error.message, ip });
    }
  }

  /**
   * Get client IP address
   * @param {Object} req - Express request object
   * @returns {string} Client IP address
   */
  getClientIP(req) {
    return req.ip || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           '0.0.0.0';
  }

  /**
   * Check if IP is private
   * @param {string} ip - IP address
   * @returns {boolean} True if private IP
   */
  isPrivateIP(ip) {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[01])\./,
      /^192\.168\./,
      /^127\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/
    ];
    
    return privateRanges.some(range => range.test(ip));
  }

  /**
   * Get threat severity level
   * @param {string} threatType - Type of threat
   * @returns {string} Severity level
   */
  getThreatSeverity(threatType) {
    const severityMap = {
      sqlInjection: 'critical',
      xss: 'high',
      commandInjection: 'critical',
      pathTraversal: 'high',
      ldapInjection: 'high'
    };
    
    return severityMap[threatType] || 'medium';
  }

  /**
   * Get threat score
   * @param {string} threatType - Type of threat
   * @returns {number} Threat score
   */
  getThreatScore(threatType) {
    const scoreMap = {
      sqlInjection: 40,
      xss: 30,
      commandInjection: 40,
      pathTraversal: 25,
      ldapInjection: 25
    };
    
    return scoreMap[threatType] || 15;
  }

  /**
   * Get offense count for IP
   * @param {string} ip - IP address
   * @returns {number} Offense count
   */
  async getOffenseCount(ip) {
    try {
      const count = await cacheManager.get(`offense_count:${ip}`, 'security') || 0;
      await cacheManager.incr(`offense_count:${ip}`, 1, 'security');
      return count;
    } catch (error) {
      logger.error('Failed to get offense count', { error: error.message, ip });
      return 0;
    }
  }

  /**
   * Load blocked IPs from database
   */
  async loadBlockedIPs() {
    try {
      const db = database.get();
      const blockedIPs = await db.all(`
        SELECT ip FROM blocked_ips 
        WHERE expires_at IS NULL OR expires_at > datetime('now')
      `);
      
      blockedIPs.forEach(row => {
        this.blockedIPs.add(row.ip);
      });
      
      logger.info('Loaded blocked IPs', { count: blockedIPs.length });
    } catch (error) {
      logger.error('Failed to load blocked IPs', { error: error.message });
    }
  }

  /**
   * Clean up expired suspicious IPs
   */
  cleanupSuspiciousIPs() {
    const now = Date.now();
    const expireTime = 24 * 60 * 60 * 1000; // 24 hours
    
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (now - data.lastSeen > expireTime) {
        this.suspiciousIPs.delete(ip);
      }
    }
    
    logger.debug('Cleaned up suspicious IPs', {
      remaining: this.suspiciousIPs.size
    });
  }

  /**
   * Update threat intelligence
   */
  async updateThreatIntelligence() {
    try {
      // This would integrate with threat intelligence feeds
      // For now, we'll just log the update
      logger.info('Threat intelligence updated');
    } catch (error) {
      logger.error('Failed to update threat intelligence', { error: error.message });
    }
  }

  /**
   * Get geolocation data for IP
   * @param {string} ip - IP address
   * @returns {Object} Geolocation data
   */
  async getGeolocation(ip) {
    try {
      // This would integrate with a GeoIP service
      // For now, return mock data
      return {
        country: 'US',
        region: 'CA',
        city: 'San Francisco',
        isProxy: false,
        isVPN: false
      };
    } catch (error) {
      logger.error('Failed to get geolocation', { error: error.message, ip });
      return null;
    }
  }

  /**
   * Get IDS statistics
   * @returns {Object} IDS statistics
   */
  getStatistics() {
    return {
      blockedIPs: this.blockedIPs.size,
      suspiciousIPs: this.suspiciousIPs.size,
      threatPatterns: Object.keys(this.threatPatterns).length,
      suspiciousUserAgents: this.suspiciousUserAgents.length,
      rateLimits: this.rateLimits
    };
  }

  /**
   * Get blocked IPs list
   * @returns {Array} List of blocked IPs
   */
  getBlockedIPs() {
    return Array.from(this.blockedIPs);
  }

  /**
   * Get suspicious IPs list
   * @returns {Array} List of suspicious IPs with data
   */
  getSuspiciousIPs() {
    return Array.from(this.suspiciousIPs.entries()).map(([ip, data]) => ({
      ip,
      ...data
    }));
  }
}

/**
 * IDS Middleware for Express
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function createIDSMiddleware(options = {}) {
  const config = {
    enabled: options.enabled !== false,
    blockHighRisk: options.blockHighRisk !== false,
    logSuspicious: options.logSuspicious !== false,
    ...options
  };

  return async (req, res, next) => {
    if (!config.enabled) {
      return next();
    }

    try {
      const analysis = intrusionDetection.analyzeRequest(req);

      // Add analysis to request for downstream use
      req.idsAnalysis = analysis;

      // Block high-risk requests
      if (config.blockHighRisk && analysis.blocked) {
        logger.logSecurity('REQUEST_BLOCKED_BY_IDS', {
          ip: intrusionDetection.getClientIP(req),
          url: req.originalUrl,
          riskScore: analysis.riskScore,
          threats: analysis.threats
        });

        return res.status(403).json({
          success: false,
          error: 'Request blocked by security system',
          code: 'SECURITY_VIOLATION'
        });
      }

      // Log suspicious requests
      if (config.logSuspicious && analysis.suspicious) {
        logger.logSecurity('SUSPICIOUS_REQUEST_DETECTED', {
          ip: intrusionDetection.getClientIP(req),
          url: req.originalUrl,
          riskScore: analysis.riskScore,
          threats: analysis.threats
        });
      }

      next();
    } catch (error) {
      logger.error('IDS middleware error', {
        error: error.message,
        url: req.originalUrl
      });

      // Continue processing on IDS error
      next();
    }
  };
}

/**
 * IDS Statistics endpoint
 */
async function getIDSStats(req, res) {
  try {
    const stats = intrusionDetection.getStatistics();
    const blockedIPs = intrusionDetection.getBlockedIPs();
    const suspiciousIPs = intrusionDetection.getSuspiciousIPs();

    res.json({
      success: true,
      data: {
        statistics: stats,
        blockedIPs: blockedIPs.slice(0, 100), // Limit for performance
        suspiciousIPs: suspiciousIPs.slice(0, 100),
        summary: {
          totalBlocked: blockedIPs.length,
          totalSuspicious: suspiciousIPs.length,
          systemStatus: 'active'
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get IDS statistics', { error: error.message });

    res.status(500).json({
      success: false,
      error: 'Failed to retrieve IDS statistics',
      code: 'IDS_STATS_ERROR'
    });
  }
}

/**
 * Block IP endpoint
 */
async function blockIPEndpoint(req, res) {
  try {
    const { ip, reason, duration } = req.body;

    if (!ip) {
      return res.status(400).json({
        success: false,
        error: 'IP address is required',
        code: 'VALIDATION_ERROR'
      });
    }

    await intrusionDetection.blockIP(ip, reason || 'Manual block', duration || 0);

    res.json({
      success: true,
      message: 'IP address blocked successfully'
    });
  } catch (error) {
    logger.error('Failed to block IP', { error: error.message });

    res.status(500).json({
      success: false,
      error: 'Failed to block IP address',
      code: 'BLOCK_IP_ERROR'
    });
  }
}

/**
 * Unblock IP endpoint
 */
async function unblockIPEndpoint(req, res) {
  try {
    const { ip } = req.params;

    await intrusionDetection.unblockIP(ip);

    res.json({
      success: true,
      message: 'IP address unblocked successfully'
    });
  } catch (error) {
    logger.error('Failed to unblock IP', { error: error.message });

    res.status(500).json({
      success: false,
      error: 'Failed to unblock IP address',
      code: 'UNBLOCK_IP_ERROR'
    });
  }
}

// Create singleton instance
const intrusionDetection = new IntrusionDetectionSystem();

module.exports = {
  IntrusionDetectionSystem,
  intrusionDetection,
  createIDSMiddleware,
  getIDSStats,
  blockIPEndpoint,
  unblockIPEndpoint
};
