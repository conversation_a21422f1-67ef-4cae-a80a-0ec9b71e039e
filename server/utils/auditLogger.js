const fs = require('fs');
const path = require('path');

/**
 * Audit Logger for security event tracking and compliance
 */
class AuditLogger {
  constructor(options = {}) {
    this.logDir = options.logDir || path.join(__dirname, '..', 'logs', 'audit');
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile !== false;
    this.maxFileSize = options.maxFileSize || 50 * 1024 * 1024; // 50MB
    this.maxFiles = options.maxFiles || 10;
    
    this.ensureLogDirectory();
  }

  /**
   * Ensure audit log directory exists
   */
  ensureLogDirectory() {
    if (this.enableFile && !fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log authentication events
   */
  logAuth(action, userId, metadata = {}) {
    this.logEvent('AUTH', action, userId, metadata);
  }

  /**
   * Log authorization events
   */
  logAuthz(action, userId, resource, metadata = {}) {
    this.logEvent('AUTHZ', action, userId, { resource, ...metadata });
  }

  /**
   * Log data access events
   */
  logDataAccess(action, userId, dataType, metadata = {}) {
    this.logEvent('DATA_ACCESS', action, userId, { dataType, ...metadata });
  }

  /**
   * Log security events
   */
  logSecurity(action, userId, severity = 'medium', metadata = {}) {
    this.logEvent('SECURITY', action, userId, { severity, ...metadata });
  }

  /**
   * Log administrative actions
   */
  logAdmin(action, userId, target, metadata = {}) {
    this.logEvent('ADMIN', action, userId, { target, ...metadata });
  }

  /**
   * Log compliance events
   */
  logCompliance(action, userId, regulation, metadata = {}) {
    this.logEvent('COMPLIANCE', action, userId, { regulation, ...metadata });
  }

  /**
   * Core event logging method
   */
  logEvent(category, action, userId, metadata = {}) {
    const event = {
      timestamp: new Date().toISOString(),
      category,
      action,
      userId: userId || 'anonymous',
      sessionId: metadata.sessionId || null,
      ipAddress: metadata.ip || metadata.ipAddress || null,
      userAgent: metadata.userAgent || null,
      resource: metadata.resource || null,
      outcome: metadata.outcome || 'unknown',
      severity: metadata.severity || 'info',
      details: this.sanitizeMetadata(metadata),
      traceId: metadata.traceId || this.generateTraceId()
    };

    // Write to console if enabled
    if (this.enableConsole) {
      console.log(`[AUDIT] ${JSON.stringify(event)}`);
    }

    // Write to file if enabled
    if (this.enableFile) {
      this.writeToFile(event);
    }

    // In production, also send to SIEM/log aggregation service
    this.sendToExternalService(event);
  }

  /**
   * Write audit event to file
   */
  writeToFile(event) {
    try {
      const logFile = path.join(this.logDir, `audit-${this.getDateString()}.log`);
      const logEntry = JSON.stringify(event) + '\n';

      // Check file size and rotate if necessary
      if (fs.existsSync(logFile)) {
        const stats = fs.statSync(logFile);
        if (stats.size > this.maxFileSize) {
          this.rotateLogFile(logFile);
        }
      }

      fs.appendFileSync(logFile, logEntry);
    } catch (error) {
      console.error('Failed to write audit log:', error.message);
    }
  }

  /**
   * Rotate log files when they get too large
   */
  rotateLogFile(logFile) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const rotatedFile = `${logFile}.${timestamp}`;
      
      fs.renameSync(logFile, rotatedFile);
      
      // Compress old log file (optional)
      this.compressLogFile(rotatedFile);
      
      // Clean up old log files
      this.cleanupOldLogFiles();
    } catch (error) {
      console.error('Failed to rotate audit log:', error.message);
    }
  }

  /**
   * Compress log file (placeholder for production implementation)
   */
  compressLogFile(filePath) {
    // In production, implement actual compression
    // Example: gzip the file to save space
    console.log(`Would compress log file: ${filePath}`);
  }

  /**
   * Clean up old log files beyond retention period
   */
  cleanupOldLogFiles() {
    try {
      const files = fs.readdirSync(this.logDir);
      const auditFiles = files
        .filter(file => file.startsWith('audit-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          mtime: fs.statSync(path.join(this.logDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      // Keep only the most recent files
      if (auditFiles.length > this.maxFiles) {
        const filesToDelete = auditFiles.slice(this.maxFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          console.log(`Deleted old audit log: ${file.name}`);
        });
      }
    } catch (error) {
      console.error('Failed to cleanup old audit logs:', error.message);
    }
  }

  /**
   * Send audit event to external service (SIEM, log aggregation)
   */
  sendToExternalService(event) {
    // In production, implement actual integration with:
    // - Splunk
    // - ELK Stack
    // - Azure Monitor
    // - AWS CloudWatch
    // - Datadog
    
    if (process.env.AUDIT_WEBHOOK_URL) {
      // Example webhook integration
      this.sendWebhook(event);
    }
  }

  /**
   * Send audit event via webhook
   */
  async sendWebhook(event) {
    try {
      const response = await fetch(process.env.AUDIT_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.AUDIT_WEBHOOK_TOKEN || ''}`
        },
        body: JSON.stringify(event)
      });

      if (!response.ok) {
        console.error('Failed to send audit webhook:', response.statusText);
      }
    } catch (error) {
      console.error('Audit webhook error:', error.message);
    }
  }

  /**
   * Sanitize metadata to remove sensitive information
   */
  sanitizeMetadata(metadata) {
    const sanitized = { ...metadata };
    
    // Remove sensitive fields
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'credential',
      'authorization', 'cookie', 'session'
    ];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Remove nested sensitive data
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'string' && sensitiveFields.some(f => key.toLowerCase().includes(f))) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Generate unique trace ID for request correlation
   */
  generateTraceId() {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current date string for log file naming
   */
  getDateString() {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Query audit logs by criteria
   */
  async queryLogs(criteria = {}) {
    try {
      const logs = [];
      const files = fs.readdirSync(this.logDir);
      
      for (const file of files) {
        if (file.startsWith('audit-') && file.endsWith('.log')) {
          const filePath = path.join(this.logDir, file);
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.trim().split('\n');
          
          for (const line of lines) {
            try {
              const event = JSON.parse(line);
              if (this.matchesCriteria(event, criteria)) {
                logs.push(event);
              }
            } catch (parseError) {
              // Skip invalid JSON lines
            }
          }
        }
      }
      
      return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('Failed to query audit logs:', error.message);
      return [];
    }
  }

  /**
   * Check if event matches query criteria
   */
  matchesCriteria(event, criteria) {
    for (const [key, value] of Object.entries(criteria)) {
      if (event[key] !== value) {
        return false;
      }
    }
    return true;
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const logs = await this.queryLogs();
    const periodLogs = logs.filter(event => {
      const eventDate = new Date(event.timestamp);
      return eventDate >= start && eventDate <= end;
    });

    const report = {
      period: { startDate, endDate },
      totalEvents: periodLogs.length,
      categories: {},
      users: {},
      securityEvents: periodLogs.filter(e => e.category === 'SECURITY'),
      authFailures: periodLogs.filter(e => e.category === 'AUTH' && e.outcome === 'failure'),
      dataAccess: periodLogs.filter(e => e.category === 'DATA_ACCESS'),
      adminActions: periodLogs.filter(e => e.category === 'ADMIN')
    };

    // Aggregate by category
    for (const event of periodLogs) {
      report.categories[event.category] = (report.categories[event.category] || 0) + 1;
      if (event.userId && event.userId !== 'anonymous') {
        report.users[event.userId] = (report.users[event.userId] || 0) + 1;
      }
    }

    return report;
  }
}

// Create singleton instance
const auditLogger = new AuditLogger({
  enableConsole: process.env.NODE_ENV === 'development',
  enableFile: true
});

module.exports = auditLogger;