const { logger } = require('./logger');
const database = require('../database');

/**
 * Error Tracking Service
 * Provides comprehensive error tracking, aggregation, and alerting
 */

class ErrorTracker {
  constructor() {
    this.errorCounts = new Map();
    this.errorPatterns = new Map();
    this.alertThresholds = {
      errorRate: 10, // errors per minute
      uniqueErrors: 5, // unique errors per hour
      criticalErrors: 1 // critical errors (immediate alert)
    };
    
    this.initializeErrorTracking();
  }

  /**
   * Initialize error tracking
   */
  initializeErrorTracking() {
    // Clean up old error counts every hour
    setInterval(() => {
      this.cleanupOldErrors();
    }, 3600000); // 1 hour

    // Check error patterns every 5 minutes
    setInterval(() => {
      this.analyzeErrorPatterns();
    }, 300000); // 5 minutes

    logger.info('Error tracking service initialized');
  }

  /**
   * Track an error
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   */
  async trackError(error, context = {}) {
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString(),
        context: {
          ...context,
          userAgent: context.req?.get('User-Agent'),
          ip: context.req?.ip,
          url: context.req?.originalUrl,
          method: context.req?.method,
          userId: context.req?.user?.id,
          correlationId: context.req?.correlationId
        }
      };

      // Generate error fingerprint for deduplication
      const fingerprint = this.generateErrorFingerprint(error, context);
      errorData.fingerprint = fingerprint;

      // Classify error severity
      const severity = this.classifyErrorSeverity(error, context);
      errorData.severity = severity;

      // Store error in database
      await this.storeError(errorData);

      // Update error counts
      this.updateErrorCounts(fingerprint, severity);

      // Check for immediate alerts
      if (severity === 'critical') {
        await this.triggerImmediateAlert(errorData);
      }

      // Log error
      logger.logError(error, {
        fingerprint,
        severity,
        ...context
      });

      return fingerprint;
    } catch (trackingError) {
      logger.error('Failed to track error', {
        originalError: error.message,
        trackingError: trackingError.message
      });
    }
  }

  /**
   * Generate error fingerprint for deduplication
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   * @returns {string} Error fingerprint
   */
  generateErrorFingerprint(error, context) {
    const crypto = require('crypto');
    
    // Create fingerprint based on error type, message, and stack trace location
    const stackLines = error.stack ? error.stack.split('\n') : [];
    const relevantStack = stackLines.slice(0, 3).join('\n'); // First 3 lines
    
    const fingerprintData = [
      error.name,
      error.message,
      relevantStack,
      context.req?.originalUrl || 'unknown'
    ].join('|');
    
    return crypto.createHash('sha256').update(fingerprintData).digest('hex').substring(0, 16);
  }

  /**
   * Classify error severity
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   * @returns {string} Severity level
   */
  classifyErrorSeverity(error, context) {
    // Critical errors
    if (error.name === 'DatabaseError' || 
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('Authentication failed') ||
        context.statusCode >= 500) {
      return 'critical';
    }

    // High severity errors
    if (error.name === 'ValidationError' ||
        error.name === 'SecurityError' ||
        context.statusCode >= 400) {
      return 'high';
    }

    // Medium severity errors
    if (error.name === 'TypeError' ||
        error.name === 'ReferenceError') {
      return 'medium';
    }

    // Low severity (warnings, etc.)
    return 'low';
  }

  /**
   * Store error in database
   * @param {Object} errorData - Error data
   */
  async storeError(errorData) {
    try {
      const db = database.get();
      
      await db.run(`
        INSERT INTO error_logs (
          fingerprint, message, stack, name, severity,
          context, user_id, ip_address, url, method,
          user_agent, correlation_id, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        errorData.fingerprint,
        errorData.message,
        errorData.stack,
        errorData.name,
        errorData.severity,
        JSON.stringify(errorData.context),
        errorData.context.userId,
        errorData.context.ip,
        errorData.context.url,
        errorData.context.method,
        errorData.context.userAgent,
        errorData.context.correlationId,
        errorData.timestamp
      ]);
    } catch (dbError) {
      logger.error('Failed to store error in database', {
        error: dbError.message,
        originalError: errorData.message
      });
    }
  }

  /**
   * Update error counts for rate tracking
   * @param {string} fingerprint - Error fingerprint
   * @param {string} severity - Error severity
   */
  updateErrorCounts(fingerprint, severity) {
    const now = Date.now();
    const minute = Math.floor(now / 60000); // Current minute
    
    // Update per-minute counts
    const minuteKey = `${minute}`;
    if (!this.errorCounts.has(minuteKey)) {
      this.errorCounts.set(minuteKey, { total: 0, critical: 0, unique: new Set() });
    }
    
    const minuteData = this.errorCounts.get(minuteKey);
    minuteData.total++;
    minuteData.unique.add(fingerprint);
    
    if (severity === 'critical') {
      minuteData.critical++;
    }
  }

  /**
   * Clean up old error counts
   */
  cleanupOldErrors() {
    const now = Date.now();
    const oneHourAgo = Math.floor((now - 3600000) / 60000);
    
    // Remove counts older than 1 hour
    for (const [key] of this.errorCounts) {
      if (parseInt(key) < oneHourAgo) {
        this.errorCounts.delete(key);
      }
    }
  }

  /**
   * Analyze error patterns and trigger alerts
   */
  async analyzeErrorPatterns() {
    try {
      const now = Date.now();
      const currentMinute = Math.floor(now / 60000);
      
      // Check error rate in last 5 minutes
      let totalErrors = 0;
      let criticalErrors = 0;
      const uniqueErrors = new Set();
      
      for (let i = 0; i < 5; i++) {
        const minute = currentMinute - i;
        const minuteData = this.errorCounts.get(minute.toString());
        
        if (minuteData) {
          totalErrors += minuteData.total;
          criticalErrors += minuteData.critical;
          minuteData.unique.forEach(fp => uniqueErrors.add(fp));
        }
      }
      
      const errorRate = totalErrors / 5; // errors per minute
      
      // Check thresholds and trigger alerts
      if (errorRate > this.alertThresholds.errorRate) {
        await this.triggerErrorRateAlert(errorRate, totalErrors);
      }
      
      if (uniqueErrors.size > this.alertThresholds.uniqueErrors) {
        await this.triggerUniqueErrorsAlert(uniqueErrors.size);
      }
      
      if (criticalErrors > 0) {
        await this.triggerCriticalErrorsAlert(criticalErrors);
      }
      
    } catch (error) {
      logger.error('Failed to analyze error patterns', { error: error.message });
    }
  }

  /**
   * Trigger immediate alert for critical errors
   * @param {Object} errorData - Error data
   */
  async triggerImmediateAlert(errorData) {
    const alert = {
      type: 'critical_error',
      level: 'critical',
      timestamp: new Date().toISOString(),
      data: {
        message: errorData.message,
        fingerprint: errorData.fingerprint,
        context: errorData.context
      }
    };
    
    await this.sendAlert(alert);
  }

  /**
   * Trigger error rate alert
   * @param {number} rate - Current error rate
   * @param {number} total - Total errors
   */
  async triggerErrorRateAlert(rate, total) {
    const alert = {
      type: 'high_error_rate',
      level: 'warning',
      timestamp: new Date().toISOString(),
      data: {
        errorRate: rate.toFixed(2),
        totalErrors: total,
        threshold: this.alertThresholds.errorRate,
        timeWindow: '5 minutes'
      }
    };
    
    await this.sendAlert(alert);
  }

  /**
   * Trigger unique errors alert
   * @param {number} count - Number of unique errors
   */
  async triggerUniqueErrorsAlert(count) {
    const alert = {
      type: 'many_unique_errors',
      level: 'warning',
      timestamp: new Date().toISOString(),
      data: {
        uniqueErrors: count,
        threshold: this.alertThresholds.uniqueErrors,
        timeWindow: '5 minutes'
      }
    };
    
    await this.sendAlert(alert);
  }

  /**
   * Trigger critical errors alert
   * @param {number} count - Number of critical errors
   */
  async triggerCriticalErrorsAlert(count) {
    const alert = {
      type: 'critical_errors_detected',
      level: 'critical',
      timestamp: new Date().toISOString(),
      data: {
        criticalErrors: count,
        timeWindow: '5 minutes'
      }
    };
    
    await this.sendAlert(alert);
  }

  /**
   * Send alert
   * @param {Object} alert - Alert object
   */
  async sendAlert(alert) {
    try {
      // Log alert
      logger.warn(`Error tracking alert: ${alert.type}`, alert);
      
      // Store alert in database
      const db = database.get();
      await db.run(`
        INSERT INTO audit_logs (
          action, resource_type, metadata, created_at
        ) VALUES (?, ?, ?, ?)
      `, [
        'ERROR_ALERT_TRIGGERED',
        'error_tracking',
        JSON.stringify(alert),
        new Date().toISOString()
      ]);
      
      // Here you would integrate with your alerting system
      // Examples: Slack, email, PagerDuty, etc.
      
    } catch (error) {
      logger.error('Failed to send error tracking alert', {
        error: error.message,
        alert
      });
    }
  }

  /**
   * Get error statistics
   * @param {number} hours - Number of hours to look back
   * @returns {Object} Error statistics
   */
  async getErrorStatistics(hours = 24) {
    try {
      const db = database.get();
      
      const stats = await db.get(`
        SELECT 
          COUNT(*) as total_errors,
          COUNT(DISTINCT fingerprint) as unique_errors,
          COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_errors,
          COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_errors,
          COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_errors,
          COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_errors
        FROM error_logs
        WHERE created_at > datetime('now', '-${hours} hours')
      `);
      
      return stats;
    } catch (error) {
      logger.error('Failed to get error statistics', { error: error.message });
      return null;
    }
  }

  /**
   * Get top errors by frequency
   * @param {number} limit - Number of errors to return
   * @param {number} hours - Hours to look back
   * @returns {Array} Top errors
   */
  async getTopErrors(limit = 10, hours = 24) {
    try {
      const db = database.get();
      
      const topErrors = await db.all(`
        SELECT 
          fingerprint,
          message,
          name,
          severity,
          COUNT(*) as count,
          MAX(created_at) as last_occurrence
        FROM error_logs
        WHERE created_at > datetime('now', '-${hours} hours')
        GROUP BY fingerprint
        ORDER BY count DESC
        LIMIT ?
      `, [limit]);
      
      return topErrors;
    } catch (error) {
      logger.error('Failed to get top errors', { error: error.message });
      return [];
    }
  }
}

// Create singleton instance
const errorTracker = new ErrorTracker();

module.exports = {
  ErrorTracker,
  errorTracker
};
