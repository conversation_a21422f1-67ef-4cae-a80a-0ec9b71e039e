const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');
const { logger } = require('./logger');
const encryptionService = require('./encryptionService');
const database = require('../database');

/**
 * Backup & Recovery Service
 * Provides automated database backups with encryption and verification
 */

class BackupService {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || path.join(__dirname, '../backups');
    this.encryptionKey = process.env.BACKUP_ENCRYPTION_KEY || process.env.ENCRYPTION_MASTER_KEY;
    this.retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
    this.compressionEnabled = process.env.BACKUP_COMPRESSION !== 'false';
    this.s3Config = {
      bucket: process.env.BACKUP_S3_BUCKET,
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    };
    
    this.initializeBackupService();
  }

  /**
   * Initialize backup service
   */
  async initializeBackupService() {
    try {
      // Ensure backup directory exists
      await this.ensureBackupDirectory();
      
      // Schedule automatic backups
      this.scheduleBackups();
      
      logger.info('Backup service initialized', {
        backupDir: this.backupDir,
        retentionDays: this.retentionDays,
        compressionEnabled: this.compressionEnabled,
        s3Enabled: !!this.s3Config.bucket
      });
    } catch (error) {
      logger.error('Failed to initialize backup service', { error: error.message });
      throw error;
    }
  }

  /**
   * Ensure backup directory exists
   */
  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupDir);
    } catch (error) {
      await fs.mkdir(this.backupDir, { recursive: true });
      logger.info('Created backup directory', { path: this.backupDir });
    }
  }

  /**
   * Schedule automatic backups
   */
  scheduleBackups() {
    const schedule = process.env.BACKUP_SCHEDULE || '0 2 * * *'; // Daily at 2 AM
    
    // For this implementation, we'll use a simple interval
    // In production, you'd use a proper cron scheduler like node-cron
    const backupInterval = 24 * 60 * 60 * 1000; // 24 hours
    
    setInterval(async () => {
      try {
        await this.createBackup();
        await this.cleanupOldBackups();
      } catch (error) {
        logger.error('Scheduled backup failed', { error: error.message });
      }
    }, backupInterval);
    
    logger.info('Backup schedule configured', { schedule });
  }

  /**
   * Create a complete database backup
   * @param {Object} options - Backup options
   * @returns {Object} Backup result
   */
  async createBackup(options = {}) {
    const startTime = Date.now();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = `backup_${timestamp}_${crypto.randomBytes(4).toString('hex')}`;
    
    logger.info('Starting database backup', { backupId });
    
    try {
      // Create backup metadata
      const metadata = {
        id: backupId,
        timestamp: new Date().toISOString(),
        type: options.type || 'full',
        compression: this.compressionEnabled,
        encryption: !!this.encryptionKey,
        version: process.env.npm_package_version || '1.0.0'
      };

      // Export database
      const dumpFile = await this.exportDatabase(backupId);
      metadata.originalSize = (await fs.stat(dumpFile)).size;

      // Compress if enabled
      let backupFile = dumpFile;
      if (this.compressionEnabled) {
        backupFile = await this.compressBackup(dumpFile);
        metadata.compressedSize = (await fs.stat(backupFile)).size;
        metadata.compressionRatio = (metadata.compressedSize / metadata.originalSize).toFixed(2);
        
        // Remove uncompressed file
        await fs.unlink(dumpFile);
      }

      // Encrypt backup
      if (this.encryptionKey) {
        const encryptedFile = await this.encryptBackup(backupFile);
        metadata.encryptedSize = (await fs.stat(encryptedFile)).size;
        
        // Remove unencrypted file
        await fs.unlink(backupFile);
        backupFile = encryptedFile;
      }

      // Generate checksum
      metadata.checksum = await this.generateChecksum(backupFile);
      metadata.filePath = backupFile;
      metadata.fileName = path.basename(backupFile);

      // Save metadata
      const metadataFile = path.join(this.backupDir, `${backupId}.metadata.json`);
      await fs.writeFile(metadataFile, JSON.stringify(metadata, null, 2));

      // Upload to S3 if configured
      if (this.s3Config.bucket) {
        try {
          await this.uploadToS3(backupFile, metadata);
          metadata.s3Uploaded = true;
          metadata.s3Key = `backups/${metadata.fileName}`;
        } catch (s3Error) {
          logger.warn('Failed to upload backup to S3', { 
            error: s3Error.message, 
            backupId 
          });
          metadata.s3Uploaded = false;
          metadata.s3Error = s3Error.message;
        }
      }

      // Verify backup integrity
      const verificationResult = await this.verifyBackup(backupFile, metadata);
      metadata.verified = verificationResult.success;
      metadata.verificationDetails = verificationResult;

      // Update metadata with final information
      metadata.duration = Date.now() - startTime;
      metadata.status = metadata.verified ? 'completed' : 'failed';
      
      // Save updated metadata
      await fs.writeFile(metadataFile, JSON.stringify(metadata, null, 2));

      // Log backup to database
      await this.logBackupToDatabase(metadata);

      const duration = Date.now() - startTime;
      logger.info('Database backup completed', {
        backupId,
        duration,
        size: metadata.encryptedSize || metadata.compressedSize || metadata.originalSize,
        verified: metadata.verified,
        s3Uploaded: metadata.s3Uploaded
      });

      return {
        success: true,
        backupId,
        metadata,
        duration
      };

    } catch (error) {
      logger.error('Database backup failed', {
        backupId,
        error: error.message,
        stack: error.stack
      });

      return {
        success: false,
        backupId,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Export database to SQL dump
   * @param {string} backupId - Backup identifier
   * @returns {string} Path to dump file
   */
  async exportDatabase(backupId) {
    const dumpFile = path.join(this.backupDir, `${backupId}.sql`);
    
    return new Promise((resolve, reject) => {
      // For SQLite, we'll copy the database file
      // For PostgreSQL, you'd use pg_dump
      const dbPath = process.env.DB_PATH || './database.sqlite';
      
      if (dbPath.includes('sqlite') || dbPath === ':memory:') {
        // SQLite backup
        const db = database.get();
        
        // Use SQLite backup API
        const backupProcess = spawn('sqlite3', [dbPath, '.dump'], {
          stdio: ['pipe', 'pipe', 'pipe']
        });
        
        const writeStream = require('fs').createWriteStream(dumpFile);
        backupProcess.stdout.pipe(writeStream);
        
        backupProcess.on('close', (code) => {
          if (code === 0) {
            resolve(dumpFile);
          } else {
            reject(new Error(`SQLite dump failed with code ${code}`));
          }
        });
        
        backupProcess.on('error', reject);
      } else {
        // PostgreSQL backup
        const dbUrl = new URL(process.env.DATABASE_URL);
        const pgDumpArgs = [
          '-h', dbUrl.hostname,
          '-p', dbUrl.port || '5432',
          '-U', dbUrl.username,
          '-d', dbUrl.pathname.substring(1),
          '-f', dumpFile,
          '--verbose',
          '--no-password'
        ];
        
        const pgDump = spawn('pg_dump', pgDumpArgs, {
          env: {
            ...process.env,
            PGPASSWORD: dbUrl.password
          }
        });
        
        pgDump.on('close', (code) => {
          if (code === 0) {
            resolve(dumpFile);
          } else {
            reject(new Error(`pg_dump failed with code ${code}`));
          }
        });
        
        pgDump.on('error', reject);
      }
    });
  }

  /**
   * Compress backup file
   * @param {string} filePath - Path to file to compress
   * @returns {string} Path to compressed file
   */
  async compressBackup(filePath) {
    const zlib = require('zlib');
    const compressedPath = `${filePath}.gz`;
    
    return new Promise((resolve, reject) => {
      const readStream = require('fs').createReadStream(filePath);
      const writeStream = require('fs').createWriteStream(compressedPath);
      const gzip = zlib.createGzip({ level: 9 });
      
      readStream
        .pipe(gzip)
        .pipe(writeStream)
        .on('finish', () => resolve(compressedPath))
        .on('error', reject);
    });
  }

  /**
   * Encrypt backup file
   * @param {string} filePath - Path to file to encrypt
   * @returns {string} Path to encrypted file
   */
  async encryptBackup(filePath) {
    const encryptedPath = `${filePath}.enc`;
    
    try {
      const data = await fs.readFile(filePath);
      const encrypted = encryptionService.encrypt(data.toString('base64'), 'backup');
      await fs.writeFile(encryptedPath, encrypted);
      
      return encryptedPath;
    } catch (error) {
      throw new Error(`Backup encryption failed: ${error.message}`);
    }
  }

  /**
   * Generate checksum for backup file
   * @param {string} filePath - Path to backup file
   * @returns {string} SHA256 checksum
   */
  async generateChecksum(filePath) {
    const data = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Verify backup integrity
   * @param {string} backupFile - Path to backup file
   * @param {Object} metadata - Backup metadata
   * @returns {Object} Verification result
   */
  async verifyBackup(backupFile, metadata) {
    try {
      // Verify file exists and is readable
      await fs.access(backupFile, fs.constants.R_OK);
      
      // Verify checksum
      const currentChecksum = await this.generateChecksum(backupFile);
      const checksumValid = currentChecksum === metadata.checksum;
      
      // Verify file size
      const stats = await fs.stat(backupFile);
      const expectedSize = metadata.encryptedSize || metadata.compressedSize || metadata.originalSize;
      const sizeValid = stats.size === expectedSize;
      
      return {
        success: checksumValid && sizeValid,
        checksumValid,
        sizeValid,
        fileSize: stats.size,
        expectedSize
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Upload backup to S3
   * @param {string} filePath - Path to backup file
   * @param {Object} metadata - Backup metadata
   */
  async uploadToS3(filePath, metadata) {
    if (!this.s3Config.bucket) {
      throw new Error('S3 configuration not provided');
    }
    
    // This is a placeholder for S3 upload
    // In a real implementation, you'd use AWS SDK
    logger.info('S3 upload would happen here', {
      bucket: this.s3Config.bucket,
      key: `backups/${metadata.fileName}`,
      size: metadata.encryptedSize || metadata.compressedSize || metadata.originalSize
    });
    
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * Log backup to database
   * @param {Object} metadata - Backup metadata
   */
  async logBackupToDatabase(metadata) {
    try {
      const db = database.get();
      
      await db.run(`
        INSERT INTO audit_logs (
          action, resource_type, resource_id, metadata, created_at
        ) VALUES (?, ?, ?, ?, ?)
      `, [
        'BACKUP_CREATED',
        'database',
        metadata.id,
        JSON.stringify(metadata),
        new Date().toISOString()
      ]);
    } catch (error) {
      logger.error('Failed to log backup to database', { error: error.message });
    }
  }

  /**
   * Clean up old backups based on retention policy
   */
  async cleanupOldBackups() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
      
      const files = await fs.readdir(this.backupDir);
      let deletedCount = 0;
      
      for (const file of files) {
        const filePath = path.join(this.backupDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          logger.debug('Deleted old backup file', { file, age: stats.mtime });
        }
      }
      
      if (deletedCount > 0) {
        logger.info('Cleaned up old backups', { 
          deletedCount, 
          retentionDays: this.retentionDays 
        });
      }
    } catch (error) {
      logger.error('Failed to cleanup old backups', { error: error.message });
    }
  }

  /**
   * List available backups
   * @returns {Array} List of backup metadata
   */
  async listBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const metadataFiles = files.filter(file => file.endsWith('.metadata.json'));
      
      const backups = [];
      for (const metadataFile of metadataFiles) {
        try {
          const metadataPath = path.join(this.backupDir, metadataFile);
          const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
          backups.push(metadata);
        } catch (error) {
          logger.warn('Failed to read backup metadata', { 
            file: metadataFile, 
            error: error.message 
          });
        }
      }
      
      return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      logger.error('Failed to list backups', { error: error.message });
      return [];
    }
  }

  /**
   * Get backup statistics
   * @returns {Object} Backup statistics
   */
  async getBackupStatistics() {
    try {
      const backups = await this.listBackups();
      
      const stats = {
        totalBackups: backups.length,
        successfulBackups: backups.filter(b => b.status === 'completed').length,
        failedBackups: backups.filter(b => b.status === 'failed').length,
        totalSize: backups.reduce((sum, b) => sum + (b.encryptedSize || b.compressedSize || b.originalSize || 0), 0),
        oldestBackup: backups.length > 0 ? backups[backups.length - 1].timestamp : null,
        newestBackup: backups.length > 0 ? backups[0].timestamp : null,
        averageSize: 0,
        averageDuration: 0
      };
      
      if (stats.totalBackups > 0) {
        stats.averageSize = Math.round(stats.totalSize / stats.totalBackups);
        stats.averageDuration = Math.round(
          backups.reduce((sum, b) => sum + (b.duration || 0), 0) / stats.totalBackups
        );
      }
      
      return stats;
    } catch (error) {
      logger.error('Failed to get backup statistics', { error: error.message });
      return null;
    }
  }
}

// Create singleton instance
const backupService = new BackupService();

module.exports = {
  BackupService,
  backupService
};
