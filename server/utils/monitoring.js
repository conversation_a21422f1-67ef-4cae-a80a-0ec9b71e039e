const { logger } = require('./logger');
const database = require('../database');

/**
 * Production Monitoring & Alerting Service
 * Provides comprehensive application monitoring, metrics collection, and alerting
 */

class MonitoringService {
  constructor() {
    this.metrics = new Map();
    this.alerts = new Map();
    this.healthChecks = new Map();
    this.startTime = Date.now();
    
    // Initialize monitoring
    this.initializeMonitoring();
  }

  /**
   * Initialize monitoring service
   */
  initializeMonitoring() {
    // Set up periodic health checks
    this.setupHealthChecks();
    
    // Set up metrics collection
    this.setupMetricsCollection();
    
    // Set up alerting
    this.setupAlerting();
    
    logger.info('Monitoring service initialized');
  }

  /**
   * Set up health checks
   */
  setupHealthChecks() {
    // Database health check
    this.registerHealthCheck('database', async () => {
      try {
        const db = database.get();
        const result = await db.get('SELECT 1 as test');
        return {
          status: 'healthy',
          responseTime: Date.now(),
          details: { connected: true, test: result.test === 1 }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message,
          details: { connected: false }
        };
      }
    });

    // Memory health check
    this.registerHealthCheck('memory', async () => {
      const memUsage = process.memoryUsage();
      const totalMem = require('os').totalmem();
      const freeMem = require('os').freemem();
      const usedMem = totalMem - freeMem;
      const memoryUsagePercent = (usedMem / totalMem) * 100;

      return {
        status: memoryUsagePercent > 90 ? 'unhealthy' : 'healthy',
        details: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
          systemMemoryUsage: memoryUsagePercent.toFixed(2) + '%'
        }
      };
    });

    // CPU health check
    this.registerHealthCheck('cpu', async () => {
      const cpus = require('os').cpus();
      const loadAvg = require('os').loadavg();
      
      return {
        status: loadAvg[0] > cpus.length * 0.8 ? 'unhealthy' : 'healthy',
        details: {
          cores: cpus.length,
          loadAverage: {
            '1min': loadAvg[0],
            '5min': loadAvg[1],
            '15min': loadAvg[2]
          }
        }
      };
    });

    // Disk space health check
    this.registerHealthCheck('disk', async () => {
      try {
        const fs = require('fs');
        const stats = fs.statSync('.');
        
        return {
          status: 'healthy',
          details: {
            available: true,
            path: process.cwd()
          }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message
        };
      }
    });

    // Run health checks every 30 seconds
    setInterval(() => {
      this.runHealthChecks();
    }, 30000);
  }

  /**
   * Set up metrics collection
   */
  setupMetricsCollection() {
    // Collect system metrics every minute
    setInterval(() => {
      this.collectSystemMetrics();
    }, 60000);

    // Collect application metrics every 30 seconds
    setInterval(() => {
      this.collectApplicationMetrics();
    }, 30000);
  }

  /**
   * Set up alerting system
   */
  setupAlerting() {
    // Define alert thresholds
    this.alertThresholds = {
      errorRate: { warning: 1, critical: 5 }, // percentage
      responseTime: { warning: 1000, critical: 3000 }, // milliseconds
      memoryUsage: { warning: 80, critical: 90 }, // percentage
      cpuUsage: { warning: 70, critical: 85 }, // percentage
      diskUsage: { warning: 80, critical: 90 }, // percentage
      failedLogins: { warning: 10, critical: 50 }, // count per hour
      rateLimitViolations: { warning: 100, critical: 500 } // count per hour
    };

    // Check alerts every 5 minutes
    setInterval(() => {
      this.checkAlerts();
    }, 300000);
  }

  /**
   * Register a health check
   * @param {string} name - Health check name
   * @param {Function} checkFunction - Function that performs the check
   */
  registerHealthCheck(name, checkFunction) {
    this.healthChecks.set(name, checkFunction);
  }

  /**
   * Run all health checks
   */
  async runHealthChecks() {
    const results = {};
    
    for (const [name, checkFunction] of this.healthChecks) {
      try {
        const startTime = Date.now();
        const result = await checkFunction();
        const duration = Date.now() - startTime;
        
        results[name] = {
          ...result,
          responseTime: duration,
          timestamp: new Date().toISOString()
        };
        
        // Log unhealthy services
        if (result.status === 'unhealthy') {
          logger.warn(`Health check failed: ${name}`, result);
          this.triggerAlert('health_check_failed', { service: name, ...result });
        }
      } catch (error) {
        results[name] = {
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        };
        
        logger.error(`Health check error: ${name}`, { error: error.message });
      }
    }
    
    // Store health check results
    this.metrics.set('health_checks', results);
    
    return results;
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    const os = require('os');
    const process = require('process');
    
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        ...process.memoryUsage(),
        systemTotal: os.totalmem(),
        systemFree: os.freemem(),
        systemUsed: os.totalmem() - os.freemem()
      },
      cpu: {
        cores: os.cpus().length,
        loadAverage: os.loadavg(),
        usage: process.cpuUsage()
      },
      system: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        pid: process.pid
      }
    };
    
    this.metrics.set('system', metrics);
    
    // Log performance metrics
    logger.logPerformance('System Metrics', 0, metrics);
  }

  /**
   * Collect application metrics
   */
  async collectApplicationMetrics() {
    try {
      const db = database.get();
      
      // Get user metrics
      const userMetrics = await db.get(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN created_at > datetime('now', '-24 hours') THEN 1 END) as new_users_24h,
          COUNT(CASE WHEN last_login_at > datetime('now', '-24 hours') THEN 1 END) as active_users_24h,
          COUNT(CASE WHEN account_status = 'active' THEN 1 END) as active_users
        FROM users
      `);

      // Get error metrics from last hour
      const errorMetrics = await db.get(`
        SELECT 
          COUNT(*) as total_errors,
          COUNT(CASE WHEN created_at > datetime('now', '-1 hour') THEN 1 END) as errors_1h
        FROM audit_logs
        WHERE action LIKE '%ERROR%' OR success = false
      `);

      // Get rate limit violations
      const rateLimitMetrics = await db.get(`
        SELECT 
          COUNT(*) as total_violations,
          COUNT(CASE WHEN created_at > datetime('now', '-1 hour') THEN 1 END) as violations_1h
        FROM security_violations
        WHERE violation_type = 'RATE_LIMIT_EXCEEDED'
      `);

      const metrics = {
        timestamp: new Date().toISOString(),
        users: userMetrics,
        errors: errorMetrics,
        rateLimits: rateLimitMetrics,
        uptime: process.uptime()
      };
      
      this.metrics.set('application', metrics);
      
      // Check for alert conditions
      this.checkMetricAlerts(metrics);
      
    } catch (error) {
      logger.error('Failed to collect application metrics', { error: error.message });
    }
  }

  /**
   * Check metrics for alert conditions
   * @param {Object} metrics - Application metrics
   */
  checkMetricAlerts(metrics) {
    // Check error rate
    if (metrics.errors.errors_1h > this.alertThresholds.errorRate.critical) {
      this.triggerAlert('high_error_rate', {
        level: 'critical',
        errors: metrics.errors.errors_1h,
        threshold: this.alertThresholds.errorRate.critical
      });
    } else if (metrics.errors.errors_1h > this.alertThresholds.errorRate.warning) {
      this.triggerAlert('high_error_rate', {
        level: 'warning',
        errors: metrics.errors.errors_1h,
        threshold: this.alertThresholds.errorRate.warning
      });
    }

    // Check rate limit violations
    if (metrics.rateLimits.violations_1h > this.alertThresholds.rateLimitViolations.critical) {
      this.triggerAlert('high_rate_limit_violations', {
        level: 'critical',
        violations: metrics.rateLimits.violations_1h,
        threshold: this.alertThresholds.rateLimitViolations.critical
      });
    }
  }

  /**
   * Trigger an alert
   * @param {string} alertType - Type of alert
   * @param {Object} data - Alert data
   */
  triggerAlert(alertType, data) {
    const alert = {
      type: alertType,
      level: data.level || 'warning',
      timestamp: new Date().toISOString(),
      data
    };
    
    // Store alert
    const alertKey = `${alertType}_${Date.now()}`;
    this.alerts.set(alertKey, alert);
    
    // Log alert
    logger.warn(`Alert triggered: ${alertType}`, alert);
    
    // Send alert notification (implement based on your notification system)
    this.sendAlertNotification(alert);
  }

  /**
   * Send alert notification
   * @param {Object} alert - Alert object
   */
  async sendAlertNotification(alert) {
    try {
      // Log to audit trail
      const db = database.get();
      await db.run(`
        INSERT INTO audit_logs (
          action, resource_type, metadata, created_at
        ) VALUES (?, ?, ?, ?)
      `, [
        'ALERT_TRIGGERED',
        'monitoring',
        JSON.stringify(alert),
        new Date().toISOString()
      ]);
      
      // Here you would integrate with your notification system
      // Examples: Slack, email, PagerDuty, etc.
      
    } catch (error) {
      logger.error('Failed to send alert notification', { error: error.message, alert });
    }
  }

  /**
   * Check all alerts
   */
  async checkAlerts() {
    try {
      await this.runHealthChecks();
      await this.collectApplicationMetrics();
      
      logger.debug('Alert check completed');
    } catch (error) {
      logger.error('Alert check failed', { error: error.message });
    }
  }

  /**
   * Get current metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    const metrics = {};
    for (const [key, value] of this.metrics) {
      metrics[key] = value;
    }
    return metrics;
  }

  /**
   * Get recent alerts
   * @param {number} limit - Number of alerts to return
   * @returns {Array} Recent alerts
   */
  getRecentAlerts(limit = 50) {
    const alerts = Array.from(this.alerts.values())
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
    
    return alerts;
  }

  /**
   * Get system health status
   * @returns {Object} Health status
   */
  async getHealthStatus() {
    const healthChecks = await this.runHealthChecks();
    const overallStatus = Object.values(healthChecks).every(check => check.status === 'healthy') 
      ? 'healthy' : 'unhealthy';
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: healthChecks
    };
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = {
  MonitoringService,
  monitoring: monitoringService
};
