const axios = require('axios');
const { logger } = require('./logger');
const { apiResilience } = require('./apiResilience');

/**
 * Resilient API Client
 * Provides a robust HTTP client with built-in resilience features
 */

class ResilientApiClient {
  constructor(baseConfig = {}) {
    this.defaultConfig = {
      timeout: 30000,
      maxRetries: 3,
      retryDelay: 1000,
      ...baseConfig
    };
    
    // Create axios instance with default config
    this.client = axios.create({
      timeout: this.defaultConfig.timeout,
      headers: {
        'User-Agent': 'CVLeap/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        
        logger.debug('API request started', {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          timeout: config.timeout
        });
        
        return config;
      },
      (error) => {
        logger.error('API request setup failed', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        const duration = Date.now() - response.config.metadata.startTime;
        
        logger.debug('API request completed', {
          method: response.config.method?.toUpperCase(),
          url: response.config.url,
          status: response.status,
          duration,
          dataSize: JSON.stringify(response.data).length
        });
        
        return response;
      },
      (error) => {
        const duration = error.config?.metadata?.startTime 
          ? Date.now() - error.config.metadata.startTime 
          : 0;
        
        logger.warn('API request failed', {
          method: error.config?.method?.toUpperCase(),
          url: error.config?.url,
          status: error.response?.status,
          duration,
          error: error.message
        });
        
        return Promise.reject(this.enhanceError(error));
      }
    );
  }

  /**
   * Enhance error with additional context
   * @param {Error} error - Original error
   * @returns {Error} Enhanced error
   */
  enhanceError(error) {
    const enhancedError = new Error(error.message);
    enhancedError.originalError = error;
    enhancedError.status = error.response?.status;
    enhancedError.statusText = error.response?.statusText;
    enhancedError.data = error.response?.data;
    enhancedError.config = error.config;
    
    // Classify error type
    if (error.code === 'ECONNABORTED') {
      enhancedError.type = 'timeout';
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      enhancedError.type = 'connection';
    } else if (error.response?.status >= 500) {
      enhancedError.type = 'server';
    } else if (error.response?.status >= 400) {
      enhancedError.type = 'client';
    } else {
      enhancedError.type = 'unknown';
    }
    
    return enhancedError;
  }

  /**
   * Make resilient API request
   * @param {string} apiName - Name of the API for circuit breaker
   * @param {Object} config - Axios request config
   * @param {Object} resilienceOptions - Resilience options
   * @returns {Promise} API response
   */
  async request(apiName, config, resilienceOptions = {}) {
    const fullConfig = { ...this.defaultConfig, ...config };
    
    return apiResilience.executeWithResilience(
      apiName,
      async () => {
        const response = await this.client.request(fullConfig);
        return response.data;
      },
      {
        ...resilienceOptions,
        request: fullConfig.timeout
      }
    );
  }

  /**
   * GET request with resilience
   * @param {string} apiName - API name
   * @param {string} url - Request URL
   * @param {Object} config - Request config
   * @param {Object} resilienceOptions - Resilience options
   * @returns {Promise} Response data
   */
  async get(apiName, url, config = {}, resilienceOptions = {}) {
    return this.request(apiName, { ...config, method: 'GET', url }, resilienceOptions);
  }

  /**
   * POST request with resilience
   * @param {string} apiName - API name
   * @param {string} url - Request URL
   * @param {any} data - Request data
   * @param {Object} config - Request config
   * @param {Object} resilienceOptions - Resilience options
   * @returns {Promise} Response data
   */
  async post(apiName, url, data, config = {}, resilienceOptions = {}) {
    return this.request(apiName, { ...config, method: 'POST', url, data }, resilienceOptions);
  }

  /**
   * PUT request with resilience
   * @param {string} apiName - API name
   * @param {string} url - Request URL
   * @param {any} data - Request data
   * @param {Object} config - Request config
   * @param {Object} resilienceOptions - Resilience options
   * @returns {Promise} Response data
   */
  async put(apiName, url, data, config = {}, resilienceOptions = {}) {
    return this.request(apiName, { ...config, method: 'PUT', url, data }, resilienceOptions);
  }

  /**
   * DELETE request with resilience
   * @param {string} apiName - API name
   * @param {string} url - Request URL
   * @param {Object} config - Request config
   * @param {Object} resilienceOptions - Resilience options
   * @returns {Promise} Response data
   */
  async delete(apiName, url, config = {}, resilienceOptions = {}) {
    return this.request(apiName, { ...config, method: 'DELETE', url }, resilienceOptions);
  }

  /**
   * Create API-specific client with pre-configured settings
   * @param {string} apiName - API name
   * @param {Object} baseConfig - Base configuration
   * @param {Object} resilienceConfig - Resilience configuration
   * @returns {Object} API-specific client
   */
  createApiClient(apiName, baseConfig = {}, resilienceConfig = {}) {
    const apiClient = {
      name: apiName,
      config: { ...this.defaultConfig, ...baseConfig },
      resilienceConfig,
      
      async get(url, config = {}) {
        return this.get(apiName, url, { ...apiClient.config, ...config }, resilienceConfig);
      }.bind(this),
      
      async post(url, data, config = {}) {
        return this.post(apiName, url, data, { ...apiClient.config, ...config }, resilienceConfig);
      }.bind(this),
      
      async put(url, data, config = {}) {
        return this.put(apiName, url, data, { ...apiClient.config, ...config }, resilienceConfig);
      }.bind(this),
      
      async delete(url, config = {}) {
        return this.delete(apiName, url, { ...apiClient.config, ...config }, resilienceConfig);
      }.bind(this),
      
      getHealth() {
        return apiResilience.getApiHealth(apiName);
      },
      
      resetHealth() {
        return apiResilience.resetApiHealth(apiName);
      }
    };
    
    return apiClient;
  }
}

// Create singleton instance
const resilientApiClient = new ResilientApiClient();

// Create pre-configured clients for external APIs
const apiClients = {
  indeed: resilientApiClient.createApiClient('indeed', {
    baseURL: 'https://api.indeed.com',
    timeout: 15000,
    headers: {
      'User-Agent': 'CVLeap Job Discovery Bot'
    }
  }, {
    maxRetries: 2,
    baseDelay: 2000,
    circuitBreaker: {
      failureThreshold: 3,
      recoveryTimeout: 30000
    }
  }),

  linkedin: resilientApiClient.createApiClient('linkedin', {
    baseURL: 'https://api.linkedin.com/v2',
    timeout: 20000,
    headers: {
      'LinkedIn-Version': '202401'
    }
  }, {
    maxRetries: 3,
    baseDelay: 1500,
    circuitBreaker: {
      failureThreshold: 5,
      recoveryTimeout: 60000
    }
  }),

  glassdoor: resilientApiClient.createApiClient('glassdoor', {
    baseURL: 'https://api.glassdoor.com/api',
    timeout: 25000
  }, {
    maxRetries: 2,
    baseDelay: 3000,
    circuitBreaker: {
      failureThreshold: 3,
      recoveryTimeout: 45000
    }
  }),

  apollo: resilientApiClient.createApiClient('apollo', {
    baseURL: 'https://api.apollo.io/v1',
    timeout: 15000
  }, {
    maxRetries: 3,
    baseDelay: 1000,
    circuitBreaker: {
      failureThreshold: 4,
      recoveryTimeout: 30000
    }
  }),

  hunter: resilientApiClient.createApiClient('hunter', {
    baseURL: 'https://api.hunter.io/v2',
    timeout: 10000
  }, {
    maxRetries: 2,
    baseDelay: 2000,
    circuitBreaker: {
      failureThreshold: 3,
      recoveryTimeout: 60000
    }
  }),

  clearbit: resilientApiClient.createApiClient('clearbit', {
    baseURL: 'https://person.clearbit.com/v2',
    timeout: 12000
  }, {
    maxRetries: 3,
    baseDelay: 1500,
    circuitBreaker: {
      failureThreshold: 5,
      recoveryTimeout: 30000
    }
  })
};

/**
 * Get health status of all API clients
 * @returns {Object} Health status of all APIs
 */
function getAllApiHealth() {
  const health = {};
  Object.keys(apiClients).forEach(apiName => {
    health[apiName] = apiClients[apiName].getHealth();
  });
  return health;
}

/**
 * Reset health status for all or specific API
 * @param {string} apiName - API name (optional)
 */
function resetApiHealth(apiName = null) {
  if (apiName && apiClients[apiName]) {
    apiClients[apiName].resetHealth();
  } else {
    Object.values(apiClients).forEach(client => client.resetHealth());
  }
}

/**
 * Get circuit breaker status for all APIs
 * @returns {Array} Circuit breaker states
 */
function getCircuitBreakerStatus() {
  return apiResilience.getCircuitBreakerStatus();
}

module.exports = {
  ResilientApiClient,
  resilientApiClient,
  apiClients,
  getAllApiHealth,
  resetApiHealth,
  getCircuitBreakerStatus
};
