const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');
const { logger } = require('./logger');
const { monitoring } = require('./monitoring');

/**
 * Vulnerability Scanner
 * Provides automated security vulnerability scanning and reporting
 */

class VulnerabilityScanner {
  constructor() {
    this.scanTypes = {
      dependencies: 'Dependency vulnerability scan',
      secrets: 'Secret detection scan',
      code: 'Static code analysis',
      configuration: 'Configuration security scan',
      permissions: 'File permissions scan'
    };
    
    this.severityLevels = {
      critical: { score: 10, color: 'red' },
      high: { score: 7, color: 'orange' },
      medium: { score: 4, color: 'yellow' },
      low: { score: 1, color: 'blue' },
      info: { score: 0, color: 'green' }
    };
    
    this.secretPatterns = [
      {
        name: 'AWS Access Key',
        pattern: /AKIA[0-9A-Z]{16}/g,
        severity: 'critical'
      },
      {
        name: 'AWS Secret Key',
        pattern: /[0-9a-zA-Z/+]{40}/g,
        severity: 'critical'
      },
      {
        name: 'GitHub Token',
        pattern: /ghp_[0-9a-zA-Z]{36}/g,
        severity: 'high'
      },
      {
        name: 'JWT Token',
        pattern: /eyJ[0-9a-zA-Z_-]*\.[0-9a-zA-Z_-]*\.[0-9a-zA-Z_-]*/g,
        severity: 'medium'
      },
      {
        name: 'API Key',
        pattern: /api[_-]?key[_-]?[=:]\s*['"]*[0-9a-zA-Z]{20,}/gi,
        severity: 'high'
      },
      {
        name: 'Database URL',
        pattern: /(postgresql|mysql|mongodb):\/\/[^\s'"]+/gi,
        severity: 'high'
      },
      {
        name: 'Private Key',
        pattern: /-----BEGIN [A-Z ]+PRIVATE KEY-----/g,
        severity: 'critical'
      },
      {
        name: 'Password',
        pattern: /password[_-]?[=:]\s*['"]*[^\s'"]{8,}/gi,
        severity: 'medium'
      }
    ];
    
    this.codePatterns = [
      {
        name: 'SQL Injection Risk',
        pattern: /query\s*\(\s*['"]*\s*\+|exec\s*\(\s*['"]*\s*\+/gi,
        severity: 'high',
        description: 'Potential SQL injection vulnerability'
      },
      {
        name: 'XSS Risk',
        pattern: /innerHTML\s*=|document\.write\s*\(/gi,
        severity: 'medium',
        description: 'Potential XSS vulnerability'
      },
      {
        name: 'Command Injection Risk',
        pattern: /exec\s*\(\s*['"]*\s*\+|spawn\s*\(\s*['"]*\s*\+/gi,
        severity: 'high',
        description: 'Potential command injection vulnerability'
      },
      {
        name: 'Hardcoded Secret',
        pattern: /(secret|password|key)\s*[=:]\s*['"]+[^'"]{8,}/gi,
        severity: 'medium',
        description: 'Hardcoded secret detected'
      },
      {
        name: 'Insecure Random',
        pattern: /Math\.random\(\)/g,
        severity: 'low',
        description: 'Insecure random number generation'
      },
      {
        name: 'Eval Usage',
        pattern: /\beval\s*\(/g,
        severity: 'high',
        description: 'Use of eval() function'
      }
    ];
    
    this.configurationChecks = [
      {
        name: 'Environment Variables',
        check: this.checkEnvironmentVariables.bind(this),
        severity: 'medium'
      },
      {
        name: 'SSL Configuration',
        check: this.checkSSLConfiguration.bind(this),
        severity: 'high'
      },
      {
        name: 'Security Headers',
        check: this.checkSecurityHeaders.bind(this),
        severity: 'medium'
      },
      {
        name: 'Database Security',
        check: this.checkDatabaseSecurity.bind(this),
        severity: 'high'
      }
    ];
    
    this.lastScanResults = null;
    this.scanHistory = [];
  }

  /**
   * Run comprehensive vulnerability scan
   * @param {Object} options - Scan options
   * @returns {Object} Scan results
   */
  async runComprehensiveScan(options = {}) {
    const scanId = crypto.randomUUID();
    const startTime = Date.now();
    
    logger.info('Starting comprehensive vulnerability scan', { scanId });
    
    const results = {
      scanId,
      timestamp: new Date().toISOString(),
      duration: 0,
      summary: {
        total: 0,
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        info: 0
      },
      scans: {}
    };

    try {
      // Run individual scans
      if (options.dependencies !== false) {
        results.scans.dependencies = await this.scanDependencies();
      }
      
      if (options.secrets !== false) {
        results.scans.secrets = await this.scanSecrets();
      }
      
      if (options.code !== false) {
        results.scans.code = await this.scanCode();
      }
      
      if (options.configuration !== false) {
        results.scans.configuration = await this.scanConfiguration();
      }
      
      if (options.permissions !== false) {
        results.scans.permissions = await this.scanPermissions();
      }

      // Calculate summary
      this.calculateSummary(results);
      
      results.duration = Date.now() - startTime;
      
      // Store results
      this.lastScanResults = results;
      this.scanHistory.push({
        scanId,
        timestamp: results.timestamp,
        summary: results.summary,
        duration: results.duration
      });
      
      // Keep only last 10 scans in history
      if (this.scanHistory.length > 10) {
        this.scanHistory = this.scanHistory.slice(-10);
      }
      
      // Log results
      logger.info('Vulnerability scan completed', {
        scanId,
        duration: results.duration,
        summary: results.summary
      });
      
      // Trigger alerts for critical/high vulnerabilities
      if (results.summary.critical > 0 || results.summary.high > 0) {
        monitoring.triggerAlert('vulnerabilities_detected', {
          level: results.summary.critical > 0 ? 'critical' : 'warning',
          scanId,
          critical: results.summary.critical,
          high: results.summary.high
        });
      }
      
      return results;
    } catch (error) {
      logger.error('Vulnerability scan failed', {
        error: error.message,
        scanId
      });
      throw error;
    }
  }

  /**
   * Scan dependencies for known vulnerabilities
   * @returns {Object} Dependency scan results
   */
  async scanDependencies() {
    const results = {
      type: 'dependencies',
      vulnerabilities: [],
      summary: { total: 0, critical: 0, high: 0, medium: 0, low: 0, info: 0 }
    };

    try {
      // Run npm audit
      const auditOutput = execSync('npm audit --json', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      
      const auditData = JSON.parse(auditOutput);
      
      if (auditData.vulnerabilities) {
        for (const [packageName, vulnData] of Object.entries(auditData.vulnerabilities)) {
          const vulnerability = {
            id: vulnData.via[0]?.source || `vuln-${packageName}`,
            package: packageName,
            severity: vulnData.severity,
            title: vulnData.via[0]?.title || 'Unknown vulnerability',
            description: vulnData.via[0]?.url || 'No description available',
            range: vulnData.range,
            fixAvailable: vulnData.fixAvailable
          };
          
          results.vulnerabilities.push(vulnerability);
          results.summary[vulnerability.severity]++;
          results.summary.total++;
        }
      }
    } catch (error) {
      // npm audit returns non-zero exit code when vulnerabilities found
      if (error.stdout) {
        try {
          const auditData = JSON.parse(error.stdout);
          // Process audit data similar to above
        } catch (parseError) {
          logger.error('Failed to parse npm audit output', { error: parseError.message });
        }
      }
    }

    return results;
  }

  /**
   * Scan for exposed secrets and credentials
   * @returns {Object} Secret scan results
   */
  async scanSecrets() {
    const results = {
      type: 'secrets',
      vulnerabilities: [],
      summary: { total: 0, critical: 0, high: 0, medium: 0, low: 0, info: 0 }
    };

    try {
      const filesToScan = await this.getFilesToScan(['.js', '.ts', '.json', '.env', '.yml', '.yaml']);
      
      for (const filePath of filesToScan) {
        try {
          const content = await fs.readFile(filePath, 'utf8');
          const secrets = this.detectSecrets(content, filePath);
          results.vulnerabilities.push(...secrets);
        } catch (error) {
          logger.debug('Failed to scan file for secrets', { file: filePath, error: error.message });
        }
      }
      
      // Calculate summary
      results.vulnerabilities.forEach(vuln => {
        results.summary[vuln.severity]++;
        results.summary.total++;
      });
    } catch (error) {
      logger.error('Secret scan failed', { error: error.message });
    }

    return results;
  }

  /**
   * Scan code for security vulnerabilities
   * @returns {Object} Code scan results
   */
  async scanCode() {
    const results = {
      type: 'code',
      vulnerabilities: [],
      summary: { total: 0, critical: 0, high: 0, medium: 0, low: 0, info: 0 }
    };

    try {
      const filesToScan = await this.getFilesToScan(['.js', '.ts']);
      
      for (const filePath of filesToScan) {
        try {
          const content = await fs.readFile(filePath, 'utf8');
          const codeIssues = this.analyzeCode(content, filePath);
          results.vulnerabilities.push(...codeIssues);
        } catch (error) {
          logger.debug('Failed to scan file for code issues', { file: filePath, error: error.message });
        }
      }
      
      // Calculate summary
      results.vulnerabilities.forEach(vuln => {
        results.summary[vuln.severity]++;
        results.summary.total++;
      });
    } catch (error) {
      logger.error('Code scan failed', { error: error.message });
    }

    return results;
  }

  /**
   * Scan configuration for security issues
   * @returns {Object} Configuration scan results
   */
  async scanConfiguration() {
    const results = {
      type: 'configuration',
      vulnerabilities: [],
      summary: { total: 0, critical: 0, high: 0, medium: 0, low: 0, info: 0 }
    };

    try {
      for (const check of this.configurationChecks) {
        try {
          const issues = await check.check();
          if (issues && issues.length > 0) {
            issues.forEach(issue => {
              issue.severity = issue.severity || check.severity;
              results.vulnerabilities.push(issue);
              results.summary[issue.severity]++;
              results.summary.total++;
            });
          }
        } catch (error) {
          logger.debug('Configuration check failed', { check: check.name, error: error.message });
        }
      }
    } catch (error) {
      logger.error('Configuration scan failed', { error: error.message });
    }

    return results;
  }

  /**
   * Scan file permissions for security issues
   * @returns {Object} Permission scan results
   */
  async scanPermissions() {
    const results = {
      type: 'permissions',
      vulnerabilities: [],
      summary: { total: 0, critical: 0, high: 0, medium: 0, low: 0, info: 0 }
    };

    try {
      const sensitiveFiles = [
        '.env',
        '.env.production',
        'config/database.js',
        'config/secrets.js',
        'private.key',
        'server.key'
      ];

      for (const file of sensitiveFiles) {
        try {
          const stats = await fs.stat(file);
          const mode = stats.mode & parseInt('777', 8);
          
          // Check if file is world-readable or world-writable
          if (mode & parseInt('044', 8)) {
            results.vulnerabilities.push({
              id: `perm-${file}`,
              file,
              severity: 'high',
              title: 'Sensitive file with insecure permissions',
              description: `File ${file} has world-readable permissions`,
              permissions: mode.toString(8)
            });
            results.summary.high++;
            results.summary.total++;
          }
        } catch (error) {
          // File doesn't exist, which is fine
        }
      }
    } catch (error) {
      logger.error('Permission scan failed', { error: error.message });
    }

    return results;
  }

  /**
   * Detect secrets in content
   * @param {string} content - File content
   * @param {string} filePath - File path
   * @returns {Array} Detected secrets
   */
  detectSecrets(content, filePath) {
    const secrets = [];
    
    for (const pattern of this.secretPatterns) {
      const matches = content.matchAll(pattern.pattern);
      
      for (const match of matches) {
        secrets.push({
          id: `secret-${crypto.createHash('md5').update(match[0]).digest('hex')}`,
          type: pattern.name,
          severity: pattern.severity,
          title: `${pattern.name} detected`,
          description: `Potential ${pattern.name} found in ${filePath}`,
          file: filePath,
          line: this.getLineNumber(content, match.index),
          match: match[0].substring(0, 20) + '...'
        });
      }
    }
    
    return secrets;
  }

  /**
   * Analyze code for security issues
   * @param {string} content - File content
   * @param {string} filePath - File path
   * @returns {Array} Code issues
   */
  analyzeCode(content, filePath) {
    const issues = [];
    
    for (const pattern of this.codePatterns) {
      const matches = content.matchAll(pattern.pattern);
      
      for (const match of matches) {
        issues.push({
          id: `code-${crypto.createHash('md5').update(match[0] + filePath).digest('hex')}`,
          type: pattern.name,
          severity: pattern.severity,
          title: pattern.name,
          description: pattern.description,
          file: filePath,
          line: this.getLineNumber(content, match.index),
          code: this.getCodeContext(content, match.index)
        });
      }
    }
    
    return issues;
  }

  /**
   * Get files to scan
   * @param {Array} extensions - File extensions to include
   * @returns {Array} File paths
   */
  async getFilesToScan(extensions) {
    const files = [];
    const excludeDirs = ['node_modules', '.git', 'dist', 'build', 'coverage'];
    
    async function scanDirectory(dir) {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !excludeDirs.includes(entry.name)) {
            await scanDirectory(fullPath);
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name);
            if (extensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }
    
    await scanDirectory(process.cwd());
    return files;
  }

  /**
   * Get line number for character index
   * @param {string} content - File content
   * @param {number} index - Character index
   * @returns {number} Line number
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * Get code context around match
   * @param {string} content - File content
   * @param {number} index - Character index
   * @returns {string} Code context
   */
  getCodeContext(content, index) {
    const lines = content.split('\n');
    const lineNumber = this.getLineNumber(content, index);
    const start = Math.max(0, lineNumber - 2);
    const end = Math.min(lines.length, lineNumber + 1);
    
    return lines.slice(start, end).join('\n');
  }

  /**
   * Check environment variables configuration
   * @returns {Array} Configuration issues
   */
  async checkEnvironmentVariables() {
    const issues = [];
    
    try {
      // Check for missing critical environment variables
      const criticalVars = [
        'NODE_ENV',
        'DATABASE_URL',
        'JWT_SECRET',
        'ENCRYPTION_MASTER_KEY'
      ];
      
      for (const varName of criticalVars) {
        if (!process.env[varName]) {
          issues.push({
            id: `env-missing-${varName}`,
            title: `Missing environment variable: ${varName}`,
            description: `Critical environment variable ${varName} is not set`,
            severity: 'high'
          });
        }
      }
      
      // Check for weak secrets
      if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
        issues.push({
          id: 'env-weak-jwt-secret',
          title: 'Weak JWT secret',
          description: 'JWT secret should be at least 32 characters long',
          severity: 'medium'
        });
      }
    } catch (error) {
      logger.error('Environment variable check failed', { error: error.message });
    }
    
    return issues;
  }

  /**
   * Check SSL configuration
   * @returns {Array} SSL issues
   */
  async checkSSLConfiguration() {
    const issues = [];
    
    // This would check SSL/TLS configuration
    // For now, return empty array
    
    return issues;
  }

  /**
   * Check security headers configuration
   * @returns {Array} Security header issues
   */
  async checkSecurityHeaders() {
    const issues = [];
    
    // This would check security headers configuration
    // For now, return empty array
    
    return issues;
  }

  /**
   * Check database security configuration
   * @returns {Array} Database security issues
   */
  async checkDatabaseSecurity() {
    const issues = [];
    
    // Check database URL for security issues
    if (process.env.DATABASE_URL) {
      const dbUrl = process.env.DATABASE_URL;
      
      // Check for unencrypted connections
      if (dbUrl.startsWith('postgresql://') && !dbUrl.includes('sslmode=require')) {
        issues.push({
          id: 'db-unencrypted',
          title: 'Unencrypted database connection',
          description: 'Database connection should use SSL/TLS encryption',
          severity: 'high'
        });
      }
      
      // Check for default credentials
      if (dbUrl.includes('password=password') || dbUrl.includes(':password@')) {
        issues.push({
          id: 'db-default-password',
          title: 'Default database password',
          description: 'Database is using default or weak password',
          severity: 'critical'
        });
      }
    }
    
    return issues;
  }

  /**
   * Calculate summary statistics
   * @param {Object} results - Scan results
   */
  calculateSummary(results) {
    for (const scan of Object.values(results.scans)) {
      results.summary.total += scan.summary.total;
      results.summary.critical += scan.summary.critical;
      results.summary.high += scan.summary.high;
      results.summary.medium += scan.summary.medium;
      results.summary.low += scan.summary.low;
      results.summary.info += scan.summary.info;
    }
  }

  /**
   * Get last scan results
   * @returns {Object} Last scan results
   */
  getLastScanResults() {
    return this.lastScanResults;
  }

  /**
   * Get scan history
   * @returns {Array} Scan history
   */
  getScanHistory() {
    return this.scanHistory;
  }

  /**
   * Generate security report
   * @param {Object} scanResults - Scan results
   * @returns {Object} Security report
   */
  generateSecurityReport(scanResults) {
    const report = {
      executive_summary: this.generateExecutiveSummary(scanResults),
      detailed_findings: this.generateDetailedFindings(scanResults),
      recommendations: this.generateRecommendations(scanResults),
      risk_assessment: this.generateRiskAssessment(scanResults)
    };
    
    return report;
  }

  /**
   * Generate executive summary
   * @param {Object} scanResults - Scan results
   * @returns {Object} Executive summary
   */
  generateExecutiveSummary(scanResults) {
    const totalScore = this.calculateSecurityScore(scanResults);
    
    return {
      security_score: totalScore,
      risk_level: this.getRiskLevel(totalScore),
      total_vulnerabilities: scanResults.summary.total,
      critical_vulnerabilities: scanResults.summary.critical,
      scan_date: scanResults.timestamp,
      scan_duration: scanResults.duration
    };
  }

  /**
   * Generate detailed findings
   * @param {Object} scanResults - Scan results
   * @returns {Object} Detailed findings
   */
  generateDetailedFindings(scanResults) {
    const findings = {};
    
    for (const [scanType, scanData] of Object.entries(scanResults.scans)) {
      findings[scanType] = {
        description: this.scanTypes[scanType],
        vulnerabilities: scanData.vulnerabilities,
        summary: scanData.summary
      };
    }
    
    return findings;
  }

  /**
   * Generate recommendations
   * @param {Object} scanResults - Scan results
   * @returns {Array} Recommendations
   */
  generateRecommendations(scanResults) {
    const recommendations = [];
    
    if (scanResults.summary.critical > 0) {
      recommendations.push({
        priority: 'critical',
        action: 'Address critical vulnerabilities immediately',
        description: 'Critical vulnerabilities pose immediate security risks and should be fixed as soon as possible.'
      });
    }
    
    if (scanResults.summary.high > 0) {
      recommendations.push({
        priority: 'high',
        action: 'Fix high-severity vulnerabilities',
        description: 'High-severity vulnerabilities should be addressed within 24-48 hours.'
      });
    }
    
    return recommendations;
  }

  /**
   * Generate risk assessment
   * @param {Object} scanResults - Scan results
   * @returns {Object} Risk assessment
   */
  generateRiskAssessment(scanResults) {
    const score = this.calculateSecurityScore(scanResults);
    
    return {
      overall_risk: this.getRiskLevel(score),
      security_score: score,
      risk_factors: this.identifyRiskFactors(scanResults),
      compliance_status: this.assessCompliance(scanResults)
    };
  }

  /**
   * Calculate security score
   * @param {Object} scanResults - Scan results
   * @returns {number} Security score (0-100)
   */
  calculateSecurityScore(scanResults) {
    let totalDeductions = 0;
    
    totalDeductions += scanResults.summary.critical * this.severityLevels.critical.score;
    totalDeductions += scanResults.summary.high * this.severityLevels.high.score;
    totalDeductions += scanResults.summary.medium * this.severityLevels.medium.score;
    totalDeductions += scanResults.summary.low * this.severityLevels.low.score;
    
    return Math.max(0, 100 - totalDeductions);
  }

  /**
   * Get risk level based on score
   * @param {number} score - Security score
   * @returns {string} Risk level
   */
  getRiskLevel(score) {
    if (score >= 90) return 'low';
    if (score >= 70) return 'medium';
    if (score >= 50) return 'high';
    return 'critical';
  }

  /**
   * Identify risk factors
   * @param {Object} scanResults - Scan results
   * @returns {Array} Risk factors
   */
  identifyRiskFactors(scanResults) {
    const factors = [];
    
    if (scanResults.summary.critical > 0) {
      factors.push('Critical vulnerabilities present');
    }
    
    if (scanResults.scans.secrets?.summary.total > 0) {
      factors.push('Exposed secrets detected');
    }
    
    if (scanResults.scans.dependencies?.summary.high > 0) {
      factors.push('High-risk dependencies');
    }
    
    return factors;
  }

  /**
   * Assess compliance status
   * @param {Object} scanResults - Scan results
   * @returns {Object} Compliance assessment
   */
  assessCompliance(scanResults) {
    const score = this.calculateSecurityScore(scanResults);
    
    return {
      pci_dss: score >= 80 ? 'compliant' : 'non-compliant',
      iso_27001: score >= 85 ? 'compliant' : 'non-compliant',
      gdpr: score >= 75 ? 'compliant' : 'non-compliant',
      overall: score >= 80 ? 'compliant' : 'non-compliant'
    };
  }
}

// Create singleton instance
const vulnerabilityScanner = new VulnerabilityScanner();

module.exports = {
  VulnerabilityScanner,
  vulnerabilityScanner
};
