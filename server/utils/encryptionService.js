const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced Encryption Service for data protection at rest and in transit
 * Uses AES-256-GCM for authenticated encryption with proper key derivation
 */
class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 12;  // 96 bits (recommended for GCM)
    this.tagLength = 16; // 128 bits
    this.saltLength = 32; // 256 bits for PBKDF2
    this.iterations = 100000; // PBKDF2 iterations

    // Version for backward compatibility
    this.version = '2.0';
    this.legacyVersion = '1.0';

    // Audit logging (initialize before master key)
    this.auditLog = [];
    this.maxAuditEntries = 1000;

    // Initialize master key from environment or generate
    this.masterKey = this.initializeMasterKey();
  }

  /**
   * Initialize master encryption key with enhanced security
   */
  initializeMasterKey() {
    const key = process.env.ENCRYPTION_MASTER_KEY;

    if (key) {
      try {
        // Validate key format and length
        const keyBuffer = Buffer.from(key, 'base64');
        if (keyBuffer.length !== this.keyLength) {
          throw new Error(`Master key must be ${this.keyLength} bytes (${this.keyLength * 8} bits)`);
        }
        this.logAudit('master_key_loaded', { source: 'environment' });
        return keyBuffer;
      } catch (error) {
        throw new Error(`Invalid master key format: ${error.message}`);
      }
    }

    // Generate random key for development (DO NOT use in production)
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️  Using random encryption key - NOT for production use');
      const devKey = crypto.randomBytes(this.keyLength);
      this.logAudit('master_key_generated', { source: 'development', warning: true });
      return devKey;
    }

    throw new Error('ENCRYPTION_MASTER_KEY environment variable is required for production');
  }

  /**
   * Generate a derived key for specific purposes using PBKDF2
   * @param {string} purpose - Purpose identifier for key derivation
   * @param {Buffer} salt - Optional salt (will generate if not provided)
   * @returns {Object} Derived key and salt
   */
  deriveKey(purpose, salt = null) {
    try {
      // Generate or use provided salt
      const keySalt = salt || crypto.randomBytes(this.saltLength);

      // Create purpose-specific salt by combining with purpose
      const purposeBuffer = Buffer.from(purpose, 'utf8');
      const combinedSalt = Buffer.concat([keySalt, purposeBuffer]);

      // Derive key using PBKDF2
      const derivedKey = crypto.pbkdf2Sync(
        this.masterKey,
        combinedSalt,
        this.iterations,
        this.keyLength,
        'sha256'
      );

      return {
        key: derivedKey,
        salt: keySalt
      };
    } catch (error) {
      this.logAudit('key_derivation_failed', { purpose, error: error.message });
      throw new Error(`Key derivation failed: ${error.message}`);
    }
  }

  /**
   * Derive key using scrypt (alternative to PBKDF2)
   * @param {string} purpose - Purpose identifier
   * @param {Buffer} salt - Salt for key derivation
   * @returns {Object} Derived key and salt
   */
  deriveKeyScrypt(purpose, salt = null) {
    try {
      const keySalt = salt || crypto.randomBytes(this.saltLength);
      const purposeBuffer = Buffer.from(purpose, 'utf8');
      const combinedSalt = Buffer.concat([keySalt, purposeBuffer]);

      // Use scrypt for key derivation (more memory-hard than PBKDF2)
      const derivedKey = crypto.scryptSync(
        this.masterKey,
        combinedSalt,
        this.keyLength,
        {
          N: 16384,  // CPU/memory cost parameter
          r: 8,      // Block size parameter
          p: 1       // Parallelization parameter
        }
      );

      return {
        key: derivedKey,
        salt: keySalt
      };
    } catch (error) {
      this.logAudit('scrypt_derivation_failed', { purpose, error: error.message });
      throw new Error(`Scrypt key derivation failed: ${error.message}`);
    }
  }

  /**
   * Encrypt sensitive data using AES-256-GCM with authenticated encryption
   * @param {any} data - Data to encrypt
   * @param {string} purpose - Purpose identifier for key derivation
   * @param {Object} options - Encryption options
   * @returns {Object} Encrypted data with metadata
   */
  encrypt(data, purpose = 'default', options = {}) {
    const startTime = Date.now();

    try {
      // Validate input
      if (data === undefined) {
        throw new Error('Data to encrypt cannot be undefined');
      }

      // Derive key for this purpose
      const { key, salt } = this.deriveKey(purpose);

      // Generate random IV (nonce) for GCM
      const iv = crypto.randomBytes(this.ivLength);

      // Create cipher with proper GCM mode
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);

      // Add additional authenticated data (AAD) if provided
      if (options.aad) {
        cipher.setAAD(Buffer.from(options.aad, 'utf8'));
      }

      // Serialize and encrypt data
      const serializedData = JSON.stringify(data);
      let encrypted = cipher.update(serializedData, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Get authentication tag
      const tag = cipher.getAuthTag();

      const result = {
        version: this.version,
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        salt: salt.toString('hex'),
        purpose,
        algorithm: this.algorithm,
        timestamp: new Date().toISOString()
      };

      // Add AAD to result if used
      if (options.aad) {
        result.aad = options.aad;
      }

      // Audit logging
      this.logAudit('encryption_success', {
        purpose,
        dataSize: serializedData.length,
        processingTime: Date.now() - startTime
      });

      return result;

    } catch (error) {
      this.logAudit('encryption_failed', {
        purpose,
        error: error.message,
        processingTime: Date.now() - startTime
      });
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypt sensitive data using AES-256-GCM with authentication verification
   * @param {Object} encryptedData - Encrypted data object
   * @param {string} purpose - Purpose identifier for key derivation
   * @returns {any} Decrypted data
   */
  decrypt(encryptedData, purpose = 'default') {
    const startTime = Date.now();

    try {
      // Validate encrypted data structure
      if (!encryptedData || typeof encryptedData !== 'object') {
        throw new Error('Invalid encrypted data format');
      }

      const { version, encrypted, iv, tag, salt, aad } = encryptedData;

      // Check if this is legacy encrypted data
      if (!version || version === this.legacyVersion) {
        return this.decryptLegacy(encryptedData, purpose);
      }

      // Validate required fields
      if (!encrypted || !iv || !tag || !salt) {
        throw new Error('Missing required encryption fields');
      }

      // Derive the same key using stored salt
      const { key } = this.deriveKey(purpose, Buffer.from(salt, 'hex'));

      // Create decipher with proper GCM mode
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        key,
        Buffer.from(iv, 'hex')
      );

      // Set additional authenticated data if present
      if (aad) {
        decipher.setAAD(Buffer.from(aad, 'utf8'));
      }

      // Set authentication tag
      decipher.setAuthTag(Buffer.from(tag, 'hex'));

      // Decrypt data
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Parse decrypted JSON
      const result = JSON.parse(decrypted);

      // Audit logging
      this.logAudit('decryption_success', {
        purpose,
        version,
        processingTime: Date.now() - startTime
      });

      return result;

    } catch (error) {
      this.logAudit('decryption_failed', {
        purpose,
        error: error.message,
        processingTime: Date.now() - startTime
      });

      // Provide specific error messages for common issues
      if (error.message.includes('bad decrypt')) {
        throw new Error('Decryption failed: Invalid key or corrupted data');
      } else if (error.message.includes('Unsupported state')) {
        throw new Error('Decryption failed: Authentication tag verification failed');
      } else {
        throw new Error(`Decryption failed: ${error.message}`);
      }
    }
  }

  /**
   * Decrypt legacy encrypted data (backward compatibility)
   * @param {Object} encryptedData - Legacy encrypted data
   * @param {string} purpose - Purpose identifier
   * @returns {any} Decrypted data
   */
  decryptLegacy(encryptedData, purpose) {
    try {
      console.warn('⚠️  Decrypting legacy data - consider migrating to new format');

      const { encrypted, iv, tag } = encryptedData;

      // Use old key derivation method for legacy data
      const purposeSalt = crypto.createHash('sha256').update(purpose).digest();
      const key = crypto.pbkdf2Sync(this.masterKey, purposeSalt, 100000, this.keyLength, 'sha256');

      // Use legacy decipher method
      const decipher = crypto.createDecipheriv(this.algorithm, key, Buffer.from(iv, 'hex'));
      decipher.setAuthTag(Buffer.from(tag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      this.logAudit('legacy_decryption', { purpose, warning: 'Consider migrating data' });

      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error(`Legacy decryption failed: ${error.message}`);
    }
  }

  /**
   * Migrate legacy encrypted data to new format
   * @param {Object} legacyData - Legacy encrypted data
   * @param {string} purpose - Purpose identifier
   * @returns {Object} New format encrypted data
   */
  migrateLegacyData(legacyData, purpose) {
    try {
      // Decrypt using legacy method
      const decryptedData = this.decryptLegacy(legacyData, purpose);

      // Re-encrypt using new format
      const newEncryptedData = this.encrypt(decryptedData, purpose);

      this.logAudit('data_migration', { purpose, from: 'legacy', to: this.version });

      return newEncryptedData;
    } catch (error) {
      this.logAudit('migration_failed', { purpose, error: error.message });
      throw new Error(`Data migration failed: ${error.message}`);
    }
  }

  /**
   * Audit logging for encryption operations
   * @param {string} action - Action performed
   * @param {Object} metadata - Additional metadata
   */
  logAudit(action, metadata = {}) {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      action,
      metadata: {
        ...metadata,
        nodeEnv: process.env.NODE_ENV,
        pid: process.pid
      }
    };

    this.auditLog.push(auditEntry);

    // Keep only recent entries to prevent memory issues
    if (this.auditLog.length > this.maxAuditEntries) {
      this.auditLog = this.auditLog.slice(-this.maxAuditEntries);
    }

    // Log critical events to console in development
    if (process.env.NODE_ENV === 'development' &&
        ['encryption_failed', 'decryption_failed', 'master_key_generated'].includes(action)) {
      console.log(`🔐 Encryption Audit: ${action}`, metadata);
    }
  }

  /**
   * Get audit log entries
   * @param {number} limit - Maximum number of entries to return
   * @returns {Array} Audit log entries
   */
  getAuditLog(limit = 100) {
    return this.auditLog.slice(-limit);
  }

  /**
   * Clear audit log
   */
  clearAuditLog() {
    const clearedCount = this.auditLog.length;
    this.auditLog = [];
    this.logAudit('audit_log_cleared', { clearedEntries: clearedCount });
  }

  /**
   * Hash sensitive data (one-way) with enhanced security
   */
  async hash(data, rounds = 12) {
    try {
      if (typeof data !== 'string') {
        data = JSON.stringify(data);
      }

      // Validate rounds parameter
      if (rounds < 10 || rounds > 15) {
        throw new Error('Hash rounds must be between 10 and 15');
      }

      const hash = await bcrypt.hash(data, rounds);

      this.logAudit('hash_created', { rounds, dataLength: data.length });

      return hash;
    } catch (error) {
      this.logAudit('hash_failed', { error: error.message });
      throw new Error(`Hashing failed: ${error.message}`);
    }
  }

  /**
   * Verify hashed data
   */
  async verifyHash(data, hash) {
    if (typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    return await bcrypt.compare(data, hash);
  }

  /**
   * Generate secure random token
   */
  generateToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate secure API key
   */
  generateApiKey(prefix = 'cvleap') {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(24).toString('base64url');
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Encrypt database field
   */
  encryptField(value, fieldName) {
    if (value === null || value === undefined) {
      return null;
    }
    return this.encrypt(value, `field_${fieldName}`);
  }

  /**
   * Decrypt database field
   */
  decryptField(encryptedValue, fieldName) {
    if (!encryptedValue) {
      return null;
    }
    return this.decrypt(encryptedValue, `field_${fieldName}`);
  }

  /**
   * Encrypt file contents
   */
  encryptFile(filePath, outputPath = null) {
    const fs = require('fs');
    const path = require('path');
    
    if (!outputPath) {
      outputPath = `${filePath}.encrypted`;
    }
    
    const data = fs.readFileSync(filePath);
    const encrypted = this.encrypt(data.toString('base64'), 'file');
    
    fs.writeFileSync(outputPath, JSON.stringify(encrypted));
    return outputPath;
  }

  /**
   * Decrypt file contents
   */
  decryptFile(encryptedFilePath, outputPath = null) {
    const fs = require('fs');
    
    if (!outputPath) {
      outputPath = encryptedFilePath.replace('.encrypted', '');
    }
    
    const encryptedData = JSON.parse(fs.readFileSync(encryptedFilePath, 'utf8'));
    const decrypted = this.decrypt(encryptedData, 'file');
    const data = Buffer.from(decrypted, 'base64');
    
    fs.writeFileSync(outputPath, data);
    return outputPath;
  }

  /**
   * Create encrypted backup of sensitive data
   */
  createEncryptedBackup(data, backupPath) {
    const fs = require('fs');
    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: this.encrypt(data, 'backup')
    };
    
    fs.writeFileSync(backupPath, JSON.stringify(backup, null, 2));
    return backupPath;
  }

  /**
   * Restore from encrypted backup
   */
  restoreFromEncryptedBackup(backupPath) {
    const fs = require('fs');
    const backup = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    return this.decrypt(backup.data, 'backup');
  }

  /**
   * Generate encryption key for new installations
   */
  static generateMasterKey() {
    return crypto.randomBytes(32).toString('base64');
  }

  /**
   * Rotate encryption keys (advanced operation)
   */
  async rotateKeys(newMasterKey) {
    // This is a complex operation that requires careful planning
    // In production, implement with proper backup and rollback procedures
    throw new Error('Key rotation not implemented - requires manual procedure');
  }

  /**
   * Generate cryptographically secure random string
   */
  generateSecureString(length = 16, charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
    try {
      if (length < 8 || length > 256) {
        throw new Error('String length must be between 8 and 256 characters');
      }

      const result = [];
      const charsetLength = charset.length;

      for (let i = 0; i < length; i++) {
        const randomIndex = crypto.randomInt(0, charsetLength);
        result.push(charset[randomIndex]);
      }

      const secureString = result.join('');

      this.logAudit('secure_string_generated', {
        length,
        charsetSize: charsetLength
      });

      return secureString;
    } catch (error) {
      this.logAudit('secure_string_failed', { error: error.message });
      throw new Error(`Secure string generation failed: ${error.message}`);
    }
  }

  /**
   * Secure memory cleanup (best effort)
   */
  secureCleanup() {
    try {
      // Clear sensitive data from memory (best effort in Node.js)
      if (this.masterKey) {
        this.masterKey.fill(0);
      }

      // Clear audit log
      this.auditLog = [];

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      console.log('🔐 Encryption service cleanup completed');
    } catch (error) {
      console.warn('⚠️  Encryption service cleanup failed:', error.message);
    }
  }

  /**
   * Get encryption service statistics
   */
  getStatistics() {
    const auditCounts = this.auditLog.reduce((counts, entry) => {
      counts[entry.action] = (counts[entry.action] || 0) + 1;
      return counts;
    }, {});

    return {
      version: this.version,
      algorithm: this.algorithm,
      keyLength: this.keyLength,
      auditEntries: this.auditLog.length,
      auditCounts,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    };
  }

  /**
   * Validate encryption configuration
   */
  validateConfiguration() {
    const issues = [];

    if (!this.masterKey || this.masterKey.length !== this.keyLength) {
      issues.push('Invalid master key length');
    }

    if (process.env.NODE_ENV === 'production' && !process.env.ENCRYPTION_MASTER_KEY) {
      issues.push('Master key not set in production environment');
    }

    if (this.iterations < 100000) {
      issues.push('PBKDF2 iterations too low for security');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Test encryption/decryption cycle with enhanced validation
   */
  testEncryption() {
    const testData = {
      text: 'Hello, World!',
      number: 12345,
      object: { nested: 'value' },
      array: [1, 2, 3],
      boolean: true,
      null: null
    };

    try {
      const startTime = Date.now();

      // Test new encryption format
      const encrypted = this.encrypt(testData, 'test');
      const decrypted = this.decrypt(encrypted, 'test');

      const processingTime = Date.now() - startTime;
      const isValid = JSON.stringify(testData) === JSON.stringify(decrypted);

      // Test with AAD
      const encryptedWithAAD = this.encrypt(testData, 'test', { aad: 'test-aad' });
      const decryptedWithAAD = this.decrypt(encryptedWithAAD, 'test');
      const isValidAAD = JSON.stringify(testData) === JSON.stringify(decryptedWithAAD);

      return {
        success: isValid && isValidAAD,
        version: this.version,
        algorithm: this.algorithm,
        processingTime,
        tests: {
          basic: {
            success: isValid,
            encrypted: encrypted.encrypted.substring(0, 20) + '...',
            hasTag: !!encrypted.tag,
            hasSalt: !!encrypted.salt
          },
          withAAD: {
            success: isValidAAD,
            hasAAD: !!encryptedWithAAD.aad
          }
        },
        original: testData,
        decrypted: decrypted
      };
    } catch (error) {
      this.logAudit('encryption_test_failed', { error: error.message });
      return {
        success: false,
        error: error.message,
        version: this.version
      };
    }
  }
}

// Create singleton instance
const encryptionService = new EncryptionService();

module.exports = encryptionService;