const crypto = require('crypto');
const bcrypt = require('bcryptjs');

/**
 * Encryption Service for data protection at rest and in transit
 */
class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 16;  // 128 bits
    this.tagLength = 16; // 128 bits
    
    // Initialize master key from environment or generate
    this.masterKey = this.initializeMasterKey();
  }

  /**
   * Initialize master encryption key
   */
  initializeMasterKey() {
    const key = process.env.ENCRYPTION_MASTER_KEY;
    
    if (key) {
      // Use provided key (should be base64 encoded)
      return Buffer.from(key, 'base64');
    }
    
    // Generate random key for development (DO NOT use in production)
    if (process.env.NODE_ENV === 'development') {
      console.warn('Using random encryption key - NOT for production use');
      return crypto.randomBytes(this.keyLength);
    }
    
    throw new Error('ENCRYPTION_MASTER_KEY environment variable is required for production');
  }

  /**
   * Generate a derived key for specific purposes
   */
  deriveKey(purpose, salt = null) {
    const purposeSalt = salt || crypto.createHash('sha256').update(purpose).digest();
    return crypto.pbkdf2Sync(this.masterKey, purposeSalt, 100000, this.keyLength, 'sha256');
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(data, purpose = 'default') {
    try {
      const key = this.deriveKey(purpose);
      const iv = crypto.randomBytes(this.ivLength);
      
      const cipher = crypto.createCipher(this.algorithm, key, { iv });
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        purpose
      };
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData, purpose = 'default') {
    try {
      const { encrypted, iv, tag } = encryptedData;
      const key = this.deriveKey(purpose);
      
      const decipher = crypto.createDecipher(this.algorithm, key, { 
        iv: Buffer.from(iv, 'hex')
      });
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * Hash sensitive data (one-way)
   */
  async hash(data, rounds = 12) {
    if (typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    return await bcrypt.hash(data, rounds);
  }

  /**
   * Verify hashed data
   */
  async verifyHash(data, hash) {
    if (typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    return await bcrypt.compare(data, hash);
  }

  /**
   * Generate secure random token
   */
  generateToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate secure API key
   */
  generateApiKey(prefix = 'cvleap') {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(24).toString('base64url');
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Encrypt database field
   */
  encryptField(value, fieldName) {
    if (value === null || value === undefined) {
      return null;
    }
    return this.encrypt(value, `field_${fieldName}`);
  }

  /**
   * Decrypt database field
   */
  decryptField(encryptedValue, fieldName) {
    if (!encryptedValue) {
      return null;
    }
    return this.decrypt(encryptedValue, `field_${fieldName}`);
  }

  /**
   * Encrypt file contents
   */
  encryptFile(filePath, outputPath = null) {
    const fs = require('fs');
    const path = require('path');
    
    if (!outputPath) {
      outputPath = `${filePath}.encrypted`;
    }
    
    const data = fs.readFileSync(filePath);
    const encrypted = this.encrypt(data.toString('base64'), 'file');
    
    fs.writeFileSync(outputPath, JSON.stringify(encrypted));
    return outputPath;
  }

  /**
   * Decrypt file contents
   */
  decryptFile(encryptedFilePath, outputPath = null) {
    const fs = require('fs');
    
    if (!outputPath) {
      outputPath = encryptedFilePath.replace('.encrypted', '');
    }
    
    const encryptedData = JSON.parse(fs.readFileSync(encryptedFilePath, 'utf8'));
    const decrypted = this.decrypt(encryptedData, 'file');
    const data = Buffer.from(decrypted, 'base64');
    
    fs.writeFileSync(outputPath, data);
    return outputPath;
  }

  /**
   * Create encrypted backup of sensitive data
   */
  createEncryptedBackup(data, backupPath) {
    const fs = require('fs');
    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: this.encrypt(data, 'backup')
    };
    
    fs.writeFileSync(backupPath, JSON.stringify(backup, null, 2));
    return backupPath;
  }

  /**
   * Restore from encrypted backup
   */
  restoreFromEncryptedBackup(backupPath) {
    const fs = require('fs');
    const backup = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    return this.decrypt(backup.data, 'backup');
  }

  /**
   * Generate encryption key for new installations
   */
  static generateMasterKey() {
    return crypto.randomBytes(32).toString('base64');
  }

  /**
   * Rotate encryption keys (advanced operation)
   */
  async rotateKeys(newMasterKey) {
    // This is a complex operation that requires careful planning
    // In production, implement with proper backup and rollback procedures
    throw new Error('Key rotation not implemented - requires manual procedure');
  }

  /**
   * Test encryption/decryption cycle
   */
  testEncryption() {
    const testData = {
      text: 'Hello, World!',
      number: 12345,
      object: { nested: 'value' }
    };

    try {
      const encrypted = this.encrypt(testData, 'test');
      const decrypted = this.decrypt(encrypted, 'test');
      
      const isValid = JSON.stringify(testData) === JSON.stringify(decrypted);
      
      return {
        success: isValid,
        original: testData,
        encrypted: encrypted.encrypted.substring(0, 20) + '...',
        decrypted: decrypted
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
const encryptionService = new EncryptionService();

module.exports = encryptionService;