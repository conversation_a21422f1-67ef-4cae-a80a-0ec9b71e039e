const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const Groq = require('groq-sdk');
const { NovitaSDK } = require('novita-sdk');
const AIConfig = require('./aiConfig');

class MultiModelAIClient {
  constructor() {
    this.config = new AIConfig();
    this.clients = {};
    this.initializeClients();
  }

  initializeClients() {
    // Initialize OpenAI client
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here') {
      try {
        this.clients.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY
        });
        console.log('OpenAI client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize OpenAI client:', error.message);
      }
    }

    // Initialize Anthropic Claude client
    if (process.env.ANTHROPIC_API_KEY && process.env.ANTHROPIC_API_KEY !== 'your_anthropic_api_key_here') {
      try {
        this.clients.claude = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY
        });
        console.log('Claude client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Claude client:', error.message);
      }
    }

    // Initialize Google Gemini client
    if (process.env.GOOGLE_AI_API_KEY && process.env.GOOGLE_AI_API_KEY !== 'your_google_ai_api_key_here') {
      try {
        this.clients.gemini = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
        console.log('Gemini client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Gemini client:', error.message);
      }
    }

    // Initialize Groq client
    if (process.env.GROQ_API_KEY && process.env.GROQ_API_KEY !== 'your_groq_api_key_here') {
      try {
        this.clients.groq = new Groq({
          apiKey: process.env.GROQ_API_KEY
        });
        console.log('Groq client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Groq client:', error.message);
      }
    }

    // Initialize Novita AI client
    if (process.env.NOVITA_API_KEY && process.env.NOVITA_API_KEY !== 'your_novita_api_key_here') {
      try {
        this.clients.novita = new NovitaSDK(process.env.NOVITA_API_KEY);
        console.log('Novita AI client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Novita AI client:', error.message);
      }
    }
  }

  async generateWithFallback(prompt, options = {}) {
    const {
      preferredModel = null,
      maxRetries = 3,
      temperature = 0.7,
      maxTokens = 1500
    } = options;

    let modelChain = this.config.fallbackChain;
    
    // If preferred model is specified and available, try it first
    if (preferredModel && this.config.isModelAvailable(preferredModel)) {
      modelChain = [preferredModel, ...modelChain.filter(m => m !== preferredModel)];
    }

    let lastError = null;

    for (const modelKey of modelChain) {
      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          const result = await this.generateWithModel(modelKey, prompt, {
            temperature,
            maxTokens
          });
          
          return {
            content: result,
            modelUsed: modelKey,
            attempt: attempt + 1,
            success: true
          };
        } catch (error) {
          lastError = error;
          console.warn(`AI generation failed for model ${modelKey}, attempt ${attempt + 1}:`, error.message);
          
          // Wait before retry (exponential backoff)
          if (attempt < maxRetries - 1) {
            await this.sleep(Math.pow(2, attempt) * 1000);
          }
        }
      }
    }

    throw new Error(`All AI models failed. Last error: ${lastError?.message || 'Unknown error'}`);
  }

  async generateWithModel(modelKey, prompt, options = {}) {
    const modelConfig = this.config.getModelConfig(modelKey);
    if (!modelConfig || !modelConfig.enabled) {
      throw new Error(`Model ${modelKey} is not available`);
    }

    const client = this.clients[modelKey];
    if (!client) {
      throw new Error(`Client for model ${modelKey} is not initialized`);
    }

    switch (modelConfig.provider) {
      case 'openai':
        return await this.generateOpenAI(client, modelConfig, prompt, options);
      case 'anthropic':
        return await this.generateClaude(client, modelConfig, prompt, options);
      case 'google':
        return await this.generateGemini(client, modelConfig, prompt, options);
      case 'groq':
        return await this.generateGroq(client, modelConfig, prompt, options);
      case 'novita':
        return await this.generateNovita(client, modelConfig, prompt, options);
      default:
        throw new Error(`Unknown provider: ${modelConfig.provider}`);
    }
  }

  async generateOpenAI(client, config, prompt, options) {
    const model = options.model || config.model;
    
    try {
      const response = await client.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || config.temperature,
        max_tokens: options.maxTokens || config.maxTokens,
      });

      return response.choices[0].message.content;
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying fallback model: ${config.fallback}`);
        const response = await client.chat.completions.create({
          model: config.fallback,
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || config.temperature,
          max_tokens: options.maxTokens || config.maxTokens,
        });

        return response.choices[0].message.content;
      }
      throw error;
    }
  }

  async generateClaude(client, config, prompt, options) {
    const model = options.model || config.model;

    try {
      const response = await client.messages.create({
        model: model,
        max_tokens: options.maxTokens || config.maxTokens,
        temperature: options.temperature || config.temperature,
        messages: [{ role: 'user', content: prompt }],
      });

      return response.content[0].text;
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying Claude fallback model: ${config.fallback}`);
        const response = await client.messages.create({
          model: config.fallback,
          max_tokens: options.maxTokens || config.maxTokens,
          temperature: options.temperature || config.temperature,
          messages: [{ role: 'user', content: prompt }],
        });

        return response.content[0].text;
      }

      // Handle specific Claude API errors
      if (error.status === 429) {
        throw new Error('Claude API rate limit exceeded. Please try again later.');
      } else if (error.status === 401) {
        throw new Error('Claude API authentication failed. Please check your API key.');
      } else if (error.status === 400) {
        throw new Error(`Claude API request error: ${error.message}`);
      }

      throw error;
    }
  }

  async generateGemini(client, config, prompt, options) {
    const model = options.model || config.model;

    try {
      const generativeModel = client.getGenerativeModel({
        model: model,
        generationConfig: {
          temperature: options.temperature || config.temperature,
          maxOutputTokens: options.maxTokens || config.maxTokens,
        }
      });

      const result = await generativeModel.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying Gemini fallback model: ${config.fallback}`);
        const generativeModel = client.getGenerativeModel({
          model: config.fallback,
          generationConfig: {
            temperature: options.temperature || config.temperature,
            maxOutputTokens: options.maxTokens || config.maxTokens,
          }
        });

        const result = await generativeModel.generateContent(prompt);
        const response = await result.response;
        return response.text();
      }

      // Handle specific Gemini API errors
      if (error.message?.includes('API_KEY_INVALID')) {
        throw new Error('Gemini API authentication failed. Please check your API key.');
      } else if (error.message?.includes('QUOTA_EXCEEDED')) {
        throw new Error('Gemini API quota exceeded. Please try again later.');
      } else if (error.message?.includes('SAFETY')) {
        throw new Error('Gemini API blocked the request due to safety concerns.');
      }

      throw error;
    }
  }

  async generateGroq(client, config, prompt, options) {
    const model = options.model || config.model;

    try {
      const response = await client.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || config.temperature,
        max_tokens: options.maxTokens || config.maxTokens,
      });

      return response.choices[0].message.content;
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying Groq fallback model: ${config.fallback}`);
        const response = await client.chat.completions.create({
          model: config.fallback,
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || config.temperature,
          max_tokens: options.maxTokens || config.maxTokens,
        });

        return response.choices[0].message.content;
      }

      // Handle specific Groq API errors
      if (error.status === 429) {
        throw new Error('Groq API rate limit exceeded. Please try again later.');
      } else if (error.status === 401) {
        throw new Error('Groq API authentication failed. Please check your API key.');
      } else if (error.status === 400) {
        throw new Error(`Groq API request error: ${error.message}`);
      }

      throw error;
    }
  }

  async generateNovita(client, config, prompt, options) {
    const model = options.model || config.model;

    try {
      const response = await client.chatCompletion({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || config.temperature,
        max_tokens: options.maxTokens || config.maxTokens,
      });

      return response.choices[0].message.content;
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying Novita fallback model: ${config.fallback}`);
        const response = await client.chatCompletion({
          model: config.fallback,
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || config.temperature,
          max_tokens: options.maxTokens || config.maxTokens,
        });

        return response.choices[0].message.content;
      }

      // Handle specific Novita API errors
      if (error.status === 429) {
        throw new Error('Novita API rate limit exceeded. Please try again later.');
      } else if (error.status === 401) {
        throw new Error('Novita API authentication failed. Please check your API key.');
      } else if (error.status === 400) {
        throw new Error(`Novita API request error: ${error.message}`);
      }

      throw error;
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getAvailableModels() {
    return this.config.getAvailableModels();
  }

  getModelStatus() {
    const models = this.config.getAvailableModels();
    return models.map(model => ({
      ...model,
      clientInitialized: !!this.clients[model.key],
      status: this.clients[model.key] ? 'ready' : 'not_initialized'
    }));
  }
}

module.exports = MultiModelAIClient;