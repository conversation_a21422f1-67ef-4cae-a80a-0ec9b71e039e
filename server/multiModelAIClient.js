const OpenAI = require('openai');
const AIConfig = require('./aiConfig');

class MultiModelAIClient {
  constructor() {
    this.config = new AIConfig();
    this.clients = {};
    this.initializeClients();
  }

  initializeClients() {
    // Initialize OpenAI client
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here') {
      this.clients.openai = new OpenAI({ 
        apiKey: process.env.OPENAI_API_KEY 
      });
    }

    // Initialize Anthropic client (placeholder - would need anthropic package)
    if (process.env.ANTHROPIC_API_KEY) {
      // this.clients.claude = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
      console.log('Claude API key found but client not implemented yet');
    }

    // Initialize Google AI client (placeholder - would need @google/generative-ai package)
    if (process.env.GOOGLE_AI_API_KEY) {
      // this.clients.gemini = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
      console.log('Gemini API key found but client not implemented yet');
    }
  }

  async generateWithFallback(prompt, options = {}) {
    const {
      preferredModel = null,
      maxRetries = 3,
      temperature = 0.7,
      maxTokens = 1500
    } = options;

    let modelChain = this.config.fallbackChain;
    
    // If preferred model is specified and available, try it first
    if (preferredModel && this.config.isModelAvailable(preferredModel)) {
      modelChain = [preferredModel, ...modelChain.filter(m => m !== preferredModel)];
    }

    let lastError = null;

    for (const modelKey of modelChain) {
      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          const result = await this.generateWithModel(modelKey, prompt, {
            temperature,
            maxTokens
          });
          
          return {
            content: result,
            modelUsed: modelKey,
            attempt: attempt + 1,
            success: true
          };
        } catch (error) {
          lastError = error;
          console.warn(`AI generation failed for model ${modelKey}, attempt ${attempt + 1}:`, error.message);
          
          // Wait before retry (exponential backoff)
          if (attempt < maxRetries - 1) {
            await this.sleep(Math.pow(2, attempt) * 1000);
          }
        }
      }
    }

    throw new Error(`All AI models failed. Last error: ${lastError?.message || 'Unknown error'}`);
  }

  async generateWithModel(modelKey, prompt, options = {}) {
    const modelConfig = this.config.getModelConfig(modelKey);
    if (!modelConfig || !modelConfig.enabled) {
      throw new Error(`Model ${modelKey} is not available`);
    }

    const client = this.clients[modelKey];
    if (!client) {
      throw new Error(`Client for model ${modelKey} is not initialized`);
    }

    switch (modelConfig.provider) {
      case 'openai':
        return await this.generateOpenAI(client, modelConfig, prompt, options);
      case 'anthropic':
        return await this.generateClaude(client, modelConfig, prompt, options);
      case 'google':
        return await this.generateGemini(client, modelConfig, prompt, options);
      default:
        throw new Error(`Unknown provider: ${modelConfig.provider}`);
    }
  }

  async generateOpenAI(client, config, prompt, options) {
    const model = options.model || config.model;
    
    try {
      const response = await client.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || config.temperature,
        max_tokens: options.maxTokens || config.maxTokens,
      });

      return response.choices[0].message.content;
    } catch (error) {
      // Try fallback model if primary fails
      if (model !== config.fallback && config.fallback) {
        console.log(`Trying fallback model: ${config.fallback}`);
        const response = await client.chat.completions.create({
          model: config.fallback,
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || config.temperature,
          max_tokens: options.maxTokens || config.maxTokens,
        });

        return response.choices[0].message.content;
      }
      throw error;
    }
  }

  async generateClaude(client, config, prompt, options) {
    // Placeholder for Claude implementation
    throw new Error('Claude integration not implemented yet');
  }

  async generateGemini(client, config, prompt, options) {
    // Placeholder for Gemini implementation
    throw new Error('Gemini integration not implemented yet');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getAvailableModels() {
    return this.config.getAvailableModels();
  }

  getModelStatus() {
    const models = this.config.getAvailableModels();
    return models.map(model => ({
      ...model,
      clientInitialized: !!this.clients[model.key],
      status: this.clients[model.key] ? 'ready' : 'not_initialized'
    }));
  }
}

module.exports = MultiModelAIClient;