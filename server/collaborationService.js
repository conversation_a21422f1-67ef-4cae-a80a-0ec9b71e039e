const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const database = require('./database');
const notificationService = require('./notificationService');

/**
 * Real-Time Collaboration Service
 * Handles multi-user editing, presence tracking, and operational transformation
 */
class CollaborationService {
  constructor() {
    // Track active editing sessions per resume
    this.activeSessions = new Map(); // resumeId -> Set of sessionIds
    this.sessionData = new Map(); // sessionId -> { userId, resumeId, ws, cursor, selection }
    this.operationQueue = new Map(); // resumeId -> Array of pending operations
    this.conflictResolver = new ConflictResolver();
  }

  /**
   * Initialize collaborative editing session
   */
  async joinEditingSession(ws, resumeId, userId, sessionId) {
    try {
      // Verify user has permission to edit this resume
      const hasPermission = await this.checkEditPermission(resumeId, userId);
      if (!hasPermission) {
        ws.send(JSON.stringify({
          type: 'collaboration_error',
          message: 'No permission to edit this resume'
        }));
        return false;
      }

      // Store session data
      this.sessionData.set(sessionId, {
        userId,
        resumeId,
        ws,
        cursor: { line: 0, column: 0 },
        selection: null,
        lastActivity: Date.now()
      });

      // Add to active sessions for this resume
      if (!this.activeSessions.has(resumeId)) {
        this.activeSessions.set(resumeId, new Set());
      }
      this.activeSessions.get(resumeId).add(sessionId);

      // Store in database
      await this.storeEditingSession(resumeId, userId, sessionId);

      // Notify other users in the session
      this.broadcastToResume(resumeId, {
        type: 'user_joined',
        userId,
        sessionId,
        timestamp: new Date().toISOString()
      }, sessionId);

      // Send current active users to the new joiner
      const activeUsers = await this.getActiveUsers(resumeId);
      ws.send(JSON.stringify({
        type: 'session_joined',
        resumeId,
        activeUsers,
        timestamp: new Date().toISOString()
      }));

      return true;
    } catch (error) {
      console.error('Error joining editing session:', error);
      return false;
    }
  }

  /**
   * Handle real-time editing operations
   */
  async handleOperation(sessionId, operation) {
    const session = this.sessionData.get(sessionId);
    if (!session) return;

    const { resumeId, userId } = session;

    try {
      // Add operation metadata
      const enrichedOperation = {
        ...operation,
        userId,
        sessionId,
        timestamp: Date.now(),
        operationId: this.generateOperationId()
      };

      // Apply operational transformation for conflict resolution
      const transformedOperation = await this.conflictResolver.transform(
        resumeId, 
        enrichedOperation, 
        this.operationQueue.get(resumeId) || []
      );

      // Store operation in queue
      if (!this.operationQueue.has(resumeId)) {
        this.operationQueue.set(resumeId, []);
      }
      this.operationQueue.get(resumeId).push(transformedOperation);

      // Store change in database
      await this.storeResumeChange(transformedOperation);

      // Broadcast to other users
      this.broadcastToResume(resumeId, {
        type: 'operation',
        operation: transformedOperation
      }, sessionId);

      // Clean up old operations (keep last 100)
      const queue = this.operationQueue.get(resumeId);
      if (queue.length > 100) {
        this.operationQueue.set(resumeId, queue.slice(-100));
      }

    } catch (error) {
      console.error('Error handling operation:', error);
      session.ws.send(JSON.stringify({
        type: 'operation_error',
        message: 'Failed to process operation'
      }));
    }
  }

  /**
   * Update cursor position
   */
  async updateCursor(sessionId, cursor, selection = null) {
    const session = this.sessionData.get(sessionId);
    if (!session) return;

    session.cursor = cursor;
    session.selection = selection;
    session.lastActivity = Date.now();

    // Update in database
    await this.updateSessionActivity(sessionId, cursor, selection);

    // Broadcast cursor position to other users
    this.broadcastToResume(session.resumeId, {
      type: 'cursor_update',
      userId: session.userId,
      sessionId,
      cursor,
      selection,
      timestamp: Date.now()
    }, sessionId);
  }

  /**
   * Handle user leaving editing session
   */
  async leaveEditingSession(sessionId) {
    const session = this.sessionData.get(sessionId);
    if (!session) return;

    const { resumeId, userId } = session;

    try {
      // Remove from active sessions
      if (this.activeSessions.has(resumeId)) {
        this.activeSessions.get(resumeId).delete(sessionId);
        
        // Clean up empty resume sessions
        if (this.activeSessions.get(resumeId).size === 0) {
          this.activeSessions.delete(resumeId);
          this.operationQueue.delete(resumeId);
        }
      }

      // Remove session data
      this.sessionData.delete(sessionId);

      // Update database
      await this.endEditingSession(sessionId);

      // Notify other users
      this.broadcastToResume(resumeId, {
        type: 'user_left',
        userId,
        sessionId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error leaving editing session:', error);
    }
  }

  /**
   * Broadcast message to all users editing a specific resume
   */
  broadcastToResume(resumeId, message, excludeSessionId = null) {
    const sessions = this.activeSessions.get(resumeId);
    if (!sessions) return;

    sessions.forEach(sessionId => {
      if (sessionId === excludeSessionId) return;
      
      const session = this.sessionData.get(sessionId);
      if (session && session.ws.readyState === WebSocket.OPEN) {
        try {
          session.ws.send(JSON.stringify(message));
        } catch (error) {
          console.error('Error broadcasting to session:', sessionId, error);
        }
      }
    });
  }

  /**
   * Get active users for a resume
   */
  async getActiveUsers(resumeId) {
    const sessions = this.activeSessions.get(resumeId);
    if (!sessions) return [];

    const activeUsers = [];
    for (const sessionId of sessions) {
      const session = this.sessionData.get(sessionId);
      if (session) {
        // Get user details from database
        const user = await this.getUserDetails(session.userId);
        activeUsers.push({
          userId: session.userId,
          sessionId,
          name: user.name,
          cursor: session.cursor,
          selection: session.selection,
          lastActivity: session.lastActivity
        });
      }
    }

    return activeUsers;
  }

  /**
   * Database operations
   */
  async checkEditPermission(resumeId, userId) {
    return new Promise((resolve, reject) => {
      // Check if user owns the resume
      database.get().get(
        'SELECT id FROM resumes WHERE id = ? AND user_id = ?',
        [resumeId, userId],
        (err, row) => {
          if (err) {
            // Check collaborative permissions
            database.get().get(
              `SELECT rp.permission_type 
               FROM resume_permissions rp 
               WHERE rp.resume_id = ? AND rp.user_id = ? 
               AND rp.permission_type IN ('edit', 'admin')`,
              [resumeId, userId],
              (err2, row2) => {
                if (err2) reject(err2);
                else resolve(!!row2);
              }
            );
          } else {
            resolve(!!row);
          }
        }
      );
    });
  }

  async storeEditingSession(resumeId, userId, sessionId) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT OR REPLACE INTO editing_sessions 
         (resume_id, user_id, session_id, last_activity, status) 
         VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'active')`,
        [resumeId, userId, sessionId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async updateSessionActivity(sessionId, cursor, selection) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `UPDATE editing_sessions 
         SET cursor_position = ?, current_selection = ?, last_activity = CURRENT_TIMESTAMP 
         WHERE session_id = ?`,
        [JSON.stringify(cursor), JSON.stringify(selection), sessionId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async endEditingSession(sessionId) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `UPDATE editing_sessions 
         SET status = 'ended', last_activity = CURRENT_TIMESTAMP 
         WHERE session_id = ?`,
        [sessionId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async storeResumeChange(operation) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT INTO resume_changes 
         (resume_id, user_id, change_type, section_id, old_content, new_content, 
          change_position, change_metadata, operation_id) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          operation.resumeId,
          operation.userId,
          operation.type,
          operation.sectionId || null,
          operation.oldContent || null,
          operation.newContent || null,
          operation.position || null,
          JSON.stringify(operation.metadata || {}),
          operation.operationId
        ],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async getUserDetails(userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id, name, email FROM users WHERE id = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row || { id: userId, name: 'Unknown User', email: '' });
        }
      );
    });
  }

  generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup inactive sessions
   */
  cleanupInactiveSessions() {
    const cutoffTime = Date.now() - (30 * 60 * 1000); // 30 minutes

    for (const [sessionId, session] of this.sessionData.entries()) {
      if (session.lastActivity < cutoffTime) {
        this.leaveEditingSession(sessionId);
      }
    }
  }

  /**
   * Get collaboration statistics
   */
  getStats() {
    return {
      activeSessions: this.sessionData.size,
      activeResumes: this.activeSessions.size,
      pendingOperations: Array.from(this.operationQueue.values())
        .reduce((total, queue) => total + queue.length, 0)
    };
  }
}

/**
 * Conflict Resolution using Operational Transformation
 */
class ConflictResolver {
  async transform(resumeId, operation, existingOperations) {
    // Simple operational transformation
    // In a production system, this would implement proper OT algorithms
    
    let transformedOperation = { ...operation };
    
    // Adjust position based on previous operations
    for (const existingOp of existingOperations) {
      if (existingOp.timestamp < operation.timestamp) {
        transformedOperation = this.adjustPosition(transformedOperation, existingOp);
      }
    }
    
    return transformedOperation;
  }

  adjustPosition(operation, existingOperation) {
    // Simple position adjustment logic
    // This would be much more sophisticated in production
    if (operation.type === 'insert' && existingOperation.type === 'insert') {
      if (existingOperation.position <= operation.position) {
        operation.position += existingOperation.newContent.length;
      }
    } else if (operation.type === 'delete' && existingOperation.type === 'insert') {
      if (existingOperation.position < operation.position) {
        operation.position += existingOperation.newContent.length;
      }
    }
    
    return operation;
  }
}

// Create singleton instance
const collaborationService = new CollaborationService();

// Cleanup inactive sessions every 5 minutes
setInterval(() => {
  collaborationService.cleanupInactiveSessions();
}, 5 * 60 * 1000);

module.exports = collaborationService;