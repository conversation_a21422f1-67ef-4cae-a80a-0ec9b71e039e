const fs = require('fs').promises;
const path = require('path');

/**
 * Enhanced Template Export Service
 * Handles multiple export formats: PDF, Word, Google Docs
 */
class TemplateExportService {
  constructor() {
    this.exportDirectory = path.join(__dirname, '../exports');
    this.ensureExportDirectory();
  }

  async ensureExportDirectory() {
    try {
      await fs.mkdir(this.exportDirectory, { recursive: true });
    } catch (error) {
      console.error('Failed to create export directory:', error);
    }
  }

  /**
   * Export template in specified format
   */
  async exportTemplate(templateId, format, resumeData, options = {}) {
    try {
      switch (format.toLowerCase()) {
        case 'pdf':
          return await this.exportToPDF(templateId, resumeData, options);
        case 'word':
        case 'docx':
          return await this.exportToWord(templateId, resumeData, options);
        case 'google-docs':
          return await this.exportToGoogleDocs(templateId, resumeData, options);
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      console.error(`Export failed for format ${format}:`, error);
      throw error;
    }
  }

  /**
   * Export to PDF format
   */
  async exportToPDF(templateId, resumeData, options) {
    // For this implementation, we'll create a mock PDF export
    // In a real implementation, you would use libraries like puppeteer, jsPDF, or PDF-lib
    const filename = `resume_${templateId}_${Date.now()}.pdf`;
    const filepath = path.join(this.exportDirectory, filename);

    // Mock PDF content - replace with actual PDF generation
    const mockPDFContent = this.generateMockPDFContent(resumeData);
    
    try {
      await fs.writeFile(filepath, mockPDFContent);
      
      return {
        success: true,
        format: 'pdf',
        filename,
        filepath,
        downloadUrl: `/api/templates/download/${filename}`,
        metadata: {
          pageCount: 1,
          fileSize: mockPDFContent.length,
          atsOptimized: options.atsOptimized || false,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      throw new Error(`PDF generation failed: ${error.message}`);
    }
  }

  /**
   * Export to Word format
   */
  async exportToWord(templateId, resumeData, options) {
    // Mock Word export - in reality, use docx or officegen libraries
    const filename = `resume_${templateId}_${Date.now()}.docx`;
    const filepath = path.join(this.exportDirectory, filename);

    const mockWordContent = this.generateMockWordContent(resumeData);
    
    try {
      await fs.writeFile(filepath, mockWordContent);
      
      return {
        success: true,
        format: 'word',
        filename,
        filepath,
        downloadUrl: `/api/templates/download/${filename}`,
        metadata: {
          wordVersion: '2016+',
          fileSize: mockWordContent.length,
          atsCompatible: true,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      throw new Error(`Word generation failed: ${error.message}`);
    }
  }

  /**
   * Export to Google Docs format
   */
  async exportToGoogleDocs(templateId, resumeData, options) {
    // Mock Google Docs export - in reality, use Google Docs API
    const filename = `resume_${templateId}_${Date.now()}.gdoc`;
    const filepath = path.join(this.exportDirectory, filename);

    const googleDocsData = {
      title: `Resume - ${resumeData.personalInfo?.fullName || 'Untitled'}`,
      content: this.generateGoogleDocsContent(resumeData),
      format: 'google-docs',
      sharing: options.sharing || 'private',
      templateId,
      generatedAt: new Date().toISOString()
    };

    try {
      await fs.writeFile(filepath, JSON.stringify(googleDocsData, null, 2));
      
      return {
        success: true,
        format: 'google-docs',
        filename,
        filepath,
        downloadUrl: `/api/templates/download/${filename}`,
        shareUrl: `https://docs.google.com/document/d/mock-document-id`,
        metadata: {
          documentId: 'mock-document-id',
          sharing: googleDocsData.sharing,
          collaborative: true,
          generatedAt: googleDocsData.generatedAt
        }
      };
    } catch (error) {
      throw new Error(`Google Docs generation failed: ${error.message}`);
    }
  }

  /**
   * Generate mock PDF content
   */
  generateMockPDFContent(resumeData) {
    return `%PDF-1.4
Mock PDF Resume Content for ${resumeData.personalInfo?.fullName || 'User'}
Generated at: ${new Date().toISOString()}

This is a mock PDF file. In a real implementation, this would be
generated using a proper PDF library like puppeteer, jsPDF, or PDF-lib.

Resume Data:
- Name: ${resumeData.personalInfo?.fullName || 'N/A'}
- Email: ${resumeData.personalInfo?.email || 'N/A'}
- Phone: ${resumeData.personalInfo?.phone || 'N/A'}
- Summary: ${resumeData.summary || 'N/A'}

Experience:
${resumeData.experience?.map(exp => `- ${exp.position} at ${exp.company}`).join('\n') || 'N/A'}

Education:
${resumeData.education?.map(edu => `- ${edu.degree} from ${edu.school}`).join('\n') || 'N/A'}

Skills:
${Object.entries(resumeData.skills || {}).map(([category, skills]) => 
  `${category}: ${Array.isArray(skills) ? skills.join(', ') : skills}`
).join('\n') || 'N/A'}
`;
  }

  /**
   * Generate mock Word content
   */
  generateMockWordContent(resumeData) {
    return `
Mock Word Document Resume for ${resumeData.personalInfo?.fullName || 'User'}

Personal Information:
Name: ${resumeData.personalInfo?.fullName || 'N/A'}
Email: ${resumeData.personalInfo?.email || 'N/A'}
Phone: ${resumeData.personalInfo?.phone || 'N/A'}
Location: ${resumeData.personalInfo?.location || 'N/A'}

Professional Summary:
${resumeData.summary || 'N/A'}

Work Experience:
${resumeData.experience?.map(exp => `
${exp.position} - ${exp.company}
${exp.duration}
${Array.isArray(exp.achievements) ? exp.achievements.map(a => `• ${a}`).join('\n') : ''}
`).join('\n') || 'N/A'}

Education:
${resumeData.education?.map(edu => `
${edu.degree}
${edu.school} - ${edu.year}
`).join('\n') || 'N/A'}

Skills:
${Object.entries(resumeData.skills || {}).map(([category, skills]) => `
${category}:
${Array.isArray(skills) ? skills.map(s => `• ${s}`).join('\n') : skills}
`).join('\n') || 'N/A'}

Projects:
${resumeData.projects?.map(project => `
${project.name}
${project.description}
Technologies: ${project.technologies.join(', ')}
`).join('\n') || 'N/A'}

Generated at: ${new Date().toISOString()}
`;
  }

  /**
   * Generate Google Docs content structure
   */
  generateGoogleDocsContent(resumeData) {
    return {
      sections: [
        {
          type: 'header',
          content: {
            name: resumeData.personalInfo?.fullName || 'N/A',
            title: resumeData.personalInfo?.title || 'N/A',
            contact: {
              email: resumeData.personalInfo?.email || 'N/A',
              phone: resumeData.personalInfo?.phone || 'N/A',
              location: resumeData.personalInfo?.location || 'N/A',
              website: resumeData.personalInfo?.website
            }
          }
        },
        {
          type: 'summary',
          content: resumeData.summary || 'N/A'
        },
        {
          type: 'experience',
          content: resumeData.experience || []
        },
        {
          type: 'education',
          content: resumeData.education || []
        },
        {
          type: 'skills',
          content: resumeData.skills || {}
        },
        {
          type: 'projects',
          content: resumeData.projects || []
        }
      ],
      formatting: {
        fontSize: 12,
        fontFamily: 'Arial',
        lineSpacing: 1.15,
        margins: { top: 1, bottom: 1, left: 1, right: 1 }
      }
    };
  }

  /**
   * Get export file by filename
   */
  async getExportFile(filename) {
    const filepath = path.join(this.exportDirectory, filename);
    
    try {
      await fs.access(filepath);
      return {
        filepath,
        exists: true
      };
    } catch (error) {
      return {
        filepath: null,
        exists: false,
        error: 'File not found'
      };
    }
  }

  /**
   * Clean up old export files
   */
  async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = await fs.readdir(this.exportDirectory);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000;

      for (const file of files) {
        const filepath = path.join(this.exportDirectory, file);
        const stats = await fs.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filepath);
          console.log(`Cleaned up old export file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old export files:', error);
    }
  }

  /**
   * Get export statistics
   */
  async getExportStatistics() {
    try {
      const files = await fs.readdir(this.exportDirectory);
      const stats = {
        totalFiles: files.length,
        formats: {
          pdf: files.filter(f => f.endsWith('.pdf')).length,
          word: files.filter(f => f.endsWith('.docx')).length,
          googleDocs: files.filter(f => f.endsWith('.gdoc')).length
        },
        totalSize: 0
      };

      for (const file of files) {
        const filepath = path.join(this.exportDirectory, file);
        const fileStats = await fs.stat(filepath);
        stats.totalSize += fileStats.size;
      }

      return stats;
    } catch (error) {
      console.error('Failed to get export statistics:', error);
      return null;
    }
  }
}

// Export singleton instance
module.exports = new TemplateExportService();