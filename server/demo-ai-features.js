#!/usr/bin/env node

/**
 * Demo script showing AI integrations with CVLeap features
 * This demonstrates how the multi-model AI client integrates with the application
 */

require('dotenv').config();
const MultiModelAIClient = require('./multiModelAIClient');

async function demoAIFeatures() {
  console.log('🚀 CVLeap AI Features Demo\n');

  const client = new MultiModelAIClient();
  
  // Check system status
  console.log('📊 System Status:');
  const modelStatus = client.getModelStatus();
  const activeModels = modelStatus.filter(m => m.status === 'ready');
  console.log(`   Active AI Models: ${activeModels.length}/${modelStatus.length}`);
  activeModels.forEach(model => {
    console.log(`   ✅ ${model.name}`);
  });
  
  if (activeModels.length === 0) {
    console.log('\n❌ No AI models are configured. Please set up API keys to continue.');
    console.log('   Required environment variables:');
    console.log('   - OPENAI_API_KEY');
    console.log('   - ANTHROPIC_API_KEY');
    console.log('   - GOOGLE_AI_API_KEY');
    console.log('   - GROQ_API_KEY');
    console.log('   - NOVITA_API_KEY');
    return;
  }
  
  console.log('\n🎯 Demo Scenarios:\n');

  // Demo 1: Resume Enhancement
  console.log('1️⃣ Resume Enhancement');
  const resumePrompt = `
Enhance this resume summary for a software engineer position:

"I am a developer with experience in JavaScript. I have worked on web applications and know React."

Make it more professional and compelling, highlighting specific skills and achievements.
  `.trim();

  try {
    const result = await client.generateWithFallback(resumePrompt, {
      temperature: 0.7,
      maxTokens: 300
    });
    console.log(`   ✅ Enhanced by ${result.modelUsed}`);
    console.log(`   📝 Result: ${result.content.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
  }
  console.log();

  // Demo 2: Cover Letter Generation
  console.log('2️⃣ Cover Letter Generation');
  const coverLetterPrompt = `
Generate a professional cover letter opening paragraph for:
- Position: Senior Frontend Developer
- Company: TechCorp Inc.
- Candidate: 5 years experience in React, TypeScript, and Node.js
- Key achievement: Led a team that increased app performance by 40%

Keep it concise and engaging.
  `.trim();

  try {
    const result = await client.generateWithFallback(coverLetterPrompt, {
      preferredModel: 'claude', // Claude is often better for creative writing
      temperature: 0.8,
      maxTokens: 250
    });
    console.log(`   ✅ Generated by ${result.modelUsed}`);
    console.log(`   📝 Result: ${result.content.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
  }
  console.log();

  // Demo 3: Job Description Analysis
  console.log('3️⃣ Job Description Analysis');
  const analysisPrompt = `
Analyze this job description and extract key requirements:

"We're looking for a Full Stack Developer with 3+ years experience in React, Node.js, and PostgreSQL. Must have experience with AWS, Docker, and agile methodologies. Strong communication skills required."

Provide a JSON response with: required_skills, preferred_skills, experience_level, and key_technologies.
  `.trim();

  try {
    const result = await client.generateWithFallback(analysisPrompt, {
      preferredModel: 'openai', // GPT-4 is often better for structured output
      temperature: 0.3, // Lower temperature for more consistent JSON
      maxTokens: 400
    });
    console.log(`   ✅ Analyzed by ${result.modelUsed}`);
    console.log(`   📊 Result: ${result.content.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
  }
  console.log();

  // Demo 4: Skill Gap Analysis
  console.log('4️⃣ Skill Gap Analysis');
  const skillGapPrompt = `
Compare these skills and identify gaps:

Current Skills: JavaScript, React, HTML, CSS, Git
Job Requirements: JavaScript, React, TypeScript, Node.js, Docker, AWS, PostgreSQL

Provide recommendations for skill development priority.
  `.trim();

  try {
    const result = await client.generateWithFallback(skillGapPrompt, {
      preferredModel: 'gemini', // Gemini is good for analytical tasks
      temperature: 0.5,
      maxTokens: 350
    });
    console.log(`   ✅ Analyzed by ${result.modelUsed}`);
    console.log(`   📈 Result: ${result.content.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
  }
  console.log();

  // Demo 5: Interview Preparation
  console.log('5️⃣ Interview Question Generation');
  const interviewPrompt = `
Generate 3 technical interview questions for a React developer position, including:
- One coding question
- One system design question  
- One behavioral question

Format as a numbered list.
  `.trim();

  try {
    const result = await client.generateWithFallback(interviewPrompt, {
      preferredModel: 'groq', // Groq is fast for quick generation
      temperature: 0.6,
      maxTokens: 400
    });
    console.log(`   ✅ Generated by ${result.modelUsed}`);
    console.log(`   ❓ Questions: ${result.content.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
  }
  console.log();

  // Demo 6: Performance Comparison
  console.log('6️⃣ Performance Comparison');
  const simplePrompt = "Write a one-sentence professional summary for a data scientist.";
  
  console.log('   Testing response times across models...');
  for (const model of activeModels.slice(0, 3)) { // Test up to 3 models
    try {
      const startTime = Date.now();
      const result = await client.generateWithFallback(simplePrompt, {
        preferredModel: model.key,
        maxRetries: 1,
        maxTokens: 100
      });
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`   ⚡ ${model.name}: ${duration}ms`);
    } catch (error) {
      console.log(`   ❌ ${model.name}: Failed (${error.message.substring(0, 50)}...)`);
    }
  }
  console.log();

  // Demo 7: Fallback Chain Test
  console.log('7️⃣ Fallback Chain Reliability');
  console.log('   Testing automatic fallback when preferred model fails...');
  
  try {
    const result = await client.generateWithFallback(
      "Generate a brief professional email signature.",
      {
        preferredModel: 'nonexistent-model', // This will fail and trigger fallback
        maxTokens: 150
      }
    );
    console.log(`   ✅ Fallback successful! Used: ${result.modelUsed}`);
    console.log(`   📧 Result: ${result.content.substring(0, 100)}...`);
  } catch (error) {
    console.log(`   ❌ All models failed: ${error.message}`);
  }
  console.log();

  console.log('🎉 Demo Complete!');
  console.log('\n💡 Key Features Demonstrated:');
  console.log('   ✅ Multi-provider AI integration');
  console.log('   ✅ Automatic fallback chain');
  console.log('   ✅ Error handling and recovery');
  console.log('   ✅ Performance optimization');
  console.log('   ✅ CVLeap-specific use cases');
  console.log('   ✅ Flexible model selection');
}

// Run the demo
if (require.main === module) {
  demoAIFeatures().catch(console.error);
}

module.exports = demoAIFeatures;
