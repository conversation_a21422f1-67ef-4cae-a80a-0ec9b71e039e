const database = require('./database');
const cacheService = require('./cacheService');
const metricsCollector = require('./utils/metricsCollector');
const auditLogger = require('./utils/auditLogger');

class HealthController {
  constructor() {
    this.startTime = new Date();
  }

  // Basic health check
  async healthCheck(req, res) {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: this.getUptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      };

      res.json(health);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Detailed system status
  async systemStatus(req, res) {
    try {
      const [dbStatus, cacheStats, memoryUsage] = await Promise.all([
        this.checkDatabaseHealth(),
        this.getCacheStats(),
        this.getMemoryUsage()
      ]);

      const status = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: this.getUptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: dbStatus,
          cache: cacheStats,
          ai: this.getAIServiceStatus()
        },
        system: {
          memory: memoryUsage,
          cpu: process.cpuUsage(),
          nodeVersion: process.version,
          platform: process.platform
        }
      };

      // Determine overall health
      const allServicesHealthy = Object.values(status.services).every(
        service => service.status === 'healthy'
      );

      if (!allServicesHealthy) {
        status.status = 'degraded';
      }

      const responseCode = status.status === 'healthy' ? 200 : 503;
      res.status(responseCode).json(status);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Readiness check for Kubernetes
  async readiness(req, res) {
    try {
      // Check if all required services are ready
      const dbReady = await this.isDatabaseReady();
      const cacheReady = this.isCacheReady();

      if (dbReady && cacheReady) {
        res.status(200).json({ status: 'ready' });
      } else {
        res.status(503).json({ 
          status: 'not ready',
          services: {
            database: dbReady ? 'ready' : 'not ready',
            cache: cacheReady ? 'ready' : 'not ready'
          }
        });
      }
    } catch (error) {
      res.status(503).json({
        status: 'not ready',
        error: error.message
      });
    }
  }

  // Liveness check for Kubernetes
  async liveness(req, res) {
    try {
      // Simple check that the application is running
      const uptime = process.uptime();
      
      if (uptime > 10) { // App has been running for at least 10 seconds
        res.status(200).json({ 
          status: 'alive',
          uptime: uptime
        });
      } else {
        res.status(503).json({ 
          status: 'starting',
          uptime: uptime
        });
      }
    } catch (error) {
      res.status(503).json({
        status: 'dead',
        error: error.message
      });
    }
  }

  // Get application metrics
  async metrics(req, res) {
    try {
      const format = req.query.format || 'json';
      
      if (format === 'prometheus') {
        res.setHeader('Content-Type', 'text/plain');
        res.send(metricsCollector.exportPrometheus());
      } else {
        const metrics = {
          timestamp: new Date().toISOString(),
          uptime: this.getUptime(),
          memory: this.getMemoryUsage(),
          cache: cacheService.getStats(),
          application: metricsCollector.getSummary(),
          process: {
            pid: process.pid,
            cpuUsage: process.cpuUsage(),
            resourceUsage: process.resourceUsage ? process.resourceUsage() : null
          },
          system: {
            loadAverage: require('os').loadavg(),
            platform: process.platform,
            nodeVersion: process.version,
            totalMemory: require('os').totalmem(),
            freeMemory: require('os').freemem()
          }
        };

        res.json(metrics);
      }
      
      auditLogger.logAdmin('METRICS_ACCESS', req.user?.userId, 'metrics_endpoint', {
        format,
        ip: req.ip
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to collect metrics',
        details: error.message
      });
    }
  }

  // Get metrics summary
  async metricsSummary(req, res) {
    try {
      const summary = metricsCollector.getSummary();
      res.json(summary);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to retrieve metrics summary',
        details: error.message
      });
    }
  }

  // Advanced health check with dependencies
  async advancedHealth(req, res) {
    try {
      const [
        dbStatus,
        cacheStats,
        memoryUsage,
        aiServiceStatus,
        diskUsage,
        networkConnectivity
      ] = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.getCacheStats(),
        this.getMemoryUsage(),
        this.getAIServiceStatus(),
        this.checkDiskUsage(),
        this.checkNetworkConnectivity()
      ]);

      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: this.getUptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: {
          database: dbStatus.status === 'fulfilled' ? dbStatus.value : { status: 'unhealthy', error: dbStatus.reason?.message },
          cache: cacheStats.status === 'fulfilled' ? cacheStats.value : { status: 'unhealthy', error: cacheStats.reason?.message },
          memory: memoryUsage.status === 'fulfilled' ? memoryUsage.value : { status: 'unhealthy', error: memoryUsage.reason?.message },
          ai_services: aiServiceStatus.status === 'fulfilled' ? aiServiceStatus.value : { status: 'unhealthy', error: aiServiceStatus.reason?.message },
          disk: diskUsage.status === 'fulfilled' ? diskUsage.value : { status: 'unhealthy', error: diskUsage.reason?.message },
          network: networkConnectivity.status === 'fulfilled' ? networkConnectivity.value : { status: 'unhealthy', error: networkConnectivity.reason?.message }
        },
        metrics: metricsCollector.getSummary()
      };

      // Determine overall health status
      const unhealthyChecks = Object.values(health.checks).filter(check => check.status === 'unhealthy');
      if (unhealthyChecks.length > 0) {
        health.status = 'degraded';
        if (unhealthyChecks.length > 2) {
          health.status = 'unhealthy';
        }
      }

      const statusCode = health.status === 'healthy' ? 200 : health.status === 'degraded' ? 206 : 503;
      res.status(statusCode).json(health);

    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Helper methods
  getUptime() {
    const uptimeSeconds = Date.now() - this.startTime;
    const days = Math.floor(uptimeSeconds / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptimeSeconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptimeSeconds % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptimeSeconds % (1000 * 60)) / 1000);

    return {
      milliseconds: uptimeSeconds,
      human: `${days}d ${hours}h ${minutes}m ${seconds}s`,
      seconds: Math.floor(uptimeSeconds / 1000)
    };
  }

  async checkDatabaseHealth() {
    try {
      await new Promise((resolve, reject) => {
        database.get().get('SELECT 1 as test', (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      return {
        status: 'healthy',
        type: 'SQLite',
        latency: await this.measureDatabaseLatency()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        type: 'SQLite',
        error: error.message
      };
    }
  }

  async measureDatabaseLatency() {
    const start = Date.now();
    try {
      await new Promise((resolve, reject) => {
        database.get().get('SELECT 1', (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });
      return Date.now() - start;
    } catch (error) {
      return null;
    }
  }

  getCacheStats() {
    try {
      const stats = cacheService.getStats();
      return {
        status: 'healthy',
        ...stats
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      rss: {
        bytes: usage.rss,
        mb: (usage.rss / 1024 / 1024).toFixed(2)
      },
      heapTotal: {
        bytes: usage.heapTotal,
        mb: (usage.heapTotal / 1024 / 1024).toFixed(2)
      },
      heapUsed: {
        bytes: usage.heapUsed,
        mb: (usage.heapUsed / 1024 / 1024).toFixed(2)
      },
      external: {
        bytes: usage.external,
        mb: (usage.external / 1024 / 1024).toFixed(2)
      }
    };
  }

  getAIServiceStatus() {
    // Check if AI service environment variables are configured
    const hasOpenAI = !!(process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here');
    const hasClaude = !!(process.env.ANTHROPIC_API_KEY && process.env.ANTHROPIC_API_KEY !== 'your_anthropic_api_key_here');
    const hasGemini = !!(process.env.GOOGLE_AI_API_KEY && process.env.GOOGLE_AI_API_KEY !== 'your_google_ai_api_key_here');
    const hasGroq = !!(process.env.GROQ_API_KEY && process.env.GROQ_API_KEY !== 'your_groq_api_key_here');
    const hasNovita = !!(process.env.NOVITA_API_KEY && process.env.NOVITA_API_KEY !== 'your_novita_api_key_here');

    const configuredProviders = [hasOpenAI, hasClaude, hasGemini, hasGroq, hasNovita].filter(Boolean).length;

    return {
      status: configuredProviders > 0 ? 'healthy' : 'degraded',
      configuredProviders,
      providers: {
        openai: hasOpenAI ? 'configured' : 'not configured',
        claude: hasClaude ? 'configured' : 'not configured',
        gemini: hasGemini ? 'configured' : 'not configured',
        groq: hasGroq ? 'configured' : 'not configured',
        novita: hasNovita ? 'configured' : 'not configured'
      }
    };
  }

  async isDatabaseReady() {
    try {
      await new Promise((resolve, reject) => {
        database.get().get('SELECT 1', (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  isCacheReady() {
    try {
      cacheService.getStats();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Additional health check methods for advanced monitoring
  async checkDiskUsage() {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const stats = fs.statSync('./');
      return {
        status: 'healthy',
        available: stats.size || 'unknown',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  async checkNetworkConnectivity() {
    try {
      // Simple network connectivity check
      const dns = require('dns').promises;
      await dns.lookup('google.com');
      
      return {
        status: 'healthy',
        connectivity: 'ok',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'degraded',
        connectivity: 'limited',
        error: error.message
      };
    }
  }
}

module.exports = HealthController;