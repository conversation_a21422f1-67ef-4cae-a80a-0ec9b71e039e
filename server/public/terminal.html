<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVLeap Job Execution Terminal</title>
    <style>
        body {
            font-family: 'Monaco', 'Consolas', monospace;
            background-color: #1e1e1e;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .terminal-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 5px 5px 0 0;
            border-bottom: 1px solid #444;
        }
        
        .header h1 {
            margin: 0;
            color: #4CAF50;
            font-size: 18px;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        
        .status.connected {
            background: #2d5a2d;
            color: #90EE90;
        }
        
        .status.disconnected {
            background: #5a2d2d;
            color: #FFB6C1;
        }
        
        .terminal {
            background: #000000;
            border: 1px solid #333;
            border-radius: 0 0 5px 5px;
            height: 500px;
            overflow-y: auto;
            padding: 15px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .log-line {
            margin: 2px 0;
            white-space: pre-wrap;
        }
        
        .log-stdout {
            color: #ffffff;
        }
        
        .log-stderr {
            color: #ff6b6b;
        }
        
        .log-system {
            color: #4ecdc4;
            font-style: italic;
        }
        
        .timestamp {
            color: #666;
            font-size: 12px;
        }
        
        .controls {
            margin-top: 20px;
            padding: 15px;
            background: #2d2d2d;
            border-radius: 5px;
        }
        
        .control-group {
            margin: 10px 0;
        }
        
        label {
            display: inline-block;
            width: 120px;
            color: #ccc;
        }
        
        input[type="text"] {
            background: #1e1e1e;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 3px;
            width: 300px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .job-info {
            background: #2d2d2d;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        
        .approval-panel {
            background: #2d2d2d;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
        }
        
        .approval-actions {
            margin-top: 15px;
        }
        
        textarea {
            background: #1e1e1e;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 3px;
            width: 100%;
            min-height: 60px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="terminal-container">
        <div class="header">
            <h1>🐳 CVLeap Containerized Job Execution System</h1>
            <div id="status" class="status disconnected">
                Disconnected - Enter job details to start
            </div>
        </div>

        <div class="job-info">
            <h3>Job Submission</h3>
            <div class="control-group">
                <label>Job Title:</label>
                <input type="text" id="jobTitle" placeholder="e.g., Senior Software Engineer" />
            </div>
            <div class="control-group">
                <label>Company:</label>
                <input type="text" id="company" placeholder="e.g., Tech Corp Inc." />
            </div>
            <div class="control-group">
                <label>Job URL:</label>
                <input type="text" id="jobUrl" placeholder="https://example.com/jobs/123" />
            </div>
            <div class="control-group">
                <label>Auth Token:</label>
                <input type="text" id="authToken" placeholder="Your JWT token" />
            </div>
            <button onclick="submitJob()">Submit Job for Execution</button>
        </div>

        <div id="approvalPanel" class="approval-panel" style="display: none;">
            <h3>⏳ Approval Required</h3>
            <div id="approvalInfo"></div>
            <div class="approval-actions">
                <textarea id="approvalComments" placeholder="Optional comments or modifications..."></textarea>
                <br>
                <button onclick="approveJob()">✅ Approve</button>
                <button onclick="rejectJob()">❌ Reject</button>
            </div>
        </div>

        <div class="terminal" id="terminal">
            <div class="log-line log-system">
                <span class="timestamp">[System]</span> 
                CVLeap Containerized Job Execution Terminal v1.0
            </div>
            <div class="log-line log-system">
                <span class="timestamp">[System]</span> 
                Enter job details above to begin execution monitoring
            </div>
        </div>

        <div class="controls">
            <h3>Terminal Controls</h3>
            <div class="control-group">
                <label>Terminal ID:</label>
                <input type="text" id="terminalId" placeholder="Will be populated automatically" readonly />
                <button onclick="connectToTerminal()" id="connectBtn">Connect to Terminal</button>
            </div>
            <div class="control-group">
                <button onclick="clearTerminal()">Clear Terminal</button>
                <button onclick="disconnect()" id="disconnectBtn" disabled>Disconnect</button>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let currentJobId = null;
        let currentApprovalId = null;
        let authToken = null;

        function updateStatus(message, isConnected = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
        }

        function addLogLine(content, type = 'stdout') {
            const terminal = document.getElementById('terminal');
            const logLine = document.createElement('div');
            logLine.className = `log-line log-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${content}`;
            
            terminal.appendChild(logLine);
            terminal.scrollTop = terminal.scrollHeight;
        }

        function clearTerminal() {
            const terminal = document.getElementById('terminal');
            terminal.innerHTML = '';
            addLogLine('Terminal cleared', 'system');
        }

        async function submitJob() {
            const jobTitle = document.getElementById('jobTitle').value;
            const company = document.getElementById('company').value;
            const jobUrl = document.getElementById('jobUrl').value;
            authToken = document.getElementById('authToken').value;

            if (!jobTitle || !company || !jobUrl || !authToken) {
                addLogLine('❌ Error: Please fill in all job details and auth token', 'stderr');
                return;
            }

            const jobData = {
                jobTitle,
                company,
                jobUrl,
                confidenceScore: 0.8
            };

            try {
                addLogLine('📤 Submitting job for execution...', 'system');
                
                const response = await fetch('/api/containerized-jobs/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(jobData)
                });

                const result = await response.json();

                if (result.success) {
                    currentJobId = result.jobId;
                    addLogLine(`✅ Job submitted successfully: ${result.jobId}`, 'system');

                    if (result.approval.requiresApproval) {
                        currentApprovalId = result.approval.id;
                        showApprovalPanel(result.approval);
                        addLogLine('⏳ Job requires approval before execution', 'system');
                    } else {
                        // Auto-approved, start monitoring
                        if (result.execution) {
                            document.getElementById('terminalId').value = result.execution.terminalId;
                            connectToTerminal();
                        }
                    }
                } else {
                    addLogLine(`❌ Job submission failed: ${result.error}`, 'stderr');
                }
            } catch (error) {
                addLogLine(`❌ Network error: ${error.message}`, 'stderr');
            }
        }

        function showApprovalPanel(approval) {
            const panel = document.getElementById('approvalPanel');
            const info = document.getElementById('approvalInfo');
            
            info.innerHTML = `
                <p><strong>Status:</strong> ${approval.status}</p>
                <p><strong>Reasons:</strong> ${approval.reasons?.join(', ') || 'N/A'}</p>
                <p><strong>Recommendations:</strong></p>
                <ul>${approval.recommendations?.map(r => `<li>${r}</li>`).join('') || '<li>None</li>'}</ul>
            `;
            
            panel.style.display = 'block';
        }

        function hideApprovalPanel() {
            document.getElementById('approvalPanel').style.display = 'none';
        }

        async function approveJob() {
            if (!currentApprovalId || !authToken) {
                addLogLine('❌ No approval pending or missing auth token', 'stderr');
                return;
            }

            const comments = document.getElementById('approvalComments').value;

            try {
                const response = await fetch(`/api/containerized-jobs/approvals/${currentApprovalId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ comments })
                });

                const result = await response.json();

                if (result.success) {
                    addLogLine('✅ Job approved and execution started', 'system');
                    hideApprovalPanel();
                    
                    if (result.execution) {
                        document.getElementById('terminalId').value = result.execution.terminalId;
                        connectToTerminal();
                    }
                } else {
                    addLogLine(`❌ Approval failed: ${result.error}`, 'stderr');
                }
            } catch (error) {
                addLogLine(`❌ Network error: ${error.message}`, 'stderr');
            }
        }

        async function rejectJob() {
            if (!currentApprovalId || !authToken) {
                addLogLine('❌ No approval pending or missing auth token', 'stderr');
                return;
            }

            const reason = document.getElementById('approvalComments').value || 'No reason provided';

            try {
                const response = await fetch(`/api/containerized-jobs/approvals/${currentApprovalId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ reason })
                });

                const result = await response.json();

                if (result.success) {
                    addLogLine('❌ Job rejected', 'system');
                    hideApprovalPanel();
                } else {
                    addLogLine(`❌ Rejection failed: ${result.error}`, 'stderr');
                }
            } catch (error) {
                addLogLine(`❌ Network error: ${error.message}`, 'stderr');
            }
        }

        function connectToTerminal() {
            const terminalId = document.getElementById('terminalId').value;
            
            if (!terminalId || !authToken) {
                addLogLine('❌ Terminal ID and auth token required', 'stderr');
                return;
            }

            if (ws) {
                ws.close();
            }

            const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/terminal?terminalId=${terminalId}&token=${authToken}`;
            
            addLogLine(`🔌 Connecting to terminal: ${terminalId}`, 'system');
            
            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                updateStatus(`Connected to terminal: ${terminalId}`, true);
                addLogLine('🔌 WebSocket connection established', 'system');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleTerminalMessage(data);
                } catch (error) {
                    addLogLine(`📨 Raw: ${event.data}`, 'stdout');
                }
            };

            ws.onclose = () => {
                updateStatus('Disconnected from terminal', false);
                addLogLine('🔌 WebSocket connection closed', 'system');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            };

            ws.onerror = (error) => {
                addLogLine('❌ WebSocket error occurred', 'stderr');
                console.error('WebSocket error:', error);
            };
        }

        function handleTerminalMessage(data) {
            switch (data.type) {
                case 'terminal_output':
                    const logType = data.data.type === 'stderr' ? 'stderr' : 'stdout';
                    addLogLine(data.data.content, logType);
                    break;
                    
                case 'terminal_status':
                    addLogLine(`📊 Terminal status: ${data.status}`, 'system');
                    if (data.metadata) {
                        addLogLine(`📊 Metadata: ${JSON.stringify(data.metadata)}`, 'system');
                    }
                    break;
                    
                case 'terminal_history':
                    data.history.forEach(entry => {
                        const logType = entry.type === 'stderr' ? 'stderr' : 'stdout';
                        addLogLine(entry.content, logType);
                    });
                    break;
                    
                case 'terminal_closed':
                    addLogLine(`🔚 Terminal closed: ${data.reason}`, 'system');
                    break;
                    
                default:
                    addLogLine(`📨 ${data.type}: ${JSON.stringify(data)}`, 'system');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        // Auto-populate token from localStorage if available
        window.onload = () => {
            const savedToken = localStorage.getItem('cvleap_token');
            if (savedToken) {
                document.getElementById('authToken').value = savedToken;
            }
        };

        // Save token to localStorage when entered
        document.getElementById('authToken').addEventListener('change', (e) => {
            localStorage.setItem('cvleap_token', e.target.value);
        });
    </script>
</body>
</html>