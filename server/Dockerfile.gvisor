# Multi-stage Dockerfile optimized for gVisor runtime
# Reduces syscall overhead and improves security isolation

# Build stage
FROM node:18-alpine AS builder

# Install build dependencies with minimal syscalls
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    git \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with optimizations for gVisor
ENV NPM_CONFIG_CACHE=/tmp/.npm
ENV NODE_OPTIONS="--max-old-space-size=1024"
RUN npm ci --only=production --no-audit --no-fund \
    && npm cache clean --force \
    && apk del .build-deps

# Production stage optimized for gVisor
FROM node:18-alpine AS production

# Create non-root user for enhanced security
RUN addgroup -g 1001 -S cvleap && \
    adduser -S -D -H -u 1001 -s /sbin/nologin -G cvleap cvleap

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy application files
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=cvleap:cvleap . .

# gVisor optimizations
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"
ENV GVISOR_RUNTIME=true

# Optimize for reduced syscalls
ENV UV_THREADPOOL_SIZE=4
ENV NODE_NO_WARNINGS=1

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/uploads /app/temp \
    && chown -R cvleap:cvleap /app \
    && chmod -R 755 /app

# Remove unnecessary files to reduce attack surface
RUN find /app -name "*.md" -delete \
    && find /app -name "*.test.js" -delete \
    && find /app -name "*.spec.js" -delete \
    && find /app -name ".git*" -delete \
    && rm -rf /app/tests /app/docs

# Security: Switch to non-root user
USER cvleap

# Health check optimized for gVisor
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Expose port
EXPOSE 3000

# Use dumb-init for proper signal handling in gVisor
ENTRYPOINT ["dumb-init", "--"]

# Start application with gVisor optimizations
CMD ["node", "server.js"]

# Labels for gVisor runtime identification
LABEL runtime="gvisor"
LABEL security.profile="strict"
LABEL gvisor.optimized="true"
LABEL version="1.0.0"
LABEL description="CVLeap server optimized for gVisor runtime"
