/**
 * Enhanced error handling middleware with comprehensive error types,
 * structured logging, and security-conscious error responses
 */

const { getRequestDuration } = require('./requestId');
const { logger } = require('../utils/logger');

/**
 * Custom error classes for better error categorization
 */
class AppError extends Error {
  constructor(message, statusCode, errorCode = null, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR');
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
  }
}

/**
 * Structured error logger with different log levels
 * @param {Error} error - Error object to log
 * @param {Object} req - Express request object
 * @param {string} level - Log level (error, warn, info)
 */
const logError = (error, req = null, level = 'error') => {
  const errorMeta = {
    error: {
      name: error.name,
      message: error.message,
      statusCode: error.statusCode,
      errorCode: error.errorCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }
  };

  if (req) {
    errorMeta.request = {
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.userId || null,
      correlationId: req.correlationId || null
    };
  }

  // Use structured logger instead of console
  switch (level) {
    case 'error':
      logger.error(`${error.name}: ${error.message}`, errorMeta);
      break;
    case 'warn':
      logger.warn(`${error.name}: ${error.message}`, errorMeta);
      break;
    default:
      logger.info(`${error.name}: ${error.message}`, errorMeta);
  }
};

/**
 * Enhanced error handler middleware with security-conscious responses
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Handle known database errors
  if (err.name === 'CastError') {
    error = new NotFoundError('Resource');
  }

  // Handle duplicate key errors (SQLite/MongoDB)
  if (err.code === 11000 || err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    const field = err.message.match(/index: (.+?)_/)?.[1] || 'field';
    error = new ConflictError(`Duplicate value for ${field}`);
  }

  // Handle Mongoose validation errors
  if (err.name === 'ValidationError') {
    const details = Object.values(err.errors).map(val => ({
      field: val.path,
      message: val.message,
      value: val.value
    }));
    error = new ValidationError('Validation failed', details);
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = new AuthenticationError('Invalid authentication token');
  }

  if (err.name === 'TokenExpiredError') {
    error = new AuthenticationError('Authentication token has expired');
  }

  // Handle file upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = new ValidationError('File size exceeds maximum limit');
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    error = new ValidationError('Unexpected file field');
  }

  // Handle network/timeout errors
  if (err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') {
    error = new ExternalServiceError('External service', 'Service temporarily unavailable');
  }

  // Handle syntax errors (malformed JSON, etc.)
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    error = new ValidationError('Invalid JSON format in request body');
  }

  // Log the error
  const logLevel = error.statusCode >= 500 ? 'error' : 'warn';
  logError(error, req, logLevel);

  // Prepare response
  const statusCode = error.statusCode || 500;
  const response = {
    success: false,
    error: {
      message: error.isOperational ? error.message : 'Internal server error',
      code: error.errorCode || 'INTERNAL_ERROR',
      timestamp: error.timestamp || new Date().toISOString(),
      requestId: req.correlationId || null
    }
  };

  // Add additional details for client errors (4xx)
  if (statusCode < 500) {
    if (error.details) {
      response.error.details = error.details;
    }
    
    // Add helpful information for development
    if (process.env.NODE_ENV === 'development') {
      response.error.stack = err.stack;
      response.error.performance = {
        requestDuration: getRequestDuration(req),
        memoryUsage: process.memoryUsage()
      };
    }
  }

  // Add request ID if available (useful for debugging)
  if (req.correlationId) {
    response.error.correlationId = req.correlationId;
  }


  res.status(statusCode).json(response);
};

/**
 * Async error wrapper for route handlers
 * Automatically catches async errors and passes them to error handler
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * 404 handler for routes that don't exist
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl}`);
  next(error);
};

/**
 * Setup global error handlers for unhandled promise rejections and exceptions
 */
const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    const error = reason instanceof Error ? reason : new AppError(String(reason), 500, 'UNHANDLED_REJECTION', false);
    
    logError(error, null, 'error');
    
    console.error('🚨 Unhandled Promise Rejection:', {
      reason: reason,
      promise: promise,
      timestamp: new Date().toISOString()
    });

    // In production, you might want to gracefully shutdown
    if (process.env.NODE_ENV === 'production') {
      console.error('Shutting down due to unhandled promise rejection...');
      process.exit(1);
    }
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logError(error, null, 'error');
    
    console.error('🚨 Uncaught Exception:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Always exit on uncaught exceptions
    console.error('Shutting down due to uncaught exception...');
    process.exit(1);
  });

  // Handle SIGTERM gracefully
  process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
  });

  // Handle SIGINT gracefully  
  process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
  });
};

module.exports = { 
  errorHandler,
  asyncHandler,
  notFoundHandler,
  logError,
  setupGlobalErrorHandlers,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError
};