const jwt = require('jsonwebtoken');
const database = require('../database');
const { logger } = require('../utils/logger');
const { requireRole } = require('./rbac');

/**
 * Admin Authentication Middleware
 * Provides enhanced authentication for admin routes with additional security checks
 */

/**
 * Enhanced admin authentication middleware
 * Validates JWT token and ensures user has admin privileges
 */
async function authenticateAdmin(req, res, next) {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn('Admin auth: Missing or invalid authorization header', {
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: 'Authorization token required',
        code: 'TOKEN_REQUIRED'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (jwtError) {
      logger.warn('Admin auth: Invalid JWT token', {
        error: jwtError.message,
        path: req.path,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
        code: 'TOKEN_INVALID'
      });
    }

    // Get user from database with admin information
    const db = database.get();
    const user = await db.get(`
      SELECT 
        u.id, u.email, u.name, u.user_role, u.account_status,
        u.is_active, u.last_login_at, u.failed_login_attempts,
        u.account_locked_until, u.two_factor_enabled,
        au.admin_level, au.permissions as admin_permissions,
        au.last_admin_action_at
      FROM users u
      LEFT JOIN admin_users au ON u.id = au.user_id
      WHERE u.id = ? AND u.is_active = true
    `, [decoded.userId]);

    if (!user) {
      logger.warn('Admin auth: User not found or inactive', {
        userId: decoded.userId,
        path: req.path,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if account is locked
    if (user.account_locked_until && new Date(user.account_locked_until) > new Date()) {
      logger.warn('Admin auth: Account locked', {
        userId: user.id,
        lockedUntil: user.account_locked_until,
        path: req.path,
        ip: req.ip
      });
      
      return res.status(423).json({
        success: false,
        error: 'Account is locked',
        code: 'ACCOUNT_LOCKED',
        lockedUntil: user.account_locked_until
      });
    }

    // Check if user has admin role
    if (!['admin', 'super_admin'].includes(user.user_role)) {
      logger.warn('Admin auth: Insufficient role for admin access', {
        userId: user.id,
        userRole: user.user_role,
        path: req.path,
        ip: req.ip
      });
      
      // Log security event
      await logAdminSecurityEvent(user.id, 'UNAUTHORIZED_ADMIN_ACCESS', {
        userRole: user.user_role,
        path: req.path,
        method: req.method
      }, req);
      
      return res.status(403).json({
        success: false,
        error: 'Admin access required',
        code: 'ADMIN_ACCESS_REQUIRED'
      });
    }

    // Check if user is in admin_users table
    if (!user.admin_level) {
      logger.warn('Admin auth: User not in admin_users table', {
        userId: user.id,
        userRole: user.user_role,
        path: req.path,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        error: 'Admin privileges not configured',
        code: 'ADMIN_NOT_CONFIGURED'
      });
    }

    // Parse admin permissions
    let adminPermissions = {};
    try {
      adminPermissions = user.admin_permissions ? JSON.parse(user.admin_permissions) : {};
    } catch (error) {
      logger.error('Admin auth: Failed to parse admin permissions', {
        userId: user.id,
        error: error.message
      });
      adminPermissions = {};
    }

    // Add user information to request
    req.user = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.user_role,
      user_role: user.user_role,
      account_status: user.account_status,
      admin_level: user.admin_level,
      admin_permissions: adminPermissions,
      two_factor_enabled: user.two_factor_enabled,
      last_admin_action_at: user.last_admin_action_at
    };

    // Update last admin action timestamp
    await updateLastAdminAction(user.id);

    // Log successful admin authentication
    logger.info('Admin auth: Successful authentication', {
      userId: user.id,
      email: user.email,
      adminLevel: user.admin_level,
      path: req.path,
      ip: req.ip
    });

    next();
  } catch (error) {
    logger.error('Admin auth: Authentication error', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      ip: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Authentication error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Require specific admin level
 * @param {number} minLevel - Minimum admin level required (1-10)
 * @returns {Function} Express middleware
 */
function requireAdminLevel(minLevel) {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userAdminLevel = req.user.admin_level || 0;
      
      if (userAdminLevel < minLevel) {
        logger.warn('Admin auth: Insufficient admin level', {
          userId: req.user.id,
          userLevel: userAdminLevel,
          requiredLevel: minLevel,
          path: req.path,
          ip: req.ip
        });

        await logAdminSecurityEvent(req.user.id, 'INSUFFICIENT_ADMIN_LEVEL', {
          userLevel: userAdminLevel,
          requiredLevel: minLevel,
          path: req.path,
          method: req.method
        }, req);

        return res.status(403).json({
          success: false,
          error: 'Insufficient admin level',
          code: 'INSUFFICIENT_ADMIN_LEVEL',
          required: minLevel,
          current: userAdminLevel
        });
      }

      next();
    } catch (error) {
      logger.error('Admin auth: Admin level check error', {
        error: error.message,
        userId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Authorization error',
        code: 'AUTH_ERROR'
      });
    }
  };
}

/**
 * Require specific admin permission
 * @param {string} permission - Required permission
 * @returns {Function} Express middleware
 */
function requireAdminPermission(permission) {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const adminPermissions = req.user.admin_permissions || {};
      const hasPermission = checkAdminPermission(adminPermissions, permission);
      
      if (!hasPermission) {
        logger.warn('Admin auth: Missing admin permission', {
          userId: req.user.id,
          requiredPermission: permission,
          userPermissions: Object.keys(adminPermissions),
          path: req.path,
          ip: req.ip
        });

        await logAdminSecurityEvent(req.user.id, 'MISSING_ADMIN_PERMISSION', {
          requiredPermission: permission,
          path: req.path,
          method: req.method
        }, req);

        return res.status(403).json({
          success: false,
          error: 'Missing admin permission',
          code: 'MISSING_ADMIN_PERMISSION',
          required: permission
        });
      }

      next();
    } catch (error) {
      logger.error('Admin auth: Permission check error', {
        error: error.message,
        userId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Authorization error',
        code: 'AUTH_ERROR'
      });
    }
  };
}

/**
 * Check if admin has specific permission
 * @param {Object} adminPermissions - Admin permissions object
 * @param {string} permission - Permission to check
 * @returns {boolean} True if permission exists
 */
function checkAdminPermission(adminPermissions, permission) {
  const [resource, action] = permission.split(':');
  
  if (!resource || !action) {
    return false;
  }
  
  const resourcePermissions = adminPermissions[resource];
  if (!resourcePermissions || !Array.isArray(resourcePermissions)) {
    return false;
  }
  
  return resourcePermissions.includes(action);
}

/**
 * Update last admin action timestamp
 * @param {string} userId - User ID
 */
async function updateLastAdminAction(userId) {
  try {
    const db = database.get();
    await db.run(`
      UPDATE admin_users 
      SET last_admin_action_at = ? 
      WHERE user_id = ?
    `, [new Date().toISOString(), userId]);
  } catch (error) {
    logger.error('Admin auth: Failed to update last admin action', {
      error: error.message,
      userId
    });
  }
}

/**
 * Log admin security events
 * @param {string} userId - User ID
 * @param {string} action - Security action
 * @param {Object} metadata - Additional metadata
 * @param {Object} req - Express request object
 */
async function logAdminSecurityEvent(userId, action, metadata, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId,
      action,
      'admin_security',
      null,
      JSON.stringify(metadata),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Admin auth: Failed to log security event', {
      error: error.message,
      userId,
      action
    });
  }
}

// Combine admin authentication with role checking
const requireAdmin = [authenticateAdmin, requireRole(['admin', 'super_admin'])];
const requireSuperAdmin = [authenticateAdmin, requireRole(['super_admin'])];

module.exports = {
  authenticateAdmin,
  requireAdminLevel,
  requireAdminPermission,
  requireAdmin,
  requireSuperAdmin,
  checkAdminPermission
};
