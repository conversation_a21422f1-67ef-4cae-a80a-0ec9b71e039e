const crypto = require('crypto');
const { logger } = require('../utils/logger');
const { monitoring } = require('../utils/monitoring');

/**
 * Content Security Policy (CSP) Implementation
 * Provides comprehensive CSP protection with dynamic nonce generation
 */

// CSP directive configurations
const CSP_DIRECTIVES = {
  production: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Will be replaced with nonce in strict mode
      'https://cdn.jsdelivr.net',
      'https://unpkg.com',
      'https://cdnjs.cloudflare.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'https://images.unsplash.com',
      'https://via.placeholder.com'
    ],
    'connect-src': [
      "'self'",
      'https://api.github.com',
      'https://api.linkedin.com',
      'https://api.indeed.com',
      'wss:',
      'ws:'
    ],
    'frame-src': [
      "'none'"
    ],
    'object-src': [
      "'none'"
    ],
    'base-uri': [
      "'self'"
    ],
    'form-action': [
      "'self'"
    ],
    'frame-ancestors': [
      "'none'"
    ],
    'upgrade-insecure-requests': true,
    'block-all-mixed-content': true
  },
  development: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'", // Needed for development tools
      'localhost:*',
      '127.0.0.1:*',
      'https://cdn.jsdelivr.net'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'localhost:*',
      '127.0.0.1:*',
      'https://fonts.googleapis.com'
    ],
    'font-src': [
      "'self'",
      'localhost:*',
      '127.0.0.1:*',
      'https://fonts.gstatic.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'localhost:*',
      '127.0.0.1:*',
      'https:'
    ],
    'connect-src': [
      "'self'",
      'localhost:*',
      '127.0.0.1:*',
      'ws://localhost:*',
      'wss://localhost:*',
      'https:'
    ],
    'frame-src': [
      "'self'",
      'localhost:*',
      '127.0.0.1:*'
    ],
    'object-src': [
      "'none'"
    ],
    'base-uri': [
      "'self'"
    ],
    'form-action': [
      "'self'"
    ]
  }
};

// CSP violation categories
const VIOLATION_CATEGORIES = {
  'script-src': 'Script Execution',
  'style-src': 'Style Injection',
  'img-src': 'Image Loading',
  'connect-src': 'Network Connection',
  'frame-src': 'Frame Embedding',
  'object-src': 'Object Embedding',
  'media-src': 'Media Loading',
  'font-src': 'Font Loading'
};

/**
 * Content Security Policy middleware
 */
function contentSecurityPolicy(options = {}) {
  const config = {
    environment: process.env.NODE_ENV || 'development',
    reportOnly: false,
    enableNonce: true,
    enableReporting: true,
    reportUri: '/api/security/csp-report',
    customDirectives: {},
    strictMode: false,
    ...options
  };
  
  return (req, res, next) => {
    try {
      // Generate nonce for this request
      const nonce = config.enableNonce ? generateNonce() : null;
      
      // Store nonce in request for use in templates
      if (nonce) {
        req.cspNonce = nonce;
        res.locals.cspNonce = nonce;
      }
      
      // Build CSP header
      const cspHeader = buildCSPHeader(config, nonce);
      
      // Set CSP header
      const headerName = config.reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
      res.setHeader(headerName, cspHeader);
      
      // Log CSP header for debugging
      logger.debug('CSP header set', {
        header: headerName,
        policy: cspHeader,
        nonce: nonce ? `${nonce.substring(0, 8)}...` : null
      });
      
      next();
    } catch (error) {
      logger.error('CSP middleware error', {
        error: error.message,
        path: req.path
      });
      
      // Continue without CSP in case of error
      next();
    }
  };
}

/**
 * Generate cryptographically secure nonce
 * @returns {string} Base64 encoded nonce
 */
function generateNonce() {
  return crypto.randomBytes(16).toString('base64');
}

/**
 * Build CSP header string
 * @param {Object} config - CSP configuration
 * @param {string} nonce - Generated nonce
 * @returns {string} CSP header value
 */
function buildCSPHeader(config, nonce) {
  const directives = {
    ...CSP_DIRECTIVES[config.environment],
    ...config.customDirectives
  };
  
  const cspParts = [];
  
  Object.entries(directives).forEach(([directive, values]) => {
    if (typeof values === 'boolean') {
      if (values) {
        cspParts.push(directive);
      }
    } else if (Array.isArray(values)) {
      let directiveValues = [...values];
      
      // Add nonce to script-src and style-src if enabled
      if (nonce && (directive === 'script-src' || directive === 'style-src')) {
        directiveValues.push(`'nonce-${nonce}'`);
        
        // Remove unsafe-inline when using nonce in strict mode
        if (config.strictMode) {
          directiveValues = directiveValues.filter(val => val !== "'unsafe-inline'");
        }
      }
      
      cspParts.push(`${directive} ${directiveValues.join(' ')}`);
    }
  });
  
  // Add report-uri if reporting is enabled
  if (config.enableReporting && config.reportUri) {
    cspParts.push(`report-uri ${config.reportUri}`);
  }
  
  return cspParts.join('; ');
}

/**
 * CSP violation report handler
 */
function handleCSPViolation(req, res) {
  try {
    const report = req.body;
    
    if (!report || !report['csp-report']) {
      return res.status(400).json({
        success: false,
        error: 'Invalid CSP report format'
      });
    }
    
    const violation = report['csp-report'];
    
    // Log violation
    logger.logSecurity('CSP_VIOLATION', {
      documentUri: violation['document-uri'],
      violatedDirective: violation['violated-directive'],
      blockedUri: violation['blocked-uri'],
      sourceFile: violation['source-file'],
      lineNumber: violation['line-number'],
      columnNumber: violation['column-number'],
      originalPolicy: violation['original-policy'],
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    // Categorize violation
    const category = categorizeViolation(violation);
    
    // Store violation in database
    storeCSPViolation(violation, category, req);
    
    // Trigger alert for critical violations
    if (isCriticalViolation(violation)) {
      monitoring.triggerAlert('critical_csp_violation', {
        level: 'warning',
        violatedDirective: violation['violated-directive'],
        blockedUri: violation['blocked-uri'],
        category
      });
    }
    
    res.status(204).send();
  } catch (error) {
    logger.error('CSP violation handler error', {
      error: error.message,
      body: req.body
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to process CSP violation report'
    });
  }
}

/**
 * Categorize CSP violation
 * @param {Object} violation - CSP violation report
 * @returns {string} Violation category
 */
function categorizeViolation(violation) {
  const directive = violation['violated-directive'];
  const blockedUri = violation['blocked-uri'];
  
  // Extract base directive
  const baseDirective = directive.split(' ')[0];
  
  // Check for common attack patterns
  if (blockedUri.includes('javascript:')) {
    return 'JavaScript Injection';
  }
  
  if (blockedUri.includes('data:') && baseDirective === 'script-src') {
    return 'Data URI Script Injection';
  }
  
  if (blockedUri.includes('eval') || blockedUri.includes('inline')) {
    return 'Inline Script/Style Violation';
  }
  
  return VIOLATION_CATEGORIES[baseDirective] || 'Unknown Violation';
}

/**
 * Check if violation is critical
 * @param {Object} violation - CSP violation report
 * @returns {boolean} True if critical
 */
function isCriticalViolation(violation) {
  const directive = violation['violated-directive'];
  const blockedUri = violation['blocked-uri'];
  
  // Critical patterns
  const criticalPatterns = [
    /javascript:/i,
    /data:.*script/i,
    /eval/i,
    /vbscript:/i,
    /about:blank/i
  ];
  
  // Critical directives
  const criticalDirectives = ['script-src', 'object-src', 'base-uri'];
  
  const baseDirective = directive.split(' ')[0];
  
  return criticalDirectives.includes(baseDirective) || 
         criticalPatterns.some(pattern => pattern.test(blockedUri));
}

/**
 * Store CSP violation in database
 * @param {Object} violation - CSP violation report
 * @param {string} category - Violation category
 * @param {Object} req - Express request object
 */
async function storeCSPViolation(violation, category, req) {
  try {
    const database = require('../database');
    const db = database.get();
    
    await db.run(`
      INSERT INTO security_violations (
        violation_type, severity, ip_address, user_agent,
        request_path, violation_data, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'CSP_VIOLATION',
      isCriticalViolation(violation) ? 'high' : 'medium',
      req.ip,
      req.get('User-Agent'),
      violation['document-uri'],
      JSON.stringify({
        ...violation,
        category
      }),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Failed to store CSP violation', {
      error: error.message,
      violation
    });
  }
}

/**
 * Get CSP statistics
 * @param {number} hours - Hours to look back
 * @returns {Object} CSP statistics
 */
async function getCSPStatistics(hours = 24) {
  try {
    const database = require('../database');
    const db = database.get();
    
    const stats = await db.get(`
      SELECT 
        COUNT(*) as total_violations,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as critical_violations,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_violations,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_violations
      FROM security_violations
      WHERE violation_type = 'CSP_VIOLATION' 
        AND created_at > datetime('now', '-${hours} hours')
    `);
    
    // Get top violated directives
    const topDirectives = await db.all(`
      SELECT 
        JSON_EXTRACT(violation_data, '$.violated-directive') as directive,
        COUNT(*) as count
      FROM security_violations
      WHERE violation_type = 'CSP_VIOLATION' 
        AND created_at > datetime('now', '-${hours} hours')
      GROUP BY directive
      ORDER BY count DESC
      LIMIT 10
    `);
    
    return {
      ...stats,
      topDirectives
    };
  } catch (error) {
    logger.error('Failed to get CSP statistics', { error: error.message });
    return null;
  }
}

/**
 * Create route-specific CSP middleware
 * @param {Object} routeConfig - Route-specific CSP configuration
 * @returns {Function} CSP middleware
 */
function createRouteCSP(routeConfig = {}) {
  return contentSecurityPolicy(routeConfig);
}

/**
 * CSP nonce helper for templates
 * @param {Object} req - Express request object
 * @returns {string} Nonce value
 */
function getCSPNonce(req) {
  return req.cspNonce || '';
}

/**
 * Generate CSP meta tag for HTML
 * @param {string} nonce - CSP nonce
 * @param {Object} config - CSP configuration
 * @returns {string} CSP meta tag
 */
function generateCSPMetaTag(nonce, config = {}) {
  const cspHeader = buildCSPHeader({
    environment: process.env.NODE_ENV || 'development',
    enableNonce: true,
    enableReporting: false, // No reporting in meta tags
    ...config
  }, nonce);
  
  return `<meta http-equiv="Content-Security-Policy" content="${cspHeader}">`;
}

module.exports = {
  contentSecurityPolicy,
  handleCSPViolation,
  createRouteCSP,
  getCSPNonce,
  generateCSPMetaTag,
  getCSPStatistics,
  CSP_DIRECTIVES
};
