const helmet = require('helmet');
const cors = require('cors');
const { logger } = require('../utils/logger');
const database = require('../database');

/**
 * Security Middleware
 * Provides comprehensive security headers and protection mechanisms
 */

/**
 * Security headers configuration
 */
function configureSecurityHeaders() {
  const isProduction = process.env.NODE_ENV === 'production';
  const appUrl = process.env.APP_URL || 'https://app.yourdomain.com';
  const apiUrl = process.env.API_URL || 'https://api.yourdomain.com';
  
  return helmet({
    // Content Security Policy
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:", "blob:"],
        scriptSrc: ["'self'", "'unsafe-eval'"], // unsafe-eval needed for some frameworks
        connectSrc: ["'self'", apiUrl, "wss:", "ws:"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"],
        upgradeInsecureRequests: isProduction ? [] : null
      },
      reportOnly: !isProduction,
      reportUri: '/api/security/csp-report'
    },

    // HTTP Strict Transport Security
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    },

    // X-Frame-Options
    frameguard: {
      action: 'deny'
    },

    // X-Content-Type-Options
    noSniff: true,

    // X-XSS-Protection
    xssFilter: true,

    // Referrer Policy
    referrerPolicy: {
      policy: 'strict-origin-when-cross-origin'
    },

    // Permissions Policy
    permissionsPolicy: {
      features: {
        camera: ['none'],
        microphone: ['none'],
        geolocation: ['none'],
        payment: ['none'],
        usb: ['none']
      }
    },

    // Hide X-Powered-By header
    hidePoweredBy: true,

    // DNS Prefetch Control
    dnsPrefetchControl: {
      allow: false
    },

    // Expect-CT
    expectCt: isProduction ? {
      maxAge: 86400,
      enforce: true
    } : false
  });
}

/**
 * CORS configuration
 */
function configureCORS() {
  const allowedOrigins = process.env.CORS_ORIGIN 
    ? process.env.CORS_ORIGIN.split(',').map(origin => origin.trim())
    : ['http://localhost:3000', 'http://localhost:3001'];

  return cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        logger.warn('CORS: Blocked request from unauthorized origin', { origin });
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-API-Key',
      'X-Client-Version'
    ],
    exposedHeaders: [
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset'
    ],
    maxAge: 86400 // 24 hours
  });
}

/**
 * Input sanitization middleware
 */
function sanitizeInput(req, res, next) {
  try {
    // Sanitize query parameters
    if (req.query) {
      for (const [key, value] of Object.entries(req.query)) {
        if (typeof value === 'string') {
          req.query[key] = sanitizeString(value);
        }
      }
    }

    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    next();
  } catch (error) {
    logger.error('Input sanitization error', {
      error: error.message,
      path: req.path,
      method: req.method
    });
    
    res.status(400).json({
      success: false,
      error: 'Invalid input data',
      code: 'INVALID_INPUT'
    });
  }
}

/**
 * Sanitize string input
 */
function sanitizeString(str) {
  if (typeof str !== 'string') return str;
  
  // Remove null bytes
  str = str.replace(/\0/g, '');
  
  // Basic XSS protection
  str = str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  str = str.replace(/javascript:/gi, '');
  str = str.replace(/on\w+\s*=/gi, '');
  
  // SQL injection protection
  str = str.replace(/(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/gi, '');
  
  return str.trim();
}

/**
 * Sanitize object recursively
 */
function sanitizeObject(obj) {
  if (obj === null || typeof obj !== 'object') {
    return typeof obj === 'string' ? sanitizeString(obj) : obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    const sanitizedKey = sanitizeString(key);
    sanitized[sanitizedKey] = sanitizeObject(value);
  }

  return sanitized;
}

/**
 * SQL injection protection middleware
 */
function sqlInjectionProtection(req, res, next) {
  const suspiciousPatterns = [
    /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b.*\b(from|where|into|values|set|table)\b)/i,
    /(\b(or|and)\b.*\b(1=1|1=0|true|false)\b)/i,
    /(--|\/\*|\*\/|;)/,
    /(\b(script|javascript|vbscript|onload|onerror|onclick)\b)/i
  ];

  const checkValue = (value) => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj) => {
    if (obj === null || typeof obj !== 'object') {
      return checkValue(obj);
    }

    if (Array.isArray(obj)) {
      return obj.some(item => checkObject(item));
    }

    return Object.values(obj).some(value => checkObject(value));
  };

  // Check query parameters
  if (req.query && checkObject(req.query)) {
    logger.warn('SQL injection attempt detected in query', {
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });

    return res.status(400).json({
      success: false,
      error: 'Invalid request parameters',
      code: 'INVALID_PARAMETERS'
    });
  }

  // Check request body
  if (req.body && checkObject(req.body)) {
    logger.warn('SQL injection attempt detected in body', {
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });

    return res.status(400).json({
      success: false,
      error: 'Invalid request data',
      code: 'INVALID_DATA'
    });
  }

  next();
}

/**
 * XSS protection middleware
 */
function xssProtection(req, res, next) {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[\\s]*=[\\s]*["\']javascript:/gi
  ];

  const checkForXSS = (value) => {
    if (typeof value === 'string') {
      return xssPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObjectForXSS = (obj) => {
    if (obj === null || typeof obj !== 'object') {
      return checkForXSS(obj);
    }

    if (Array.isArray(obj)) {
      return obj.some(item => checkObjectForXSS(item));
    }

    return Object.values(obj).some(value => checkObjectForXSS(value));
  };

  if ((req.query && checkObjectForXSS(req.query)) || 
      (req.body && checkObjectForXSS(req.body))) {
    
    logger.warn('XSS attempt detected', {
      query: req.query,
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });

    return res.status(400).json({
      success: false,
      error: 'Invalid content detected',
      code: 'XSS_DETECTED'
    });
  }

  next();
}

/**
 * CSP violation reporting endpoint
 */
function cspReportHandler(req, res) {
  try {
    const report = req.body;
    
    logger.warn('CSP violation reported', {
      report,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Store CSP violations in database for analysis
    if (report && report['csp-report']) {
      storeCspViolation(report['csp-report'], req);
    }

    res.status(204).send();
  } catch (error) {
    logger.error('CSP report handler error', { error: error.message });
    res.status(500).send();
  }
}

/**
 * Store CSP violation in database
 */
async function storeCspViolation(violation, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'CSP_VIOLATION',
      'security',
      null,
      JSON.stringify(violation),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Failed to store CSP violation', { error: error.message });
  }
}

/**
 * Security monitoring middleware
 */
function securityMonitoring(req, res, next) {
  // Track suspicious patterns
  const suspiciousIndicators = [
    req.get('User-Agent')?.includes('sqlmap'),
    req.get('User-Agent')?.includes('nikto'),
    req.path.includes('../'),
    req.path.includes('..\\'),
    req.path.includes('/etc/passwd'),
    req.path.includes('/proc/'),
    req.query?.toString().includes('base64'),
    req.query?.toString().includes('eval(')
  ];

  if (suspiciousIndicators.some(indicator => indicator)) {
    logger.warn('Suspicious request detected', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      query: req.query,
      method: req.method
    });

    // Log security event
    logSecurityEvent('SUSPICIOUS_REQUEST', {
      path: req.path,
      method: req.method,
      query: req.query,
      indicators: suspiciousIndicators.map((indicator, index) => ({ index, triggered: indicator }))
    }, req);
  }

  next();
}

/**
 * Log security events
 */
async function logSecurityEvent(action, metadata, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      action,
      'security',
      null,
      JSON.stringify(metadata),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Failed to log security event', { error: error.message });
  }
}

module.exports = {
  configureSecurityHeaders,
  configureCORS,
  sanitizeInput,
  sqlInjectionProtection,
  xssProtection,
  cspReportHandler,
  securityMonitoring
};
