const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { logger } = require('../utils/logger');
const database = require('../database');

/**
 * Rate Limiting Middleware
 * Provides comprehensive rate limiting and DDoS protection
 */

// Store for tracking rate limit violations
const violationStore = new Map();

/**
 * Custom rate limit store using database for persistence
 */
class DatabaseRateLimitStore {
  constructor() {
    this.prefix = 'rate_limit:';
    this.cleanupInterval = 60000; // 1 minute
    this.startCleanup();
  }

  async get(key) {
    try {
      const db = database.get();
      const result = await db.get(
        'SELECT count, reset_time FROM rate_limits WHERE key = ? AND reset_time > ?',
        [key, Date.now()]
      );
      return result ? { totalHits: result.count, resetTime: new Date(result.reset_time) } : undefined;
    } catch (error) {
      logger.error('Rate limit store get error', { error: error.message, key });
      return undefined;
    }
  }

  async set(key, value, windowMs) {
    try {
      const db = database.get();
      const resetTime = Date.now() + windowMs;
      
      await db.run(`
        INSERT OR REPLACE INTO rate_limits (key, count, reset_time, created_at)
        VALUES (?, ?, ?, ?)
      `, [key, value.totalHits, resetTime, Date.now()]);
      
      return true;
    } catch (error) {
      logger.error('Rate limit store set error', { error: error.message, key });
      return false;
    }
  }

  async increment(key, windowMs) {
    try {
      const db = database.get();
      const resetTime = Date.now() + windowMs;
      
      const result = await db.get(
        'SELECT count FROM rate_limits WHERE key = ? AND reset_time > ?',
        [key, Date.now()]
      );
      
      if (result) {
        await db.run(
          'UPDATE rate_limits SET count = count + 1 WHERE key = ?',
          [key]
        );
        return { totalHits: result.count + 1, resetTime: new Date(resetTime) };
      } else {
        await db.run(`
          INSERT OR REPLACE INTO rate_limits (key, count, reset_time, created_at)
          VALUES (?, 1, ?, ?)
        `, [key, resetTime, Date.now()]);
        return { totalHits: 1, resetTime: new Date(resetTime) };
      }
    } catch (error) {
      logger.error('Rate limit store increment error', { error: error.message, key });
      return { totalHits: 1, resetTime: new Date(Date.now() + windowMs) };
    }
  }

  async decrement(key) {
    try {
      const db = database.get();
      await db.run(
        'UPDATE rate_limits SET count = MAX(0, count - 1) WHERE key = ?',
        [key]
      );
    } catch (error) {
      logger.error('Rate limit store decrement error', { error: error.message, key });
    }
  }

  async resetKey(key) {
    try {
      const db = database.get();
      await db.run('DELETE FROM rate_limits WHERE key = ?', [key]);
    } catch (error) {
      logger.error('Rate limit store reset error', { error: error.message, key });
    }
  }

  startCleanup() {
    setInterval(async () => {
      try {
        const db = database.get();
        await db.run('DELETE FROM rate_limits WHERE reset_time <= ?', [Date.now()]);
      } catch (error) {
        logger.error('Rate limit cleanup error', { error: error.message });
      }
    }, this.cleanupInterval);
  }
}

// Initialize rate limit store
const rateLimitStore = new DatabaseRateLimitStore();

/**
 * Custom key generator for rate limiting
 */
function generateRateLimitKey(req) {
  const userId = req.user?.id;
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent') || 'unknown';
  
  // Use user ID if authenticated, otherwise use IP + User-Agent hash
  if (userId) {
    return `user:${userId}`;
  } else {
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(ip + userAgent).digest('hex').substring(0, 16);
    return `ip:${ip}:${hash}`;
  }
}

/**
 * Rate limit violation handler
 */
function handleRateLimitViolation(req, res, next, options) {
  const key = generateRateLimitKey(req);
  const violation = {
    timestamp: Date.now(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
    userId: req.user?.id
  };

  // Track violations
  if (!violationStore.has(key)) {
    violationStore.set(key, []);
  }
  violationStore.get(key).push(violation);

  // Log security event
  logger.warn('Rate limit exceeded', {
    key,
    violation,
    limit: options.max,
    window: options.windowMs
  });

  // Log to audit if user is authenticated
  if (req.user?.id) {
    logSecurityEvent(req.user.id, 'RATE_LIMIT_EXCEEDED', {
      path: req.path,
      method: req.method,
      limit: options.max,
      window: options.windowMs
    }, req);
  }

  res.status(429).json({
    success: false,
    error: 'Too many requests',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: Math.ceil(options.windowMs / 1000),
    limit: options.max,
    window: options.windowMs
  });
}

/**
 * Global rate limiter
 */
const globalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  store: rateLimitStore,
  keyGenerator: generateRateLimitKey,
  standardHeaders: true,
  legacyHeaders: false,
  handler: handleRateLimitViolation,
  skip: (req) => {
    // Skip rate limiting for health checks and metrics
    return req.path === '/health' || req.path === '/metrics';
  }
});

/**
 * Authentication rate limiter (stricter)
 */
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 auth requests per windowMs
  store: rateLimitStore,
  keyGenerator: generateRateLimitKey,
  standardHeaders: true,
  legacyHeaders: false,
  handler: handleRateLimitViolation,
  skipSuccessfulRequests: true // Don't count successful requests
});

/**
 * API rate limiter
 */
const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each user to 100 API requests per windowMs
  store: rateLimitStore,
  keyGenerator: generateRateLimitKey,
  standardHeaders: true,
  legacyHeaders: false,
  handler: handleRateLimitViolation
});

/**
 * Admin rate limiter (more permissive for admin users)
 */
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Higher limit for admin users
  store: rateLimitStore,
  keyGenerator: generateRateLimitKey,
  standardHeaders: true,
  legacyHeaders: false,
  handler: handleRateLimitViolation,
  skip: (req) => {
    // Skip for super admin users
    return req.user?.user_role === 'super_admin';
  }
});

/**
 * Slow down middleware for progressive delays
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
  keyGenerator: generateRateLimitKey,
  skip: (req) => {
    return req.path === '/health' || req.path === '/metrics';
  }
});

/**
 * DDoS protection middleware
 */
function ddosProtection(req, res, next) {
  const key = generateRateLimitKey(req);
  const violations = violationStore.get(key) || [];
  
  // Check for DDoS patterns
  const recentViolations = violations.filter(v => Date.now() - v.timestamp < 60000); // Last minute
  
  if (recentViolations.length >= 10) {
    logger.error('Potential DDoS attack detected', {
      key,
      violations: recentViolations.length,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Block for 1 hour
    res.status(429).json({
      success: false,
      error: 'Access temporarily blocked due to suspicious activity',
      code: 'DDOS_PROTECTION_TRIGGERED',
      retryAfter: 3600
    });
    return;
  }

  next();
}

/**
 * Request size limiter
 */
function requestSizeLimiter(maxSize = '10mb') {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxBytes = parseSize(maxSize);
    
    if (contentLength > maxBytes) {
      logger.warn('Request size limit exceeded', {
        contentLength,
        maxBytes,
        ip: req.ip,
        path: req.path
      });

      res.status(413).json({
        success: false,
        error: 'Request entity too large',
        code: 'REQUEST_TOO_LARGE',
        maxSize
      });
      return;
    }

    next();
  };
}

/**
 * Parse size string to bytes
 */
function parseSize(size) {
  const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
}

/**
 * Log security events to audit log
 */
async function logSecurityEvent(userId, action, metadata, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId,
      action,
      'security',
      null,
      JSON.stringify(metadata),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Failed to log security event', {
      error: error.message,
      userId,
      action
    });
  }
}

/**
 * Get rate limit statistics
 */
async function getRateLimitStats() {
  try {
    const db = database.get();
    
    const stats = await db.get(`
      SELECT 
        COUNT(*) as total_keys,
        SUM(count) as total_requests,
        AVG(count) as avg_requests_per_key,
        MAX(count) as max_requests_per_key
      FROM rate_limits
      WHERE reset_time > ?
    `, [Date.now()]);

    const violations = Array.from(violationStore.entries()).map(([key, violations]) => ({
      key,
      violations: violations.length,
      lastViolation: Math.max(...violations.map(v => v.timestamp))
    }));

    return {
      ...stats,
      violations: violations.length,
      topViolators: violations.sort((a, b) => b.violations - a.violations).slice(0, 10)
    };
  } catch (error) {
    logger.error('Failed to get rate limit stats', { error: error.message });
    return null;
  }
}

module.exports = {
  globalRateLimit,
  authRateLimit,
  apiRateLimit,
  adminRateLimit,
  speedLimiter,
  ddosProtection,
  requestSizeLimiter,
  getRateLimitStats,
  DatabaseRateLimitStore
};
