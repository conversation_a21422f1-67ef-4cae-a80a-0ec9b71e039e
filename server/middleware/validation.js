const Joi = require('joi');

/**
 * Enhanced request validation middleware with comprehensive error handling
 * @param {Joi.Schema} schema - Joi validation schema
 * @param {string} source - Source of data to validate ('body', 'query', 'params')
 * @returns {Function} Express middleware function
 */
const validateRequest = (schema, source = 'body') => {
  return (req, res, next) => {
    const dataToValidate = req[source];
    const { error, value } = schema.validate(dataToValidate, { 
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false
    });
    
    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));
      
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errorDetails
      });
    }
    
    // Replace original data with validated/sanitized data
    req[source] = value;
    next();
  };
};

/**
 * Comprehensive validation schemas for all API endpoints
 * Enhanced with better security, data types, and edge case handling
 */
const schemas = {
  // Authentication schemas
  register: Joi.object({
    email: Joi.string().email().lowercase().trim().required()
      .messages({'string.email': 'Please provide a valid email address'}),
    password: Joi.string().min(8).max(128).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .required().messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain uppercase, lowercase, number and special character'
      }),
    name: Joi.string().min(2).max(100).trim().required(),
    firstName: Joi.string().min(2).max(50).trim().optional(),
    lastName: Joi.string().min(2).max(50).trim().optional()
  }),

  login: Joi.object({
    email: Joi.string().email().lowercase().trim().required(),
    password: Joi.string().min(1).max(128).required(),
    rememberMe: Joi.boolean().optional()
  }),

  // Resume schemas
  resume: Joi.object({
    title: Joi.string().min(1).max(255).trim().required(),
    data: Joi.alternatives().try(
      Joi.object().max(50), // Limit object properties
      Joi.string().max(10000) // Limit string length
    ).required(),
    templateId: Joi.string().alphanum().max(50).optional(),
    isPublic: Joi.boolean().default(false),
    tags: Joi.array().items(Joi.string().max(30)).max(20).optional()
  }),

  resumeUpdate: Joi.object({
    title: Joi.string().min(1).max(255).trim().optional(),
    data: Joi.alternatives().try(
      Joi.object().max(50),
      Joi.string().max(10000)
    ).optional(),
    templateId: Joi.string().alphanum().max(50).optional(),
    isPublic: Joi.boolean().optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(20).optional()
  }),

  // AI request schemas
  aiRequest: Joi.object({
    type: Joi.string().valid('enhance', 'cover_letter', 'ats_analysis', 'suggest_skills', 
                           'interview_prep', 'salary_negotiation').required(),
    data: Joi.object().required(),
    targetJob: Joi.string().max(500).optional(),
    model: Joi.string().valid('gpt-4', 'claude-3', 'gemini-pro').optional(),
    options: Joi.object({
      creativity: Joi.number().min(0).max(1).optional(),
      tone: Joi.string().valid('professional', 'casual', 'confident', 'friendly').optional()
    }).optional()
  }),

  // Profile schemas
  updateProfile: Joi.object({
    firstName: Joi.string().min(2).max(50).trim().optional(),
    lastName: Joi.string().min(2).max(50).trim().optional(),
    profilePictureUrl: Joi.string().uri().max(500).optional(),
    subscriptionTier: Joi.string().valid('free', 'premium', 'enterprise').optional(),
    preferences: Joi.object({
      theme: Joi.string().valid('light', 'dark', 'auto').optional(),
      notifications: Joi.boolean().optional(),
      language: Joi.string().length(2).optional()
    }).optional()
  }),

  // Job application schemas
  jobApplication: Joi.object({
    jobId: Joi.string().required(),
    resumeId: Joi.string().required(),
    coverLetter: Joi.string().max(5000).optional(),
    customMessage: Joi.string().max(1000).optional(),
    scheduledFor: Joi.date().greater('now').optional()
  }),

  // Automation schemas
  automationSettings: Joi.object({
    enabled: Joi.boolean().required(),
    maxApplicationsPerDay: Joi.number().integer().min(1).max(50).optional(),
    targetLocations: Joi.array().items(Joi.string().max(100)).max(10).optional(),
    excludeCompanies: Joi.array().items(Joi.string().max(100)).max(50).optional(),
    salaryRange: Joi.object({
      min: Joi.number().positive().optional(),
      max: Joi.number().positive().optional()
    }).optional()
  }),

  // Query parameter schemas
  paginationQuery: Joi.object({
    page: Joi.number().integer().min(1).max(1000).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().max(50).optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }),

  searchQuery: Joi.object({
    q: Joi.string().min(1).max(200).trim().optional(),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).optional()
  }),

  // Enhanced CVleap-specific schemas
  jobSearch: Joi.object({
    keywords: Joi.string().max(200).trim().optional(),
    location: Joi.string().max(100).trim().optional(),
    company: Joi.string().max(100).trim().optional(),
    salaryMin: Joi.number().positive().max(10000000).optional(),
    salaryMax: Joi.number().positive().max(10000000).optional(),
    jobType: Joi.string().valid('full-time', 'part-time', 'contract', 'internship', 'remote').optional(),
    experienceLevel: Joi.string().valid('entry', 'mid', 'senior', 'executive').optional(),
    datePosted: Joi.string().valid('24h', '3d', '7d', '14d', '30d').optional(),
    remote: Joi.boolean().optional()
  }),

  careerGoals: Joi.object({
    targetRole: Joi.string().max(100).trim().required(),
    targetIndustry: Joi.string().max(100).trim().optional(),
    targetSalary: Joi.number().positive().max(10000000).optional(),
    timeline: Joi.string().valid('immediate', '3months', '6months', '1year', 'flexible').optional(),
    skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    locations: Joi.array().items(Joi.string().max(100)).max(10).optional(),
    remotePreference: Joi.string().valid('onsite', 'remote', 'hybrid', 'flexible').optional()
  }),

  skillAssessment: Joi.object({
    skillName: Joi.string().min(2).max(50).trim().required(),
    proficiencyLevel: Joi.string().valid('beginner', 'intermediate', 'advanced', 'expert').required(),
    yearsOfExperience: Joi.number().min(0).max(50).optional(),
    certifications: Joi.array().items(Joi.string().max(100)).max(10).optional(),
    verified: Joi.boolean().default(false)
  }),

  networkingContact: Joi.object({
    name: Joi.string().min(2).max(100).trim().required(),
    email: Joi.string().email().optional(),
    company: Joi.string().max(100).trim().optional(),
    position: Joi.string().max(100).trim().optional(),
    linkedinUrl: Joi.string().uri().max(500).optional(),
    relationship: Joi.string().valid('colleague', 'manager', 'mentor', 'peer', 'contact').optional(),
    notes: Joi.string().max(1000).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).optional()
  }),

  interviewFeedback: Joi.object({
    applicationId: Joi.string().required(),
    interviewDate: Joi.date().max('now').required(),
    interviewType: Joi.string().valid('phone', 'video', 'onsite', 'technical', 'behavioral').required(),
    rating: Joi.number().min(1).max(5).required(),
    feedback: Joi.string().max(2000).optional(),
    nextSteps: Joi.string().max(500).optional(),
    followUpDate: Joi.date().greater('now').optional()
  }),

  companyResearch: Joi.object({
    companyName: Joi.string().min(2).max(100).trim().required(),
    industry: Joi.string().max(100).trim().optional(),
    size: Joi.string().valid('startup', 'small', 'medium', 'large', 'enterprise').optional(),
    culture: Joi.string().max(1000).optional(),
    benefits: Joi.array().items(Joi.string().max(100)).max(20).optional(),
    glassdoorRating: Joi.number().min(1).max(5).optional(),
    notes: Joi.string().max(2000).optional()

  }),

  // Company validation schema
  company: Joi.object({
    name: Joi.string().min(1).max(200).trim().required(),
    industry: Joi.string().max(100).optional(),
    size: Joi.string().valid('startup', 'small', 'medium', 'large', 'enterprise').optional(),
    location: Joi.string().max(200).optional(),
    website: Joi.string().uri().max(500).optional(),
    description: Joi.string().max(2000).optional()
  }),

  // Interview preparation schema
  interviewPrep: Joi.object({
    jobRole: Joi.string().min(1).max(200).required(),
    company: Joi.string().min(1).max(200).optional(),
    interviewType: Joi.string().valid('phone', 'video', 'onsite', 'technical', 'behavioral').required(),
    difficulty: Joi.string().valid('entry', 'intermediate', 'senior', 'expert').optional(),
    topics: Joi.array().items(Joi.string().max(100)).max(20).optional()
  }),

  // Skill assessment schema
  skillAssessment: Joi.object({
    skills: Joi.array().items(Joi.string().max(50)).min(1).max(30).required(),
    experienceLevel: Joi.string().valid('beginner', 'intermediate', 'advanced', 'expert').optional(),
    industry: Joi.string().max(100).optional()
  }),

  // Feedback schema
  feedback: Joi.object({
    type: Joi.string().valid('bug', 'feature', 'improvement', 'general').required(),
    subject: Joi.string().min(5).max(200).trim().required(),
    message: Joi.string().min(10).max(2000).trim().required(),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium'),
    category: Joi.string().max(100).optional()
  })
};

/**
 * Enhanced input sanitization middleware with comprehensive security measures
 * Protects against XSS, SQL injection, and other malicious inputs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const sanitizeInput = (req, res, next) => {
  /**
   * Deep sanitization function for nested objects and arrays
   * @param {*} obj - Object to sanitize
   * @returns {*} Sanitized object
   */
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj
        .trim()
        // Remove script tags and their content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        // Remove dangerous HTML attributes
        .replace(/\bon\w+\s*=\s*"[^"]*"/gi, '')
        .replace(/\bon\w+\s*=\s*'[^']*'/gi, '')
        // Remove javascript: protocol
        .replace(/javascript:/gi, '')
        // Remove data: URLs (can contain malicious code)
        .replace(/data:\s*text\/html/gi, '')
        // Remove vbscript: protocol
        .replace(/vbscript:/gi, '')
        // Remove expression() CSS
        .replace(/expression\s*\(/gi, '')
        // Remove @import CSS
        .replace(/@import/gi, '')
        // Remove potentially dangerous HTML tags
        .replace(/<(iframe|object|embed|applet|link|meta|base|form|input|textarea|select|option|button)\b[^>]*>/gi, '')
        .replace(/<\/(iframe|object|embed|applet|link|meta|base|form|input|textarea|select|option|button)>/gi, '')
        // Remove SQL injection patterns
        .replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|OR|AND)\b.*\b(FROM|INTO|SET|WHERE|TABLE|DATABASE|SCHEMA)\b)/gi, '')
        .replace(/(;|\b(OR|AND)\s+\d+\s*=\s*\d+|'(\s*OR\s*'.*'='|'|--)|\/\*.*\*\/)/gi, '')
        // Remove LDAP injection patterns
        .replace(/(\(|\)|\*|\||\&|!|=|<|>|~|;)/g, '')
        // Remove XML/XXE patterns
        .replace(/<!ENTITY/gi, '')
        .replace(/<!DOCTYPE/gi, '')
        // Remove NoSQL injection patterns
        .replace(/\$\w+:/gi, '')
        // Limit string length to prevent DoS
        .substring(0, 10000);
    }
    
    if (Array.isArray(obj)) {
      return obj.slice(0, 100).map(item => sanitize(item)); // Limit array size
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const sanitized = {};
      let propertyCount = 0;
      
      for (const key in obj) {
        if (propertyCount >= 100) break; // Limit object properties
        
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          // Sanitize property keys
          const cleanKey = key.toString().replace(/[^\w\-_]/g, '').substring(0, 100);
          if (cleanKey) {
            sanitized[cleanKey] = sanitize(obj[key]);
            propertyCount++;
          }
        }
      }
      return sanitized;
    }
    
    return obj;
  };

  try {
    if (req.body) req.body = sanitize(req.body);
    if (req.query) req.query = sanitize(req.query);
    if (req.params) req.params = sanitize(req.params);
    next();
  } catch (error) {
    console.error('Input sanitization error:', error);
    return res.status(400).json({
      success: false,
      error: 'Invalid input format'
    });
  }
};

/**
 * Advanced content security middleware for file uploads and content
 * Enhanced with comprehensive MIME type validation and security checks
 * @param {Array} allowedMimeTypes - Array of allowed MIME types
 * @param {number} maxSize - Maximum file size in bytes
 * @param {Object} options - Additional security options
 * @returns {Function} Express middleware function
 */
const validateFileUpload = (allowedMimeTypes = [], maxSize = 5 * 1024 * 1024, options = {}) => {
  const {
    maxFiles = 10,
    checkMagicBytes = true,
    allowedExtensions = [],
    blockedExtensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js', '.jar', '.com', '.pif'],
    enableVirusScan = false
  } = options;

  // Magic bytes for common file types
  const magicBytes = {
    'image/jpeg': [0xFF, 0xD8, 0xFF],
    'image/png': [0x89, 0x50, 0x4E, 0x47],
    'image/gif': [0x47, 0x49, 0x46, 0x38],
    'application/pdf': [0x25, 0x50, 0x44, 0x46],
    'application/zip': [0x50, 0x4B, 0x03, 0x04],
    'text/plain': [], // Text files don't have consistent magic bytes
    'application/json': []
  };

  const checkFileMagicBytes = (buffer, expectedMimeType) => {
    if (!checkMagicBytes || !magicBytes[expectedMimeType] || magicBytes[expectedMimeType].length === 0) {
      return true; // Skip check for unsupported types
    }
    
    const expectedBytes = magicBytes[expectedMimeType];
    for (let i = 0; i < expectedBytes.length; i++) {
      if (buffer[i] !== expectedBytes[i]) {
        return false;
      }
    }
    return true;
  };

  return (req, res, next) => {
    // Handle both single file (req.file) and multiple files (req.files)
    const files = [];
    if (req.file) {
      files.push(req.file);
    }
    if (req.files && Array.isArray(req.files)) {
      files.push(...req.files);
    }
    if (req.files && !Array.isArray(req.files)) {
      // Handle multer field-based files object
      Object.values(req.files).forEach(fileArray => {
        if (Array.isArray(fileArray)) {
          files.push(...fileArray);
        } else {
          files.push(fileArray);
        }
      });
    }

    if (files.length === 0) {
      return next();
    }

    // Check number of files
    if (req.files.length > maxFiles) {
      return res.status(413).json({
        success: false,
        error: `Too many files. Maximum ${maxFiles} files allowed`
      });
    }

    for (const file of files) {
      // Check file size
      if (file.size > maxSize) {
        return res.status(413).json({
          success: false,
          error: `File size exceeds maximum limit of ${Math.round(maxSize / (1024 * 1024))}MB`,
          fileName: file.originalname
        });
      }

      // Check MIME type
      if (allowedMimeTypes.length > 0 && !allowedMimeTypes.includes(file.mimetype)) {
        return res.status(415).json({
          success: false,
          error: `File type ${file.mimetype} is not allowed`,
          fileName: file.originalname,
          allowedTypes: allowedMimeTypes
        });
      }

      // Enhanced security checks for file content
      if (file.originalname) {
        // Check for dangerous file extensions (more comprehensive list)
        const dangerousExtensions = [
          '.exe', '.bat', '.cmd', '.scr', '.vbs', '.js', '.jar', '.com', '.pif',
          '.application', '.gadget', '.msi', '.msp', '.hta', '.cpl', '.msc',
          '.wsf', '.wsh', '.ps1', '.ps1xml', '.ps2', '.ps2xml', '.psc1', '.psc2',
          '.msh', '.msh1', '.msh2', '.mshxml', '.msh1xml', '.msh2xml'
        ];
        const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
        
        // Check against blocked extensions
        if (blockedExtensions.includes(fileExtension)) {
          return res.status(415).json({
            success: false,
            error: 'File type not allowed for security reasons',
            fileName: file.originalname
          });
        }

        // Check against allowed extensions if specified
        if (allowedExtensions.length > 0 && !allowedExtensions.includes(fileExtension)) {
          return res.status(415).json({
            success: false,
            error: `File extension ${fileExtension} is not allowed`,
            fileName: file.originalname,
            allowedExtensions: allowedExtensions
          });
        }
      }

      // Magic bytes validation
      if (checkMagicBytes && file.buffer && !checkFileMagicBytes(file.buffer, file.mimetype)) {
        return res.status(415).json({
          success: false,
          error: 'File content does not match declared MIME type',
          fileName: file.originalname
        });
      }

      // Additional security checks
      if (file.originalname) {
        // Check for dangerous file extensions
        const dangerousFileExtensionPatterns = [
          /\.(php|asp|jsp|cgi)$/i,
          /\.(htaccess|htpasswd)$/i,
          /^\.{1,2}$/,
          /<script/i,
          /javascript:/i
        ];

        for (const pattern of dangerousFileExtensionPatterns) {
          if (pattern.test(file.originalname)) {
            return res.status(415).json({
              success: false,
              error: 'Suspicious file name detected',
              fileName: file.originalname
            });
          }
        }

        // Check file name length
        if (file.originalname.length > 255) {
          return res.status(400).json({
            success: false,
            error: 'File name too long (max 255 characters)',
            fileName: file.originalname.substring(0, 50) + '...'
          });
        }

        // Check for double extensions (e.g., .txt.exe)
        const nameParts = file.originalname.toLowerCase().split('.');
        if (nameParts.length > 2) {
          for (let i = 1; i < nameParts.length; i++) {
            if (dangerousExtensions.includes('.' + nameParts[i])) {
              return res.status(415).json({
                success: false,
                error: 'File with potentially dangerous double extension detected'
              });
            }
          }
        }

        // Check for invalid filename patterns
        const invalidFilenamePatterns = [
          /^(con|prn|aux|nul|com[1-9]|lpt[1-9])(\.|$)/i, // Windows reserved names
          /[<>:"|?*]/,  // Invalid filename characters
          /^\s+|\s+$/,  // Leading/trailing whitespace
          /\.\./,       // Directory traversal
        ];
        
        for (const pattern of invalidFilenamePatterns) {
          if (pattern.test(file.originalname)) {
            return res.status(415).json({
              success: false,
              error: 'File name contains invalid or suspicious characters'
            });
          }
        }
      }

      // Check for embedded content (basic check for file headers)
      if (file.buffer) {
        const header = file.buffer.slice(0, 10).toString('hex');
        // Check for executable headers
        const executableHeaders = [
          '4d5a',     // PE/COFF executable
          '7f454c46', // ELF executable
          'cafebabe', // Java class file
          'feedface', // Mach-O executable
        ];
        
        for (const execHeader of executableHeaders) {
          if (header.startsWith(execHeader)) {
            return res.status(415).json({
              success: false,
              error: 'File appears to contain executable code'
            });
          }
        }
      }
    }

    next();
  };
};

/**
 * Enhanced rate limiting validation middleware with user-based and IP-based limiting
 * @param {Object} options - Rate limiting configuration
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.maxRequests - Maximum requests per window
 * @param {boolean} options.useUserBased - Enable user-based rate limiting
 * @param {boolean} options.useIpBased - Enable IP-based rate limiting (default: true)
 * @param {Function} options.keyGenerator - Custom key generator function
 * @returns {Function} Express middleware function
 */
const createCustomRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000,
    maxRequests = 100,
    useUserBased = false,
    useIpBased = true,
    keyGenerator = null
  } = options;
  
  const requestCounts = new Map();
  
  return (req, res, next) => {
    let clientId;
    
    // Determine client identifier based on configuration
    if (keyGenerator) {
      clientId = keyGenerator(req);
    } else if (useUserBased && req.user && req.user.userId) {
      clientId = `user:${req.user.userId}`;
    } else if (useIpBased) {
      clientId = `ip:${req.ip || req.connection.remoteAddress}`;
    } else {
      // Default to IP if no specific configuration
      clientId = `ip:${req.ip || req.connection.remoteAddress}`;
    }
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries to prevent memory leaks
    for (const [id, data] of requestCounts) {
      if (data.timestamp < windowStart) {
        requestCounts.delete(id);
      }
    }
    
    const clientData = requestCounts.get(clientId) || { count: 0, timestamp: now };
    
    if (clientData.timestamp < windowStart) {
      clientData.count = 1;
      clientData.timestamp = now;
    } else {
      clientData.count++;
    }
    
    requestCounts.set(clientId, clientData);
    
    if (clientData.count > maxRequests) {
      return res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        retryAfter: Math.ceil((windowStart + windowMs - now) / 1000),
        limitType: useUserBased && req.user ? 'user' : 'ip'
      });
    }
    
    // Add enhanced rate limit headers
    res.setHeader('X-RateLimit-Limit', maxRequests);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, maxRequests - clientData.count));
    res.setHeader('X-RateLimit-Reset', Math.ceil((windowStart + windowMs) / 1000));
    res.setHeader('X-RateLimit-Type', useUserBased && req.user ? 'user' : 'ip');
    
    next();
  };
};

/**
 * Creates a complete validation middleware stack for enhanced security
 * @param {Joi.Schema} schema - Joi validation schema
 * @param {Object} options - Configuration options
 * @returns {Array} Array of middleware functions
 */
const createValidationStack = (schema, options = {}) => {
  const {
    source = 'body',
    rateLimitOptions = null,
    sanitize = true,
    fileUpload = null
  } = options;

  const middleware = [];

  // Add rate limiting if specified
  if (rateLimitOptions) {
    middleware.push(createCustomRateLimit(rateLimitOptions));
  }

  // Add input sanitization if enabled
  if (sanitize) {
    middleware.push(sanitizeInput);
  }

  // Add file upload validation if specified
  if (fileUpload) {
    middleware.push(validateFileUpload(
      fileUpload.allowedMimeTypes,
      fileUpload.maxSize,
      fileUpload.options
    ));
  }

  // Add request validation
  middleware.push(validateRequest(schema, source));

  return middleware;
};

/**
 * Enhanced middleware composer for common validation patterns
 */
const validationComposer = {
  // For authentication endpoints
  auth: (schema) => createValidationStack(schema, {
    rateLimitOptions: { windowMs: 15 * 60 * 1000, maxRequests: 5, useIpBased: true },
    sanitize: true
  }),

  // For API endpoints with user-based rate limiting
  userApi: (schema) => createValidationStack(schema, {
    rateLimitOptions: { windowMs: 15 * 60 * 1000, maxRequests: 100, useUserBased: true },
    sanitize: true
  }),

  // For file upload endpoints
  fileUpload: (schema, uploadConfig) => createValidationStack(schema, {
    rateLimitOptions: { windowMs: 5 * 60 * 1000, maxRequests: 10, useIpBased: true },
    sanitize: true,
    fileUpload: uploadConfig
  }),

  // For search/query endpoints
  search: (schema) => createValidationStack(schema, {
    source: 'query',
    rateLimitOptions: { windowMs: 1 * 60 * 1000, maxRequests: 60, useIpBased: true },
    sanitize: true
  }),

  // For high-frequency endpoints
  highFrequency: (schema) => createValidationStack(schema, {
    rateLimitOptions: { windowMs: 1 * 60 * 1000, maxRequests: 300, useUserBased: true },
    sanitize: true
  })
};

module.exports = { 
  validateRequest, 
  schemas, 
  sanitizeInput, 
  validateFileUpload, 
  createCustomRateLimit,
  createValidationStack,
  validationComposer
};