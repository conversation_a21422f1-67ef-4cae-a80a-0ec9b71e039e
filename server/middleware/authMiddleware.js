const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const auditLogger = require('../utils/auditLogger');

/**
 * Enhanced Authentication Middleware with MFA and session management
 */
class AuthMiddleware {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret-change-in-production';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
    this.sessions = new Map(); // In production, use Redis
    this.mfaSessions = new Map(); // Temporary MFA sessions
  }

  /**
   * Generate access and refresh tokens
   */
  generateTokens(user) {
    const sessionId = uuidv4();
    const payload = {
      userId: user.id,
      email: user.email,
      sessionId,
      permissions: user.permissions || []
    };

    const accessToken = jwt.sign(payload, this.jwtSecret, { 
      expiresIn: '15m',
      issuer: 'cvleap-server',
      audience: 'cvleap-client'
    });

    const refreshToken = jwt.sign(
      { sessionId, userId: user.id }, 
      this.jwtRefreshSecret, 
      { expiresIn: '7d' }
    );

    // Store session information
    this.sessions.set(sessionId, {
      userId: user.id,
      email: user.email,
      createdAt: new Date(),
      lastActivity: new Date(),
      ipAddress: null, // Set during verification
      userAgent: null,
      refreshToken
    });

    auditLogger.logAuth('TOKEN_GENERATED', user.id, { sessionId });

    return { accessToken, refreshToken, sessionId };
  }

  /**
   * Verify JWT token and session
   */
  async verifyToken(req, res, next) {
    try {
      const token = this.extractToken(req);
      if (!token) {
        return res.status(401).json({ 
          error: 'Access token required',
          code: 'TOKEN_MISSING'
        });
      }

      const decoded = jwt.verify(token, this.jwtSecret);
      const session = this.sessions.get(decoded.sessionId);

      if (!session) {
        auditLogger.logAuth('INVALID_SESSION', decoded.userId, { 
          sessionId: decoded.sessionId,
          ip: req.ip 
        });
        return res.status(401).json({ 
          error: 'Invalid session',
          code: 'SESSION_INVALID'
        });
      }

      // Update session activity
      session.lastActivity = new Date();
      session.ipAddress = req.ip;
      session.userAgent = req.get('User-Agent');

      // Attach user info to request
      req.user = {
        userId: decoded.userId,
        email: decoded.email,
        sessionId: decoded.sessionId,
        permissions: decoded.permissions || []
      };

      next();
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({ 
          error: 'Token expired',
          code: 'TOKEN_EXPIRED'
        });
      }

      auditLogger.logAuth('TOKEN_VERIFICATION_FAILED', null, { 
        error: error.message,
        ip: req.ip 
      });

      return res.status(401).json({ 
        error: 'Invalid token',
        code: 'TOKEN_INVALID'
      });
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;
      if (!refreshToken) {
        return res.status(400).json({ error: 'Refresh token required' });
      }

      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret);
      const session = this.sessions.get(decoded.sessionId);

      if (!session || session.refreshToken !== refreshToken) {
        auditLogger.logAuth('INVALID_REFRESH_TOKEN', decoded.userId, { 
          sessionId: decoded.sessionId,
          ip: req.ip 
        });
        return res.status(401).json({ error: 'Invalid refresh token' });
      }

      // Generate new access token
      const user = { id: session.userId, email: session.email };
      const { accessToken } = this.generateTokens(user);

      auditLogger.logAuth('TOKEN_REFRESHED', session.userId, { 
        sessionId: decoded.sessionId 
      });

      res.json({ accessToken });
    } catch (error) {
      auditLogger.logAuth('TOKEN_REFRESH_FAILED', null, { 
        error: error.message,
        ip: req.ip 
      });
      return res.status(401).json({ error: 'Token refresh failed' });
    }
  }

  /**
   * Initiate MFA challenge
   */
  async initiateMFA(userId, method = 'totp') {
    const mfaToken = uuidv4();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    this.mfaSessions.set(mfaToken, {
      userId,
      method,
      createdAt: new Date(),
      expiresAt,
      attempts: 0
    });

    auditLogger.logAuth('MFA_INITIATED', userId, { method, mfaToken });

    // In production, generate and send actual MFA challenge
    // For now, return a mock challenge
    return {
      mfaToken,
      challenge: method === 'totp' ? 'Enter your authenticator code' : 'Check your phone for SMS',
      expiresAt
    };
  }

  /**
   * Verify MFA challenge
   */
  async verifyMFA(mfaToken, challenge) {
    const mfaSession = this.mfaSessions.get(mfaToken);
    
    if (!mfaSession) {
      auditLogger.logAuth('MFA_SESSION_NOT_FOUND', null, { mfaToken });
      return { success: false, error: 'Invalid MFA session' };
    }

    if (new Date() > mfaSession.expiresAt) {
      this.mfaSessions.delete(mfaToken);
      auditLogger.logAuth('MFA_EXPIRED', mfaSession.userId, { mfaToken });
      return { success: false, error: 'MFA session expired' };
    }

    mfaSession.attempts++;

    // In production, verify actual MFA challenge
    // For demo, accept "123456" as valid TOTP
    const isValid = challenge === '123456' || challenge === 'valid';

    if (isValid) {
      this.mfaSessions.delete(mfaToken);
      auditLogger.logAuth('MFA_SUCCESS', mfaSession.userId, { mfaToken });
      return { success: true, userId: mfaSession.userId };
    } else {
      auditLogger.logAuth('MFA_FAILED', mfaSession.userId, { 
        mfaToken, 
        attempts: mfaSession.attempts 
      });

      if (mfaSession.attempts >= 3) {
        this.mfaSessions.delete(mfaToken);
        return { success: false, error: 'Too many failed attempts' };
      }

      return { success: false, error: 'Invalid MFA code' };
    }
  }

  /**
   * Logout and invalidate session
   */
  async logout(req, res) {
    try {
      const sessionId = req.user?.sessionId;
      if (sessionId) {
        this.sessions.delete(sessionId);
        auditLogger.logAuth('LOGOUT', req.user.userId, { sessionId });
      }

      res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
      res.status(500).json({ error: 'Logout failed' });
    }
  }

  /**
   * Check if user has required permission
   */
  requirePermission(permission) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      if (!req.user.permissions.includes(permission) && !req.user.permissions.includes('admin')) {
        auditLogger.logAuth('PERMISSION_DENIED', req.user.userId, { 
          permission,
          endpoint: req.path 
        });
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      next();
    };
  }

  /**
   * Extract token from request
   */
  extractToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * Get active sessions for a user
   */
  getUserSessions(userId) {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId)
      .map(session => ({
        sessionId: session.sessionId,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent
      }));
  }

  /**
   * Invalidate all sessions for a user
   */
  invalidateUserSessions(userId) {
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId) {
        this.sessions.delete(sessionId);
      }
    }
    auditLogger.logAuth('ALL_SESSIONS_INVALIDATED', userId);
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions() {
    const now = new Date();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > maxAge) {
        this.sessions.delete(sessionId);
      }
    }

    // Clean up expired MFA sessions
    for (const [mfaToken, mfaSession] of this.mfaSessions.entries()) {
      if (now > mfaSession.expiresAt) {
        this.mfaSessions.delete(mfaToken);
      }
    }
  }
}

module.exports = AuthMiddleware;