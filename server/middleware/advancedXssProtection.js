const DOMPurify = require('isomorphic-dompurify');
const { logger } = require('../utils/logger');
const { monitoring } = require('../utils/monitoring');

/**
 * Advanced XSS Protection Middleware
 * Provides comprehensive XSS prevention with context-aware sanitization
 */

// XSS attack patterns
const XSS_PATTERNS = [
  // Script tags
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<script[^>]*>[\s\S]*?<\/script>/gi,
  /<script[^>]*\/>/gi,
  
  // Event handlers
  /on\w+\s*=\s*["'][^"']*["']/gi,
  /on\w+\s*=\s*[^"'\s>]+/gi,
  
  // JavaScript URLs
  /javascript\s*:/gi,
  /vbscript\s*:/gi,
  /data\s*:\s*text\/html/gi,
  
  // HTML injection
  /<iframe\b[^>]*>/gi,
  /<object\b[^>]*>/gi,
  /<embed\b[^>]*>/gi,
  /<applet\b[^>]*>/gi,
  /<meta\b[^>]*>/gi,
  /<link\b[^>]*>/gi,
  /<style\b[^>]*>/gi,
  
  // Form injection
  /<form\b[^>]*>/gi,
  /<input\b[^>]*>/gi,
  /<textarea\b[^>]*>/gi,
  /<select\b[^>]*>/gi,
  
  // Expression injection
  /expression\s*\(/gi,
  /url\s*\(/gi,
  /@import/gi,
  
  // Data URLs with scripts
  /data\s*:\s*[^,]*script/gi,
  
  // SVG injection
  /<svg\b[^>]*>[\s\S]*?<\/svg>/gi,
  
  // CSS injection
  /style\s*=\s*["'][^"']*expression/gi,
  /style\s*=\s*["'][^"']*javascript/gi,
  
  // HTML entities that could be dangerous
  /&#x?[0-9a-f]+;?/gi
];

// Context-specific sanitization rules
const SANITIZATION_CONTEXTS = {
  html: {
    allowedTags: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    allowedAttributes: {},
    stripTags: false
  },
  text: {
    allowedTags: [],
    allowedAttributes: {},
    stripTags: true
  },
  url: {
    allowedProtocols: ['http', 'https', 'mailto'],
    stripTags: true
  },
  email: {
    allowedTags: [],
    allowedAttributes: {},
    stripTags: true,
    emailValidation: true
  },
  json: {
    allowedTags: [],
    allowedAttributes: {},
    stripTags: true,
    jsonValidation: true
  }
};

/**
 * Advanced XSS protection middleware
 */
function advancedXssProtection(options = {}) {
  const config = {
    enableLogging: true,
    enableBlocking: true,
    enableSanitization: true,
    strictMode: false,
    ...options
  };
  
  return (req, res, next) => {
    try {
      // Skip for certain content types
      if (req.get('Content-Type')?.includes('multipart/form-data')) {
        return next();
      }
      
      // Process query parameters
      if (req.query) {
        req.query = sanitizeObject(req.query, 'text', config);
      }
      
      // Process request body
      if (req.body) {
        req.body = sanitizeObject(req.body, 'html', config);
      }
      
      // Process headers (selective)
      const headersToCheck = ['user-agent', 'referer', 'x-forwarded-for'];
      headersToCheck.forEach(header => {
        if (req.headers[header]) {
          const sanitized = sanitizeString(req.headers[header], 'text', config);
          if (sanitized !== req.headers[header]) {
            logger.logSecurity('XSS_ATTEMPT_IN_HEADER', {
              header,
              original: req.headers[header],
              sanitized,
              ip: req.ip,
              userAgent: req.get('User-Agent')
            });
            
            if (config.enableBlocking) {
              return res.status(400).json({
                success: false,
                error: 'Malicious content detected in request headers',
                code: 'XSS_DETECTED'
              });
            }
          }
        }
      });
      
      next();
    } catch (error) {
      logger.error('XSS protection middleware error', {
        error: error.message,
        path: req.path,
        method: req.method
      });
      
      if (config.strictMode) {
        return res.status(400).json({
          success: false,
          error: 'Request processing failed',
          code: 'XSS_PROTECTION_ERROR'
        });
      }
      
      next();
    }
  };
}

/**
 * Sanitize object recursively
 * @param {Object} obj - Object to sanitize
 * @param {string} context - Sanitization context
 * @param {Object} config - Configuration
 * @returns {Object} Sanitized object
 */
function sanitizeObject(obj, context, config) {
  if (obj === null || typeof obj !== 'object') {
    return sanitizeString(obj, context, config);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, context, config));
  }
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    const sanitizedKey = sanitizeString(key, 'text', config);
    const sanitizedValue = sanitizeObject(value, getContextForField(key, context), config);
    
    // Check if sanitization changed the values
    if (config.enableLogging && (sanitizedKey !== key || sanitizedValue !== value)) {
      logger.logSecurity('XSS_CONTENT_SANITIZED', {
        field: key,
        originalKey: key,
        sanitizedKey,
        originalValue: typeof value === 'string' ? value.substring(0, 100) : value,
        sanitizedValue: typeof sanitizedValue === 'string' ? sanitizedValue.substring(0, 100) : sanitizedValue
      });
    }
    
    sanitized[sanitizedKey] = sanitizedValue;
  }
  
  return sanitized;
}

/**
 * Sanitize string based on context
 * @param {any} input - Input to sanitize
 * @param {string} context - Sanitization context
 * @param {Object} config - Configuration
 * @returns {any} Sanitized input
 */
function sanitizeString(input, context, config) {
  if (typeof input !== 'string') {
    return input;
  }
  
  // Check for XSS patterns
  const xssDetected = detectXssPatterns(input);
  if (xssDetected.length > 0) {
    if (config.enableLogging) {
      logger.logSecurity('XSS_PATTERN_DETECTED', {
        input: input.substring(0, 200),
        patterns: xssDetected,
        context
      });
      
      // Trigger monitoring alert
      monitoring.triggerAlert('xss_attempt_detected', {
        level: 'warning',
        patterns: xssDetected,
        context,
        inputLength: input.length
      });
    }
    
    if (config.enableBlocking && !config.enableSanitization) {
      throw new Error('XSS pattern detected and blocking enabled');
    }
  }
  
  if (!config.enableSanitization) {
    return input;
  }
  
  // Apply context-specific sanitization
  return applySanitization(input, context);
}

/**
 * Detect XSS patterns in input
 * @param {string} input - Input to check
 * @returns {Array} Detected patterns
 */
function detectXssPatterns(input) {
  const detected = [];
  
  for (let i = 0; i < XSS_PATTERNS.length; i++) {
    const pattern = XSS_PATTERNS[i];
    if (pattern.test(input)) {
      detected.push({
        pattern: pattern.source,
        index: i
      });
    }
  }
  
  return detected;
}

/**
 * Apply context-specific sanitization
 * @param {string} input - Input to sanitize
 * @param {string} context - Sanitization context
 * @returns {string} Sanitized input
 */
function applySanitization(input, context) {
  const rules = SANITIZATION_CONTEXTS[context] || SANITIZATION_CONTEXTS.text;
  
  switch (context) {
    case 'html':
      return sanitizeHtml(input, rules);
    case 'url':
      return sanitizeUrl(input, rules);
    case 'email':
      return sanitizeEmail(input, rules);
    case 'json':
      return sanitizeJson(input, rules);
    case 'text':
    default:
      return sanitizeText(input, rules);
  }
}

/**
 * Sanitize HTML content
 * @param {string} input - HTML input
 * @param {Object} rules - Sanitization rules
 * @returns {string} Sanitized HTML
 */
function sanitizeHtml(input, rules) {
  try {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: rules.allowedTags,
      ALLOWED_ATTR: Object.keys(rules.allowedAttributes),
      KEEP_CONTENT: !rules.stripTags,
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      SANITIZE_NAMED_PROPS: true,
      FORBID_SCRIPT: true,
      FORBID_TAGS: ['script', 'object', 'embed', 'applet', 'meta', 'link'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur']
    });
  } catch (error) {
    logger.error('HTML sanitization failed', { error: error.message });
    return sanitizeText(input, rules);
  }
}

/**
 * Sanitize URL
 * @param {string} input - URL input
 * @param {Object} rules - Sanitization rules
 * @returns {string} Sanitized URL
 */
function sanitizeUrl(input, rules) {
  try {
    const url = new URL(input);
    
    if (!rules.allowedProtocols.includes(url.protocol.replace(':', ''))) {
      return '';
    }
    
    // Remove dangerous parameters
    url.searchParams.delete('javascript');
    url.searchParams.delete('vbscript');
    
    return url.toString();
  } catch (error) {
    // Invalid URL, sanitize as text
    return sanitizeText(input, rules);
  }
}

/**
 * Sanitize email
 * @param {string} input - Email input
 * @param {Object} rules - Sanitization rules
 * @returns {string} Sanitized email
 */
function sanitizeEmail(input, rules) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const sanitized = sanitizeText(input, rules);
  
  if (rules.emailValidation && !emailRegex.test(sanitized)) {
    return '';
  }
  
  return sanitized;
}

/**
 * Sanitize JSON string
 * @param {string} input - JSON input
 * @param {Object} rules - Sanitization rules
 * @returns {string} Sanitized JSON
 */
function sanitizeJson(input, rules) {
  try {
    const parsed = JSON.parse(input);
    const sanitized = sanitizeObject(parsed, 'text', { enableSanitization: true });
    return JSON.stringify(sanitized);
  } catch (error) {
    return sanitizeText(input, rules);
  }
}

/**
 * Sanitize plain text
 * @param {string} input - Text input
 * @param {Object} rules - Sanitization rules
 * @returns {string} Sanitized text
 */
function sanitizeText(input, rules) {
  let sanitized = input;
  
  // Remove HTML tags if required
  if (rules.stripTags) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }
  
  // Remove dangerous patterns
  XSS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Encode HTML entities
  sanitized = sanitized
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
  
  // Remove null bytes and control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  
  return sanitized;
}

/**
 * Get appropriate context for field
 * @param {string} fieldName - Field name
 * @param {string} defaultContext - Default context
 * @returns {string} Context
 */
function getContextForField(fieldName, defaultContext) {
  const fieldContextMap = {
    email: 'email',
    url: 'url',
    website: 'url',
    link: 'url',
    description: 'html',
    summary: 'html',
    bio: 'html',
    content: 'html',
    message: 'html',
    notes: 'html'
  };
  
  const lowerFieldName = fieldName.toLowerCase();
  return fieldContextMap[lowerFieldName] || defaultContext;
}

/**
 * Create XSS protection for specific routes
 * @param {Object} routeConfig - Route-specific configuration
 * @returns {Function} Middleware function
 */
function createRouteXssProtection(routeConfig = {}) {
  return advancedXssProtection({
    enableLogging: true,
    enableBlocking: true,
    enableSanitization: true,
    strictMode: false,
    ...routeConfig
  });
}

/**
 * Validate and sanitize specific field
 * @param {string} value - Value to sanitize
 * @param {string} context - Context for sanitization
 * @param {Object} options - Options
 * @returns {string} Sanitized value
 */
function sanitizeField(value, context = 'text', options = {}) {
  const config = {
    enableLogging: false,
    enableSanitization: true,
    ...options
  };
  
  return sanitizeString(value, context, config);
}

module.exports = {
  advancedXssProtection,
  createRouteXssProtection,
  sanitizeField,
  sanitizeObject,
  SANITIZATION_CONTEXTS
};
