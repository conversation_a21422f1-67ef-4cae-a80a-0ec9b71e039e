const multer = require('multer');
const path = require('path');
const crypto = require('crypto');
const fs = require('fs').promises;
const { logger } = require('../utils/logger');
const { monitoring } = require('../utils/monitoring');

/**
 * Enhanced File Upload Validation and Scanning
 * Provides comprehensive file security validation and malware scanning
 */

// File type configurations
const FILE_TYPES = {
  images: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedFor: ['profile_picture', 'resume_image']
  },
  documents: {
    extensions: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/rtf'],
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedFor: ['resume_upload', 'cover_letter']
  },
  archives: {
    extensions: ['.zip'],
    mimeTypes: ['application/zip'],
    maxSize: 25 * 1024 * 1024, // 25MB
    allowedFor: ['portfolio_upload']
  }
};

// Dangerous file patterns
const DANGEROUS_PATTERNS = [
  // Executable files
  /\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|app|deb|pkg|dmg)$/i,
  // Script files
  /\.(php|asp|aspx|jsp|pl|py|rb|sh|bash)$/i,
  // System files
  /\.(dll|sys|ini|cfg|conf)$/i,
  // Double extensions
  /\.[^.]+\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i,
  // Hidden executables
  /\.(pdf|doc|docx|jpg|png)\.exe$/i
];

// Malicious content signatures (simplified)
const MALICIOUS_SIGNATURES = [
  // PE header
  Buffer.from([0x4D, 0x5A]), // MZ
  // ELF header
  Buffer.from([0x7F, 0x45, 0x4C, 0x46]), // .ELF
  // Script patterns
  Buffer.from('<?php'),
  Buffer.from('<script'),
  Buffer.from('javascript:'),
  Buffer.from('vbscript:'),
  // Suspicious strings
  Buffer.from('eval('),
  Buffer.from('exec('),
  Buffer.from('system('),
  Buffer.from('shell_exec')
];

/**
 * Configure multer storage with security checks
 */
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      const uploadDir = path.join(__dirname, '../uploads', req.user?.id || 'temp');
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      logger.error('Failed to create upload directory', { error: error.message });
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate secure filename
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(8).toString('hex');
    const ext = path.extname(file.originalname).toLowerCase();
    const safeName = `${timestamp}_${randomBytes}${ext}`;
    
    // Store original filename for reference
    file.originalSafeName = safeName;
    
    cb(null, safeName);
  }
});

/**
 * File filter with comprehensive security checks
 */
const fileFilter = (req, file, cb) => {
  try {
    const uploadType = req.body.uploadType || req.query.uploadType;
    
    // Validate upload type
    if (!uploadType) {
      return cb(new Error('Upload type is required'), false);
    }
    
    // Get file extension and mime type
    const ext = path.extname(file.originalname).toLowerCase();
    const mimeType = file.mimetype.toLowerCase();
    
    // Check for dangerous file patterns
    if (DANGEROUS_PATTERNS.some(pattern => pattern.test(file.originalname))) {
      logger.warn('Dangerous file pattern detected', {
        filename: file.originalname,
        mimetype: mimeType,
        userId: req.user?.id
      });
      return cb(new Error('File type not allowed for security reasons'), false);
    }
    
    // Find matching file type configuration
    let allowedType = null;
    for (const [typeName, config] of Object.entries(FILE_TYPES)) {
      if (config.allowedFor.includes(uploadType)) {
        if (config.extensions.includes(ext) && config.mimeTypes.includes(mimeType)) {
          allowedType = config;
          break;
        }
      }
    }
    
    if (!allowedType) {
      logger.warn('File type not allowed', {
        filename: file.originalname,
        extension: ext,
        mimetype: mimeType,
        uploadType,
        userId: req.user?.id
      });
      return cb(new Error(`File type ${ext} not allowed for ${uploadType}`), false);
    }
    
    // Store allowed type for size validation
    file.allowedType = allowedType;
    
    cb(null, true);
  } catch (error) {
    logger.error('File filter error', { error: error.message });
    cb(error, false);
  }
};

/**
 * Create multer upload middleware
 */
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB max (will be checked per type)
    files: 5, // Max 5 files per request
    fields: 10, // Max 10 form fields
    fieldNameSize: 100, // Max field name length
    fieldSize: 1024 * 1024 // Max field value size (1MB)
  }
});

/**
 * Enhanced file validation middleware
 */
function validateUploadedFile(req, res, next) {
  if (!req.file && !req.files) {
    return next();
  }
  
  const files = req.files || [req.file];
  
  Promise.all(files.map(file => validateSingleFile(file, req)))
    .then(() => next())
    .catch(error => {
      logger.error('File validation failed', {
        error: error.message,
        userId: req.user?.id
      });
      
      // Clean up uploaded files
      files.forEach(file => {
        if (file.path) {
          fs.unlink(file.path).catch(err => 
            logger.error('Failed to cleanup invalid file', { error: err.message })
          );
        }
      });
      
      res.status(400).json({
        success: false,
        error: 'File validation failed',
        details: error.message,
        code: 'FILE_VALIDATION_ERROR'
      });
    });
}

/**
 * Validate a single uploaded file
 * @param {Object} file - Multer file object
 * @param {Object} req - Express request object
 */
async function validateSingleFile(file, req) {
  try {
    // Check file size against type-specific limits
    if (file.allowedType && file.size > file.allowedType.maxSize) {
      throw new Error(`File size ${file.size} exceeds limit ${file.allowedType.maxSize} for this file type`);
    }
    
    // Read file content for scanning
    const fileContent = await fs.readFile(file.path);
    
    // Scan for malicious signatures
    await scanFileContent(fileContent, file);
    
    // Validate file structure based on type
    await validateFileStructure(fileContent, file);
    
    // Log successful validation
    logger.info('File validation passed', {
      filename: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      userId: req.user?.id
    });
    
    // Store file metadata for audit
    await logFileUpload(file, req);
    
  } catch (error) {
    // Log security event
    logger.logSecurity('FILE_VALIDATION_FAILED', {
      filename: file.originalname,
      error: error.message,
      userId: req.user?.id,
      ip: req.ip
    });
    
    throw error;
  }
}

/**
 * Scan file content for malicious signatures
 * @param {Buffer} content - File content
 * @param {Object} file - File metadata
 */
async function scanFileContent(content, file) {
  // Check for malicious signatures
  for (const signature of MALICIOUS_SIGNATURES) {
    if (content.includes(signature)) {
      throw new Error(`Malicious content detected in file: ${file.originalname}`);
    }
  }
  
  // Check for suspicious patterns in text files
  if (file.mimetype.startsWith('text/') || file.mimetype === 'application/pdf') {
    const textContent = content.toString('utf8', 0, Math.min(content.length, 10000));
    
    const suspiciousPatterns = [
      /<script[^>]*>/i,
      /javascript:/i,
      /vbscript:/i,
      /on\w+\s*=/i,
      /eval\s*\(/i,
      /exec\s*\(/i,
      /system\s*\(/i,
      /<iframe[^>]*>/i,
      /<object[^>]*>/i,
      /<embed[^>]*>/i
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(textContent)) {
        throw new Error(`Suspicious script content detected in file: ${file.originalname}`);
      }
    }
  }
  
  // Additional checks for specific file types
  if (file.mimetype === 'application/pdf') {
    await validatePdfContent(content, file);
  } else if (file.mimetype.startsWith('image/')) {
    await validateImageContent(content, file);
  }
}

/**
 * Validate PDF file content
 * @param {Buffer} content - PDF content
 * @param {Object} file - File metadata
 */
async function validatePdfContent(content, file) {
  const pdfHeader = content.slice(0, 4);
  if (!pdfHeader.equals(Buffer.from('%PDF'))) {
    throw new Error(`Invalid PDF header in file: ${file.originalname}`);
  }
  
  // Check for embedded JavaScript
  const textContent = content.toString('utf8');
  if (textContent.includes('/JavaScript') || textContent.includes('/JS')) {
    throw new Error(`PDF contains JavaScript which is not allowed: ${file.originalname}`);
  }
}

/**
 * Validate image file content
 * @param {Buffer} content - Image content
 * @param {Object} file - File metadata
 */
async function validateImageContent(content, file) {
  // Check for valid image headers
  const validHeaders = {
    'image/jpeg': [[0xFF, 0xD8, 0xFF]],
    'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]],
    'image/gif': [[0x47, 0x49, 0x46, 0x38, 0x37, 0x61], [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]],
    'image/webp': [[0x52, 0x49, 0x46, 0x46]]
  };
  
  const expectedHeaders = validHeaders[file.mimetype];
  if (expectedHeaders) {
    const fileHeader = Array.from(content.slice(0, 8));
    const isValid = expectedHeaders.some(header => 
      header.every((byte, index) => fileHeader[index] === byte)
    );
    
    if (!isValid) {
      throw new Error(`Invalid ${file.mimetype} header in file: ${file.originalname}`);
    }
  }
  
  // Check for embedded scripts in SVG
  if (file.mimetype === 'image/svg+xml') {
    const svgContent = content.toString('utf8');
    if (/<script/i.test(svgContent) || /javascript:/i.test(svgContent)) {
      throw new Error(`SVG contains scripts which are not allowed: ${file.originalname}`);
    }
  }
}

/**
 * Validate file structure based on type
 * @param {Buffer} content - File content
 * @param {Object} file - File metadata
 */
async function validateFileStructure(content, file) {
  // Basic structure validation
  if (content.length === 0) {
    throw new Error(`Empty file not allowed: ${file.originalname}`);
  }
  
  // Check for null bytes in text files (potential binary injection)
  if (file.mimetype.startsWith('text/')) {
    if (content.includes(0x00)) {
      throw new Error(`Null bytes detected in text file: ${file.originalname}`);
    }
  }
}

/**
 * Log file upload for audit trail
 * @param {Object} file - File metadata
 * @param {Object} req - Express request object
 */
async function logFileUpload(file, req) {
  try {
    const database = require('../database');
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      req.user?.id,
      'FILE_UPLOADED',
      'file',
      file.filename,
      JSON.stringify({
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        uploadType: req.body.uploadType || req.query.uploadType
      }),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('Failed to log file upload', { error: error.message });
  }
}

/**
 * Clean up temporary files
 */
async function cleanupTempFiles() {
  try {
    const tempDir = path.join(__dirname, '../uploads/temp');
    const files = await fs.readdir(tempDir).catch(() => []);
    
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      const stats = await fs.stat(filePath).catch(() => null);
      
      if (stats && (now - stats.mtime.getTime()) > maxAge) {
        await fs.unlink(filePath);
        logger.debug('Cleaned up temp file', { file });
      }
    }
  } catch (error) {
    logger.error('Failed to cleanup temp files', { error: error.message });
  }
}

// Schedule cleanup every hour
setInterval(cleanupTempFiles, 60 * 60 * 1000);

module.exports = {
  upload,
  validateUploadedFile,
  FILE_TYPES,
  cleanupTempFiles
};
