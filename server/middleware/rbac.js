const database = require('../database');
const { logger } = require('../utils/logger');

/**
 * Role-Based Access Control (RBAC) Middleware
 * Provides comprehensive permission checking and role validation
 */

// Define role hierarchy (higher number = more permissions)
const ROLE_HIERARCHY = {
  'user': 1,
  'moderator': 2,
  'admin': 3,
  'super_admin': 4
};

// Define permission sets for each role
const ROLE_PERMISSIONS = {
  'user': [
    'resume:read', 'resume:create', 'resume:update', 'resume:delete',
    'job:read', 'job:create', 'job:update', 'job:delete',
    'application:read', 'application:create', 'application:update',
    'profile:read', 'profile:update'
  ],
  'moderator': [
    // All user permissions plus:
    'user:read', 'user:moderate',
    'content:moderate', 'reports:read'
  ],
  'admin': [
    // All moderator permissions plus:
    'user:create', 'user:update', 'user:suspend',
    'system:read', 'analytics:read', 'audit:read'
  ],
  'super_admin': [
    // All admin permissions plus:
    'user:delete', 'admin:create', 'admin:update', 'admin:delete',
    'system:configure', 'system:backup', 'audit:export',
    'security:configure'
  ]
};

/**
 * Get all permissions for a role (including inherited permissions)
 * @param {string} role - User role
 * @returns {Array} Array of permissions
 */
function getRolePermissions(role) {
  const permissions = new Set();
  const roleLevel = ROLE_HIERARCHY[role] || 0;
  
  // Add permissions from all roles at or below this level
  Object.entries(ROLE_HIERARCHY).forEach(([roleName, level]) => {
    if (level <= roleLevel) {
      const rolePerms = ROLE_PERMISSIONS[roleName] || [];
      rolePerms.forEach(perm => permissions.add(perm));
    }
  });
  
  return Array.from(permissions);
}

/**
 * Check if user has required role
 * @param {Array|string} requiredRoles - Required role(s)
 * @returns {Function} Express middleware
 */
function requireRole(requiredRoles) {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  return async (req, res, next) => {
    try {
      if (!req.user) {
        logger.warn('RBAC: No user in request', { 
          path: req.path, 
          method: req.method,
          ip: req.ip 
        });
        return res.status(401).json({ 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userRole = req.user.user_role || req.user.role;
      const userLevel = ROLE_HIERARCHY[userRole] || 0;
      
      // Check if user has any of the required roles
      const hasRequiredRole = roles.some(role => {
        const requiredLevel = ROLE_HIERARCHY[role] || 0;
        return userLevel >= requiredLevel;
      });

      if (!hasRequiredRole) {
        logger.warn('RBAC: Insufficient role', {
          userId: req.user.id,
          userRole,
          requiredRoles: roles,
          path: req.path,
          method: req.method,
          ip: req.ip
        });

        // Log security event
        await logSecurityEvent(req.user.id, 'INSUFFICIENT_ROLE', {
          userRole,
          requiredRoles: roles,
          path: req.path,
          method: req.method
        }, req);

        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_ROLE',
          required: roles,
          current: userRole
        });
      }

      // Add user permissions to request for further checks
      req.userPermissions = getRolePermissions(userRole);
      
      logger.debug('RBAC: Role check passed', {
        userId: req.user.id,
        userRole,
        requiredRoles: roles
      });

      next();
    } catch (error) {
      logger.error('RBAC: Role check error', {
        error: error.message,
        stack: error.stack,
        userId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'RBAC_ERROR'
      });
    }
  };
}

/**
 * Check if user has specific permission
 * @param {Array|string} requiredPermissions - Required permission(s)
 * @returns {Function} Express middleware
 */
function requirePermission(requiredPermissions) {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userRole = req.user.user_role || req.user.role;
      const userPermissions = req.userPermissions || getRolePermissions(userRole);
      
      // Check if user has all required permissions
      const hasAllPermissions = permissions.every(perm => userPermissions.includes(perm));

      if (!hasAllPermissions) {
        logger.warn('RBAC: Insufficient permissions', {
          userId: req.user.id,
          userRole,
          userPermissions: userPermissions.length,
          requiredPermissions: permissions,
          path: req.path,
          method: req.method
        });

        await logSecurityEvent(req.user.id, 'INSUFFICIENT_PERMISSION', {
          userRole,
          requiredPermissions: permissions,
          path: req.path,
          method: req.method
        }, req);

        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSION',
          required: permissions
        });
      }

      next();
    } catch (error) {
      logger.error('RBAC: Permission check error', {
        error: error.message,
        userId: req.user?.id
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'RBAC_ERROR'
      });
    }
  };
}

/**
 * Check if user owns the resource or has admin privileges
 * @param {string} resourceIdParam - Parameter name containing resource ID
 * @param {string} resourceType - Type of resource for logging
 * @returns {Function} Express middleware
 */
function requireOwnershipOrAdmin(resourceIdParam = 'id', resourceType = 'resource') {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;
      const userRole = req.user.user_role || req.user.role;
      
      // Admin and super_admin can access any resource
      if (['admin', 'super_admin'].includes(userRole)) {
        logger.debug('RBAC: Admin access granted', {
          userId,
          userRole,
          resourceType,
          resourceId
        });
        return next();
      }

      // Check ownership based on resource type
      const isOwner = await checkResourceOwnership(userId, resourceId, resourceType);
      
      if (!isOwner) {
        logger.warn('RBAC: Resource access denied', {
          userId,
          userRole,
          resourceType,
          resourceId,
          reason: 'not_owner'
        });

        await logSecurityEvent(userId, 'UNAUTHORIZED_RESOURCE_ACCESS', {
          resourceType,
          resourceId,
          userRole
        }, req);

        return res.status(403).json({
          success: false,
          error: 'Access denied',
          code: 'RESOURCE_ACCESS_DENIED'
        });
      }

      next();
    } catch (error) {
      logger.error('RBAC: Ownership check error', {
        error: error.message,
        userId: req.user?.id,
        resourceType
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'RBAC_ERROR'
      });
    }
  };
}

/**
 * Check if user owns a specific resource
 * @param {string} userId - User ID
 * @param {string} resourceId - Resource ID
 * @param {string} resourceType - Type of resource
 * @returns {boolean} True if user owns the resource
 */
async function checkResourceOwnership(userId, resourceId, resourceType) {
  try {
    const db = database.get();
    
    switch (resourceType) {
      case 'resume':
        const resume = await db.get('SELECT user_id FROM resumes WHERE id = ?', [resourceId]);
        return resume && resume.user_id === userId;
        
      case 'job':
        const job = await db.get('SELECT user_id FROM jobs WHERE id = ?', [resourceId]);
        return job && job.user_id === userId;
        
      case 'application':
        const application = await db.get('SELECT user_id FROM job_applications WHERE id = ?', [resourceId]);
        return application && application.user_id === userId;
        
      default:
        logger.warn('RBAC: Unknown resource type for ownership check', { resourceType });
        return false;
    }
  } catch (error) {
    logger.error('RBAC: Ownership check database error', {
      error: error.message,
      userId,
      resourceId,
      resourceType
    });
    return false;
  }
}

/**
 * Log security events to audit log
 * @param {string} userId - User ID
 * @param {string} action - Security action
 * @param {Object} metadata - Additional metadata
 * @param {Object} req - Express request object
 */
async function logSecurityEvent(userId, action, metadata, req) {
  try {
    const db = database.get();
    
    await db.run(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        metadata, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId,
      action,
      'security',
      null,
      JSON.stringify(metadata),
      req.ip,
      req.get('User-Agent'),
      new Date().toISOString()
    ]);
  } catch (error) {
    logger.error('RBAC: Failed to log security event', {
      error: error.message,
      userId,
      action
    });
  }
}

/**
 * Get user's effective permissions
 * @param {Object} user - User object
 * @returns {Array} Array of permissions
 */
function getUserPermissions(user) {
  const role = user.user_role || user.role;
  return getRolePermissions(role);
}

module.exports = {
  requireRole,
  requirePermission,
  requireOwnershipOrAdmin,
  getUserPermissions,
  getRolePermissions,
  ROLE_HIERARCHY,
  ROLE_PERMISSIONS
};
