/**
 * Request ID middleware for request correlation and tracing
 * Generates unique request IDs for better debugging and logging
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Generate unique request ID for request correlation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object  
 * @param {Function} next - Express next function
 */
const generateRequestId = (req, res, next) => {
  // Check if request ID is already provided in header
  const existingRequestId = req.get('X-Request-ID') || req.get('x-request-id');
  
  // Generate new ID if not provided
  const requestId = existingRequestId || `req_${Date.now()}_${uuidv4().substring(0, 8)}`;
  
  // Attach to request object for use in logging and error handling
  req.id = requestId;
  req.requestId = requestId;
  
  // Add to response headers for client tracking
  res.set('X-Request-ID', requestId);
  
  // Store start time for performance tracking
  req.startTime = process.hrtime.bigint();
  
  next();
};

/**
 * Alternative request ID middleware 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestIdMiddleware = (req, res, next) => {
  // Generate unique request ID if not already present
  req.id = req.headers['x-request-id'] || uuidv4();
  
  // Add request ID to response headers for client tracking
  res.setHeader('X-Request-ID', req.id);
  
  next();
};

/**
 * Get request duration in milliseconds
 * @param {Object} req - Express request object
 * @returns {number} Duration in milliseconds
 */
const getRequestDuration = (req) => {
  if (!req.startTime) return 0;
  const endTime = process.hrtime.bigint();
  return Number(endTime - req.startTime) / 1000000; // Convert nanoseconds to milliseconds
};

module.exports = {
  generateRequestId,
  requestIdMiddleware,
  getRequestDuration
};