const JobApplicationService = require('./jobApplicationService');
const EnhancedJobApplicationService = require('./enhancedJobApplicationService');
const SandboxService = require('./services/sandboxService');
const SecurityService = require('./services/securityService');
const DependencyService = require('./services/dependencyService');
const MonitoringService = require('./services/monitoringService');

/**
 * Enhanced Job Application Controller
 * Provides both legacy and enhanced job application automation capabilities with containerization
 */
class EnhancedJobApplicationController {
  constructor(notificationService = null) {
    this.legacyService = new JobApplicationService();
    this.enhancedService = new EnhancedJobApplicationService(notificationService);
    this.useEnhancedService = process.env.USE_ENHANCED_AUTOMATION === 'true';
    this.useContainerizedExecution = process.env.USE_CONTAINERIZED_EXECUTION === 'true';
    this.notificationService = notificationService;
    
    // Initialize enhanced security and dependency services
    this.securityService = new SecurityService();
    this.monitoringService = new MonitoringService();
    this.sandboxService = new SandboxService(this.securityService, this.monitoringService);
    this.dependencyService = new DependencyService();
    
    // Set up service dependencies
    this.dependencyService.setExecutionEngine(this.enhancedService);
    this.enhancedService.setSecurityService?.(this.securityService);
    this.enhancedService.setMonitoringService?.(this.monitoringService);
    
    // Start monitoring if enabled
    if (process.env.ENABLE_MONITORING === 'true') {
      this.monitoringService.startMonitoring();
    }
  }

  /**
   * Queue an automated job application with enhanced features
   */
  async queueApplication(req, res) {
    try {
      const {
        jobId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        coverLetter,
        customAnswers,
        priority,
        scheduledFor,
        useEnhanced = false // Allow per-request enhancement
      } = req.body;

      const userId = req.user.userId;

      if (!jobId || !resumeId || !jobUrl || !jobTitle || !company) {
        return res.status(400).json({
          error: 'Missing required fields: jobId, resumeId, jobUrl, jobTitle, company'
        });
      }

      const applicationData = {
        userId,
        jobId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        coverLetter,
        customAnswers,
        priority: priority || 'normal',
        scheduledFor: scheduledFor ? new Date(scheduledFor) : new Date()
      };

      // Choose service based on configuration or request preference
      const shouldUseEnhanced = this.useEnhancedService || useEnhanced;
      const service = shouldUseEnhanced ? this.enhancedService : this.legacyService;

      const queuedApplication = await service.queueApplication(applicationData);

      // Handle case where enhanced service skips low-quality jobs
      if (!queuedApplication && shouldUseEnhanced) {
        return res.status(200).json({
          message: 'Application skipped due to low compatibility score',
          skipped: true,
          reason: 'Job compatibility score below threshold'
        });
      }

      res.json({
        message: 'Application queued successfully',
        enhanced: shouldUseEnhanced,
        application: {
          id: queuedApplication.id,
          jobTitle: queuedApplication.jobTitle,
          company: queuedApplication.company,
          platform: queuedApplication.platform || 'unknown',
          priority: queuedApplication.priority,
          compatibilityScore: queuedApplication.compatibilityScore,
          scheduledFor: queuedApplication.scheduledFor,
          status: queuedApplication.status
        }
      });
    } catch (error) {
      console.error('Queue application error:', error);
      res.status(500).json({ 
        error: 'Failed to queue application',
        details: error.message 
      });
    }
  }

  /**
   * Queue multiple applications with enhanced bulk processing
   */
  async bulkQueueApplications(req, res) {
    try {
      const { applications, useEnhanced = false } = req.body;
      const userId = req.user.userId;

      if (!Array.isArray(applications) || applications.length === 0) {
        return res.status(400).json({
          error: 'Applications array is required and must not be empty'
        });
      }

      const shouldUseEnhanced = this.useEnhancedService || useEnhanced;
      const service = shouldUseEnhanced ? this.enhancedService : this.legacyService;

      const results = [];
      const errors = [];

      for (const appData of applications) {
        try {
          const applicationData = {
            userId,
            ...appData,
            priority: appData.priority || 'normal',
            scheduledFor: appData.scheduledFor ? new Date(appData.scheduledFor) : new Date()
          };

          const queuedApplication = await service.queueApplication(applicationData);
          
          if (queuedApplication) {
            results.push({
              success: true,
              application: {
                id: queuedApplication.id,
                jobTitle: queuedApplication.jobTitle,
                company: queuedApplication.company,
                platform: queuedApplication.platform || 'unknown',
                compatibilityScore: queuedApplication.compatibilityScore
              }
            });
          } else if (shouldUseEnhanced) {
            results.push({
              success: false,
              skipped: true,
              reason: 'Low compatibility score',
              jobTitle: appData.jobTitle,
              company: appData.company
            });
          }
        } catch (error) {
          errors.push({
            jobTitle: appData.jobTitle,
            company: appData.company,
            error: error.message
          });
        }
      }

      res.json({
        message: `Bulk queue processing completed`,
        enhanced: shouldUseEnhanced,
        results: {
          total: applications.length,
          queued: results.filter(r => r.success).length,
          skipped: results.filter(r => r.skipped).length,
          failed: errors.length,
          details: results,
          errors: errors
        }
      });
    } catch (error) {
      console.error('Bulk queue applications error:', error);
      res.status(500).json({ 
        error: 'Failed to process bulk applications',
        details: error.message 
      });
    }
  }

  /**
   * Get enhanced application statistics
   */
  async getApplicationStats(req, res) {
    try {
      const userId = req.user.userId;
      const { enhanced = false } = req.query;

      const shouldUseEnhanced = this.useEnhancedService || enhanced === 'true';
      
      if (shouldUseEnhanced) {
        const stats = await this.enhancedService.getEnhancedApplicationStats(userId);
        res.json({
          enhanced: true,
          stats: stats
        });
      } else {
        const stats = await this.legacyService.getApplicationStats(userId);
        res.json({
          enhanced: false,
          stats: stats
        });
      }
    } catch (error) {
      console.error('Get application stats error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve application statistics',
        details: error.message 
      });
    }
  }

  /**
   * Get enhanced queue status
   */
  async getQueueStatus(req, res) {
    try {
      const { enhanced = false } = req.query;
      const shouldUseEnhanced = this.useEnhancedService || enhanced === 'true';

      if (shouldUseEnhanced) {
        const status = this.enhancedService.getEnhancedQueueStatus();
        res.json({
          enhanced: true,
          status: status
        });
      } else {
        const status = this.legacyService.getQueueStatus();
        res.json({
          enhanced: false,
          status: status
        });
      }
    } catch (error) {
      console.error('Get queue status error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve queue status',
        details: error.message 
      });
    }
  }

  /**
   * Cancel a queued application
   */
  async cancelApplication(req, res) {
    try {
      const { applicationId } = req.params;
      const { enhanced = false } = req.query;
      
      const shouldUseEnhanced = this.useEnhancedService || enhanced === 'true';
      const service = shouldUseEnhanced ? this.enhancedService : this.legacyService;

      // Remove from queue
      service.removeFromQueue(applicationId);
      
      if (shouldUseEnhanced) {
        await service.savePersistentQueue();
      }

      res.json({
        message: 'Application cancelled successfully',
        enhanced: shouldUseEnhanced,
        applicationId: applicationId
      });
    } catch (error) {
      console.error('Cancel application error:', error);
      res.status(500).json({ 
        error: 'Failed to cancel application',
        details: error.message 
      });
    }
  }

  /**
   * Get optimal timing for job application with enhanced AI
   */
  async getOptimalTiming(req, res) {
    try {
      const { jobUrl, jobTitle, company, industry } = req.body;
      const { enhanced = false } = req.query;

      if (!jobUrl || !jobTitle || !company) {
        return res.status(400).json({
          error: 'Missing required fields: jobUrl, jobTitle, company'
        });
      }

      const shouldUseEnhanced = this.useEnhancedService || enhanced === 'true';

      if (shouldUseEnhanced) {
        const platform = this.enhancedService.detectPlatform(jobUrl);
        const timing = this.enhancedService.getOptimalApplicationTiming(
          { jobTitle, company, industry }, 
          platform
        );
        
        res.json({
          enhanced: true,
          platform: platform,
          timing: timing,
          recommendations: [
            'Apply during optimal hours for better visibility',
            `Platform-specific timing optimized for ${platform}`,
            'Consider competition levels and posting recency'
          ]
        });
      } else {
        const timing = this.legacyService.getOptimalApplicationTiming({ jobTitle, company, industry });
        res.json({
          enhanced: false,
          timing: timing
        });
      }
    } catch (error) {
      console.error('Get optimal timing error:', error);
      res.status(500).json({ 
        error: 'Failed to calculate optimal timing',
        details: error.message 
      });
    }
  }

  /**
   * Update automation settings
   */
  async updateAutomationSettings(req, res) {
    try {
      const { 
        maxConcurrentApplications,
        delayBetweenApplications,
        maxRetries,
        rateLimits,
        antiDetectionSettings,
        qualityFilters
      } = req.body;

      // Update enhanced service settings if available
      if (this.enhancedService && (this.useEnhancedService || req.body.enhanced)) {
        if (maxConcurrentApplications) {
          this.enhancedService.config.maxConcurrentWorkers = maxConcurrentApplications;
        }
        if (delayBetweenApplications) {
          this.enhancedService.config.delayBetweenApplications = delayBetweenApplications;
        }
        if (maxRetries) {
          this.enhancedService.config.maxRetries = maxRetries;
        }
        if (rateLimits) {
          Object.assign(this.enhancedService.config.rateLimits, rateLimits);
        }
        if (antiDetectionSettings) {
          Object.assign(this.enhancedService.config.antiDetection, antiDetectionSettings);
        }
        if (qualityFilters) {
          Object.assign(this.enhancedService.config.qualityFilters, qualityFilters);
        }
      }

      // Update legacy service settings
      if (maxConcurrentApplications) {
        this.legacyService.automationConfig.maxConcurrentApplications = maxConcurrentApplications;
      }
      if (delayBetweenApplications) {
        this.legacyService.automationConfig.delayBetweenApplications = delayBetweenApplications;
      }
      if (maxRetries) {
        this.legacyService.automationConfig.maxRetries = maxRetries;
      }

      res.json({
        message: 'Automation settings updated successfully',
        enhanced: this.useEnhancedService || req.body.enhanced,
        settings: {
          maxConcurrentApplications,
          delayBetweenApplications,
          maxRetries,
          rateLimits,
          antiDetectionSettings,
          qualityFilters
        }
      });
    } catch (error) {
      console.error('Update automation settings error:', error);
      res.status(500).json({ 
        error: 'Failed to update automation settings',
        details: error.message 
      });
    }
  }

  /**
   * Get automation insights and analytics
   */
  async getAutomationInsights(req, res) {
    try {
      const userId = req.user.userId;
      const { enhanced = false, timeframe = '7d' } = req.query;

      const shouldUseEnhanced = this.useEnhancedService || enhanced === 'true';

      if (shouldUseEnhanced) {
        const stats = await this.enhancedService.getEnhancedApplicationStats(userId);
        const queueStatus = this.enhancedService.getEnhancedQueueStatus();
        
        res.json({
          enhanced: true,
          insights: {
            performance: {
              successRate: stats.successRate,
              averageProcessingTime: stats.averageProcessingTime,
              platformPerformance: stats.platformBreakdown,
              qualityMetrics: stats.qualityMetrics
            },
            current: {
              queueLength: queueStatus.total,
              activeWorkers: queueStatus.activeWorkers,
              rateLimitStatus: queueStatus.rateLimitStatus
            },
            recommendations: this.generateInsightRecommendations(stats, queueStatus),
            trends: {
              last24Hours: stats.last24Hours,
              last7Days: stats.last7Days
            }
          }
        });
      } else {
        const stats = await this.legacyService.getApplicationStats(userId);
        const queueStatus = this.legacyService.getQueueStatus();
        
        res.json({
          enhanced: false,
          insights: {
            performance: {
              successRate: stats.successRate,
              averageTime: stats.averageTime
            },
            current: {
              queueLength: queueStatus.total,
              isProcessing: queueStatus.isProcessing
            }
          }
        });
      }
    } catch (error) {
      console.error('Get automation insights error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve automation insights',
        details: error.message 
      });
    }
  }

  /**
   * Test automation system
   */
  async testAutomation(req, res) {
    try {
      const { jobUrl, enhanced = false } = req.body;
      
      if (!jobUrl) {
        return res.status(400).json({
          error: 'Job URL is required for testing'
        });
      }

      const shouldUseEnhanced = this.useEnhancedService || enhanced;

      if (shouldUseEnhanced) {
        // Test enhanced automation capabilities
        const platform = this.enhancedService.detectPlatform(jobUrl);
        const browserStats = this.enhancedService.browserEngine.getStats();
        
        res.json({
          enhanced: true,
          test: {
            platform: platform,
            browserStatus: browserStats,
            capabilities: [
              'Real browser automation',
              'Anti-detection measures',
              'Platform-specific form handling',
              'CAPTCHA solving (if configured)',
              'Rate limiting',
              'Smart retry logic'
            ],
            recommendation: `Ready for ${platform} automation`
          }
        });
      } else {
        // Test legacy automation
        res.json({
          enhanced: false,
          test: {
            platform: 'simulation',
            capabilities: [
              'Simulation-based automation',
              'Basic retry logic',
              'Queue management'
            ],
            recommendation: 'Ready for simulation mode'
          }
        });
      }
    } catch (error) {
      console.error('Test automation error:', error);
      res.status(500).json({ 
        error: 'Failed to test automation system',
        details: error.message 
      });
    }
  }

  /**
   * Generate insight recommendations based on performance data
   */
  generateInsightRecommendations(stats, queueStatus) {
    const recommendations = [];

    // Success rate recommendations
    if (stats.successRate < 50) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        message: 'Success rate is below 50%. Consider improving job targeting or application quality.',
        action: 'review_targeting'
      });
    }

    // Platform performance recommendations
    const platformPerf = stats.platformBreakdown;
    Object.entries(platformPerf).forEach(([platform, data]) => {
      const platformSuccessRate = (data.successful / data.total) * 100;
      if (platformSuccessRate < 30 && data.total > 5) {
        recommendations.push({
          type: 'platform',
          priority: 'medium',
          message: `${platform} success rate is low (${platformSuccessRate.toFixed(1)}%). Consider platform-specific optimization.`,
          action: 'optimize_platform'
        });
      }
    });

    // Queue management recommendations
    if (queueStatus.total > 50) {
      recommendations.push({
        type: 'queue',
        priority: 'medium',
        message: 'Large queue detected. Consider increasing worker capacity or reviewing application criteria.',
        action: 'scale_workers'
      });
    }

    // Rate limiting recommendations
    const rateLimitedPlatforms = Object.entries(queueStatus.rateLimitStatus || {})
      .filter(([platform, status]) => status.current >= status.limit * 0.8);
    
    if (rateLimitedPlatforms.length > 0) {
      recommendations.push({
        type: 'rate_limit',
        priority: 'low',
        message: 'Approaching rate limits on some platforms. Applications will be automatically delayed.',
        action: 'monitor_rates'
      });
    }

    return recommendations;
  }

  /**
   * Get service health status
   */
  async getHealthStatus(req, res) {
    try {
      const legacyStatus = {
        queueLength: this.legacyService.applicationQueue.length,
        isProcessing: this.legacyService.isProcessing
      };

      const enhancedStatus = this.enhancedService ? {
        queueLength: this.enhancedService.applicationQueue.length,
        isProcessing: this.enhancedService.isProcessing,
        activeWorkers: this.enhancedService.activeWorkers,
        browserActive: this.enhancedService.browserEngine.getStats().browserActive
      } : null;

      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          legacy: legacyStatus,
          enhanced: enhancedStatus
        },
        configuration: {
          useEnhancedService: this.useEnhancedService,
          enhancedAvailable: !!this.enhancedService
        }
      });
    } catch (error) {
      console.error('Health status error:', error);
      res.status(500).json({ 
        status: 'unhealthy',
        error: error.message 
      });
    }
  }

  /**
   * Submit job for containerized execution with approval workflow
   */
  async submitContainerizedJob(req, res) {
    try {
      const userId = req.user.userId;
      const {
        jobId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        platform,
        coverLetter,
        customAnswers,
        priority,
        skillMatch,
        confidenceScore
      } = req.body;

      // Validate required fields
      if (!jobId || !resumeId || !jobUrl || !jobTitle || !company) {
        return res.status(400).json({
          error: 'Missing required fields: jobId, resumeId, jobUrl, jobTitle, company'
        });
      }

      // Check if containerized execution is enabled
      if (!this.useContainerizedExecution) {
        return res.status(400).json({
          error: 'Containerized execution is not enabled'
        });
      }

      const applicationData = {
        id: jobId,
        userId,
        resumeId,
        jobUrl,
        jobTitle,
        company,
        platform: platform || 'company_website',
        coverLetter,
        customAnswers,
        priority: priority || 'normal',
        skillMatch: skillMatch || 0.5,
        confidenceScore: confidenceScore || 0.7,
        submittedAt: new Date(),
        source: 'enhanced_controller'
      };

      const metadata = {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        submissionMethod: 'api',
        enhanced: true,
        containerized: true
      };

      // Submit through enhanced service
      const result = await this.enhancedService.submitJobForContainerizedExecution(
        applicationData,
        userId,
        metadata
      );

      res.json({
        success: true,
        message: result.message,
        jobId: result.jobId,
        approval: {
          id: result.approval.id,
          status: result.approval.status,
          requiresApproval: result.requiresApproval || false,
          reasons: result.approval.analysisResult?.reasons || [],
          recommendations: result.approval.analysisResult?.recommendations || []
        },
        execution: result.execution || null,
        containerized: true
      });

    } catch (error) {
      console.error('Containerized job submission error:', error);
      res.status(500).json({
        error: 'Failed to submit job for containerized execution',
        message: error.message
      });
    }
  }

  /**
   * Get containerized execution status
   */
  async getContainerizedStatus(req, res) {
    try {
      if (!this.useContainerizedExecution) {
        return res.status(400).json({
          error: 'Containerized execution is not enabled'
        });
      }

      const status = this.enhancedService.getEnhancedQueueStatusWithContainers();

      res.json({
        success: true,
        status,
        containerized: true
      });

    } catch (error) {
      console.error('Error getting containerized status:', error);
      res.status(500).json({
        error: 'Failed to get containerized status',
        message: error.message
      });
    }
  }

  /**
   * Enhanced health status including containerized services
   */
  async getEnhancedHealthStatus(req, res) {
    try {
      const baseHealth = await this.getHealthStatus(req, res);
      
      if (this.useContainerizedExecution) {
        const containerHealth = await this.enhancedService.containerService.healthCheck();
        const terminalStats = this.enhancedService.terminalService.getStats();
        const approvalStats = this.enhancedService.approvalService.getStats();
        const auditStats = this.enhancedService.auditService.getStats();

        // If this is called directly, send enhanced response
        if (!res.headersSent) {
          res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            services: {
              enhanced: true,
              containerized: true,
              containers: containerHealth,
              terminals: terminalStats,
              approvals: approvalStats,
              audit: auditStats
            },
            configuration: {
              useEnhancedService: this.useEnhancedService,
              useContainerizedExecution: this.useContainerizedExecution,
              enhancedAvailable: !!this.enhancedService
            }
          });
        }
      }

    } catch (error) {
      console.error('Enhanced health status error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          status: 'unhealthy',
          error: error.message
        });
      }
    }
  }

  // ==================== NEW ENHANCED SECURITY & DEPENDENCY API ENDPOINTS ====================

  /**
   * Execute workflow with dependency management
   */
  async executeWorkflow(req, res) {
    try {
      const { workflow } = req.body;
      const userId = req.user.userId;

      if (!workflow || !workflow.nodes || workflow.nodes.length === 0) {
        return res.status(400).json({
          error: 'Invalid workflow: must contain at least one node'
        });
      }

      // Reset dependency service for new workflow
      this.dependencyService.reset();

      // Add jobs to dependency service
      const jobIds = [];
      for (const node of workflow.nodes) {
        if (node.type === 'job') {
          const jobId = this.dependencyService.addJob(
            {
              id: node.id,
              userId,
              ...node.data,
              platform: node.data.platform || 'linkedin'
            },
            node.dependencies,
            node.data.conditions,
            node.data.priority || 5
          );
          jobIds.push(jobId);
        }
      }

      // Start processing
      this.dependencyService.startProcessing();

      res.json({
        success: true,
        workflowId: workflow.id || `workflow_${Date.now()}`,
        jobIds,
        status: 'started',
        message: `Started workflow with ${jobIds.length} jobs`
      });

    } catch (error) {
      console.error('Workflow execution error:', error);
      res.status(500).json({
        error: 'Failed to execute workflow',
        message: error.message
      });
    }
  }

  /**
   * Get dependency graph data
   */
  async getDependencyGraph(req, res) {
    try {
      const graphData = this.dependencyService.getDependencyGraphData();
      
      res.json({
        success: true,
        graph: graphData,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting dependency graph:', error);
      res.status(500).json({
        error: 'Failed to get dependency graph',
        message: error.message
      });
    }
  }

  /**
   * Get dependency service status
   */
  async getDependencyStatus(req, res) {
    try {
      const status = this.dependencyService.getStatus();
      const summary = this.dependencyService.getProcessingSummary();
      
      res.json({
        success: true,
        status,
        summary,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting dependency status:', error);
      res.status(500).json({
        error: 'Failed to get dependency status',
        message: error.message
      });
    }
  }

  /**
   * Start dependency processing
   */
  async startDependencyProcessing(req, res) {
    try {
      if (this.dependencyService.isProcessing) {
        return res.status(400).json({
          error: 'Processing already in progress'
        });
      }

      this.dependencyService.startProcessing();
      
      res.json({
        success: true,
        message: 'Dependency processing started'
      });

    } catch (error) {
      console.error('Error starting dependency processing:', error);
      res.status(500).json({
        error: 'Failed to start dependency processing',
        message: error.message
      });
    }
  }

  /**
   * Pause dependency processing
   */
  async pauseDependencyProcessing(req, res) {
    try {
      this.dependencyService.isProcessing = false;
      
      res.json({
        success: true,
        message: 'Dependency processing paused'
      });

    } catch (error) {
      console.error('Error pausing dependency processing:', error);
      res.status(500).json({
        error: 'Failed to pause dependency processing',
        message: error.message
      });
    }
  }

  /**
   * Stop dependency processing
   */
  async stopDependencyProcessing(req, res) {
    try {
      this.dependencyService.isProcessing = false;
      
      res.json({
        success: true,
        message: 'Dependency processing stopped'
      });

    } catch (error) {
      console.error('Error stopping dependency processing:', error);
      res.status(500).json({
        error: 'Failed to stop dependency processing',
        message: error.message
      });
    }
  }

  /**
   * Reset dependency service
   */
  async resetDependencyService(req, res) {
    try {
      this.dependencyService.reset();
      
      res.json({
        success: true,
        message: 'Dependency service reset'
      });

    } catch (error) {
      console.error('Error resetting dependency service:', error);
      res.status(500).json({
        error: 'Failed to reset dependency service',
        message: error.message
      });
    }
  }

  /**
   * Get monitoring dashboard data
   */
  async getMonitoringDashboard(req, res) {
    try {
      const dashboardData = this.monitoringService.getDashboardData();
      
      res.json({
        success: true,
        dashboard: dashboardData,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting monitoring dashboard:', error);
      res.status(500).json({
        error: 'Failed to get monitoring dashboard',
        message: error.message
      });
    }
  }

  /**
   * Get performance trends
   */
  async getPerformanceTrends(req, res) {
    try {
      const hours = parseInt(req.query.hours) || 24;
      const trends = this.monitoringService.getPerformanceTrends(hours);
      
      res.json({
        success: true,
        trends,
        timeRange: `${hours} hours`,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting performance trends:', error);
      res.status(500).json({
        error: 'Failed to get performance trends',
        message: error.message
      });
    }
  }

  /**
   * Acknowledge monitoring alert
   */
  async acknowledgeAlert(req, res) {
    try {
      const { alertId } = req.params;
      
      const acknowledged = this.monitoringService.acknowledgeAlert(alertId);
      
      if (acknowledged) {
        res.json({
          success: true,
          message: 'Alert acknowledged'
        });
      } else {
        res.status(404).json({
          error: 'Alert not found'
        });
      }

    } catch (error) {
      console.error('Error acknowledging alert:', error);
      res.status(500).json({
        error: 'Failed to acknowledge alert',
        message: error.message
      });
    }
  }

  /**
   * Get security service status
   */
  async getSecurityStatus(req, res) {
    try {
      const status = this.securityService.getStatus();
      
      res.json({
        success: true,
        security: status,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting security status:', error);
      res.status(500).json({
        error: 'Failed to get security status',
        message: error.message
      });
    }
  }

  /**
   * Get security events
   */
  async getSecurityEvents(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 100;
      const events = this.securityService.getSecurityEvents(limit);
      
      res.json({
        success: true,
        events,
        count: events.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting security events:', error);
      res.status(500).json({
        error: 'Failed to get security events',
        message: error.message
      });
    }
  }

  /**
   * Scan container image for vulnerabilities
   */
  async scanImage(req, res) {
    try {
      const { imageName } = req.body;
      
      if (!imageName) {
        return res.status(400).json({
          error: 'Image name is required'
        });
      }

      const scanResult = await this.securityService.scanContainerImage(imageName);
      
      res.json({
        success: true,
        scan: scanResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error scanning image:', error);
      res.status(500).json({
        error: 'Failed to scan image',
        message: error.message
      });
    }
  }

  /**
   * Get vulnerability scan results
   */
  async getScanResults(req, res) {
    try {
      const { scanId } = req.params;
      
      const scanResult = this.securityService.getScanResults(scanId);
      
      if (scanResult) {
        res.json({
          success: true,
          scan: scanResult,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(404).json({
          error: 'Scan not found'
        });
      }

    } catch (error) {
      console.error('Error getting scan results:', error);
      res.status(500).json({
        error: 'Failed to get scan results',
        message: error.message
      });
    }
  }

  /**
   * Get sandbox service status
   */
  async getSandboxStatus(req, res) {
    try {
      const status = this.sandboxService.getHealthStatus();
      const activeSandboxes = this.sandboxService.listActiveSandboxes();
      
      res.json({
        success: true,
        status,
        activeSandboxes,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting sandbox status:', error);
      res.status(500).json({
        error: 'Failed to get sandbox status',
        message: error.message
      });
    }
  }

  /**
   * Create secure sandbox
   */
  async createSecureSandbox(req, res) {
    try {
      const { jobData, securityProfile = 'strict', networkPolicy = 'none' } = req.body;
      const userId = req.user.userId;
      
      if (!jobData) {
        return res.status(400).json({
          error: 'Job data is required'
        });
      }

      const sandboxId = await this.sandboxService.createSecureSandbox(
        { ...jobData, userId },
        securityProfile,
        networkPolicy
      );
      
      res.json({
        success: true,
        sandboxId,
        message: 'Secure sandbox created',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error creating secure sandbox:', error);
      res.status(500).json({
        error: 'Failed to create secure sandbox',
        message: error.message
      });
    }
  }

  /**
   * Get sandbox information
   */
  async getSandboxInfo(req, res) {
    try {
      const { sandboxId } = req.params;
      
      const sandboxInfo = this.sandboxService.getSandboxInfo(sandboxId);
      
      if (sandboxInfo) {
        res.json({
          success: true,
          sandbox: sandboxInfo,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(404).json({
          error: 'Sandbox not found'
        });
      }

    } catch (error) {
      console.error('Error getting sandbox info:', error);
      res.status(500).json({
        error: 'Failed to get sandbox info',
        message: error.message
      });
    }
  }

  /**
   * Terminate sandbox
   */
  async terminateSandbox(req, res) {
    try {
      const { sandboxId } = req.params;
      
      await this.sandboxService.terminateSandbox(sandboxId);
      
      res.json({
        success: true,
        message: 'Sandbox terminated',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error terminating sandbox:', error);
      res.status(500).json({
        error: 'Failed to terminate sandbox',
        message: error.message
      });
    }
  }
}

module.exports = EnhancedJobApplicationController;