const fs = require('fs');
const path = require('path');

/**
 * Production Configuration Module
 * Handles environment-specific settings with security validation
 */

/**
 * Validate required environment variables
 */
function validateEnvironment() {
  const requiredVars = [
    'DATABASE_URL',
    'ENCRYPTION_MASTER_KEY',
    'JWT_SECRET',
    'ADMIN_EMAIL',
    'ADMIN_PASSWORD'
  ];

  const missing = requiredVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate password strength
  const adminPassword = process.env.ADMIN_PASSWORD;
  if (adminPassword.length < 12) {
    throw new Error('Admin password must be at least 12 characters long');
  }

  const hasUpper = /[A-Z]/.test(adminPassword);
  const hasLower = /[a-z]/.test(adminPassword);
  const hasNumber = /\d/.test(adminPassword);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(adminPassword);

  if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
    throw new Error('Admin password must contain uppercase, lowercase, number, and special character');
  }

  // Validate database URL format
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl.startsWith('postgresql://') && !dbUrl.startsWith('postgres://')) {
    throw new Error('Invalid PostgreSQL connection string format');
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(process.env.ADMIN_EMAIL)) {
    throw new Error('Invalid admin email format');
  }

  console.log('✅ Environment validation passed');
}

/**
 * Production configuration object
 */
const productionConfig = {
  // Application settings
  app: {
    name: 'CVLeap',
    version: process.env.npm_package_version || '1.0.0',
    environment: 'production',
    port: parseInt(process.env.PORT) || 3000,
    host: process.env.HOST || '0.0.0.0',
    url: process.env.APP_URL || 'https://app.yourdomain.com',
    apiUrl: process.env.API_URL || 'https://api.yourdomain.com'
  },

  // Database configuration
  database: {
    url: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: true,
      ca: process.env.DB_SSL_CA ? fs.readFileSync(process.env.DB_SSL_CA) : undefined,
      cert: process.env.DB_SSL_CERT ? fs.readFileSync(process.env.DB_SSL_CERT) : undefined,
      key: process.env.DB_SSL_KEY ? fs.readFileSync(process.env.DB_SSL_KEY) : undefined
    },
    pool: {
      min: parseInt(process.env.DB_POOL_MIN) || 5,
      max: parseInt(process.env.DB_POOL_MAX) || 25,
      idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 10000
    },
    options: {
      statement_timeout: 120000,
      query_timeout: 120000,
      application_name: 'CVLeap-Production'
    }
  },

  // Security configuration
  security: {
    encryption: {
      masterKey: process.env.ENCRYPTION_MASTER_KEY,
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
      iterations: 100000
    },
    jwt: {
      secret: process.env.JWT_SECRET,
      expiresIn: process.env.JWT_EXPIRES_IN || '8h',
      refreshSecret: process.env.REFRESH_TOKEN_SECRET,
      refreshExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d'
    },
    bcrypt: {
      rounds: parseInt(process.env.BCRYPT_ROUNDS) || 14
    },
    session: {
      timeout: parseInt(process.env.SESSION_TIMEOUT) || 28800000, // 8 hours
      maxFailedLogins: parseInt(process.env.MAX_FAILED_LOGINS) || 3,
      lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 3600000 // 1 hour
    },
    twoFactor: {
      enabled: process.env.ENABLE_TWO_FACTOR === 'true',
      issuer: 'CVLeap',
      window: 2
    },
    headers: {
      helmet: process.env.ENABLE_HELMET !== 'false',
      csp: process.env.ENABLE_CSP !== 'false',
      hsts: true,
      noSniff: true,
      xssFilter: true,
      frameguard: { action: 'deny' }
    }
  },

  // Rate limiting configuration
  rateLimit: {
    enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      error: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    }
  },

  // SSL/TLS configuration
  ssl: {
    enabled: process.env.SSL_ENABLED === 'true',
    cert: process.env.SSL_CERT_PATH,
    key: process.env.SSL_KEY_PATH,
    ca: process.env.SSL_CA_PATH,
    forceHttps: process.env.FORCE_HTTPS !== 'false'
  },

  // CORS configuration
  cors: {
    enabled: process.env.ENABLE_CORS !== 'false',
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['https://yourdomain.com'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    auditEnabled: process.env.ENABLE_AUDIT_LOGGING !== 'false',
    format: 'json',
    timestamp: true,
    colorize: false,
    maxFiles: 30,
    maxSize: '100m'
  },

  // Monitoring configuration
  monitoring: {
    sentry: {
      dsn: process.env.SENTRY_DSN,
      environment: 'production',
      tracesSampleRate: 0.1
    },
    newRelic: {
      licenseKey: process.env.NEW_RELIC_LICENSE_KEY,
      appName: 'CVLeap-Production'
    },
    datadog: {
      apiKey: process.env.DATADOG_API_KEY,
      service: 'cvleap',
      env: 'production'
    },
    healthCheck: {
      enabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
      path: process.env.HEALTH_CHECK_PATH || '/health'
    },
    metrics: {
      enabled: process.env.METRICS_ENABLED !== 'false',
      path: process.env.METRICS_PATH || '/metrics'
    }
  },

  // External API configuration
  apis: {
    indeed: {
      apiKey: process.env.INDEED_API_KEY,
      baseUrl: 'https://api.indeed.com/ads/apisearch',
      rateLimit: 1000
    },
    linkedin: {
      apiKey: process.env.LINKEDIN_API_KEY,
      baseUrl: 'https://api.linkedin.com/v2',
      rateLimit: 2000
    },
    glassdoor: {
      apiKey: process.env.GLASSDOOR_API_KEY,
      partnerId: process.env.GLASSDOOR_PARTNER_ID,
      baseUrl: 'https://api.glassdoor.com/api/api.htm',
      rateLimit: 1500
    },
    apollo: {
      apiKey: process.env.APOLLO_API_KEY,
      baseUrl: 'https://api.apollo.io/v1',
      rateLimit: 1000
    },
    hunter: {
      apiKey: process.env.HUNTER_API_KEY,
      baseUrl: 'https://api.hunter.io/v2',
      rateLimit: 1500
    },
    clearbit: {
      apiKey: process.env.CLEARBIT_API_KEY,
      baseUrl: 'https://person.clearbit.com/v2',
      rateLimit: 2000
    }
  },

  // Email configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    },
    from: process.env.EMAIL_FROM || 'CVLeap <<EMAIL>>',
    templates: {
      path: path.join(__dirname, '../templates/email')
    }
  },

  // Redis configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'cvleap:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },

  // File storage configuration
  storage: {
    type: process.env.STORAGE_TYPE || 's3',
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET
    },
    cdn: {
      url: process.env.CDN_URL,
      staticUrl: process.env.STATIC_FILES_URL
    }
  },

  // Performance configuration
  performance: {
    compression: process.env.ENABLE_COMPRESSION !== 'false',
    etag: process.env.ENABLE_ETAG !== 'false',
    cacheControl: {
      maxAge: parseInt(process.env.CACHE_CONTROL_MAX_AGE) || 86400
    }
  },

  // Backup configuration
  backup: {
    enabled: process.env.BACKUP_ENABLED === 'true',
    schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *', // Daily at 2 AM
    retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS) || 30,
    encryptionKey: process.env.BACKUP_ENCRYPTION_KEY,
    s3Bucket: process.env.BACKUP_S3_BUCKET
  },

  // Feature flags
  features: {
    jobDiscovery: process.env.ENABLE_JOB_DISCOVERY !== 'false',
    recruiterDiscovery: process.env.ENABLE_RECRUITER_DISCOVERY !== 'false',
    aiFeatures: process.env.ENABLE_AI_FEATURES !== 'false',
    analytics: process.env.ENABLE_ANALYTICS !== 'false',
    notifications: process.env.ENABLE_NOTIFICATIONS !== 'false'
  },

  // Admin configuration
  admin: {
    email: process.env.ADMIN_EMAIL,
    password: process.env.ADMIN_PASSWORD,
    name: process.env.ADMIN_NAME || 'System Administrator',
    firstName: process.env.ADMIN_FIRST_NAME || 'System',
    lastName: process.env.ADMIN_LAST_NAME || 'Administrator'
  }
};

/**
 * Initialize production configuration
 */
function initializeProductionConfig() {
  console.log('🔧 Initializing production configuration...');
  
  try {
    // Validate environment
    validateEnvironment();
    
    // Log configuration summary (without sensitive data)
    console.log('📋 Production Configuration Summary:');
    console.log(`   Environment: ${productionConfig.app.environment}`);
    console.log(`   Port: ${productionConfig.app.port}`);
    console.log(`   Database SSL: ${productionConfig.database.ssl.rejectUnauthorized ? 'Enabled' : 'Disabled'}`);
    console.log(`   Rate Limiting: ${productionConfig.rateLimit.enabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   HTTPS: ${productionConfig.ssl.forceHttps ? 'Enforced' : 'Optional'}`);
    console.log(`   Audit Logging: ${productionConfig.logging.auditEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   Two-Factor Auth: ${productionConfig.security.twoFactor.enabled ? 'Enabled' : 'Disabled'}`);
    
    console.log('✅ Production configuration initialized successfully');
    
    return productionConfig;
  } catch (error) {
    console.error('❌ Production configuration failed:', error.message);
    process.exit(1);
  }
}

module.exports = {
  productionConfig,
  initializeProductionConfig,
  validateEnvironment
};
