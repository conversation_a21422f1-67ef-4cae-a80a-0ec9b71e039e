const SandboxService = require('../services/sandboxService');
const SecurityService = require('../services/securityService');
const DependencyService = require('../services/dependencyService');
const MonitoringService = require('../services/monitoringService');

/**
 * Test Suite for Enhanced Security and Dependency Management
 */
class EnhancedSecurityTest {
  constructor() {
    this.testResults = [];
    this.services = {};
  }

  async runAllTests() {
    console.log('🧪 Running Enhanced Security and Dependency Management Tests...\n');

    try {
      // Initialize services
      await this.initializeServices();
      
      // Run test suites
      await this.testSecurityService();
      await this.testSandboxService();
      await this.testDependencyService();
      await this.testMonitoringService();
      await this.testServiceIntegration();
      
      // Report results
      this.reportResults();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return false;
    }
  }

  async initializeServices() {
    console.log('🔧 Initializing services...');
    
    this.services.security = new SecurityService();
    this.services.monitoring = new MonitoringService();
    this.services.sandbox = new SandboxService();
    this.services.dependency = new DependencyService();
    
    // Set up service dependencies
    this.services.sandbox.securityService = this.services.security;
    this.services.sandbox.monitoringService = this.services.monitoring;
    
    console.log('✅ Services initialized\n');
  }

  async testSecurityService() {
    console.log('🔒 Testing Security Service...');
    
    // Test 1: Security policy validation
    await this.runTest('Security Policy Validation', async () => {
      const config = {
        memory: '512m',
        cpus: '0.5',
        user: 'cvleap',
        network: 'none'
      };
      
      const validation = this.services.security.validateContainerConfig(config);
      
      if (!validation.valid) {
        throw new Error('Valid configuration rejected');
      }
      
      console.log('  ✓ Container configuration validation passed');
    });

    // Test 2: Security event logging
    await this.runTest('Security Event Logging', async () => {
      const event = this.services.security.logSecurityEvent('TEST_EVENT', {
        testData: 'security test',
        timestamp: new Date()
      });
      
      if (!event.id || !event.timestamp) {
        throw new Error('Security event not properly logged');
      }
      
      console.log('  ✓ Security event logging works');
    });

    // Test 3: Image security assessment (mock)
    await this.runTest('Image Security Assessment', async () => {
      const mockScanResult = {
        summary: { critical: 0, high: 2, medium: 5, low: 3, info: 1 }
      };
      
      const assessment = this.services.security.assessImageSecurity(mockScanResult);
      
      if (!assessment.approved) {
        throw new Error('Image should be approved with low risk vulnerabilities');
      }
      
      console.log('  ✓ Image security assessment works');
    });

    console.log('✅ Security Service tests completed\n');
  }

  async testSandboxService() {
    console.log('🏗️ Testing Sandbox Service...');
    
    // Test 1: Sandbox configuration generation
    await this.runTest('Sandbox Configuration Generation', async () => {
      const securityConfig = this.services.sandbox.generateSecurityConfig('strict');
      
      if (!securityConfig.readOnlyRootfs || !securityConfig.noNewPrivileges) {
        throw new Error('Security configuration not properly generated');
      }
      
      console.log('  ✓ Security configuration generation works');
    });

    // Test 2: Network configuration
    await this.runTest('Network Configuration', async () => {
      const networkConfig = this.services.sandbox.generateNetworkConfig('none');
      
      if (networkConfig.networkMode !== 'none') {
        throw new Error('Network configuration not properly set');
      }
      
      console.log('  ✓ Network configuration generation works');
    });

    // Test 3: Resource validation
    await this.runTest('Resource Validation', async () => {
      try {
        await this.services.sandbox.validateResourceAvailability();
        console.log('  ✓ Resource validation works');
      } catch (error) {
        if (error.message.includes('Maximum concurrent sandboxes')) {
          console.log('  ✓ Resource validation properly enforces limits');
        } else {
          throw error;
        }
      }
    });

    console.log('✅ Sandbox Service tests completed\n');
  }

  async testDependencyService() {
    console.log('🔗 Testing Dependency Service...');
    
    // Test 1: Job addition and dependency tracking
    await this.runTest('Job Dependency Management', async () => {
      const jobData1 = {
        id: 'job1',
        company: 'Test Company A',
        position: 'Developer',
        platform: 'linkedin'
      };
      
      const jobData2 = {
        id: 'job2',
        company: 'Test Company B',
        position: 'Engineer',
        platform: 'indeed'
      };
      
      const job1Id = this.services.dependency.addJob(jobData1, [], null, 5);
      const job2Id = this.services.dependency.addJob(jobData2, [job1Id], null, 3);
      
      if (!job1Id || !job2Id) {
        throw new Error('Jobs not properly added');
      }
      
      const job2Info = this.services.dependency.dependencyGraph.get(job2Id);
      if (!job2Info.dependencies.has(job1Id)) {
        throw new Error('Dependency relationship not established');
      }
      
      console.log('  ✓ Job dependency management works');
    });

    // Test 2: Conditional execution evaluation
    await this.runTest('Conditional Execution', async () => {
      const conditions = {
        type: 'success_rate',
        minimumSuccessRate: 0.8,
        windowSize: 5
      };
      
      // Add some mock completed jobs
      this.services.dependency.completedJobs.set('test1', { success: true, completedAt: new Date() });
      this.services.dependency.completedJobs.set('test2', { success: true, completedAt: new Date() });
      this.services.dependency.completedJobs.set('test3', { success: false, completedAt: new Date() });
      
      const shouldExecute = this.services.dependency.evaluateConditions(conditions);
      
      if (shouldExecute) {
        console.log('  ✓ Conditional execution evaluation works');
      } else {
        console.log('  ✓ Conditional execution properly blocks based on success rate');
      }
    });

    // Test 3: Dependency graph visualization
    await this.runTest('Dependency Graph Data', async () => {
      const graphData = this.services.dependency.getDependencyGraphData();
      
      if (!graphData.nodes || !graphData.edges) {
        throw new Error('Dependency graph data not properly formatted');
      }
      
      console.log('  ✓ Dependency graph data generation works');
    });

    console.log('✅ Dependency Service tests completed\n');
  }

  async testMonitoringService() {
    console.log('📊 Testing Monitoring Service...');
    
    // Test 1: Metric initialization and setting
    await this.runTest('Metric Management', async () => {
      this.services.monitoring.setMetric('test.metric', 42);
      const metric = this.services.monitoring.getMetric('test.metric');
      
      if (!metric || metric.value !== 42) {
        throw new Error('Metric not properly set or retrieved');
      }
      
      console.log('  ✓ Metric management works');
    });

    // Test 2: Alert threshold checking
    await this.runTest('Alert System', async () => {
      // Set high CPU usage to trigger alert
      this.services.monitoring.setMetric('system.cpu.usage', 85);
      
      // Check alerts (this would normally be done by the monitoring interval)
      this.services.monitoring.checkAlerts(Date.now());
      
      const activeAlerts = this.services.monitoring.alerts.filter(a => !a.acknowledged);
      if (activeAlerts.length === 0) {
        throw new Error('Alert not triggered for high CPU usage');
      }
      
      console.log('  ✓ Alert system works');
    });

    // Test 3: Performance trends
    await this.runTest('Performance Trends', async () => {
      // Add some metric history
      for (let i = 0; i < 10; i++) {
        this.services.monitoring.setMetric('test.trend.metric', Math.random() * 100);
        await new Promise(resolve => setTimeout(resolve, 10)); // Small delay
      }
      
      const trends = this.services.monitoring.getPerformanceTrends(1); // 1 hour
      
      if (!trends['test.trend.metric']) {
        throw new Error('Performance trends not properly calculated');
      }
      
      console.log('  ✓ Performance trends calculation works');
    });

    console.log('✅ Monitoring Service tests completed\n');
  }

  async testServiceIntegration() {
    console.log('🔄 Testing Service Integration...');
    
    // Test 1: Security service integration with sandbox
    await this.runTest('Security-Sandbox Integration', async () => {
      // Mock security validation
      const mockJobData = {
        id: 'integration-test-job',
        company: 'Integration Test Co',
        position: 'Test Position',
        platform: 'test'
      };
      
      // Check that sandbox service can access security service
      if (!this.services.sandbox.securityService) {
        throw new Error('Security service not properly integrated with sandbox');
      }
      
      console.log('  ✓ Security-Sandbox integration works');
    });

    // Test 2: Monitoring integration
    await this.runTest('Monitoring Integration', async () => {
      // Check that services can report metrics
      if (!this.services.sandbox.monitoringService) {
        throw new Error('Monitoring service not properly integrated');
      }
      
      // Test metric reporting
      const dashboardData = this.services.monitoring.getDashboardData();
      if (!dashboardData.timestamp || !dashboardData.system) {
        throw new Error('Dashboard data not properly generated');
      }
      
      console.log('  ✓ Monitoring integration works');
    });

    // Test 3: Cross-service event handling
    await this.runTest('Cross-Service Events', async () => {
      let eventReceived = false;
      
      // Set up event listener
      this.services.dependency.on('jobAdded', (data) => {
        eventReceived = true;
      });
      
      // Add a job to trigger event
      this.services.dependency.addJob({
        id: 'event-test',
        company: 'Event Test',
        position: 'Event Position',
        platform: 'test'
      });
      
      // Wait a bit for event processing
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (!eventReceived) {
        throw new Error('Cross-service event not properly handled');
      }
      
      console.log('  ✓ Cross-service event handling works');
    });

    console.log('✅ Service Integration tests completed\n');
  }

  async runTest(testName, testFunction) {
    try {
      await testFunction();
      this.testResults.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      console.error(`  ❌ ${testName} failed:`, error.message);
      this.testResults.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  reportResults() {
    console.log('📋 Test Results Summary:');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const total = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });
    
    console.log(`\n📊 Summary: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (failed > 0) {
      console.log(`❌ ${failed} tests failed`);
      return false;
    } else {
      console.log('✅ All tests passed!');
      return true;
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new EnhancedSecurityTest();
  testSuite.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = EnhancedSecurityTest;