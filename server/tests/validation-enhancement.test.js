const { validateRequest, schemas, sanitizeInput, validateFileUpload, createCustomRateLimit } = require('../middleware/validation');

/**
 * Test suite for enhanced validation middleware
 * Tests the new security enhancements and validation improvements
 */

let testsPassed = 0;
let testsFailed = 0;

function assert(condition, message) {
  if (condition) {
    console.log(`✅ ${message}`);
    testsPassed++;
  } else {
    console.log(`❌ ${message}`);
    testsFailed++;
  }
}

// Mock Express objects
function createMockReq(body = {}, query = {}, params = {}, file = null, files = null) {
  return { 
    body, 
    query, 
    params, 
    file, 
    files,
    ip: '127.0.0.1',
    connection: { remoteAddress: '127.0.0.1' }
  };
}

function createMockRes() {
  const res = {
    status: (code) => res,
    json: (data) => ({ status: code, data }),
    setHeader: () => res
  };
  return res;
}

function createMockNext() {
  return () => {};
}

console.log('🧪 Running Enhanced Validation Tests...\n');

// Test 1: Enhanced XSS Protection
console.log('Testing Enhanced XSS Protection:');
const xssMiddleware = sanitizeInput;
const xssReq = createMockReq({
  maliciousScript: '<script>alert("XSS")</script>',
  onclickHandler: 'onclick="alert(1)"',
  jsProtocol: 'javascript:alert(1)',
  vbsProtocol: 'vbscript:msgbox(1)',
  cssExpression: 'expression(alert(1))',
  htmlImport: '@import url(evil.css)',
  dangerousHtml: '<iframe src="evil.com"></iframe>',
  sqlInjection: "'; DROP TABLE users; --",
  orAttack: "1 OR 1=1",
  entityAttack: '<!ENTITY xxe SYSTEM "file:///etc/passwd">',
  nosqlAttack: '$where: function() { return true; }'
});

xssMiddleware(xssReq, createMockRes(), createMockNext());

assert(!xssReq.body.maliciousScript.includes('<script>'), 'Script tags should be removed');
assert(!xssReq.body.onclickHandler.includes('onclick'), 'Event handlers should be removed');
assert(!xssReq.body.jsProtocol.includes('javascript:'), 'JavaScript protocol should be removed');
assert(!xssReq.body.vbsProtocol.includes('vbscript:'), 'VBScript protocol should be removed');
assert(!xssReq.body.cssExpression.includes('expression'), 'CSS expressions should be removed');
assert(!xssReq.body.htmlImport.includes('@import'), 'CSS imports should be removed');
assert(!xssReq.body.dangerousHtml.includes('<iframe'), 'Dangerous HTML tags should be removed');
assert(!xssReq.body.sqlInjection.includes('DROP TABLE'), 'SQL injection attempts should be sanitized');
assert(!xssReq.body.entityAttack.includes('<!ENTITY'), 'XML entities should be removed');
assert(!xssReq.body.nosqlAttack.includes('$where'), 'NoSQL injection patterns should be removed');

// Test 2: Enhanced File Upload Validation - Single File
console.log('\nTesting Enhanced File Upload Validation (Single File):');
const singleFileMiddleware = validateFileUpload(['image/jpeg', 'image/png'], 1024 * 1024); // 1MB limit

// Test valid file
const validFileReq = createMockReq({}, {}, {}, {
  originalname: 'photo.jpg',
  mimetype: 'image/jpeg',
  size: 500000
});

let nextCalled = false;
singleFileMiddleware(validFileReq, createMockRes(), () => { nextCalled = true; });
assert(nextCalled, 'Valid single file should pass validation');

// Test dangerous double extension
const doubleExtReq = createMockReq({}, {}, {}, {
  originalname: 'photo.jpg.exe',
  mimetype: 'image/jpeg',
  size: 500000
});

let doubleExtBlocked = false;
const doubleExtRes = {
  status: (code) => ({ json: (data) => { doubleExtBlocked = code === 415; } })
};
singleFileMiddleware(doubleExtReq, doubleExtRes, createMockNext());
assert(doubleExtBlocked, 'Files with dangerous double extensions should be blocked');

// Test suspicious filename
const suspiciousNameReq = createMockReq({}, {}, {}, {
  originalname: '../../../etc/passwd',
  mimetype: 'image/jpeg',
  size: 500000
});

let suspiciousBlocked = false;
const suspiciousRes = {
  status: (code) => ({ json: (data) => { suspiciousBlocked = code === 415; } })
};
singleFileMiddleware(suspiciousNameReq, suspiciousRes, createMockNext());
assert(suspiciousBlocked, 'Files with suspicious names should be blocked');

// Test 3: Enhanced File Upload Validation - Multiple Files
console.log('\nTesting Enhanced File Upload Validation (Multiple Files):');
const multiFileReq = createMockReq({}, {}, {}, null, [
  { originalname: 'doc1.pdf', mimetype: 'application/pdf', size: 500000 },
  { originalname: 'doc2.pdf', mimetype: 'application/pdf', size: 500000 }
]);

const multiFileMiddleware = validateFileUpload(['application/pdf'], 1024 * 1024);
nextCalled = false;
multiFileMiddleware(multiFileReq, createMockRes(), () => { nextCalled = true; });
assert(nextCalled, 'Valid multiple files should pass validation');

// Test 4: Deep Object Sanitization
console.log('\nTesting Deep Object Sanitization:');
const deepReq = createMockReq({
  user: {
    profile: {
      bio: '<script>alert("nested XSS")</script>',
      preferences: {
        theme: 'javascript:alert(1)'
      }
    },
    tags: ['<script>evil</script>', 'normal tag']
  }
});

sanitizeInput(deepReq, createMockRes(), createMockNext());
assert(!deepReq.body.user.profile.bio.includes('<script>'), 'Nested XSS should be sanitized');
assert(!deepReq.body.user.profile.preferences.theme.includes('javascript:'), 'Deep nested JS should be sanitized');
assert(!deepReq.body.user.tags[0].includes('<script>'), 'Array elements should be sanitized');
assert(deepReq.body.user.tags[1] === 'normal tag', 'Safe content should remain unchanged');

// Test 5: New Validation Schemas
console.log('\nTesting New Validation Schemas:');

// Test company schema
const companySchema = schemas.company;
const validCompany = {
  name: 'Tech Corp',
  industry: 'Technology',
  size: 'medium',
  location: 'San Francisco, CA',
  website: 'https://techcorp.com'
};

const companyResult = companySchema.validate(validCompany);
assert(!companyResult.error, 'Valid company data should pass validation');

// Test interview prep schema
const interviewSchema = schemas.interviewPrep;
const validInterview = {
  jobRole: 'Software Engineer',
  company: 'Tech Corp',
  interviewType: 'technical',
  difficulty: 'intermediate',
  topics: ['algorithms', 'system design']
};

const interviewResult = interviewSchema.validate(validInterview);
assert(!interviewResult.error, 'Valid interview prep data should pass validation');

// Test feedback schema
const feedbackSchema = schemas.feedback;
const validFeedback = {
  type: 'bug',
  subject: 'Login issue',
  message: 'Unable to login with valid credentials',
  priority: 'high'
};

const feedbackResult = feedbackSchema.validate(validFeedback);
assert(!feedbackResult.error, 'Valid feedback data should pass validation');

// Test 6: Rate Limiting
console.log('\nTesting Custom Rate Limiting:');
const rateLimiter = createCustomRateLimit(1000, 2); // 2 requests per second

let rateLimitResponse = null;
const rateLimitRes = {
  status: (code) => ({ json: (data) => { rateLimitResponse = { code, data }; } }),
  setHeader: () => {}
};

// First request should pass
rateLimiter(createMockReq(), rateLimitRes, () => { rateLimitResponse = { code: 200 }; });
assert(rateLimitResponse.code === 200, 'First request should pass rate limit');

// Second request should pass
rateLimiter(createMockReq(), rateLimitRes, () => { rateLimitResponse = { code: 200 }; });
assert(rateLimitResponse.code === 200, 'Second request should pass rate limit');

// Third request should be blocked
rateLimiter(createMockReq(), rateLimitRes, () => { rateLimitResponse = { code: 200 }; });
assert(rateLimitResponse.code === 429, 'Third request should be rate limited');

// Test 7: Request Validation with Enhanced Error Details
console.log('\nTesting Enhanced Request Validation:');
const registerValidator = validateRequest(schemas.register);

// Test with invalid data
const invalidReq = createMockReq({
  email: 'invalid-email',
  password: 'weak',
  name: ''
});

let validationResponse = null;
const validationRes = {
  status: (code) => ({ json: (data) => { validationResponse = { code, data }; } })
};

registerValidator(invalidReq, validationRes, createMockNext());
assert(validationResponse.code === 400, 'Invalid data should return 400 status');
assert(validationResponse.data.details && validationResponse.data.details.length > 0, 'Validation should provide detailed error information');
assert(validationResponse.data.details.some(d => d.field === 'email'), 'Should include email field error');
assert(validationResponse.data.details.some(d => d.field === 'password'), 'Should include password field error');

console.log('\n📊 Enhanced Validation Test Results:');
console.log(`Total: ${testsPassed + testsFailed}, Passed: ${testsPassed}, Failed: ${testsFailed}`);
console.log(`Success Rate: ${((testsPassed / (testsPassed + testsFailed)) * 100).toFixed(1)}%`);

if (testsFailed > 0) {
  process.exit(1);
}