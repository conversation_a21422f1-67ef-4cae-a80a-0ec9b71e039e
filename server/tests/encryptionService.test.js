const encryptionService = require('../utils/encryptionService');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

describe('Enhanced Encryption Service', () => {
  beforeEach(() => {
    // Clear audit log before each test
    encryptionService.clearAuditLog();
  });

  describe('Configuration Validation', () => {
    test('should validate encryption configuration', () => {
      const validation = encryptionService.validateConfiguration();
      
      if (process.env.NODE_ENV === 'production') {
        expect(validation.valid).toBe(true);
        expect(validation.issues).toHaveLength(0);
      } else {
        // In development, some warnings are acceptable
        expect(validation).toHaveProperty('valid');
        expect(validation).toHaveProperty('issues');
      }
    });

    test('should have proper algorithm and key settings', () => {
      expect(encryptionService.algorithm).toBe('aes-256-gcm');
      expect(encryptionService.keyLength).toBe(32);
      expect(encryptionService.ivLength).toBe(12);
      expect(encryptionService.tagLength).toBe(16);
      expect(encryptionService.iterations).toBeGreaterThanOrEqual(100000);
    });
  });

  describe('Key Derivation', () => {
    test('should derive keys with proper salt', () => {
      const purpose = 'test-purpose';
      const result = encryptionService.deriveKey(purpose);
      
      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('salt');
      expect(result.key).toBeInstanceOf(Buffer);
      expect(result.salt).toBeInstanceOf(Buffer);
      expect(result.key.length).toBe(32);
      expect(result.salt.length).toBe(32);
    });

    test('should derive different keys for different purposes', () => {
      const key1 = encryptionService.deriveKey('purpose1');
      const key2 = encryptionService.deriveKey('purpose2');
      
      expect(key1.key.equals(key2.key)).toBe(false);
      expect(key1.salt.equals(key2.salt)).toBe(false);
    });

    test('should derive same key with same purpose and salt', () => {
      const purpose = 'test-purpose';
      const firstDerivation = encryptionService.deriveKey(purpose);
      const secondDerivation = encryptionService.deriveKey(purpose, firstDerivation.salt);
      
      expect(firstDerivation.key.equals(secondDerivation.key)).toBe(true);
    });

    test('should support scrypt key derivation', () => {
      const purpose = 'scrypt-test';
      const result = encryptionService.deriveKeyScrypt(purpose);
      
      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('salt');
      expect(result.key.length).toBe(32);
    });
  });

  describe('Encryption and Decryption', () => {
    test('should encrypt and decrypt data successfully', () => {
      const testData = {
        string: 'Hello, World!',
        number: 12345,
        boolean: true,
        object: { nested: 'value' },
        array: [1, 2, 3],
        null: null
      };

      const encrypted = encryptionService.encrypt(testData, 'test');
      const decrypted = encryptionService.decrypt(encrypted, 'test');

      expect(decrypted).toEqual(testData);
    });

    test('should include proper metadata in encrypted data', () => {
      const testData = 'test string';
      const encrypted = encryptionService.encrypt(testData, 'test');

      expect(encrypted).toHaveProperty('version');
      expect(encrypted).toHaveProperty('encrypted');
      expect(encrypted).toHaveProperty('iv');
      expect(encrypted).toHaveProperty('tag');
      expect(encrypted).toHaveProperty('salt');
      expect(encrypted).toHaveProperty('purpose');
      expect(encrypted).toHaveProperty('algorithm');
      expect(encrypted).toHaveProperty('timestamp');
      
      expect(encrypted.version).toBe('2.0');
      expect(encrypted.algorithm).toBe('aes-256-gcm');
      expect(encrypted.purpose).toBe('test');
    });

    test('should support additional authenticated data (AAD)', () => {
      const testData = 'sensitive data';
      const aad = 'additional-auth-data';
      
      const encrypted = encryptionService.encrypt(testData, 'test', { aad });
      const decrypted = encryptionService.decrypt(encrypted, 'test');

      expect(decrypted).toBe(testData);
      expect(encrypted.aad).toBe(aad);
    });

    test('should fail decryption with wrong purpose', () => {
      const testData = 'test data';
      const encrypted = encryptionService.encrypt(testData, 'purpose1');

      expect(() => {
        encryptionService.decrypt(encrypted, 'purpose2');
      }).toThrow();
    });

    test('should fail decryption with tampered data', () => {
      const testData = 'test data';
      const encrypted = encryptionService.encrypt(testData, 'test');
      
      // Tamper with encrypted data
      encrypted.encrypted = encrypted.encrypted.slice(0, -2) + 'ff';

      expect(() => {
        encryptionService.decrypt(encrypted, 'test');
      }).toThrow();
    });

    test('should fail decryption with tampered authentication tag', () => {
      const testData = 'test data';
      const encrypted = encryptionService.encrypt(testData, 'test');
      
      // Tamper with authentication tag
      encrypted.tag = encrypted.tag.slice(0, -2) + 'ff';

      expect(() => {
        encryptionService.decrypt(encrypted, 'test');
      }).toThrow();
    });

    test('should handle undefined data gracefully', () => {
      expect(() => {
        encryptionService.encrypt(undefined, 'test');
      }).toThrow('Data to encrypt cannot be undefined');
    });
  });

  describe('Legacy Data Support', () => {
    test('should handle legacy data migration', () => {
      // Create mock legacy data
      const legacyData = {
        version: '1.0',
        encrypted: 'mock-encrypted-data',
        iv: crypto.randomBytes(12).toString('hex'),
        tag: crypto.randomBytes(16).toString('hex'),
        purpose: 'test'
      };

      // This should trigger legacy handling
      expect(() => {
        encryptionService.decrypt(legacyData, 'test');
      }).toThrow(); // Expected since it's mock data
    });

    test('should migrate legacy data to new format', () => {
      // This test would require actual legacy data
      // For now, test the migration function exists
      expect(typeof encryptionService.migrateLegacyData).toBe('function');
    });
  });

  describe('Hashing', () => {
    test('should hash data securely', async () => {
      const data = 'password123';
      const hash = await encryptionService.hash(data);

      expect(hash).toBeDefined();
      expect(hash).not.toBe(data);
      expect(hash.length).toBeGreaterThan(50); // bcrypt hashes are long
    });

    test('should verify hashed data correctly', async () => {
      const data = 'password123';
      const hash = await encryptionService.hash(data);
      
      const isValid = await encryptionService.verifyHash(data, hash);
      const isInvalid = await encryptionService.verifyHash('wrongpassword', hash);

      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });

    test('should validate hash rounds parameter', async () => {
      const data = 'test';
      
      await expect(encryptionService.hash(data, 5)).rejects.toThrow();
      await expect(encryptionService.hash(data, 20)).rejects.toThrow();
    });
  });

  describe('Token Generation', () => {
    test('should generate secure tokens', () => {
      const token = encryptionService.generateToken();
      
      expect(token).toBeDefined();
      expect(token.length).toBe(64); // 32 bytes = 64 hex chars
      expect(/^[a-f0-9]+$/.test(token)).toBe(true);
    });

    test('should generate tokens of specified length', () => {
      const token16 = encryptionService.generateToken(16);
      const token64 = encryptionService.generateToken(64);
      
      expect(token16.length).toBe(32); // 16 bytes = 32 hex chars
      expect(token64.length).toBe(128); // 64 bytes = 128 hex chars
    });

    test('should validate token length parameters', () => {
      expect(() => encryptionService.generateToken(8)).toThrow();
      expect(() => encryptionService.generateToken(256)).toThrow();
    });

    test('should generate secure strings with custom charset', () => {
      const charset = 'ABCDEF0123456789';
      const secureString = encryptionService.generateSecureString(16, charset);
      
      expect(secureString.length).toBe(16);
      expect(/^[ABCDEF0123456789]+$/.test(secureString)).toBe(true);
    });

    test('should generate API keys with proper format', () => {
      const apiKey = encryptionService.generateApiKey('test');
      
      expect(apiKey).toMatch(/^test_[a-z0-9]+_[A-Za-z0-9_-]+$/);
    });
  });

  describe('File Operations', () => {
    const testDir = path.join(__dirname, 'temp');
    const testFile = path.join(testDir, 'test.txt');
    const encryptedFile = path.join(testDir, 'test.txt.encrypted');
    const decryptedFile = path.join(testDir, 'test-decrypted.txt');

    beforeAll(async () => {
      await fs.mkdir(testDir, { recursive: true });
    });

    afterAll(async () => {
      try {
        await fs.rmdir(testDir, { recursive: true });
      } catch (error) {
        // Ignore cleanup errors
      }
    });

    test('should encrypt and decrypt files', async () => {
      const testContent = 'This is test file content for encryption testing.';
      
      // Create test file
      await fs.writeFile(testFile, testContent);
      
      // Encrypt file
      const encryptResult = await encryptionService.encryptFile(testFile, encryptedFile);
      expect(encryptResult.success).toBe(true);
      expect(encryptResult.inputSize).toBe(testContent.length);
      
      // Decrypt file
      const decryptResult = await encryptionService.decryptFile(encryptedFile, decryptedFile);
      expect(decryptResult.success).toBe(true);
      
      // Verify content
      const decryptedContent = await fs.readFile(decryptedFile, 'utf8');
      expect(decryptedContent).toBe(testContent);
    });

    test('should handle large file size limits', async () => {
      const largeContent = 'x'.repeat(101 * 1024 * 1024); // 101MB
      await fs.writeFile(testFile, largeContent);
      
      await expect(
        encryptionService.encryptFile(testFile, encryptedFile)
      ).rejects.toThrow('File too large');
    });
  });

  describe('Audit Logging', () => {
    test('should log encryption operations', () => {
      const testData = 'test';
      encryptionService.encrypt(testData, 'audit-test');
      
      const auditLog = encryptionService.getAuditLog();
      const encryptionEntry = auditLog.find(entry => entry.action === 'encryption_success');
      
      expect(encryptionEntry).toBeDefined();
      expect(encryptionEntry.metadata.purpose).toBe('audit-test');
    });

    test('should log decryption operations', () => {
      const testData = 'test';
      const encrypted = encryptionService.encrypt(testData, 'audit-test');
      encryptionService.decrypt(encrypted, 'audit-test');
      
      const auditLog = encryptionService.getAuditLog();
      const decryptionEntry = auditLog.find(entry => entry.action === 'decryption_success');
      
      expect(decryptionEntry).toBeDefined();
    });

    test('should provide audit statistics', () => {
      const stats = encryptionService.getStatistics();
      
      expect(stats).toHaveProperty('version');
      expect(stats).toHaveProperty('algorithm');
      expect(stats).toHaveProperty('auditEntries');
      expect(stats).toHaveProperty('auditCounts');
      expect(stats.version).toBe('2.0');
    });
  });

  describe('Security Tests', () => {
    test('should use different IVs for each encryption', () => {
      const testData = 'same data';
      const encrypted1 = encryptionService.encrypt(testData, 'test');
      const encrypted2 = encryptionService.encrypt(testData, 'test');
      
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      expect(encrypted1.encrypted).not.toBe(encrypted2.encrypted);
    });

    test('should use different salts for each encryption', () => {
      const testData = 'same data';
      const encrypted1 = encryptionService.encrypt(testData, 'test');
      const encrypted2 = encryptionService.encrypt(testData, 'test');
      
      expect(encrypted1.salt).not.toBe(encrypted2.salt);
    });

    test('should perform comprehensive encryption test', () => {
      const testResult = encryptionService.testEncryption();
      
      expect(testResult.success).toBe(true);
      expect(testResult.version).toBe('2.0');
      expect(testResult.tests.basic.success).toBe(true);
      expect(testResult.tests.withAAD.success).toBe(true);
      expect(testResult.tests.basic.hasTag).toBe(true);
      expect(testResult.tests.basic.hasSalt).toBe(true);
    });
  });

  describe('Memory Management', () => {
    test('should provide secure cleanup functionality', () => {
      expect(() => {
        encryptionService.secureCleanup();
      }).not.toThrow();
    });

    test('should clear audit log', () => {
      encryptionService.encrypt('test', 'test');
      expect(encryptionService.getAuditLog().length).toBeGreaterThan(0);
      
      encryptionService.clearAuditLog();
      expect(encryptionService.getAuditLog().length).toBe(1); // Only the clear log entry
    });
  });
});
