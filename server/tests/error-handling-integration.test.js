/**
 * Integration test for error handling system
 * Tests the actual usage patterns specified in requirements
 */

const assert = require('assert');

// Test the usage pattern from requirements
const { AuthenticationError, asyncHandler } = require('../middleware/errorHandler');

// Test: Throw custom errors
function testCustomErrorThrow() {
  try {
    throw new AuthenticationError('Invalid credentials');
  } catch (error) {
    assert(error instanceof AuthenticationError);
    assert.strictEqual(error.message, 'Invalid credentials');
    assert.strictEqual(error.statusCode, 401);
    assert.strictEqual(error.errorCode, 'AUTHENTICATION_ERROR');
    console.log('✅ Custom error throw works correctly');
  }
}

// Test: Use async error wrapper
async function testAsyncWrapper() {
  let errorCaught = null;

  const mockRouteHandler = asyncHandler(async (req, res) => {
    throw new AuthenticationError('Token expired');
  });

  await mockRouteHandler({}, {}, (error) => {
    errorCaught = error;
  });

  assert(errorCaught instanceof AuthenticationError);
  assert.strictEqual(errorCaught.message, 'Token expired');
  console.log('✅ Async error wrapper works correctly');
}

// Test: Verify all required exports are available
function testExports() {
  const errorHandler = require('../middleware/errorHandler');
  
  const requiredExports = [
    'AppError',
    'ValidationError', 
    'AuthenticationError',
    'AuthorizationError',
    'NotFoundError',
    'ConflictError',
    'RateLimitError',
    'ExternalServiceError',
    'errorHandler',
    'asyncHandler',
    'notFoundHandler',
    'logError',
    'setupGlobalErrorHandlers'
  ];

  for (const exportName of requiredExports) {
    assert(errorHandler[exportName], `Missing export: ${exportName}`);
  }
  
  console.log('✅ All required exports are available');
}

// Test: Request ID middleware integration
function testRequestIdIntegration() {
  const { generateRequestId } = require('../middleware/requestId');
  
  const req = { get: () => null };
  const res = { set: () => {} };
  let nextCalled = false;

  generateRequestId(req, res, () => { nextCalled = true; });

  assert(req.id);
  assert(req.requestId);
  assert(req.startTime);
  assert(nextCalled);
  
  console.log('✅ Request ID middleware integration works');
}

// Test: Error response structure matches requirements
function testResponseStructure() {
  const { errorHandler } = require('../middleware/errorHandler');
  const { ValidationError } = require('../middleware/errorHandler');

  const error = new ValidationError('Test validation error', [
    { field: 'email', message: 'Required' }
  ]);
  
  const req = {
    id: 'test-123',
    get: () => null,
    startTime: process.hrtime.bigint()
  };
  
  let responseData = null;
  let statusCode = null;

  const res = {
    status: (code) => {
      statusCode = code;
      return res;
    },
    json: (data) => {
      responseData = data;
    }
  };

  // Suppress console output
  const originalWarn = console.warn;
  console.warn = () => {};

  errorHandler(error, req, res, () => {});

  console.warn = originalWarn;

  // Verify response structure matches the requirements specification
  assert.strictEqual(responseData.success, false);
  assert.strictEqual(responseData.error.message, 'Test validation error');
  assert.strictEqual(responseData.error.code, 'VALIDATION_ERROR');
  assert.strictEqual(responseData.error.requestId, 'test-123');
  assert(responseData.error.timestamp);
  assert(Array.isArray(responseData.error.details));
  assert.strictEqual(statusCode, 400);

  console.log('✅ Error response structure matches requirements');
}

// Run integration tests
async function runIntegrationTests() {
  console.log('\n🔗 Running Error Handling Integration Tests...\n');

  try {
    testCustomErrorThrow();
    await testAsyncWrapper();
    testExports();
    testRequestIdIntegration();
    testResponseStructure();
    
    console.log('\n🎉 All integration tests passed!');
    console.log('✨ Enhanced error handling system is working correctly');
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

if (require.main === module) {
  runIntegrationTests();
}

module.exports = { runIntegrationTests };