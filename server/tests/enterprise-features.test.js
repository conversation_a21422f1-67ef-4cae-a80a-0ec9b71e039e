/**
 * Test suite for enterprise-grade production features
 */

// Test helper function
function assert(condition, message) {
  if (!condition) {
    throw new Error(`❌ ${message}`);
  }
  console.log(`✅ ${message}`);
}

async function runTests() {
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  console.log('🧪 Running Enterprise Production Features Tests...\n');

  try {
    // Test Authentication Middleware
    console.log('Testing Enhanced Authentication...');
    const AuthMiddleware = require('../middleware/authMiddleware');
    const authMiddleware = new AuthMiddleware();
    
    // Test token generation
    const user = { id: 1, email: '<EMAIL>', permissions: ['read', 'write'] };
    const tokens = authMiddleware.generateTokens(user);
    
    totalTests++;
    assert(tokens.accessToken, 'Should generate access token');
    passedTests++;
    
    totalTests++;
    assert(tokens.refreshToken, 'Should generate refresh token');
    passedTests++;
    
    totalTests++;
    assert(tokens.sessionId, 'Should generate session ID');
    passedTests++;

    // Test MFA
    const mfaChallenge = await authMiddleware.initiateMFA(user.id);
    totalTests++;
    assert(mfaChallenge.mfaToken, 'Should generate MFA token');
    passedTests++;

    const mfaResult = await authMiddleware.verifyMFA(mfaChallenge.mfaToken, '123456');
    totalTests++;
    assert(mfaResult.success, 'Should verify MFA with correct code');
    passedTests++;

    console.log('✅ Authentication middleware tests passed\n');

  } catch (error) {
    console.error(`❌ Authentication test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Audit Logger
    console.log('Testing Audit Logging...');
    const auditLogger = require('../utils/auditLogger');
    
    // Test auth logging
    auditLogger.logAuth('LOGIN_SUCCESS', 'user123', { ip: '127.0.0.1' });
    auditLogger.logSecurity('SUSPICIOUS_ACTIVITY', 'user456', 'high', { attempts: 5 });
    
    totalTests++;
    assert(true, 'Should log audit events without errors');
    passedTests++;

    // Test compliance report generation
    const report = await auditLogger.generateComplianceReport(
      new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      new Date().toISOString()
    );
    
    totalTests++;
    assert(report.period, 'Should generate compliance report');
    passedTests++;

    console.log('✅ Audit logging tests passed\n');

  } catch (error) {
    console.error(`❌ Audit logging test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Encryption Service
    console.log('Testing Encryption Service...');
    const encryptionService = require('../utils/encryptionService');
    
    // Test encryption/decryption
    const testResult = encryptionService.testEncryption();
    
    totalTests++;
    assert(testResult.success, 'Should encrypt and decrypt data correctly');
    passedTests++;

    // Test token generation
    const token = encryptionService.generateToken();
    totalTests++;
    assert(token && token.length === 64, 'Should generate secure token');
    passedTests++;

    // Test API key generation
    const apiKey = encryptionService.generateApiKey();
    totalTests++;
    assert(apiKey.startsWith('cvleap_'), 'Should generate API key with prefix');
    passedTests++;

    console.log('✅ Encryption service tests passed\n');

  } catch (error) {
    console.error(`❌ Encryption service test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Metrics Collector
    console.log('Testing Metrics Collection...');
    const metricsCollector = require('../utils/metricsCollector');
    
    // Test counter increment
    metricsCollector.incrementCounter('test_counter', { service: 'test' });
    
    // Test gauge setting
    metricsCollector.setGauge('test_gauge', 42, { type: 'test' });
    
    // Test histogram recording
    metricsCollector.recordHistogram('test_histogram', 150, { endpoint: '/test' });
    
    // Test timer functionality
    const timerId = metricsCollector.startTimer('test_timer', { operation: 'test' });
    setTimeout(() => {
      const duration = metricsCollector.endTimer(timerId);
      totalTests++;
      assert(duration >= 0, 'Should measure timer duration');
      passedTests++;
    }, 10);

    // Test metrics export
    const jsonMetrics = metricsCollector.exportJSON();
    totalTests++;
    assert(jsonMetrics.timestamp, 'Should export metrics in JSON format');
    passedTests++;

    const prometheusMetrics = metricsCollector.exportPrometheus();
    totalTests++;
    assert(typeof prometheusMetrics === 'string', 'Should export metrics in Prometheus format');
    passedTests++;

    // Test summary
    const summary = metricsCollector.getSummary();
    totalTests++;
    assert(summary.uptime >= 0, 'Should provide metrics summary');
    passedTests++;

    console.log('✅ Metrics collector tests passed\n');

  } catch (error) {
    console.error(`❌ Metrics collector test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Configuration Manager
    console.log('Testing Configuration Management...');
    const configManager = require('../utils/configManager');
    
    // Test configuration access
    const appConfig = configManager.get('app');
    totalTests++;
    assert(appConfig && appConfig.name === 'cvleap', 'Should load application configuration');
    passedTests++;

    // Test feature flags
    const aiFeature = configManager.isFeatureEnabled('aiAssistant');
    totalTests++;
    assert(typeof aiFeature === 'boolean', 'Should check feature flags');
    passedTests++;

    // Test database config
    const dbConfig = configManager.getDatabaseConfig();
    totalTests++;
    assert(dbConfig && dbConfig.type, 'Should provide database configuration');
    passedTests++;

    // Test environment file generation
    const sampleEnv = configManager.generateSampleEnv();
    totalTests++;
    assert(sampleEnv.includes('NODE_ENV'), 'Should generate sample environment file');
    passedTests++;

    console.log('✅ Configuration management tests passed\n');

  } catch (error) {
    console.error(`❌ Configuration management test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Enhanced Health Controller
    console.log('Testing Enhanced Health Monitoring...');
    const HealthController = require('../healthController');
    const healthController = new HealthController();
    
    // Mock request/response objects
    const mockReq = { user: { userId: 'test-user' }, ip: '127.0.0.1', query: {} };
    const mockRes = {
      json: (data) => {
        totalTests++;
        assert(data.timestamp, 'Health check should return timestamp');
        passedTests++;
        return mockRes;
      },
      status: (code) => mockRes,
      setHeader: (name, value) => mockRes,
      send: (data) => {
        totalTests++;
        assert(typeof data === 'string', 'Prometheus metrics should be string');
        passedTests++;
        return mockRes;
      }
    };

    // Test basic health check
    await healthController.healthCheck(mockReq, mockRes);

    // Test metrics endpoint
    await healthController.metrics(mockReq, mockRes);

    // Test Prometheus format
    mockReq.query.format = 'prometheus';
    await healthController.metrics(mockReq, mockRes);

    console.log('✅ Enhanced health monitoring tests passed\n');

  } catch (error) {
    console.error(`❌ Health monitoring test failed: ${error.message}`);
    failedTests++;
  }

  try {
    // Test Security Middleware Integration
    console.log('Testing Security Middleware Integration...');
    const SecurityMiddleware = require('../securityMiddleware');
    const securityMiddleware = new SecurityMiddleware();
    
    // Test rate limit creation
    const generalRateLimit = securityMiddleware.createGeneralRateLimit();
    totalTests++;
    assert(typeof generalRateLimit === 'function', 'Should create rate limit middleware');
    passedTests++;

    const aiRateLimit = securityMiddleware.createAIRateLimit();
    totalTests++;
    assert(typeof aiRateLimit === 'function', 'Should create AI-specific rate limit');
    passedTests++;

    console.log('✅ Security middleware integration tests passed\n');

  } catch (error) {
    console.error(`❌ Security middleware integration test failed: ${error.message}`);
    failedTests++;
  }

  // Test business metrics recording
  try {
    console.log('Testing Business Metrics...');
    const metricsCollector = require('../utils/metricsCollector');
    
    metricsCollector.recordBusinessMetric('user_registration', 'user123', { plan: 'premium' });
    metricsCollector.recordBusinessMetric('resume_generation', 'user123', { template: 'modern' });
    metricsCollector.recordBusinessMetric('job_application', 'user123', { platform: 'linkedin' });

    totalTests++;
    assert(true, 'Should record business metrics without errors');
    passedTests++;

    console.log('✅ Business metrics tests passed\n');

  } catch (error) {
    console.error(`❌ Business metrics test failed: ${error.message}`);
    failedTests++;
  }

  // Final results
  console.log('📊 Test Results:');
  console.log(`Total: ${totalTests}, Passed: ${passedTests}, Failed: ${failedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (failedTests === 0) {
    console.log('\n🎉 All enterprise production features tests passed!');
    process.exit(0);
  } else {
    console.log(`\n⚠️  ${failedTests} test(s) failed`);
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };