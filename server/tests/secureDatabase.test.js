const { DatabaseSetup } = require('../database/setup');
const SecureDatabaseInit = require('../database/secureInit');
const encryptionService = require('../utils/encryptionService');
const bcrypt = require('bcrypt');

// Mock environment variables for testing
const originalEnv = process.env;

describe('Secure Database Initialization', () => {
  let dbSetup;
  let mockClient;

  beforeEach(() => {
    // Reset environment
    process.env = {
      ...originalEnv,
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://test:test@localhost:5432/cvleap_test',
      ENCRYPTION_MASTER_KEY: 'test_master_key_32_characters_long',
      ADMIN_EMAIL: '<EMAIL>',
      ADMIN_PASSWORD: 'SecureTestPassword123!',
      JWT_SECRET: 'test_jwt_secret'
    };

    // Mock database client
    mockClient = {
      query: jest.fn(),
      end: jest.fn()
    };

    dbSetup = new DatabaseSetup();
    dbSetup.client = mockClient;
    dbSetup.usePostgreSQL = true;
  });

  afterEach(() => {
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  describe('Environment Validation', () => {
    test('should validate required environment variables', async () => {
      const secureInit = new SecureDatabaseInit(mockClient);
      
      // Should pass with all required vars
      await expect(secureInit.validateEnvironment()).resolves.not.toThrow();
    });

    test('should fail with missing environment variables', async () => {
      delete process.env.ENCRYPTION_MASTER_KEY;
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.validateEnvironment()).rejects.toThrow('Missing required environment variables');
    });

    test('should validate password strength in production', async () => {
      process.env.NODE_ENV = 'production';
      process.env.ADMIN_PASSWORD = 'weak';
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.validateEnvironment()).rejects.toThrow('Password must be at least');
    });

    test('should validate encryption service', async () => {
      // Mock encryption service failure
      jest.spyOn(encryptionService, 'testEncryption').mockReturnValue({ success: false });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.validateEnvironment()).rejects.toThrow('Encryption service validation failed');
    });
  });

  describe('Database Schema Initialization', () => {
    test('should initialize database schema successfully', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.initializeSchema()).resolves.not.toThrow();
      expect(mockClient.query).toHaveBeenCalled();
    });

    test('should handle schema initialization errors', async () => {
      mockClient.query.mockRejectedValue(new Error('Schema error'));
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.initializeSchema()).rejects.toThrow('Schema initialization failed');
    });
  });

  describe('Admin User Creation', () => {
    test('should create secure admin user successfully', async () => {
      const mockUserId = 'test-user-id';
      
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: mockUserId }] }) // User creation
        .mockResolvedValueOnce({ rows: [] }) // Admin user creation
        .mockResolvedValueOnce({ rows: [] }); // API key creation
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      const result = await secureInit.createSecureAdminUser();
      
      expect(result.userId).toBe(mockUserId);
      expect(result.apiKey).toBeDefined();
      expect(mockClient.query).toHaveBeenCalledTimes(3);
    });

    test('should hash admin password securely', async () => {
      const mockUserId = 'test-user-id';
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: mockUserId }] })
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ rows: [] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      await secureInit.createSecureAdminUser();
      
      // Check that password was hashed
      const userCreationCall = mockClient.query.mock.calls[0];
      const hashedPassword = userCreationCall[1][1]; // Second parameter (password_hash)
      
      expect(hashedPassword).not.toBe(process.env.ADMIN_PASSWORD);
      expect(hashedPassword.startsWith('$2a$')).toBe(true); // bcrypt hash format
    });

    test('should generate secure API key', async () => {
      const secureInit = new SecureDatabaseInit(mockClient);
      mockClient.query.mockResolvedValue({ rows: [] });
      
      const apiKey = await secureInit.generateSecureApiKey('test-user-id', 'Test Key');
      
      expect(apiKey.key).toMatch(/^cvl_[a-f0-9]{64}$/);
      expect(apiKey.prefix).toBe('cvl_');
      expect(apiKey.expiresAt).toBeInstanceOf(Date);
    });
  });

  describe('Security Policies', () => {
    test('should set up security policies', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.setupSecurityPolicies()).resolves.not.toThrow();
      expect(mockClient.query).toHaveBeenCalled();
    });

    test('should handle security policy setup errors gracefully', async () => {
      mockClient.query.mockRejectedValue(new Error('Policy error'));
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      // Should not throw, just warn
      await expect(secureInit.setupSecurityPolicies()).resolves.not.toThrow();
    });
  });

  describe('Audit Logging', () => {
    test('should initialize audit logging', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.initializeAuditLogging()).resolves.not.toThrow();
      expect(mockClient.query).toHaveBeenCalled();
    });

    test('should log system initialization event', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      await secureInit.initializeAuditLogging();
      
      const auditCall = mockClient.query.mock.calls[0];
      expect(auditCall[0]).toContain('INSERT INTO audit_logs');
      expect(auditCall[1]).toContain('SYSTEM_INIT');
    });
  });

  describe('Security Verification', () => {
    test('should verify security setup successfully', async () => {
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: 'test-id', email: '<EMAIL>' }] }) // Admin check
        .mockResolvedValueOnce({ rows: [{ count: '1' }] }); // Audit check
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.verifySecuritySetup()).resolves.not.toThrow();
    });

    test('should fail verification if admin user not found', async () => {
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // No admin user
        .mockResolvedValueOnce({ rows: [{ count: '1' }] });
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.verifySecuritySetup()).rejects.toThrow('Admin user not found');
    });

    test('should fail verification if audit logging not working', async () => {
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: 'test-id' }] })
        .mockResolvedValueOnce({ rows: [{ count: '0' }] }); // No audit logs
      
      const secureInit = new SecureDatabaseInit(mockClient);
      
      await expect(secureInit.verifySecuritySetup()).rejects.toThrow('Audit logging not working');
    });
  });

  describe('Password Security', () => {
    test('should generate secure passwords', () => {
      const secureInit = new SecureDatabaseInit(mockClient);
      
      const password = secureInit.generateSecurePassword();
      
      expect(password).toHaveLength(16);
      expect(/[A-Z]/.test(password)).toBe(true);
      expect(/[a-z]/.test(password)).toBe(true);
      expect(/\d/.test(password)).toBe(true);
      expect(/[!@#$%^&*]/.test(password)).toBe(true);
    });

    test('should validate password strength correctly', () => {
      const secureInit = new SecureDatabaseInit(mockClient);
      
      // Valid password
      expect(() => {
        secureInit.validatePasswordStrength('SecurePassword123!');
      }).not.toThrow();
      
      // Too short
      expect(() => {
        secureInit.validatePasswordStrength('Short1!');
      }).toThrow('Password must be at least');
      
      // Missing uppercase
      expect(() => {
        secureInit.validatePasswordStrength('lowercase123!');
      }).toThrow('Password must contain');
      
      // Missing special character
      expect(() => {
        secureInit.validatePasswordStrength('NoSpecialChar123');
      }).toThrow('Password must contain');
    });
  });

  describe('Database Setup Integration', () => {
    test('should initialize PostgreSQL successfully', async () => {
      // Mock successful PostgreSQL connection
      const mockPool = {
        query: jest.fn().mockResolvedValue({ rows: [{ now: new Date() }] }),
        end: jest.fn()
      };
      
      // Mock Pool constructor
      jest.doMock('pg', () => ({
        Pool: jest.fn(() => mockPool)
      }));
      
      const dbSetup = new DatabaseSetup();
      await dbSetup.initializePostgreSQL();
      
      expect(dbSetup.usePostgreSQL).toBe(true);
    });

    test('should fallback to SQLite on PostgreSQL failure', async () => {
      // Mock failed PostgreSQL connection
      jest.doMock('pg', () => ({
        Pool: jest.fn(() => {
          throw new Error('Connection failed');
        })
      }));
      
      const dbSetup = new DatabaseSetup();
      await dbSetup.initDatabase();
      
      expect(dbSetup.usePostgreSQL).toBe(false);
    });

    test('should run migrations successfully', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });
      
      // Mock file system
      const fs = require('fs').promises;
      jest.spyOn(fs, 'readdir').mockResolvedValue(['001_initial_schema.sql']);
      jest.spyOn(fs, 'readFile').mockResolvedValue('CREATE TABLE test;');
      
      await dbSetup.runMigrations();
      
      expect(mockClient.query).toHaveBeenCalled();
    });

    test('should track applied migrations', async () => {
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Create migrations table
        .mockResolvedValueOnce({ rows: [{ version: '001_initial_schema' }] }); // Get applied migrations
      
      const appliedMigrations = await dbSetup.getAppliedMigrations();
      
      expect(appliedMigrations).toContain('001_initial_schema');
    });
  });

  describe('Health Monitoring', () => {
    test('should report healthy status for PostgreSQL', async () => {
      mockClient.query.mockResolvedValue({
        rows: [{
          database_name: 'cvleap_test',
          version: 'PostgreSQL 13.0',
          server_time: new Date()
        }]
      });
      
      const health = await dbSetup.getHealthStatus();
      
      expect(health.status).toBe('healthy');
      expect(health.database).toBe('PostgreSQL');
      expect(health.responseTime).toBeGreaterThan(0);
    });

    test('should report unhealthy status on database error', async () => {
      mockClient.query.mockRejectedValue(new Error('Database down'));
      
      const health = await dbSetup.getHealthStatus();
      
      expect(health.status).toBe('unhealthy');
      expect(health.error).toBe('Database down');
    });
  });
});
