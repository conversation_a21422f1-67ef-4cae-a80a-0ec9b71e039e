/**
 * Basic testing infrastructure for enhanced functions
 * Minimal test framework for validating core functionality
 */

// Set required environment variables for testing
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret-key';
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

const assert = require('assert');
const { stringUtils, arrayUtils, objectUtils, validationUtils, performanceUtils } = require('../utils/helpers');
const { config } = require('../utils/config');
const cacheService = require('../cacheService');

/**
 * Simple test runner with basic assertions
 */
class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  /**
   * Add a test case
   * @param {string} name - Test name
   * @param {Function} testFunction - Test function
   */
  test(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  /**
   * Run all tests
   * @returns {Promise<Object>} Test results
   */
  async run() {
    console.log(`\n🧪 Running ${this.tests.length} tests...\n`);

    for (const { name, testFunction } of this.tests) {
      try {
        const startTime = Date.now();
        await testFunction();
        const duration = Date.now() - startTime;
        
        this.passed++;
        this.results.push({ name, status: 'PASS', duration });
        console.log(`✅ ${name} (${duration}ms)`);
      } catch (error) {
        this.failed++;
        this.results.push({ name, status: 'FAIL', error: error.message });
        console.log(`❌ ${name} - ${error.message}`);
      }
    }

    const total = this.passed + this.failed;
    const successRate = total > 0 ? (this.passed / total * 100).toFixed(1) : 0;

    console.log(`\n📊 Test Results:`);
    console.log(`Total: ${total}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${successRate}%\n`);

    return {
      total,
      passed: this.passed,
      failed: this.failed,
      successRate: parseFloat(successRate),
      results: this.results
    };
  }
}

// Create test runner instance
const runner = new TestRunner();

// String utilities tests
runner.test('stringUtils.titleCase should capitalize words', () => {
  assert.strictEqual(stringUtils.titleCase('hello world'), 'Hello World');
  assert.strictEqual(stringUtils.titleCase(''), '');
  assert.strictEqual(stringUtils.titleCase(null), '');
});

runner.test('stringUtils.slugify should create URL-friendly strings', () => {
  assert.strictEqual(stringUtils.slugify('Hello World!'), 'hello-world');
  assert.strictEqual(stringUtils.slugify('Test & Example'), 'test-example');
  assert.strictEqual(stringUtils.slugify(''), '');
});

runner.test('stringUtils.truncate should limit string length', () => {
  assert.strictEqual(stringUtils.truncate('Hello World', 5), 'He...');
  assert.strictEqual(stringUtils.truncate('Hi', 10), 'Hi');
  assert.strictEqual(stringUtils.truncate('', 5), '');
});

runner.test('stringUtils.getInitials should extract initials', () => {
  assert.strictEqual(stringUtils.getInitials('John Doe'), 'JD');
  assert.strictEqual(stringUtils.getInitials('John Michael Doe'), 'JMD');
  assert.strictEqual(stringUtils.getInitials(''), '');
});

// Array utilities tests
runner.test('arrayUtils.removeDuplicates should remove duplicate values', () => {
  assert.deepStrictEqual(arrayUtils.removeDuplicates([1, 2, 2, 3]), [1, 2, 3]);
  assert.deepStrictEqual(arrayUtils.removeDuplicates([]), []);
  assert.deepStrictEqual(arrayUtils.removeDuplicates('not-array'), []);
});

runner.test('arrayUtils.chunk should split arrays into chunks', () => {
  assert.deepStrictEqual(arrayUtils.chunk([1, 2, 3, 4, 5], 2), [[1, 2], [3, 4], [5]]);
  assert.deepStrictEqual(arrayUtils.chunk([], 2), []);
  assert.deepStrictEqual(arrayUtils.chunk([1, 2], 0), []);
});

runner.test('arrayUtils.groupBy should group array items', () => {
  const items = [
    { type: 'fruit', name: 'apple' },
    { type: 'fruit', name: 'banana' },
    { type: 'vegetable', name: 'carrot' }
  ];
  const grouped = arrayUtils.groupBy(items, 'type');
  assert.strictEqual(grouped.fruit.length, 2);
  assert.strictEqual(grouped.vegetable.length, 1);
});

// Object utilities tests
runner.test('objectUtils.deepClone should create deep copies', () => {
  const original = { a: 1, b: { c: 2 } };
  const cloned = objectUtils.deepClone(original);
  cloned.b.c = 3;
  assert.strictEqual(original.b.c, 2); // Original should be unchanged
  assert.strictEqual(cloned.b.c, 3);
});

runner.test('objectUtils.pick should select specified keys', () => {
  const obj = { a: 1, b: 2, c: 3 };
  const picked = objectUtils.pick(obj, ['a', 'c']);
  assert.deepStrictEqual(picked, { a: 1, c: 3 });
});

runner.test('objectUtils.omit should exclude specified keys', () => {
  const obj = { a: 1, b: 2, c: 3 };
  const omitted = objectUtils.omit(obj, ['b']);
  assert.deepStrictEqual(omitted, { a: 1, c: 3 });
});

// Validation utilities tests
runner.test('validationUtils.isValidEmail should validate email addresses', () => {
  assert.strictEqual(validationUtils.isValidEmail('<EMAIL>'), true);
  assert.strictEqual(validationUtils.isValidEmail('invalid-email'), false);
  assert.strictEqual(validationUtils.isValidEmail(''), false);
});

runner.test('validationUtils.validatePassword should check password strength', () => {
  const weak = validationUtils.validatePassword('123');
  const strong = validationUtils.validatePassword('StrongPass123!');
  
  assert.strictEqual(weak.isValid, false);
  assert.strictEqual(strong.isValid, true);
  assert.strictEqual(strong.score, 5);
});

runner.test('validationUtils.isValidUrl should validate URLs', () => {
  assert.strictEqual(validationUtils.isValidUrl('https://example.com'), true);
  assert.strictEqual(validationUtils.isValidUrl('not-a-url'), false);
  assert.strictEqual(validationUtils.isValidUrl(''), false);
});

// Performance utilities tests
runner.test('performanceUtils.createTimer should measure execution time', () => {
  const timer = performanceUtils.createTimer('test-timer');
  // Simulate some work
  const result = timer();
  assert.strictEqual(typeof result.duration, 'number');
  assert.strictEqual(result.label, 'test-timer');
});

runner.test('performanceUtils.measureAsync should measure async functions', async () => {
  const asyncFunction = async () => {
    return new Promise(resolve => setTimeout(() => resolve('done'), 10));
  };
  
  const result = await performanceUtils.measureAsync(asyncFunction);
  assert.strictEqual(result.result, 'done');
  assert.strictEqual(result.error, null);
  assert.strictEqual(typeof result.timing.duration, 'number');
});

// Configuration tests
runner.test('config should have default values', () => {
  assert.strictEqual(config.get('server.port'), 3000);
  assert.strictEqual(config.get('cache.enabled'), true);
  assert.strictEqual(config.get('nonexistent.key', 'default'), 'default');
});

runner.test('config.isFeatureEnabled should check feature flags', () => {
  const analyticsEnabled = config.isFeatureEnabled('enableAnalytics');
  assert.strictEqual(typeof analyticsEnabled, 'boolean');
});

runner.test('config should validate environment detection', () => {
  assert.strictEqual(typeof config.isDevelopment(), 'boolean');
  assert.strictEqual(typeof config.isProduction(), 'boolean');
});

// Cache service tests
runner.test('cacheService should store and retrieve values', () => {
  const testKey = 'test-key';
  const testValue = { data: 'test-data' };
  
  cacheService.set(testKey, testValue, 5000); // 5 second TTL
  const retrieved = cacheService.get(testKey);
  
  assert.deepStrictEqual(retrieved, testValue);
  assert.strictEqual(cacheService.has(testKey), true);
  
  // Cleanup
  cacheService.delete(testKey);
});

runner.test('cacheService should handle expiration', () => {
  const testKey = 'expire-test';
  const testValue = 'expire-data';
  
  cacheService.set(testKey, testValue, 1); // 1ms TTL (will expire immediately)
  
  // Wait a bit for expiration
  setTimeout(() => {
    const retrieved = cacheService.get(testKey);
    assert.strictEqual(retrieved, null);
    assert.strictEqual(cacheService.has(testKey), false);
  }, 10);
});

runner.test('cacheService should provide statistics', () => {
  const stats = cacheService.getStats();
  
  assert.strictEqual(typeof stats.size, 'number');
  assert.strictEqual(typeof stats.hitCount, 'number');
  assert.strictEqual(typeof stats.missCount, 'number');
  assert.strictEqual(typeof stats.hitRate, 'string');
  assert.strictEqual(typeof stats.memoryUsage, 'object');
});

// Enhanced error handling tests
runner.test('enhanced validation should handle complex data', () => {
  const { validateRequest, schemas } = require('../middleware/validation');
  
  // Test that validation middleware is properly exported
  assert.strictEqual(typeof validateRequest, 'function');
  assert.strictEqual(typeof schemas, 'object');
  assert.strictEqual(typeof schemas.register, 'object');
});

// Export the test runner for external use
module.exports = { TestRunner, runner };

// Run tests if this file is executed directly
if (require.main === module) {
  runner.run().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}