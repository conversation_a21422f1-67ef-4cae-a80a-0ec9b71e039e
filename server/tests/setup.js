const database = require('../database');
const { DatabaseSetup } = require('../database/setup');
const SecureDatabaseInit = require('../database/secureInit');

/**
 * Test Setup and Teardown
 * Configures test environment and database
 */

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret_for_testing_only';
process.env.ENCRYPTION_MASTER_KEY = 'test_master_key_32_characters_long';
process.env.DB_PATH = ':memory:'; // Use in-memory SQLite for tests

let dbSetup;
let testDatabase;

/**
 * Global test setup
 */
beforeAll(async () => {
  console.log('🧪 Setting up test environment...');
  
  try {
    // Initialize test database
    dbSetup = new DatabaseSetup();
    await dbSetup.initDatabase();
    testDatabase = dbSetup.getClient();
    
    // Create test tables
    await createTestTables();
    
    // Initialize security features for testing
    await initializeTestSecurity();
    
    console.log('✅ Test environment setup complete');
  } catch (error) {
    console.error('❌ Test setup failed:', error);
    throw error;
  }
}, 30000); // 30 second timeout for setup

/**
 * Global test teardown
 */
afterAll(async () => {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    if (dbSetup) {
      await dbSetup.close();
    }
    console.log('✅ Test cleanup complete');
  } catch (error) {
    console.error('❌ Test cleanup failed:', error);
  }
});

/**
 * Create test-specific database tables
 */
async function createTestTables() {
  const db = database.get();
  
  // Enable foreign keys for SQLite
  await db.run('PRAGMA foreign_keys = ON');
  
  // Create users table
  await db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      name TEXT NOT NULL,
      first_name TEXT,
      last_name TEXT,
      profile_picture_url TEXT,
      subscription_tier TEXT DEFAULT 'free',
      user_role TEXT DEFAULT 'user',
      account_status TEXT DEFAULT 'active',
      is_active BOOLEAN DEFAULT true,
      email_verified BOOLEAN DEFAULT false,
      email_verification_token TEXT,
      email_verification_expires_at DATETIME,
      password_reset_token TEXT,
      password_reset_expires_at DATETIME,
      last_login_at DATETIME,
      last_login_ip TEXT,
      failed_login_attempts INTEGER DEFAULT 0,
      account_locked_until DATETIME,
      two_factor_enabled BOOLEAN DEFAULT false,
      two_factor_secret TEXT,
      backup_codes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create admin_users table
  await db.run(`
    CREATE TABLE IF NOT EXISTS admin_users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      admin_level INTEGER NOT NULL DEFAULT 1,
      permissions TEXT DEFAULT '{}',
      api_key_hash TEXT,
      api_key_expires_at DATETIME,
      last_admin_action_at DATETIME,
      created_by INTEGER REFERENCES users(id),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id)
    )
  `);

  // Create audit_logs table
  await db.run(`
    CREATE TABLE IF NOT EXISTS audit_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
      admin_user_id INTEGER REFERENCES admin_users(id) ON DELETE SET NULL,
      action TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_id TEXT,
      old_values TEXT,
      new_values TEXT,
      ip_address TEXT,
      user_agent TEXT,
      session_id TEXT,
      success BOOLEAN DEFAULT true,
      error_message TEXT,
      metadata TEXT DEFAULT '{}',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create resumes table
  await db.run(`
    CREATE TABLE IF NOT EXISTS resumes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      title TEXT NOT NULL,
      data TEXT NOT NULL,
      template_id TEXT DEFAULT 'default',
      is_active BOOLEAN DEFAULT true,
      version INTEGER DEFAULT 1,
      is_encrypted BOOLEAN DEFAULT false,
      encryption_key_id INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create jobs table
  await db.run(`
    CREATE TABLE IF NOT EXISTS jobs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      external_id TEXT,
      source_platform TEXT,
      title TEXT NOT NULL,
      company TEXT NOT NULL,
      location TEXT,
      description TEXT,
      url TEXT,
      salary_min INTEGER,
      salary_max INTEGER,
      currency TEXT DEFAULT 'USD',
      job_type TEXT,
      remote_type TEXT,
      status TEXT DEFAULT 'active',
      applied_at DATETIME,
      discovered_at DATETIME,
      relevance_score REAL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create job_applications table
  await db.run(`
    CREATE TABLE IF NOT EXISTS job_applications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      job_id INTEGER NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      resume_id INTEGER REFERENCES resumes(id),
      status TEXT DEFAULT 'pending',
      applied_at DATETIME,
      response_at DATETIME,
      interview_at DATETIME,
      notes TEXT,
      automated BOOLEAN DEFAULT false,
      application_data TEXT DEFAULT '{}',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create rate_limits table for testing
  await db.run(`
    CREATE TABLE IF NOT EXISTS rate_limits (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT NOT NULL UNIQUE,
      count INTEGER NOT NULL DEFAULT 0,
      reset_time INTEGER NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create security_violations table for testing
  await db.run(`
    CREATE TABLE IF NOT EXISTS security_violations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      violation_type TEXT NOT NULL,
      severity TEXT NOT NULL DEFAULT 'medium',
      ip_address TEXT,
      user_agent TEXT,
      user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
      request_path TEXT,
      request_method TEXT,
      request_data TEXT,
      violation_data TEXT,
      blocked BOOLEAN DEFAULT false,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  console.log('✅ Test tables created');
}

/**
 * Initialize security features for testing
 */
async function initializeTestSecurity() {
  try {
    // Test encryption service
    const encryptionService = require('../utils/encryptionService');
    const testResult = encryptionService.testEncryption();
    
    if (!testResult.success) {
      throw new Error('Encryption service test failed');
    }
    
    console.log('✅ Security features initialized for testing');
  } catch (error) {
    console.error('❌ Security initialization failed:', error);
    throw error;
  }
}

/**
 * Helper function to create test user
 */
async function createTestUser(userData = {}) {
  const bcrypt = require('bcrypt');
  const db = database.get();
  
  const defaultData = {
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    name: 'Test User',
    firstName: 'Test',
    lastName: 'User',
    role: 'user'
  };
  
  const user = { ...defaultData, ...userData };
  const passwordHash = await bcrypt.hash(user.password, 12);
  
  const result = await db.run(`
    INSERT INTO users (
      email, password_hash, name, first_name, last_name,
      user_role, account_status, email_verified, is_active
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [
    user.email, passwordHash, user.name, user.firstName, user.lastName,
    user.role, 'active', true, true
  ]);
  
  return {
    id: result.lastID,
    ...user,
    password_hash: passwordHash
  };
}

/**
 * Helper function to create test admin user
 */
async function createTestAdminUser(userData = {}) {
  const user = await createTestUser({
    ...userData,
    role: 'super_admin',
    email: userData.email || `admin${Date.now()}@example.com`
  });
  
  const db = database.get();
  
  // Create admin user entry
  await db.run(`
    INSERT INTO admin_users (
      user_id, admin_level, permissions, created_by
    ) VALUES (?, ?, ?, ?)
  `, [
    user.id,
    10,
    JSON.stringify({
      users: ['create', 'read', 'update', 'delete'],
      admin: ['create', 'read', 'update', 'delete'],
      system: ['configure', 'monitor', 'backup']
    }),
    user.id
  ]);
  
  return user;
}

/**
 * Helper function to generate test JWT token
 */
function generateTestToken(userId) {
  const jwt = require('jsonwebtoken');
  return jwt.sign({ userId }, process.env.JWT_SECRET);
}

/**
 * Helper function to clean up test data
 */
async function cleanupTestData() {
  const db = database.get();
  
  // Clean up in reverse order of dependencies
  await db.run('DELETE FROM job_applications');
  await db.run('DELETE FROM jobs');
  await db.run('DELETE FROM resumes');
  await db.run('DELETE FROM admin_users');
  await db.run('DELETE FROM audit_logs');
  await db.run('DELETE FROM rate_limits');
  await db.run('DELETE FROM security_violations');
  await db.run('DELETE FROM users');
}

// Export helper functions for use in tests
global.testHelpers = {
  createTestUser,
  createTestAdminUser,
  generateTestToken,
  cleanupTestData,
  database: () => database.get()
};

module.exports = {
  createTestUser,
  createTestAdminUser,
  generateTestToken,
  cleanupTestData
};
