const MultiModelAIClient = require('../multiModelAIClient');
const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const Groq = require('groq-sdk');
const { NovitaSDK } = require('novita-sdk');

// Mock all AI service SDKs
jest.mock('openai');
jest.mock('@anthropic-ai/sdk');
jest.mock('@google/generative-ai');
jest.mock('groq-sdk');
jest.mock('novita-sdk');

describe('MultiModelAIClient', () => {
  let client;
  let mockOpenAI, mockAnthropic, mockGemini, mockGroq, mockNovita;

  beforeEach(() => {
    // Reset environment variables
    process.env.OPENAI_API_KEY = 'test-openai-key';
    process.env.ANTHROPIC_API_KEY = 'test-anthropic-key';
    process.env.GOOGLE_AI_API_KEY = 'test-google-key';
    process.env.GROQ_API_KEY = 'test-groq-key';
    process.env.NOVITA_API_KEY = 'test-novita-key';

    // Create mock instances
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };

    mockAnthropic = {
      messages: {
        create: jest.fn()
      }
    };

    mockGemini = {
      getGenerativeModel: jest.fn()
    };

    mockGroq = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };

    mockNovita = {
      chatCompletion: jest.fn()
    };

    // Mock constructors
    OpenAI.mockImplementation(() => mockOpenAI);
    Anthropic.mockImplementation(() => mockAnthropic);
    GoogleGenerativeAI.mockImplementation(() => mockGemini);
    Groq.mockImplementation(() => mockGroq);
    NovitaSDK.mockImplementation(() => mockNovita);

    client = new MultiModelAIClient();
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.OPENAI_API_KEY;
    delete process.env.ANTHROPIC_API_KEY;
    delete process.env.GOOGLE_AI_API_KEY;
    delete process.env.GROQ_API_KEY;
    delete process.env.NOVITA_API_KEY;
  });

  describe('Client Initialization', () => {
    test('should initialize all clients when API keys are provided', () => {
      expect(OpenAI).toHaveBeenCalledWith({ apiKey: 'test-openai-key' });
      expect(Anthropic).toHaveBeenCalledWith({ apiKey: 'test-anthropic-key' });
      expect(GoogleGenerativeAI).toHaveBeenCalledWith('test-google-key');
      expect(Groq).toHaveBeenCalledWith({ apiKey: 'test-groq-key' });
      expect(NovitaSDK).toHaveBeenCalledWith('test-novita-key');
    });

    test('should not initialize clients when API keys are missing', () => {
      delete process.env.OPENAI_API_KEY;
      delete process.env.ANTHROPIC_API_KEY;
      delete process.env.GOOGLE_AI_API_KEY;
      delete process.env.GROQ_API_KEY;
      delete process.env.NOVITA_API_KEY;

      jest.clearAllMocks();
      const clientWithoutKeys = new MultiModelAIClient();

      expect(OpenAI).not.toHaveBeenCalled();
      expect(Anthropic).not.toHaveBeenCalled();
      expect(GoogleGenerativeAI).not.toHaveBeenCalled();
      expect(Groq).not.toHaveBeenCalled();
      expect(NovitaSDK).not.toHaveBeenCalled();
    });
  });

  describe('OpenAI Integration', () => {
    test('should generate content using OpenAI', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'OpenAI response' } }]
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'openai'
      });

      expect(result.content).toBe('OpenAI response');
      expect(result.modelUsed).toBe('openai');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: 'Test prompt' }],
        temperature: 0.7,
        max_tokens: 1500
      });
    });

    test('should handle OpenAI API errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('OpenAI API Error'));

      await expect(client.generateWithFallback('Test prompt', {
        preferredModel: 'openai'
      })).rejects.toThrow();
    });
  });

  describe('Claude Integration', () => {
    test('should generate content using Claude', async () => {
      const mockResponse = {
        content: [{ text: 'Claude response' }]
      };
      mockAnthropic.messages.create.mockResolvedValue(mockResponse);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'claude'
      });

      expect(result.content).toBe('Claude response');
      expect(result.modelUsed).toBe('claude');
      expect(mockAnthropic.messages.create).toHaveBeenCalledWith({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 1500,
        temperature: 0.7,
        messages: [{ role: 'user', content: 'Test prompt' }]
      });
    });

    test('should handle Claude rate limit errors', async () => {
      const error = new Error('Rate limit exceeded');
      error.status = 429;
      mockAnthropic.messages.create.mockRejectedValue(error);

      await expect(client.generateWithFallback('Test prompt', {
        preferredModel: 'claude'
      })).rejects.toThrow('Claude API rate limit exceeded');
    });
  });

  describe('Gemini Integration', () => {
    test('should generate content using Gemini', async () => {
      const mockModel = {
        generateContent: jest.fn().mockResolvedValue({
          response: {
            text: jest.fn().mockReturnValue('Gemini response')
          }
        })
      };
      mockGemini.getGenerativeModel.mockReturnValue(mockModel);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'gemini'
      });

      expect(result.content).toBe('Gemini response');
      expect(result.modelUsed).toBe('gemini');
      expect(mockGemini.getGenerativeModel).toHaveBeenCalledWith({
        model: 'gemini-1.5-pro',
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1500
        }
      });
    });
  });

  describe('Groq Integration', () => {
    test('should generate content using Groq', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Groq response' } }]
      };
      mockGroq.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'groq'
      });

      expect(result.content).toBe('Groq response');
      expect(result.modelUsed).toBe('groq');
      expect(mockGroq.chat.completions.create).toHaveBeenCalledWith({
        model: 'llama-3.1-70b-versatile',
        messages: [{ role: 'user', content: 'Test prompt' }],
        temperature: 0.7,
        max_tokens: 1500
      });
    });
  });

  describe('Novita Integration', () => {
    test('should generate content using Novita', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Novita response' } }]
      };
      mockNovita.chatCompletion.mockResolvedValue(mockResponse);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'novita'
      });

      expect(result.content).toBe('Novita response');
      expect(result.modelUsed).toBe('novita');
      expect(mockNovita.chatCompletion).toHaveBeenCalledWith({
        model: 'meta-llama/llama-3.1-70b-instruct',
        messages: [{ role: 'user', content: 'Test prompt' }],
        temperature: 0.7,
        max_tokens: 1500
      });
    });
  });

  describe('Fallback Chain', () => {
    test('should fallback to next provider when primary fails', async () => {
      // Make OpenAI fail
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('OpenAI failed'));
      
      // Make Claude succeed
      const mockResponse = {
        content: [{ text: 'Claude fallback response' }]
      };
      mockAnthropic.messages.create.mockResolvedValue(mockResponse);

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'openai'
      });

      expect(result.content).toBe('Claude fallback response');
      expect(result.modelUsed).toBe('claude');
    });

    test('should retry with exponential backoff', async () => {
      const error = new Error('Temporary failure');
      mockOpenAI.chat.completions.create
        .mockRejectedValueOnce(error)
        .mockRejectedValueOnce(error)
        .mockResolvedValue({
          choices: [{ message: { content: 'Success after retries' } }]
        });

      const result = await client.generateWithFallback('Test prompt', {
        preferredModel: 'openai',
        maxRetries: 3
      });

      expect(result.content).toBe('Success after retries');
      expect(result.attempt).toBe(3);
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(3);
    });
  });

  describe('Model Status', () => {
    test('should return correct model status', () => {
      const status = client.getModelStatus();
      
      expect(status).toHaveLength(5);
      expect(status.find(s => s.key === 'openai')).toMatchObject({
        clientInitialized: true,
        status: 'ready'
      });
      expect(status.find(s => s.key === 'claude')).toMatchObject({
        clientInitialized: true,
        status: 'ready'
      });
    });
  });
});
