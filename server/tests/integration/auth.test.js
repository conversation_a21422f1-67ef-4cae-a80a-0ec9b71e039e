const request = require('supertest');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const app = require('../../app');
const database = require('../../database');
const encryptionService = require('../../utils/encryptionService');

describe('Authentication Integration Tests', () => {
  let db;
  let testUser;
  let adminUser;
  let authToken;
  let adminToken;

  beforeAll(async () => {
    // Initialize test database
    db = database.get();
    
    // Create test users
    const passwordHash = await bcrypt.hash('TestPassword123!', 12);
    const adminPasswordHash = await bcrypt.hash('AdminPassword123!', 12);

    // Create regular test user
    const userResult = await db.run(`
      INSERT INTO users (
        email, password_hash, name, first_name, last_name,
        user_role, account_status, email_verified, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      '<EMAIL>', passwordHash, 'Test User', 'Test', 'User',
      'user', 'active', true, true
    ]);

    testUser = {
      id: userResult.lastID,
      email: '<EMAIL>',
      password: 'TestPassword123!'
    };

    // Create admin test user
    const adminResult = await db.run(`
      INSERT INTO users (
        email, password_hash, name, first_name, last_name,
        user_role, account_status, email_verified, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      '<EMAIL>', adminPasswordHash, 'Admin User', 'Admin', 'User',
      'super_admin', 'active', true, true
    ]);

    adminUser = {
      id: adminResult.lastID,
      email: '<EMAIL>',
      password: 'AdminPassword123!'
    };

    // Create admin user entry
    await db.run(`
      INSERT INTO admin_users (
        user_id, admin_level, permissions, created_by
      ) VALUES (?, ?, ?, ?)
    `, [
      adminUser.id,
      10,
      JSON.stringify({
        users: ['create', 'read', 'update', 'delete'],
        admin: ['create', 'read', 'update', 'delete'],
        system: ['configure', 'monitor', 'backup']
      }),
      adminUser.id
    ]);

    // Generate tokens
    authToken = jwt.sign({ userId: testUser.id }, process.env.JWT_SECRET || 'test_secret');
    adminToken = jwt.sign({ userId: adminUser.id }, process.env.JWT_SECRET || 'test_secret');
  });

  afterAll(async () => {
    // Clean up test data
    await db.run('DELETE FROM users WHERE email IN (?, ?)', ['<EMAIL>', '<EMAIL>']);
    await db.run('DELETE FROM admin_users WHERE user_id IN (?, ?)', [testUser.id, adminUser.id]);
  });

  describe('User Registration', () => {
    test('should register new user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'NewPassword123!',
        name: 'New User',
        firstName: 'New',
        lastName: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.password_hash).toBeUndefined();
      expect(response.body.data.token).toBeDefined();

      // Clean up
      await db.run('DELETE FROM users WHERE email = ?', [userData.email]);
    });

    test('should reject registration with weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123',
        name: 'Weak Password User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('password');
    });

    test('should reject registration with duplicate email', async () => {
      const userData = {
        email: testUser.email,
        password: 'AnotherPassword123!',
        name: 'Duplicate User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('already exists');
    });

    test('should reject registration with invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'ValidPassword123!',
        name: 'Invalid Email User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('email');
    });
  });

  describe('User Login', () => {
    test('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.email).toBe(testUser.email);
      expect(response.body.data.user.password_hash).toBeUndefined();
    });

    test('should reject login with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword123!'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid credentials');
    });

    test('should reject login with non-existent email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SomePassword123!'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid credentials');
    });

    test('should implement rate limiting for failed login attempts', async () => {
      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: testUser.email,
            password: 'WrongPassword'
          });
      }

      // Next attempt should be rate limited
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword'
        })
        .expect(429);

      expect(response.body.error).toContain('Too many requests');
    });
  });

  describe('Token Validation', () => {
    test('should validate valid JWT token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.id).toBe(testUser.id);
    });

    test('should reject invalid JWT token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid token');
    });

    test('should reject missing authorization header', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Authorization token required');
    });
  });

  describe('Admin Authentication', () => {
    test('should authenticate admin user', async () => {
      const response = await request(app)
        .get('/api/admin/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
    });

    test('should reject non-admin user from admin routes', async () => {
      const response = await request(app)
        .get('/api/admin/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Admin access required');
    });

    test('should validate admin permissions', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
    });
  });

  describe('Password Reset', () => {
    test('should initiate password reset for valid email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: testUser.email })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('reset instructions');
    });

    test('should not reveal if email does not exist', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('reset instructions');
    });
  });

  describe('Security Features', () => {
    test('should log authentication events to audit log', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      // Check audit log
      const auditLog = await db.get(`
        SELECT * FROM audit_logs 
        WHERE action = 'USER_LOGIN' AND user_id = ?
        ORDER BY created_at DESC LIMIT 1
      `, [testUser.id]);

      expect(auditLog).toBeDefined();
      expect(auditLog.action).toBe('USER_LOGIN');
    });

    test('should encrypt sensitive user data', async () => {
      // Test that encryption service is working
      const testData = 'sensitive information';
      const encrypted = encryptionService.encrypt(testData, 'test');
      const decrypted = encryptionService.decrypt(encrypted, 'test');

      expect(decrypted).toBe(testData);
      expect(encrypted).not.toBe(testData);
    });

    test('should handle account lockout after failed attempts', async () => {
      // This would require implementing account lockout logic
      // For now, we test that the user exists and can be locked
      const user = await db.get('SELECT * FROM users WHERE id = ?', [testUser.id]);
      expect(user.failed_login_attempts).toBeDefined();
      expect(user.account_locked_until).toBeDefined();
    });
  });
});
