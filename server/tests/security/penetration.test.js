const request = require('supertest');
const app = require('../../app');
const database = require('../../database');
const encryptionService = require('../../utils/encryptionService');

describe('Security Penetration Tests', () => {
  let db;
  let testToken;

  beforeAll(async () => {
    db = database.get();
    
    // Create test user for security testing
    const bcrypt = require('bcrypt');
    const jwt = require('jsonwebtoken');
    
    const passwordHash = await bcrypt.hash('SecurityTest123!', 12);
    const userResult = await db.run(`
      INSERT INTO users (
        email, password_hash, name, user_role, account_status, email_verified, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      '<EMAIL>', passwordHash, 'Security Test User',
      'user', 'active', true, true
    ]);

    testToken = jwt.sign({ userId: userResult.lastID }, process.env.JWT_SECRET || 'test_secret');
  });

  afterAll(async () => {
    await db.run('DELETE FROM users WHERE email = ?', ['<EMAIL>']);
  });

  describe('SQL Injection Protection', () => {
    test('should block SQL injection in login', async () => {
      const sqlInjectionPayloads = [
        "admin'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "'; INSERT INTO users (email) VALUES ('hacked'); --"
      ];

      for (const payload of sqlInjectionPayloads) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: payload,
            password: 'any_password'
          });

        expect(response.status).not.toBe(200);
        expect(response.body.success).toBe(false);
      }
    });

    test('should block SQL injection in search parameters', async () => {
      const sqlInjectionPayloads = [
        "'; DROP TABLE jobs; --",
        "' OR 1=1 --",
        "' UNION SELECT password_hash FROM users --"
      ];

      for (const payload of sqlInjectionPayloads) {
        const response = await request(app)
          .get('/api/jobs/search')
          .query({ query: payload })
          .set('Authorization', `Bearer ${testToken}`);

        expect(response.status).not.toBe(500);
        if (response.status === 400) {
          expect(response.body.error).toContain('Invalid');
        }
      }
    });

    test('should sanitize input in resume creation', async () => {
      const maliciousData = {
        title: "'; DROP TABLE resumes; --",
        data: {
          personalInfo: {
            name: "' OR '1'='1",
            email: "<EMAIL>"
          }
        }
      };

      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${testToken}`)
        .send(maliciousData);

      // Should either reject the input or sanitize it
      if (response.status === 201) {
        expect(response.body.data.resume.title).not.toContain('DROP TABLE');
      } else {
        expect(response.status).toBe(400);
      }
    });
  });

  describe('XSS Protection', () => {
    test('should block XSS in user input', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("XSS")',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<svg onload="alert(1)"></svg>'
      ];

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/resumes')
          .set('Authorization', `Bearer ${testToken}`)
          .send({
            title: payload,
            data: { personalInfo: { name: payload } }
          });

        if (response.status === 201) {
          // If created, ensure XSS payload was sanitized
          expect(response.body.data.resume.title).not.toContain('<script>');
          expect(response.body.data.resume.title).not.toContain('javascript:');
        } else {
          // Should be rejected
          expect(response.status).toBe(400);
        }
      }
    });

    test('should sanitize XSS in query parameters', async () => {
      const xssPayload = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .get('/api/jobs')
        .query({ search: xssPayload })
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).not.toBe(500);
      if (response.body.data) {
        const responseString = JSON.stringify(response.body);
        expect(responseString).not.toContain('<script>');
      }
    });
  });

  describe('Authentication Security', () => {
    test('should reject weak passwords', async () => {
      const weakPasswords = [
        '123456',
        'password',
        'qwerty',
        'abc123',
        '12345678'
      ];

      for (const password of weakPasswords) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: `weak${Date.now()}@test.com`,
            password: password,
            name: 'Weak Password User'
          });

        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
      }
    });

    test('should implement proper session management', async () => {
      // Test token expiration
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIxMjMiLCJleHAiOjE2MDk0NTkyMDB9.invalid';
      
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${expiredToken}`);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('should prevent JWT token manipulation', async () => {
      const manipulatedTokens = [
        testToken.slice(0, -5) + 'XXXXX', // Modified signature
        testToken.replace(/\./g, 'X'), // Invalid format
        'Bearer ' + testToken, // Double Bearer
        testToken + 'extra_data' // Appended data
      ];

      for (const token of manipulatedTokens) {
        const response = await request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${token}`);

        expect(response.status).toBe(401);
        expect(response.body.success).toBe(false);
      }
    });
  });

  describe('Authorization Security', () => {
    test('should prevent horizontal privilege escalation', async () => {
      // Create another user
      const bcrypt = require('bcrypt');
      const jwt = require('jsonwebtoken');
      
      const passwordHash = await bcrypt.hash('AnotherUser123!', 12);
      const anotherUserResult = await db.run(`
        INSERT INTO users (
          email, password_hash, name, user_role, account_status, email_verified, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        '<EMAIL>', passwordHash, 'Another User',
        'user', 'active', true, true
      ]);

      const anotherToken = jwt.sign({ userId: anotherUserResult.lastID }, process.env.JWT_SECRET || 'test_secret');

      // Create resume with first user
      const resumeResponse = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          title: 'Test Resume',
          data: { personalInfo: { name: 'Test' } }
        });

      const resumeId = resumeResponse.body.data.resume.id;

      // Try to access first user's resume with second user's token
      const unauthorizedResponse = await request(app)
        .get(`/api/resumes/${resumeId}`)
        .set('Authorization', `Bearer ${anotherToken}`);

      expect(unauthorizedResponse.status).toBe(403);
      expect(unauthorizedResponse.body.success).toBe(false);

      // Cleanup
      await db.run('DELETE FROM users WHERE email = ?', ['<EMAIL>']);
    });

    test('should prevent vertical privilege escalation', async () => {
      // Regular user should not access admin routes
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Admin access required');
    });
  });

  describe('Input Validation Security', () => {
    test('should validate file upload security', async () => {
      const maliciousFiles = [
        { filename: '../../../etc/passwd', content: 'malicious content' },
        { filename: 'script.js', content: '<script>alert("XSS")</script>' },
        { filename: 'test.php', content: '<?php system($_GET["cmd"]); ?>' }
      ];

      for (const file of maliciousFiles) {
        const response = await request(app)
          .post('/api/upload')
          .set('Authorization', `Bearer ${testToken}`)
          .attach('file', Buffer.from(file.content), file.filename);

        // Should reject malicious files
        expect(response.status).not.toBe(200);
      }
    });

    test('should validate request size limits', async () => {
      const largeData = 'x'.repeat(50 * 1024 * 1024); // 50MB string
      
      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          title: 'Large Resume',
          data: { description: largeData }
        });

      expect(response.status).toBe(413);
      expect(response.body.error).toContain('too large');
    });

    test('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${testToken}`)
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting Security', () => {
    test('should implement rate limiting on authentication endpoints', async () => {
      const promises = [];
      
      // Make many rapid requests
      for (let i = 0; i < 20; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrong_password'
            })
        );
      }

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should implement rate limiting on API endpoints', async () => {
      const promises = [];
      
      // Make many rapid API requests
      for (let i = 0; i < 150; i++) {
        promises.push(
          request(app)
            .get('/api/jobs')
            .set('Authorization', `Bearer ${testToken}`)
        );
      }

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Encryption Security', () => {
    test('should properly encrypt sensitive data', async () => {
      const sensitiveData = 'confidential information';
      const encrypted = encryptionService.encrypt(sensitiveData, 'test');
      
      expect(encrypted).not.toBe(sensitiveData);
      expect(encrypted).toContain('.');
      expect(encrypted.length).toBeGreaterThan(sensitiveData.length);
    });

    test('should detect tampered encrypted data', async () => {
      const sensitiveData = 'confidential information';
      const encrypted = encryptionService.encrypt(sensitiveData, 'test');
      
      // Tamper with the encrypted data
      const tamperedEncrypted = encrypted.slice(0, -5) + 'XXXXX';
      
      expect(() => {
        encryptionService.decrypt(tamperedEncrypted, 'test');
      }).toThrow();
    });

    test('should use different encryption for different purposes', async () => {
      const data = 'same data';
      const encrypted1 = encryptionService.encrypt(data, 'purpose1');
      const encrypted2 = encryptionService.encrypt(data, 'purpose2');
      
      expect(encrypted1).not.toBe(encrypted2);
    });
  });

  describe('Security Headers', () => {
    test('should include security headers', async () => {
      const response = await request(app)
        .get('/api/health');

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBeDefined();
    });

    test('should implement CORS properly', async () => {
      const response = await request(app)
        .options('/api/auth/login')
        .set('Origin', 'https://malicious-site.com');

      expect(response.status).toBe(200);
      expect(response.headers['access-control-allow-origin']).not.toBe('https://malicious-site.com');
    });
  });

  describe('Error Handling Security', () => {
    test('should not expose sensitive information in errors', async () => {
      const response = await request(app)
        .get('/api/nonexistent-endpoint');

      expect(response.status).toBe(404);
      expect(response.body.error).not.toContain('database');
      expect(response.body.error).not.toContain('password');
      expect(response.body.error).not.toContain('secret');
    });

    test('should handle database errors securely', async () => {
      // This would require mocking database errors
      // For now, we test that errors don't expose internal details
      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          title: null, // This might cause a database error
          data: {}
        });

      if (response.status === 500) {
        expect(response.body.error).not.toContain('SQL');
        expect(response.body.error).not.toContain('database');
        expect(response.body.error).not.toContain('constraint');
      }
    });
  });
});
