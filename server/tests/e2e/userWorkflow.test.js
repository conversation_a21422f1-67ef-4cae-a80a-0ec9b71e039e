const request = require('supertest');
const app = require('../../app');
const database = require('../../database');

describe('End-to-End User Workflow Tests', () => {
  let db;
  let userToken;
  let userId;
  let resumeId;
  let jobId;

  beforeAll(async () => {
    db = database.get();
  });

  afterAll(async () => {
    // Clean up test data
    if (userId) {
      await db.run('DELETE FROM users WHERE id = ?', [userId]);
      await db.run('DELETE FROM resumes WHERE user_id = ?', [userId]);
      await db.run('DELETE FROM jobs WHERE user_id = ?', [userId]);
      await db.run('DELETE FROM job_applications WHERE user_id = ?', [userId]);
    }
  });

  describe('Complete User Journey', () => {
    test('1. User Registration', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'E2ETestPassword123!',
        name: 'E2E Test User',
        firstName: 'E2E',
        lastName: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.token).toBeDefined();

      userToken = response.body.data.token;
      userId = response.body.data.user.id;
    });

    test('2. User Login', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'E2ETestPassword123!'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
      
      userToken = response.body.data.token;
    });

    test('3. Get User Profile', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('4. Create Resume', async () => {
      const resumeData = {
        title: 'Software Engineer Resume',
        data: {
          personalInfo: {
            name: 'E2E Test User',
            email: '<EMAIL>',
            phone: '******-0123',
            location: 'San Francisco, CA'
          },
          summary: 'Experienced software engineer with expertise in full-stack development.',
          experience: [
            {
              company: 'Tech Corp',
              position: 'Senior Software Engineer',
              startDate: '2020-01',
              endDate: '2024-01',
              description: 'Led development of scalable web applications using React and Node.js.'
            }
          ],
          education: [
            {
              institution: 'University of Technology',
              degree: 'Bachelor of Science in Computer Science',
              graduationDate: '2020-05'
            }
          ],
          skills: ['JavaScript', 'React', 'Node.js', 'Python', 'SQL']
        },
        templateId: 'modern'
      };

      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${userToken}`)
        .send(resumeData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.resume.title).toBe(resumeData.title);
      expect(response.body.data.resume.user_id).toBe(userId);

      resumeId = response.body.data.resume.id;
    });

    test('5. Get User Resumes', async () => {
      const response = await request(app)
        .get('/api/resumes')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.resumes)).toBe(true);
      expect(response.body.data.resumes.length).toBeGreaterThan(0);
      expect(response.body.data.resumes[0].id).toBe(resumeId);
    });

    test('6. Update Resume', async () => {
      const updateData = {
        title: 'Updated Software Engineer Resume',
        data: {
          personalInfo: {
            name: 'E2E Test User',
            email: '<EMAIL>',
            phone: '******-0123',
            location: 'San Francisco, CA'
          },
          summary: 'Highly experienced software engineer with expertise in full-stack development and team leadership.',
          experience: [
            {
              company: 'Tech Corp',
              position: 'Senior Software Engineer',
              startDate: '2020-01',
              endDate: '2024-01',
              description: 'Led development of scalable web applications using React and Node.js.'
            },
            {
              company: 'Startup Inc',
              position: 'Full Stack Developer',
              startDate: '2018-06',
              endDate: '2019-12',
              description: 'Developed MVP for fintech startup using Python and React.'
            }
          ],
          education: [
            {
              institution: 'University of Technology',
              degree: 'Bachelor of Science in Computer Science',
              graduationDate: '2020-05'
            }
          ],
          skills: ['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Docker', 'AWS']
        }
      };

      const response = await request(app)
        .put(`/api/resumes/${resumeId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.resume.title).toBe(updateData.title);
    });

    test('7. Save Job Opportunity', async () => {
      const jobData = {
        title: 'Senior Full Stack Developer',
        company: 'Amazing Tech Company',
        location: 'San Francisco, CA',
        description: 'We are looking for a senior full stack developer to join our team.',
        url: 'https://example.com/jobs/senior-fullstack',
        salaryMin: 120000,
        salaryMax: 180000,
        jobType: 'fulltime',
        status: 'saved'
      };

      const response = await request(app)
        .post('/api/jobs')
        .set('Authorization', `Bearer ${userToken}`)
        .send(jobData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.job.title).toBe(jobData.title);
      expect(response.body.data.job.user_id).toBe(userId);

      jobId = response.body.data.job.id;
    });

    test('8. Get Saved Jobs', async () => {
      const response = await request(app)
        .get('/api/jobs')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.jobs)).toBe(true);
      expect(response.body.data.jobs.length).toBeGreaterThan(0);
      expect(response.body.data.jobs[0].id).toBe(jobId);
    });

    test('9. Apply to Job', async () => {
      const applicationData = {
        jobId: jobId,
        resumeId: resumeId,
        notes: 'Very interested in this position. My experience aligns well with the requirements.'
      };

      const response = await request(app)
        .post('/api/applications')
        .set('Authorization', `Bearer ${userToken}`)
        .send(applicationData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.application.job_id).toBe(jobId);
      expect(response.body.data.application.resume_id).toBe(resumeId);
      expect(response.body.data.application.user_id).toBe(userId);
    });

    test('10. Get Job Applications', async () => {
      const response = await request(app)
        .get('/api/applications')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.applications)).toBe(true);
      expect(response.body.data.applications.length).toBeGreaterThan(0);
    });

    test('11. Update Job Application Status', async () => {
      // First get the application ID
      const applicationsResponse = await request(app)
        .get('/api/applications')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      const applicationId = applicationsResponse.body.data.applications[0].id;

      const updateData = {
        status: 'interview_scheduled',
        notes: 'Phone interview scheduled for next week.'
      };

      const response = await request(app)
        .put(`/api/applications/${applicationId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.application.status).toBe(updateData.status);
    });

    test('12. Search Jobs (if job discovery is enabled)', async () => {
      const searchParams = {
        query: 'software engineer',
        location: 'San Francisco',
        jobType: 'fulltime'
      };

      const response = await request(app)
        .get('/api/jobs/search')
        .query(searchParams)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      // Results may be empty if external APIs are not configured
      expect(Array.isArray(response.body.data.jobs)).toBe(true);
    });

    test('13. Get User Dashboard Data', async () => {
      const response = await request(app)
        .get('/api/dashboard')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.stats).toBeDefined();
      expect(response.body.data.stats.resumeCount).toBeGreaterThan(0);
      expect(response.body.data.stats.jobCount).toBeGreaterThan(0);
      expect(response.body.data.stats.applicationCount).toBeGreaterThan(0);
    });

    test('14. Update User Profile', async () => {
      const updateData = {
        name: 'E2E Test User Updated',
        firstName: 'E2E Updated',
        lastName: 'User'
      };

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.name).toBe(updateData.name);
    });

    test('15. Export Resume (PDF)', async () => {
      const response = await request(app)
        .get(`/api/resumes/${resumeId}/export/pdf`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.headers['content-type']).toContain('application/pdf');
    });

    test('16. Delete Job Application', async () => {
      // Get application ID
      const applicationsResponse = await request(app)
        .get('/api/applications')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      const applicationId = applicationsResponse.body.data.applications[0].id;

      const response = await request(app)
        .delete(`/api/applications/${applicationId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('17. Delete Job', async () => {
      const response = await request(app)
        .delete(`/api/jobs/${jobId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('18. Delete Resume', async () => {
      const response = await request(app)
        .delete(`/api/resumes/${resumeId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('19. User Logout', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('logged out');
    });

    test('20. Verify Token Invalidation After Logout', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid token');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle unauthorized access attempts', async () => {
      const response = await request(app)
        .get('/api/resumes')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Authorization token required');
    });

    test('should handle invalid resource access', async () => {
      // Try to access non-existent resume
      const response = await request(app)
        .get('/api/resumes/non-existent-id')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    test('should validate input data', async () => {
      const invalidResumeData = {
        title: '', // Empty title should be invalid
        data: 'invalid json structure'
      };

      const response = await request(app)
        .post('/api/resumes')
        .set('Authorization', `Bearer ${userToken}`)
        .send(invalidResumeData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    test('should handle rate limiting', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .get('/api/auth/me')
            .set('Authorization', `Bearer ${userToken}`)
        );
      }

      const responses = await Promise.all(promises);
      
      // At least some requests should succeed
      const successfulRequests = responses.filter(r => r.status === 200);
      expect(successfulRequests.length).toBeGreaterThan(0);
    });
  });
});
