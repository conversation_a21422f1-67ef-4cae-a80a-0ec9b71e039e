/**
 * Comprehensive tests for enhanced error handling middleware
 * Tests all error classes, logging, and security features
 */

const assert = require('assert');
const {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  logError,
  errorHandler,
  asyncHandler,
  notFoundHandler
} = require('../middleware/errorHandler');

/**
 * Simple test runner for error handling tests
 */
class ErrorTestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  test(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  async run() {
    console.log(`\n🧪 Running ${this.tests.length} error handling tests...\n`);

    for (const { name, testFunction } of this.tests) {
      try {
        const startTime = Date.now();
        await testFunction();
        const duration = Date.now() - startTime;
        
        this.passed++;
        this.results.push({ name, status: 'PASS', duration });
        console.log(`✅ ${name} (${duration}ms)`);
      } catch (error) {
        this.failed++;
        this.results.push({ name, status: 'FAIL', error: error.message });
        console.log(`❌ ${name} - ${error.message}`);
      }
    }

    const total = this.passed + this.failed;
    const successRate = total > 0 ? (this.passed / total * 100).toFixed(1) : 0;

    console.log(`\n📊 Error Handling Test Results:`);
    console.log(`Total: ${total}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${successRate}%\n`);

    return {
      total,
      passed: this.passed,
      failed: this.failed,
      successRate: parseFloat(successRate),
      results: this.results
    };
  }
}

const runner = new ErrorTestRunner();

// Test AppError base class
runner.test('AppError should create proper base error', () => {
  const error = new AppError('Test error', 500, 'TEST_ERROR');
  
  assert.strictEqual(error.message, 'Test error');
  assert.strictEqual(error.statusCode, 500);
  assert.strictEqual(error.errorCode, 'TEST_ERROR');
  assert.strictEqual(error.isOperational, true);
  assert.strictEqual(typeof error.timestamp, 'string');
  assert.strictEqual(error instanceof Error, true);
});

// Test ValidationError
runner.test('ValidationError should have correct properties', () => {
  const details = [{ field: 'email', message: 'Invalid email' }];
  const error = new ValidationError('Validation failed', details);
  
  assert.strictEqual(error.statusCode, 400);
  assert.strictEqual(error.errorCode, 'VALIDATION_ERROR');
  assert.deepStrictEqual(error.details, details);
});

// Test AuthenticationError
runner.test('AuthenticationError should have correct properties', () => {
  const error = new AuthenticationError('Invalid token');
  
  assert.strictEqual(error.statusCode, 401);
  assert.strictEqual(error.errorCode, 'AUTHENTICATION_ERROR');
  assert.strictEqual(error.message, 'Invalid token');
});

// Test AuthorizationError
runner.test('AuthorizationError should have correct properties', () => {
  const error = new AuthorizationError('Access denied');
  
  assert.strictEqual(error.statusCode, 403);
  assert.strictEqual(error.errorCode, 'AUTHORIZATION_ERROR');
  assert.strictEqual(error.message, 'Access denied');
});

// Test NotFoundError
runner.test('NotFoundError should have correct properties', () => {
  const error = new NotFoundError('User');
  
  assert.strictEqual(error.statusCode, 404);
  assert.strictEqual(error.errorCode, 'NOT_FOUND_ERROR');
  assert.strictEqual(error.message, 'User not found');
});

// Test ConflictError
runner.test('ConflictError should have correct properties', () => {
  const error = new ConflictError('Resource already exists');
  
  assert.strictEqual(error.statusCode, 409);
  assert.strictEqual(error.errorCode, 'CONFLICT_ERROR');
  assert.strictEqual(error.message, 'Resource already exists');
});

// Test RateLimitError
runner.test('RateLimitError should have correct properties', () => {
  const error = new RateLimitError('Too many requests');
  
  assert.strictEqual(error.statusCode, 429);
  assert.strictEqual(error.errorCode, 'RATE_LIMIT_ERROR');
  assert.strictEqual(error.message, 'Too many requests');
});

// Test ExternalServiceError
runner.test('ExternalServiceError should have correct properties', () => {
  const error = new ExternalServiceError('PaymentService', 'Service unavailable');
  
  assert.strictEqual(error.statusCode, 502);
  assert.strictEqual(error.errorCode, 'EXTERNAL_SERVICE_ERROR');
  assert.strictEqual(error.message, 'PaymentService: Service unavailable');
  assert.strictEqual(error.service, 'PaymentService');
});

// Test logError function
runner.test('logError should format log data correctly', () => {
  const mockError = new ValidationError('Test validation error');
  const mockReq = {
    method: 'POST',
    originalUrl: '/api/test',
    get: (header) => ({
      'User-Agent': 'test-agent',
      'Content-Type': 'application/json'
    }[header]),
    ip: '127.0.0.1',
    user: { userId: '123' },
    id: 'req-123'
  };

  // Capture console output
  const originalError = console.error;
  let logOutput = '';
  console.error = (message, data) => {
    logOutput = data;
  };

  logError(mockError, mockReq, 'error');

  // Restore console
  console.error = originalError;

  const logData = JSON.parse(logOutput);
  assert.strictEqual(logData.level, 'error');
  assert.strictEqual(logData.error.name, 'Error'); // Error.name gets overridden
  assert.strictEqual(logData.request.method, 'POST');
  assert.strictEqual(logData.request.id, 'req-123');
});

// Test asyncHandler wrapper
runner.test('asyncHandler should catch async errors', async () => {
  const asyncFunction = async (req, res, next) => {
    throw new Error('Async error');
  };

  const wrappedFunction = asyncHandler(asyncFunction);
  let caughtError = null;

  const mockNext = (error) => {
    caughtError = error;
  };

  await wrappedFunction({}, {}, mockNext);
  
  assert.strictEqual(caughtError.message, 'Async error');
});

// Test notFoundHandler
runner.test('notFoundHandler should create NotFoundError', () => {
  const mockReq = { originalUrl: '/api/nonexistent' };
  let caughtError = null;

  const mockNext = (error) => {
    caughtError = error;
  };

  notFoundHandler(mockReq, {}, mockNext);
  
  assert.strictEqual(caughtError instanceof NotFoundError, true);
  assert.strictEqual(caughtError.message, 'Route /api/nonexistent not found');
});

// Test errorHandler middleware
runner.test('errorHandler should format response correctly', () => {
  const mockError = new AuthenticationError('Invalid credentials');
  const mockReq = {
    method: 'POST',
    originalUrl: '/api/login',
    get: () => 'test-agent',
    ip: '127.0.0.1',
    id: 'req-456'
  };

  let responseData = null;
  let statusCode = null;

  const mockRes = {
    status: (code) => {
      statusCode = code;
      return mockRes;
    },
    json: (data) => {
      responseData = data;
      return mockRes;
    }
  };

  // Capture console output
  const originalError = console.error;
  console.error = () => {}; // Suppress log output

  errorHandler(mockError, mockReq, mockRes, () => {});

  // Restore console
  console.error = originalError;

  assert.strictEqual(statusCode, 401);
  assert.strictEqual(responseData.success, false);
  assert.strictEqual(responseData.error.message, 'Invalid credentials');
  assert.strictEqual(responseData.error.code, 'AUTHENTICATION_ERROR');
  assert.strictEqual(responseData.error.requestId, 'req-456');
});

// Test security-conscious responses in production
runner.test('errorHandler should sanitize errors in production', () => {
  const originalEnv = process.env.NODE_ENV;
  process.env.NODE_ENV = 'production';

  const mockError = new Error('Internal database connection failed');
  const mockReq = {
    method: 'GET',
    originalUrl: '/api/data',
    get: () => 'test-agent',
    ip: '127.0.0.1'
  };

  let responseData = null;
  let statusCode = null;

  const mockRes = {
    status: (code) => {
      statusCode = code;
      return mockRes;
    },
    json: (data) => {
      responseData = data;
      return mockRes;
    }
  };

  // Capture console output
  const originalError = console.error;
  console.error = () => {}; // Suppress log output

  errorHandler(mockError, mockReq, mockRes, () => {});

  // Restore environment and console
  process.env.NODE_ENV = originalEnv;
  console.error = originalError;

  assert.strictEqual(statusCode, 500);
  assert.strictEqual(responseData.error.message, 'Internal server error');
  assert.strictEqual(responseData.error.code, 'INTERNAL_ERROR');
});

// Test error handling for known error types
runner.test('errorHandler should handle JWT errors correctly', () => {
  const mockError = new Error('jwt malformed');
  mockError.name = 'JsonWebTokenError';
  
  const mockReq = {
    method: 'GET',
    originalUrl: '/api/protected',
    get: () => 'test-agent',
    ip: '127.0.0.1'
  };

  let responseData = null;
  let statusCode = null;

  const mockRes = {
    status: (code) => {
      statusCode = code;
      return mockRes;
    },
    json: (data) => {
      responseData = data;
      return mockRes;
    }
  };

  // Capture console output
  const originalError = console.error;
  console.error = () => {}; // Suppress log output

  errorHandler(mockError, mockReq, mockRes, () => {});

  // Restore console
  console.error = originalError;

  assert.strictEqual(statusCode, 401);
  assert.strictEqual(responseData.error.code, 'AUTHENTICATION_ERROR');
});

module.exports = { ErrorTestRunner, runner };

// Run tests if this file is executed directly
if (require.main === module) {
  runner.run().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Error test runner failed:', error);
    process.exit(1);
  });
}