/**
 * Comprehensive tests for the enhanced logging system
 * Tests all log levels, formatting, rotation, and advanced features
 */

const fs = require('fs');
const path = require('path');
const { Logger, logger, LOG_LEVELS } = require('../utils/logger');

/**
 * Simple test framework for logger testing
 */
class LoggerTestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.testLogDir = path.join(__dirname, 'temp_logs');
    this.cleanup();
  }

  test(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  async run() {
    console.log(`\n🧪 Running ${this.tests.length} logger tests...\n`);

    for (const { name, testFunction } of this.tests) {
      try {
        const startTime = Date.now();
        await testFunction();
        const duration = Date.now() - startTime;
        
        this.passed++;
        console.log(`✅ ${name} (${duration}ms)`);
      } catch (error) {
        this.failed++;
        console.log(`❌ ${name} - ${error.message}`);
      }
    }

    console.log(`\n📊 Logger Test Results:`);
    console.log(`Total: ${this.tests.length}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${Math.round((this.passed / this.tests.length) * 100)}%`);

    this.cleanup();
    return { total: this.tests.length, passed: this.passed, failed: this.failed };
  }

  cleanup() {
    try {
      if (fs.existsSync(this.testLogDir)) {
        const files = fs.readdirSync(this.testLogDir);
        files.forEach(file => {
          fs.unlinkSync(path.join(this.testLogDir, file));
        });
        fs.rmdirSync(this.testLogDir);
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }
}

const runner = new LoggerTestRunner();

// Test log levels and filtering
runner.test('Log levels should filter correctly', () => {
  const testLogger = new Logger({ level: 'WARN', enableConsole: false, enableFile: false });
  
  runner.assert(testLogger.level === LOG_LEVELS.WARN, 'Logger level should be set to WARN');
  
  // Test level filtering by intercepting the formatMessage method since log() returns early
  let debugMessageFormatted = false;
  let errorMessageFormatted = false;
  
  const originalFormatMessage = testLogger.formatMessage;
  testLogger.formatMessage = function(level, message, meta) {
    if (level === 'DEBUG') debugMessageFormatted = true;
    if (level === 'ERROR') errorMessageFormatted = true;
    return originalFormatMessage.call(this, level, message, meta);
  };
  
  testLogger.debug('This should not log');
  runner.assert(!debugMessageFormatted, 'DEBUG message should not be formatted when level is WARN');
  
  testLogger.error('This should log');
  runner.assert(errorMessageFormatted, 'ERROR message should be formatted when level is WARN');
});

// Test message formatting
runner.test('Message formatting should include all required fields', () => {
  const testLogger = new Logger({ serviceName: 'test-service', enableConsole: false, enableFile: false });
  
  const formatted = testLogger.formatMessage('INFO', 'Test message', { userId: 123 });
  
  runner.assert(formatted.timestamp, 'Should include timestamp');
  runner.assert(formatted.level === 'INFO', 'Should include correct level');
  runner.assert(formatted.service === 'test-service', 'Should include service name');
  runner.assert(formatted.message === 'Test message', 'Should include message');
  runner.assert(formatted.userId === 123, 'Should include metadata');
  runner.assert(formatted.pid, 'Should include process ID');
  runner.assert(formatted.environment, 'Should include environment');
});

// Test context preservation
runner.test('Context should be preserved and included in logs', () => {
  const testLogger = new Logger({ enableConsole: false, enableFile: false });
  
  testLogger.setContext({ correlationId: 'test-123', userId: 456 });
  const formatted = testLogger.formatMessage('INFO', 'Test', {});
  
  runner.assert(formatted.correlationId === 'test-123', 'Should include correlation ID from context');
  runner.assert(formatted.userId === 456, 'Should include user ID from context');
  
  testLogger.clearContext();
  const clearedFormatted = testLogger.formatMessage('INFO', 'Test', {});
  
  runner.assert(!clearedFormatted.correlationId, 'Context should be cleared');
  runner.assert(!clearedFormatted.userId, 'Context should be cleared');
});

// Test correlation ID generation
runner.test('Correlation ID generation should create unique IDs', () => {
  const testLogger = new Logger({ enableConsole: false, enableFile: false });
  
  const id1 = testLogger.generateCorrelationId();
  const id2 = testLogger.generateCorrelationId();
  
  runner.assert(id1 !== id2, 'Should generate unique correlation IDs');
  runner.assert(id1.length === 32, 'Should generate 32-character hex strings');
  runner.assert(/^[a-f0-9]+$/.test(id1), 'Should generate valid hex strings');
});

// Test file logging
runner.test('File logging should create and write to log files', () => {
  const testLogger = new Logger({
    enableConsole: false,
    enableFile: true,
    logDir: runner.testLogDir,
    serviceName: 'test-logger'
  });
  
  testLogger.info('Test file log message', { testData: 'value' });
  
  const logFile = path.join(runner.testLogDir, 'test-logger.log');
  runner.assert(fs.existsSync(logFile), 'Log file should be created');
  
  const logContent = fs.readFileSync(logFile, 'utf8');
  runner.assert(logContent.includes('Test file log message'), 'Log content should include message');
  runner.assert(logContent.includes('"testData":"value"'), 'Log content should include metadata');
});

// Test log rotation
runner.test('Log rotation should work when file exceeds size limit', () => {
  const testLogger = new Logger({
    enableConsole: false,
    enableFile: true,
    logDir: runner.testLogDir,
    serviceName: 'rotation-test',
    maxFileSize: 100 // Very small size to force rotation
  });
  
  // Write enough data to trigger rotation
  for (let i = 0; i < 10; i++) {
    testLogger.info(`Test message ${i}`, { data: 'x'.repeat(50) });
  }
  
  const logFile = path.join(runner.testLogDir, 'rotation-test.log');
  const rotatedFile = path.join(runner.testLogDir, 'rotation-test.log.1');
  
  runner.assert(fs.existsSync(logFile), 'Current log file should exist');
  runner.assert(fs.existsSync(rotatedFile), 'Rotated log file should exist');
});

// Test all log level methods
runner.test('All log level methods should work correctly', () => {
  const testLogger = new Logger({ level: 'TRACE', enableConsole: false, enableFile: false });
  let loggedLevels = [];
  
  const originalLog = testLogger.log;
  testLogger.log = function(level, message, meta) {
    loggedLevels.push(level);
    return originalLog.call(this, level, message, meta);
  };
  
  testLogger.error('Error message');
  testLogger.warn('Warning message');
  testLogger.info('Info message');
  testLogger.debug('Debug message');
  testLogger.trace('Trace message');
  
  runner.assert(loggedLevels.includes('ERROR'), 'Should log ERROR level');
  runner.assert(loggedLevels.includes('WARN'), 'Should log WARN level');
  runner.assert(loggedLevels.includes('INFO'), 'Should log INFO level');
  runner.assert(loggedLevels.includes('DEBUG'), 'Should log DEBUG level');
  runner.assert(loggedLevels.includes('TRACE'), 'Should log TRACE level');
});

// Test database logging
runner.test('Database logging should format correctly', () => {
  const testLogger = new Logger({ enableConsole: false, enableFile: false });
  let loggedMessages = [];
  let loggedMetas = [];
  
  const originalLog = testLogger.log;
  testLogger.log = function(level, message, meta) {
    loggedMessages.push(message);
    loggedMetas.push(meta);
    return originalLog.call(this, level, message, meta);
  };
  
  testLogger.logDatabase('SELECT', 'users', 45, { count: 150 });
  
  // Should log both database operation and metric
  runner.assert(loggedMessages.length >= 1, 'Should log at least one message');
  
  const dbMessage = loggedMessages.find(msg => msg.includes('Database SELECT on users'));
  const dbMeta = loggedMetas.find(meta => meta.operation === 'SELECT' && meta.table === 'users');
  
  runner.assert(dbMessage, 'Should format database message correctly');
  runner.assert(dbMeta.operation === 'SELECT', 'Should include operation in metadata');
  runner.assert(dbMeta.table === 'users', 'Should include table in metadata');
  runner.assert(dbMeta.duration === '45ms', 'Should include formatted duration');
  runner.assert(dbMeta.count === 150, 'Should include additional metadata');
});

// Test metric logging
runner.test('Metric logging should format correctly', () => {
  const testLogger = new Logger({ enableConsole: false, enableFile: false });
  let loggedMessage = '';
  let loggedMeta = {};
  
  const originalLog = testLogger.log;
  testLogger.log = function(level, message, meta) {
    loggedMessage = message;
    loggedMeta = meta;
    return originalLog.call(this, level, message, meta);
  };
  
  testLogger.logMetric('response_time', 250, 'ms', { endpoint: '/api/test' });
  
  runner.assert(loggedMessage.includes('Metric: response_time'), 'Should format metric message correctly');
  runner.assert(loggedMeta.metric === 'response_time', 'Should include metric name');
  runner.assert(loggedMeta.value === 250, 'Should include metric value');
  runner.assert(loggedMeta.unit === 'ms', 'Should include metric unit');
  runner.assert(loggedMeta.endpoint === '/api/test', 'Should include additional metadata');
});

// Test request middleware creation
runner.test('Request middleware should be created correctly', () => {
  const testLogger = new Logger({ enableConsole: false, enableFile: false });
  const middleware = testLogger.createRequestMiddleware();
  
  runner.assert(typeof middleware === 'function', 'Should return a middleware function');
  
  // Test middleware execution
  const mockReq = {
    method: 'GET',
    originalUrl: '/api/test',
    ip: '127.0.0.1',
    get: () => 'test-agent'
  };
  const mockRes = {
    end: function() {}
  };
  const mockNext = () => {};
  
  middleware(mockReq, mockRes, mockNext);
  
  runner.assert(mockReq.correlationId, 'Should add correlation ID to request');
  runner.assert(typeof mockReq.correlationId === 'string', 'Correlation ID should be a string');
});

// Test environment-based configuration
runner.test('Environment-based configuration should work', () => {
  const originalEnv = process.env.LOG_LEVEL;
  process.env.LOG_LEVEL = 'ERROR';
  
  // Create a new logger instance that uses environment configuration like the default logger
  const testLogger = new Logger({ 
    level: process.env.LOG_LEVEL || 'INFO'
  });
  runner.assert(testLogger.level === LOG_LEVELS.ERROR, 'Should respect LOG_LEVEL environment variable');
  
  // Restore original environment
  if (originalEnv) {
    process.env.LOG_LEVEL = originalEnv;
  } else {
    delete process.env.LOG_LEVEL;
  }
});

// Run all tests
if (require.main === module) {
  runner.run().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  });
}

module.exports = { LoggerTestRunner };
=======
 * Comprehensive test for the structured logging system
 * Validates all requirements from the enhancement specification
 */

const assert = require('assert');
const fs = require('fs');
const path = require('path');
const express = require('express');
const http = require('http');
const { Logger, logger, LOG_LEVELS } = require('../utils/logger');

class LoggerTest {
  constructor() {
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  test(name, testFunction) {
    try {
      testFunction();
      this.passed++;
      this.results.push({ name, status: 'PASS' });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.failed++;
      this.results.push({ name, status: 'FAIL', error: error.message });
      console.log(`❌ ${name} - ${error.message}`);
    }
  }

  async testAsync(name, testFunction) {
    try {
      await testFunction();
      this.passed++;
      this.results.push({ name, status: 'PASS' });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.failed++;
      this.results.push({ name, status: 'FAIL', error: error.message });
      console.log(`❌ ${name} - ${error.message}`);
    }
  }

  report() {
    console.log(`\n📊 Logger Test Results:`);
    console.log(`Total: ${this.passed + this.failed}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${((this.passed / (this.passed + this.failed)) * 100).toFixed(1)}%`);
  }
}

async function runLoggerTests() {
  console.log('🧪 Running Comprehensive Logger Tests...\n');
  
  const test = new LoggerTest();

  // Test 1: Log Levels Configuration
  test.test('LOG_LEVELS should have all required levels', () => {
    assert.strictEqual(LOG_LEVELS.ERROR, 0);
    assert.strictEqual(LOG_LEVELS.WARN, 1);
    assert.strictEqual(LOG_LEVELS.INFO, 2);
    assert.strictEqual(LOG_LEVELS.DEBUG, 3);
    assert.strictEqual(LOG_LEVELS.TRACE, 4);
  });

  // Test 2: Default Logger Configuration
  test.test('Default logger should have correct configuration', () => {
    assert.strictEqual(logger.level, LOG_LEVELS.INFO);
    assert.strictEqual(logger.serviceName, 'cvleap-server');
    assert.strictEqual(logger.enableConsole, true);
    assert.strictEqual(logger.enableFile, false);
  });

  // Test 3: Logger Constructor Options
  test.test('Logger constructor should accept all options', () => {
    const customLogger = new Logger({
      level: 'DEBUG',
      serviceName: 'test-service',
      enableConsole: false,
      enableFile: true,
      logDir: './test-logs',
      maxFileSize: 5 * 1024 * 1024, // 5MB
      maxFiles: 3
    });
    
    assert.strictEqual(customLogger.level, LOG_LEVELS.DEBUG);
    assert.strictEqual(customLogger.serviceName, 'test-service');
    assert.strictEqual(customLogger.enableConsole, false);
    assert.strictEqual(customLogger.enableFile, true);
    assert.strictEqual(customLogger.maxFileSize, 5 * 1024 * 1024);
    assert.strictEqual(customLogger.maxFiles, 3);
  });

  // Test 4: Basic Logging Methods
  test.test('All log level methods should exist', () => {
    const methods = ['error', 'warn', 'info', 'debug', 'trace'];
    methods.forEach(method => {
      assert.strictEqual(typeof logger[method], 'function');
    });
  });

  // Test 5: Message Formatting
  test.test('formatMessage should create structured log objects', () => {
    const formatted = logger.formatMessage('INFO', 'Test message', { userId: 123 });
    
    assert.strictEqual(typeof formatted.timestamp, 'string');
    assert.strictEqual(formatted.level, 'INFO');
    assert.strictEqual(formatted.service, 'cvleap-server');
    assert.strictEqual(formatted.message, 'Test message');
    assert.strictEqual(formatted.userId, 123);
    assert.strictEqual(typeof formatted.pid, 'number');
    assert.strictEqual(typeof formatted.environment, 'string');
  });

  // Test 6: Log Level Filtering
  test.test('Log level filtering should work correctly', () => {
    const debugLogger = new Logger({ level: 'DEBUG', enableConsole: false });
    
    // Mock the log method to capture calls
    let logCalled = false;
    const originalFormatMessage = debugLogger.formatMessage;
    debugLogger.formatMessage = (...args) => {
      logCalled = true;
      return originalFormatMessage.apply(debugLogger, args);
    };
    
    debugLogger.trace('This should be filtered out');
    assert.strictEqual(logCalled, false, 'TRACE should be filtered at DEBUG level');
    
    debugLogger.debug('This should pass through');
    assert.strictEqual(logCalled, true, 'DEBUG should pass at DEBUG level');
  });

  // Test 7: File Logging
  await test.testAsync('File logging should create and write to log files', async () => {
    const testLogDir = './test-logger-files';
    const fileLogger = new Logger({
      level: 'INFO',
      enableConsole: false,
      enableFile: true,
      logDir: testLogDir,
      serviceName: 'test-file-service'
    });

    fileLogger.info('Test file log message', { testData: 'value' });
    
    const logFile = path.join(testLogDir, 'test-file-service.log');
    assert.strictEqual(fs.existsSync(logFile), true, 'Log file should be created');
    
    const content = fs.readFileSync(logFile, 'utf8');
    const logEntry = JSON.parse(content.trim());
    assert.strictEqual(logEntry.level, 'INFO');
    assert.strictEqual(logEntry.message, 'Test file log message');
    assert.strictEqual(logEntry.testData, 'value');
    
    // Cleanup
    fs.unlinkSync(logFile);
    fs.rmdirSync(testLogDir);
  });

  // Test 8: Performance Logging Methods
  test.test('logDatabase should format database operations correctly', () => {
    const testLogger = new Logger({ enableConsole: false, enableFile: false });
    let capturedLog = null;
    
    // Mock the log method
    testLogger.log = (level, message, meta) => {
      capturedLog = { level, message, meta };
    };
    
    testLogger.logDatabase('SELECT', 'users', 150, { rows: 25 });
    
    assert.strictEqual(capturedLog.level, 'DEBUG');
    assert.strictEqual(capturedLog.message, 'Database SELECT on users');
    assert.strictEqual(capturedLog.meta.operation, 'SELECT');
    assert.strictEqual(capturedLog.meta.table, 'users');
    assert.strictEqual(capturedLog.meta.duration, '150ms');
    assert.strictEqual(capturedLog.meta.rows, 25);
  });

  test.test('logMetric should format metrics correctly', () => {
    const testLogger = new Logger({ enableConsole: false, enableFile: false });
    let capturedLog = null;
    
    // Mock the log method
    testLogger.log = (level, message, meta) => {
      capturedLog = { level, message, meta };
    };
    
    testLogger.logMetric('response_time', 250, 'ms', { endpoint: '/api/users' });
    
    assert.strictEqual(capturedLog.level, 'INFO');
    assert.strictEqual(capturedLog.message, 'Metric: response_time');
    assert.strictEqual(capturedLog.meta.metric, 'response_time');
    assert.strictEqual(capturedLog.meta.value, 250);
    assert.strictEqual(capturedLog.meta.unit, 'ms');
    assert.strictEqual(capturedLog.meta.endpoint, '/api/users');
  });

  // Test 9: Request Middleware
  await test.testAsync('createRequestMiddleware should work with Express', async () => {
    return new Promise((resolve, reject) => {
      const app = express();
      
      // Capture logged requests
      let requestLog = null;
      const testLogger = new Logger({ 
        enableConsole: false, 
        enableFile: false,
        level: 'DEBUG'
      });
      testLogger.logRequest = (req, res, duration) => {
        requestLog = { req, res, duration };
      };
      
      app.use(testLogger.createRequestMiddleware());
      app.get('/test-route', (req, res) => {
        res.json({ success: true });
      });
      
      const server = app.listen(0, () => {
        const port = server.address().port;
        
        const req = http.get(`http://localhost:${port}/test-route`, (res) => {
          setTimeout(() => {
            assert.notStrictEqual(requestLog, null, 'Request should be logged');
            assert.strictEqual(requestLog.req.method, 'GET');
            assert.strictEqual(requestLog.req.originalUrl, '/test-route');
            assert.strictEqual(requestLog.res.statusCode, 200);
            assert.strictEqual(typeof requestLog.duration, 'number');
            
            server.close();
            resolve();
          }, 50); // Give time for middleware to complete
        });
        
        req.on('error', (err) => {
          server.close();
          reject(err);
        });
      });
    });
  });

  // Test 10: Expected Usage Patterns from Problem Statement
  test.test('Expected usage patterns should work correctly', () => {
    // Test the exact usage patterns from the problem statement
    
    // Basic logging
    logger.info('User logged in', { userId: 123, ip: '***********' });
    logger.error('Database connection failed', { error: 'Connection timeout' });
    
    // Performance logging
    logger.logDatabase('SELECT', 'users', 45, { count: 150 });
    logger.logMetric('response_time', 250, 'ms');
    
    // Request logging middleware (already tested above)
    const middleware = logger.createRequestMiddleware();
    assert.strictEqual(typeof middleware, 'function');
    
    // All should execute without errors
    assert.ok(true, 'All expected usage patterns work');
  });

  test.report();
  return test.passed === (test.passed + test.failed);
}

// Export for use in other test files
module.exports = { LoggerTest, runLoggerTests };

// Run tests if this file is executed directly
if (require.main === module) {
  runLoggerTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}
