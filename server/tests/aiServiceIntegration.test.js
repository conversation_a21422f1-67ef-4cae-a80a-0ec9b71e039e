const MultiModelAIClient = require('../multiModelAIClient');

describe('AI Service Integration Tests', () => {
  let client;
  let originalEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Set test environment variables
    process.env.OPENAI_API_KEY = 'test-openai-key';
    process.env.ANTHROPIC_API_KEY = 'test-anthropic-key';
    process.env.GOOGLE_AI_API_KEY = 'test-google-key';
    process.env.GROQ_API_KEY = 'test-groq-key';
    process.env.NOVITA_API_KEY = 'test-novita-key';
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  describe('Error Handling', () => {
    test('should handle authentication errors gracefully', async () => {
      // Mock authentication failure
      const mockClient = {
        chat: {
          completions: {
            create: jest.fn().mockRejectedValue({
              status: 401,
              message: 'Invalid API key'
            })
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockClient;

      await expect(client.generateWithModel('openai', 'test prompt'))
        .rejects.toThrow('OpenAI API authentication failed');
    });

    test('should handle rate limiting with proper error messages', async () => {
      const mockClient = {
        messages: {
          create: jest.fn().mockRejectedValue({
            status: 429,
            message: 'Rate limit exceeded'
          })
        }
      };

      client = new MultiModelAIClient();
      client.clients.claude = mockClient;

      await expect(client.generateWithModel('claude', 'test prompt'))
        .rejects.toThrow('Claude API rate limit exceeded');
    });

    test('should handle network timeouts', async () => {
      const mockClient = {
        getGenerativeModel: jest.fn().mockReturnValue({
          generateContent: jest.fn().mockRejectedValue(new Error('TIMEOUT'))
        })
      };

      client = new MultiModelAIClient();
      client.clients.gemini = mockClient;

      await expect(client.generateWithModel('gemini', 'test prompt'))
        .rejects.toThrow('TIMEOUT');
    });

    test('should handle malformed responses', async () => {
      const mockClient = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue({
              choices: [] // Empty choices array
            })
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.groq = mockClient;

      await expect(client.generateWithModel('groq', 'test prompt'))
        .rejects.toThrow();
    });
  });

  describe('Configuration Validation', () => {
    test('should reject invalid model configurations', async () => {
      client = new MultiModelAIClient();

      await expect(client.generateWithModel('invalid-model', 'test prompt'))
        .rejects.toThrow('Model invalid-model is not available');
    });

    test('should handle missing client initialization', async () => {
      client = new MultiModelAIClient();
      client.clients = {}; // Clear all clients

      await expect(client.generateWithModel('openai', 'test prompt'))
        .rejects.toThrow('Client for model openai is not initialized');
    });

    test('should validate environment variables', () => {
      delete process.env.OPENAI_API_KEY;
      delete process.env.ANTHROPIC_API_KEY;
      delete process.env.GOOGLE_AI_API_KEY;
      delete process.env.GROQ_API_KEY;
      delete process.env.NOVITA_API_KEY;

      client = new MultiModelAIClient();
      const availableModels = client.getAvailableModels();

      expect(availableModels).toHaveLength(0);
    });
  });

  describe('Fallback Behavior', () => {
    test('should use fallback model when primary model fails', async () => {
      const mockOpenAI = {
        chat: {
          completions: {
            create: jest.fn()
              .mockRejectedValueOnce(new Error('Primary model failed'))
              .mockResolvedValueOnce({
                choices: [{ message: { content: 'Fallback success' } }]
              })
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockOpenAI;

      const result = await client.generateOpenAI(
        mockOpenAI, 
        { model: 'gpt-4o', fallback: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1500 },
        'test prompt',
        {}
      );

      expect(result).toBe('Fallback success');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    test('should exhaust all models in fallback chain', async () => {
      const mockOpenAI = {
        chat: {
          completions: {
            create: jest.fn().mockRejectedValue(new Error('OpenAI failed'))
          }
        }
      };

      const mockAnthropic = {
        messages: {
          create: jest.fn().mockRejectedValue(new Error('Claude failed'))
        }
      };

      const mockGemini = {
        getGenerativeModel: jest.fn().mockReturnValue({
          generateContent: jest.fn().mockResolvedValue({
            response: {
              text: jest.fn().mockReturnValue('Gemini success')
            }
          })
        })
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockOpenAI;
      client.clients.claude = mockAnthropic;
      client.clients.gemini = mockGemini;

      const result = await client.generateWithFallback('test prompt');

      expect(result.content).toBe('Gemini success');
      expect(result.modelUsed).toBe('gemini');
    });
  });

  describe('Performance and Reliability', () => {
    test('should implement exponential backoff correctly', async () => {
      const startTime = Date.now();
      
      const mockClient = {
        chat: {
          completions: {
            create: jest.fn()
              .mockRejectedValueOnce(new Error('Retry 1'))
              .mockRejectedValueOnce(new Error('Retry 2'))
              .mockResolvedValueOnce({
                choices: [{ message: { content: 'Success after retries' } }]
              })
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockClient;

      const result = await client.generateWithFallback('test prompt', {
        preferredModel: 'openai',
        maxRetries: 3
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result.content).toBe('Success after retries');
      expect(result.attempt).toBe(3);
      // Should take at least 3 seconds due to exponential backoff (1s + 2s)
      expect(duration).toBeGreaterThan(2900);
    });

    test('should handle concurrent requests', async () => {
      const mockClient = {
        chat: {
          completions: {
            create: jest.fn().mockImplementation((params) => 
              Promise.resolve({
                choices: [{ message: { content: `Response to: ${params.messages[0].content}` } }]
              })
            )
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockClient;

      const promises = Array.from({ length: 5 }, (_, i) =>
        client.generateWithFallback(`Prompt ${i}`, { preferredModel: 'openai' })
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach((result, i) => {
        expect(result.content).toBe(`Response to: Prompt ${i}`);
      });
    });
  });

  describe('Model-Specific Features', () => {
    test('should handle Claude-specific message format', async () => {
      const mockClient = {
        messages: {
          create: jest.fn().mockResolvedValue({
            content: [{ text: 'Claude response' }]
          })
        }
      };

      client = new MultiModelAIClient();
      client.clients.claude = mockClient;

      const result = await client.generateWithModel('claude', 'test prompt', {
        temperature: 0.5,
        maxTokens: 2000
      });

      expect(result).toBe('Claude response');
      expect(mockClient.messages.create).toHaveBeenCalledWith({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 2000,
        temperature: 0.5,
        messages: [{ role: 'user', content: 'test prompt' }]
      });
    });

    test('should handle Gemini generation config', async () => {
      const mockModel = {
        generateContent: jest.fn().mockResolvedValue({
          response: {
            text: jest.fn().mockReturnValue('Gemini response')
          }
        })
      };

      const mockClient = {
        getGenerativeModel: jest.fn().mockReturnValue(mockModel)
      };

      client = new MultiModelAIClient();
      client.clients.gemini = mockClient;

      await client.generateWithModel('gemini', 'test prompt', {
        temperature: 0.8,
        maxTokens: 3000
      });

      expect(mockClient.getGenerativeModel).toHaveBeenCalledWith({
        model: 'gemini-1.5-pro',
        generationConfig: {
          temperature: 0.8,
          maxOutputTokens: 3000
        }
      });
    });
  });

  describe('Integration with CVLeap Application', () => {
    test('should maintain compatibility with existing AI service calls', async () => {
      const mockClient = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue({
              choices: [{ message: { content: 'Resume enhancement complete' } }]
            })
          }
        }
      };

      client = new MultiModelAIClient();
      client.clients.openai = mockClient;

      // Simulate a typical CVLeap AI service call
      const result = await client.generateWithFallback(
        'Enhance this resume for a software engineer position...',
        {
          preferredModel: 'openai',
          temperature: 0.7,
          maxTokens: 2000
        }
      );

      expect(result.content).toBe('Resume enhancement complete');
      expect(result.modelUsed).toBe('openai');
      expect(result.success).toBe(true);
    });
  });
});
