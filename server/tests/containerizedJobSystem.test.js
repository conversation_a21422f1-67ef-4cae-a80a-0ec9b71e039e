#!/usr/bin/env node

/**
 * Test script for containerized job execution system
 */

const ContainerService = require('../services/containerService');
const TerminalService = require('../services/terminalService');
const ApprovalService = require('../services/approvalService');
const JobExecutionAuditService = require('../services/jobExecutionAuditService');

async function testContainerizedJobSystem() {
  console.log('🧪 Testing Containerized Job Execution System\n');

  try {
    // Test 1: Container Service
    console.log('1️⃣ Testing Container Service...');
    const containerService = new ContainerService();
    
    const healthCheck = await containerService.healthCheck();
    console.log('   Container health:', healthCheck.status);
    
    // Test 2: Terminal Service
    console.log('\n2️⃣ Testing Terminal Service...');
    const terminalService = new TerminalService();
    
    const terminalId = terminalService.createTerminal('test-container-123', 'test-user-456');
    console.log('   Created terminal:', terminalId);
    
    // Simulate terminal output
    terminalService.sendOutput(terminalId, {
      type: 'output',
      content: 'Test output message',
      source: 'container'
    });
    
    const terminalInfo = terminalService.getTerminal(terminalId);
    console.log('   Terminal info:', {
      id: terminalInfo.id,
      status: terminalInfo.status,
      historyLength: terminalInfo.historyLength
    });

    // Test 3: Approval Service
    console.log('\n3️⃣ Testing Approval Service...');
    const approvalService = new ApprovalService();
    
    const testJobData = {
      id: 'test-job-123',
      jobTitle: 'Senior Software Engineer',
      company: 'Test Company Inc.',
      jobUrl: 'https://example.com/jobs/123',
      platform: 'linkedin'
    };
    
    const approval = await approvalService.createApprovalRequest(
      testJobData,
      'test-user-456',
      { confidenceScore: 0.7 }
    );
    
    console.log('   Created approval:', {
      id: approval.id,
      status: approval.status,
      requiresApproval: approval.analysisResult.requiresApproval,
      reasons: approval.analysisResult.reasons
    });

    // Test 4: Audit Service
    console.log('\n4️⃣ Testing Audit Service...');
    const auditService = new JobExecutionAuditService();
    
    // Log some test events
    auditService.logJobApproval('SUBMITTED', 'test-user-456', testJobData);
    auditService.logContainerExecution('STARTED', 'test-user-456', 'test-container-123', testJobData);
    auditService.logTerminalAccess('CONNECTED', 'test-user-456', terminalId, 'test-container-123');
    
    const auditTrail = auditService.getJobAuditTrail(testJobData.id);
    console.log('   Audit trail entries:', auditTrail.length);

    // Test 5: Service Statistics
    console.log('\n5️⃣ Service Statistics:');
    console.log('   Terminal stats:', terminalService.getStats());
    console.log('   Approval stats:', approvalService.getStats());
    console.log('   Audit stats:', auditService.getStats());

    // Test 6: Cleanup
    console.log('\n6️⃣ Testing Cleanup...');
    terminalService.closeTerminal(terminalId, 'test_complete');
    approvalService.cleanup();
    auditService.cleanup(1); // 1 day retention for test
    
    console.log('\n✅ All tests completed successfully!');
    
    // Test results summary
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Container Service - Health check passed');
    console.log('   ✅ Terminal Service - Terminal creation and output streaming');
    console.log('   ✅ Approval Service - Job approval workflow');
    console.log('   ✅ Audit Service - Event logging and trail generation');
    console.log('   ✅ Integration - All services working together');
    
    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Additional test for container creation (without Docker dependency)
async function testContainerLogic() {
  console.log('\n🐳 Testing Container Logic (without Docker)...');
  
  try {
    const containerService = new ContainerService();
    
    // Test script generation
    const testJobData = {
      id: 'test-job-789',
      jobTitle: 'Frontend Developer',
      company: 'Web Company Ltd.',
      jobUrl: 'https://webcompany.com/careers/frontend'
    };
    
    const script = containerService.generateJobScript(testJobData);
    console.log('   Generated script length:', script.length, 'characters');
    console.log('   Script contains job data:', script.includes(testJobData.company));
    
    // Test container info storage
    const testContainerInfo = {
      id: 'test-container-456',
      name: 'cvleap-job-test-456',
      status: 'running',
      startTime: new Date(),
      logs: [],
      jobData: testJobData
    };
    
    // Simulate container tracking
    containerService.activeContainers.set('test-container-456', testContainerInfo);
    
    const retrievedInfo = containerService.getContainerInfo('test-container-456');
    console.log('   Container info stored and retrieved successfully');
    
    const activeContainers = containerService.listActiveContainers();
    console.log('   Active containers count:', activeContainers.length);
    
    console.log('   ✅ Container logic tests passed');
    
  } catch (error) {
    console.error('   ❌ Container logic test failed:', error.message);
  }
}

// Run the tests
async function main() {
  console.log('🚀 CVLeap Containerized Job Execution System Tests\n');
  console.log('=' .repeat(60));
  
  const testResult = await testContainerizedJobSystem();
  await testContainerLogic();
  
  console.log('\n' + '='.repeat(60));
  console.log(testResult ? '🎉 All tests PASSED!' : '💥 Some tests FAILED!');
  
  process.exit(testResult ? 0 : 1);
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

if (require.main === module) {
  main();
}

module.exports = { testContainerizedJobSystem, testContainerLogic };