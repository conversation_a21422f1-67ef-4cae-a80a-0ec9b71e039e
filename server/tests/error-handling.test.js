/**
 * Comprehensive test suite for enhanced error handling system
 * Tests custom error classes, logging, async handling, and security features
 */

// Set required environment variables for testing
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret-key';
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

const assert = require('assert');

// Import error handling components
const {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  errorHandler,
  asyncHandler,
  notFoundHandler,
  logError,
  setupGlobalErrorHandlers
} = require('../middleware/errorHandler');

const { generateRequestId, getRequestDuration } = require('../middleware/requestId');

/**
 * Test runner with enhanced assertions
 */
class ErrorHandlingTestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  test(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  async run() {
    console.log(`\n🧪 Running Error Handling Tests (${this.tests.length} tests)...\n`);

    for (const { name, testFunction } of this.tests) {
      try {
        const startTime = Date.now();
        await testFunction();
        const duration = Date.now() - startTime;
        
        this.passed++;
        this.results.push({ name, status: 'PASS', duration });
        console.log(`✅ ${name} (${duration}ms)`);
      } catch (error) {
        this.failed++;
        this.results.push({ name, status: 'FAIL', error: error.message });
        console.log(`❌ ${name} - ${error.message}`);
      }
    }

    console.log(`\n📊 Error Handling Test Results:`);
    console.log(`Total: ${this.tests.length}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${((this.passed / this.tests.length) * 100).toFixed(1)}%`);

    return {
      total: this.tests.length,
      passed: this.passed,
      failed: this.failed,
      results: this.results
    };
  }
}

const runner = new ErrorHandlingTestRunner();

// Test custom error classes
runner.test('AppError should create error with proper structure', () => {
  const error = new AppError('Test error', 500, 'TEST_ERROR');
  assert.strictEqual(error.message, 'Test error');
  assert.strictEqual(error.statusCode, 500);
  assert.strictEqual(error.errorCode, 'TEST_ERROR');
  assert.strictEqual(error.isOperational, true);
  assert(error.timestamp);
  assert(error instanceof Error);
});

runner.test('ValidationError should inherit from AppError', () => {
  const details = [{ field: 'email', message: 'Invalid email' }];
  const error = new ValidationError('Validation failed', details);
  assert.strictEqual(error.statusCode, 400);
  assert.strictEqual(error.errorCode, 'VALIDATION_ERROR');
  assert.deepStrictEqual(error.details, details);
  assert(error instanceof AppError);
});

runner.test('AuthenticationError should have correct defaults', () => {
  const error = new AuthenticationError();
  assert.strictEqual(error.message, 'Authentication failed');
  assert.strictEqual(error.statusCode, 401);
  assert.strictEqual(error.errorCode, 'AUTHENTICATION_ERROR');
});

runner.test('AuthorizationError should have correct defaults', () => {
  const error = new AuthorizationError();
  assert.strictEqual(error.message, 'Access denied');
  assert.strictEqual(error.statusCode, 403);
  assert.strictEqual(error.errorCode, 'AUTHORIZATION_ERROR');
});

runner.test('NotFoundError should format resource message', () => {
  const error = new NotFoundError('User');
  assert.strictEqual(error.message, 'User not found');
  assert.strictEqual(error.statusCode, 404);
  assert.strictEqual(error.errorCode, 'NOT_FOUND_ERROR');
});

runner.test('ConflictError should have correct structure', () => {
  const error = new ConflictError('Email already exists');
  assert.strictEqual(error.message, 'Email already exists');
  assert.strictEqual(error.statusCode, 409);
  assert.strictEqual(error.errorCode, 'CONFLICT_ERROR');
});

runner.test('RateLimitError should have correct structure', () => {
  const error = new RateLimitError();
  assert.strictEqual(error.message, 'Rate limit exceeded');
  assert.strictEqual(error.statusCode, 429);
  assert.strictEqual(error.errorCode, 'RATE_LIMIT_ERROR');
});

runner.test('ExternalServiceError should include service name', () => {
  const error = new ExternalServiceError('PaymentAPI', 'Connection timeout');
  assert.strictEqual(error.message, 'PaymentAPI: Connection timeout');
  assert.strictEqual(error.statusCode, 502);
  assert.strictEqual(error.errorCode, 'EXTERNAL_SERVICE_ERROR');
  assert.strictEqual(error.service, 'PaymentAPI');
});

// Test async error handler
runner.test('asyncHandler should catch async errors', async () => {
  const testError = new Error('Async test error');
  let caughtError = null;

  const mockRoute = asyncHandler(async (req, res, next) => {
    throw testError;
  });

  const mockNext = (error) => {
    caughtError = error;
  };

  await mockRoute({}, {}, mockNext);
  assert.strictEqual(caughtError, testError);
});

runner.test('asyncHandler should call next for successful async operations', async () => {
  let nextCalled = false;

  const mockRoute = asyncHandler(async (req, res, next) => {
    // Simulate successful async operation
    await new Promise(resolve => setTimeout(resolve, 10));
    // Call next explicitly for successful operations
    return Promise.resolve();
  });

  const mockNext = () => {
    nextCalled = true;
  };

  await mockRoute({}, {}, mockNext);
  // For successful operations, next() may not be called since no error was thrown
  assert.strictEqual(nextCalled, false);
});

// Test request ID middleware
runner.test('generateRequestId should create unique request ID', () => {
  const req = { get: () => null };
  const res = { set: () => {} };
  let nextCalled = false;

  generateRequestId(req, res, () => {
    nextCalled = true;
  });

  assert(req.id);
  assert(req.requestId);
  assert(req.startTime);
  assert.strictEqual(nextCalled, true);
});

runner.test('generateRequestId should use existing X-Request-ID header', () => {
  const existingId = 'existing-request-id';
  const req = { 
    get: (header) => header.toLowerCase() === 'x-request-id' ? existingId : null 
  };
  const res = { set: () => {} };

  generateRequestId(req, res, () => {});

  assert.strictEqual(req.id, existingId);
  assert.strictEqual(req.requestId, existingId);
});

// Test error logging
runner.test('logError should create structured log data', () => {
  const error = new ValidationError('Test validation error');
  const req = {
    id: 'test-request-id',
    method: 'POST',
    originalUrl: '/api/test',
    get: (header) => {
      const headerMap = {
        'User-Agent': 'test-agent',
        'Content-Length': '100',
        'Referer': 'http://localhost'
      };
      return headerMap[header] || null;
    },
    ip: '127.0.0.1',
    user: { userId: 'user123' },
    startTime: process.hrtime.bigint()
  };

  // Capture console output
  const originalWarn = console.warn;
  let loggedData = null;
  console.warn = (label, data) => {
    if (label === 'WARNING:') {
      loggedData = JSON.parse(data);
    }
  };

  logError(error, req, 'warn');

  console.warn = originalWarn;

  assert(loggedData);
  assert.strictEqual(loggedData.level, 'warn');
  assert.strictEqual(loggedData.requestId, 'test-request-id');
  assert.strictEqual(loggedData.error.name, 'Error');
  assert.strictEqual(loggedData.request.method, 'POST');
  assert(loggedData.performance.requestDuration);
});

// Test error handler middleware
runner.test('errorHandler should handle custom errors properly', () => {
  const error = new ValidationError('Test validation', [{ field: 'email' }]);
  const req = { 
    id: 'test-id', 
    startTime: process.hrtime.bigint(),
    get: (header) => null
  };
  let responseData = null;
  let statusCode = null;

  const res = {
    status: (code) => {
      statusCode = code;
      return res;
    },
    json: (data) => {
      responseData = data;
    }
  };

  // Capture console output to avoid noise
  const originalWarn = console.warn;
  console.warn = () => {};

  errorHandler(error, req, res, () => {});

  console.warn = originalWarn;

  assert.strictEqual(statusCode, 400);
  assert.strictEqual(responseData.success, false);
  assert.strictEqual(responseData.error.code, 'VALIDATION_ERROR');
  assert.strictEqual(responseData.error.requestId, 'test-id');
  assert.deepStrictEqual(responseData.error.details, [{ field: 'email' }]);
});

runner.test('errorHandler should sanitize non-operational errors', () => {
  const error = new Error('Internal system error');
  error.isOperational = false;
  const req = { 
    startTime: process.hrtime.bigint(),
    get: (header) => null
  };
  let responseData = null;

  const res = {
    status: () => res,
    json: (data) => {
      responseData = data;
    }
  };

  // Capture console output to avoid noise
  const originalError = console.error;
  console.error = () => {};

  errorHandler(error, req, res, () => {});

  console.error = originalError;

  assert.strictEqual(responseData.error.message, 'Internal server error');
  assert.strictEqual(responseData.error.code, 'INTERNAL_ERROR');
});

// Test notFoundHandler
runner.test('notFoundHandler should create NotFoundError', () => {
  const req = { originalUrl: '/api/nonexistent' };
  let passedError = null;

  notFoundHandler(req, {}, (error) => {
    passedError = error;
  });

  assert(passedError instanceof NotFoundError);
  assert(passedError.message.includes('/api/nonexistent'));
});

// Test Express integration (simplified without supertest)
runner.test('Error response format should be correct', () => {
  const error = new ValidationError('Invalid input', [
    { field: 'email', message: 'Required' }
  ]);
  const req = { 
    id: 'test-request-id', 
    startTime: process.hrtime.bigint(),
    get: (header) => null
  };
  let responseData = null;
  let statusCode = null;

  const res = {
    status: (code) => {
      statusCode = code;
      return res;
    },
    json: (data) => {
      responseData = data;
    }
  };

  // Capture console output to avoid noise
  const originalWarn = console.warn;
  console.warn = () => {};

  errorHandler(error, req, res, () => {});

  console.warn = originalWarn;

  // Verify response structure matches requirements
  assert.strictEqual(statusCode, 400);
  assert.strictEqual(responseData.success, false);
  assert.strictEqual(responseData.error.code, 'VALIDATION_ERROR');
  assert.strictEqual(responseData.error.message, 'Invalid input');
  assert.strictEqual(responseData.error.requestId, 'test-request-id');
  assert(responseData.error.timestamp);
  assert.deepStrictEqual(responseData.error.details, [{ field: 'email', message: 'Required' }]);
});

// Run the tests
if (require.main === module) {
  runner.run().then(results => {
    process.exit(results.failed === 0 ? 0 : 1);
  }).catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runner, ErrorHandlingTestRunner };