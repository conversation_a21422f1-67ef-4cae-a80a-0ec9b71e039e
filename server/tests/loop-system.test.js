const loopService = require('../services/loopService');
const jobDiscovery = require('../services/jobDiscovery');

async function testLoopSystem() {
  console.log('🧪 Testing Loop Creation System...\n');

  try {
    // Test 1: Create a loop
    console.log('1. Testing loop creation...');
    const loopData = {
      name: 'Test Senior Developer Loop',
      description: 'Testing the loop creation system',
      configuration: {
        jobTitles: ['Senior Developer', 'Software Engineer'],
        locations: ['San Francisco', 'Remote'],
        industries: ['Technology'],
        maxApplicationsPerDay: 5,
        salaryMin: 100000,
        salaryMax: 150000,
        remoteOptions: 'any',
        excludedCompanies: ['BadCompany'],
        keywords: ['React', 'JavaScript']
      }
    };

    const createdLoop = await loopService.createLoop(1, loopData); // User ID 1
    console.log('✅ Loop created:', createdLoop.name);

    // Test 2: Get user loops
    console.log('\n2. Testing loop retrieval...');
    const userLoops = await loopService.getUserLoops(1);
    console.log(`✅ Found ${userLoops.length} loops for user`);

    // Test 3: Test job discovery
    console.log('\n3. Testing job discovery...');
    const mockConfig = {
      id: createdLoop.id,
      job_titles: JSON.stringify(['Senior Developer']),
      locations: JSON.stringify(['San Francisco']),
      industries: JSON.stringify(['Technology']),
      excluded_companies: JSON.stringify([]),
      keywords: JSON.stringify(['React']),
      salary_min: 100000,
      salary_max: 150000,
      remote_options: 'any'
    };

    const discoveredJobs = await jobDiscovery.discoverJobs(mockConfig);
    console.log(`✅ Discovered ${discoveredJobs.length} jobs`);
    
    if (discoveredJobs.length > 0) {
      console.log('   Sample job:', {
        title: discoveredJobs[0].title,
        company: discoveredJobs[0].company,
        relevanceScore: discoveredJobs[0].relevanceScore,
        platform: discoveredJobs[0].sourcePlatform
      });
    }

    // Test 4: Record loop metrics
    console.log('\n4. Testing analytics...');
    await loopService.recordLoopMetric(createdLoop.id, 'test_execution', 1, { test: true });
    console.log('✅ Analytics recorded');

    // Test 5: Get loop performance
    console.log('\n5. Testing performance retrieval...');
    const performance = await loopService.getLoopPerformance(1, createdLoop.id);
    console.log(`✅ Performance data retrieved: ${performance.analytics.length} metrics`);

    // Test 6: Update loop
    console.log('\n6. Testing loop update...');
    await loopService.updateLoop(1, createdLoop.id, {
      name: 'Updated Test Loop',
      description: 'Updated description',
      configuration: loopData.configuration,
      status: 'active'
    });
    console.log('✅ Loop updated successfully');

    // Test 7: Get discovered jobs
    console.log('\n7. Testing discovered jobs retrieval...');
    const recentJobs = await loopService.getDiscoveredJobs(1, 10);
    console.log(`✅ Retrieved ${recentJobs.length} recent discovered jobs`);

    console.log('\n🎉 All tests passed! Loop Creation System is working correctly.\n');

    return {
      success: true,
      tests: 7,
      loopId: createdLoop.id,
      jobsDiscovered: discoveredJobs.length
    };

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testLoopSystem()
    .then(result => {
      if (result.success) {
        console.log('Test Summary:');
        console.log(`- Tests run: ${result.tests}`);
        console.log(`- Loop ID: ${result.loopId}`);
        console.log(`- Jobs discovered: ${result.jobsDiscovered}`);
        process.exit(0);
      } else {
        console.error('Tests failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testLoopSystem;