const JobDiscoveryService = require('../services/jobDiscovery');
const axios = require('axios');

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('Enhanced Job Discovery Service', () => {
  let jobDiscoveryService;
  let mockLoopConfig;

  beforeEach(() => {
    jobDiscoveryService = new JobDiscoveryService();
    
    mockLoopConfig = {
      loop_id: 1,
      job_titles: '["Software Engineer", "Frontend Developer"]',
      locations: '["San Francisco", "Remote"]',
      industries: '["Technology", "Software"]',
      excluded_companies: '["BadCompany"]',
      keywords: '["React", "JavaScript"]',
      salary_min: 80000,
      salary_max: 150000,
      remote_options: 'remote_ok',
      experience_level: 'mid',
      job_type: 'fulltime'
    };

    // Clear cache and reset stats
    jobDiscoveryService.cache.clear();
    jobDiscoveryService.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      rateLimitHits: 0
    };

    jest.clearAllMocks();
  });

  describe('Configuration Parsing', () => {
    test('should parse loop configuration correctly', () => {
      const criteria = jobDiscoveryService.parseLoopConfig(mockLoopConfig);
      
      expect(criteria.jobTitles).toEqual(['Software Engineer', 'Frontend Developer']);
      expect(criteria.locations).toEqual(['San Francisco', 'Remote']);
      expect(criteria.industries).toEqual(['Technology', 'Software']);
      expect(criteria.excludedCompanies).toEqual(['BadCompany']);
      expect(criteria.keywords).toEqual(['React', 'JavaScript']);
      expect(criteria.salaryMin).toBe(80000);
      expect(criteria.salaryMax).toBe(150000);
    });

    test('should handle invalid JSON in configuration', () => {
      const invalidConfig = {
        ...mockLoopConfig,
        job_titles: 'invalid json'
      };

      expect(() => {
        jobDiscoveryService.parseLoopConfig(invalidConfig);
      }).toThrow('Invalid loop configuration');
    });

    test('should validate search criteria', () => {
      const validCriteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco'],
        salaryMin: 80000,
        salaryMax: 150000
      };

      expect(jobDiscoveryService.validateSearchCriteria(validCriteria)).toBe(true);

      const invalidCriteria = {
        jobTitles: [],
        locations: ['San Francisco']
      };

      expect(() => {
        jobDiscoveryService.validateSearchCriteria(invalidCriteria);
      }).toThrow('At least one job title is required');
    });
  });

  describe('Platform Integration', () => {
    test('should search Indeed API successfully', async () => {
      const mockIndeedResponse = {
        data: {
          results: [
            {
              jobkey: '123',
              jobtitle: 'Software Engineer',
              company: 'TechCorp',
              formattedLocation: 'San Francisco, CA',
              snippet: 'Great opportunity for a software engineer',
              url: 'https://indeed.com/job/123',
              date: '2024-01-15',
              salary: '$120,000'
            }
          ]
        }
      };

      mockedAxios.get.mockResolvedValue(mockIndeedResponse);

      const criteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco'],
        jobType: 'fulltime'
      };

      const jobs = await jobDiscoveryService.searchIndeed(criteria);

      expect(jobs).toHaveLength(1);
      expect(jobs[0].title).toBe('Software Engineer');
      expect(jobs[0].company).toBe('TechCorp');
      expect(jobs[0].sourcePlatform).toBe('indeed');
      expect(jobs[0].externalId).toBe('indeed_123');
    });

    test('should search RemoteOK API successfully', async () => {
      const mockRemoteOKResponse = {
        data: [
          { id: 'header' }, // RemoteOK includes a header object
          {
            id: '456',
            position: 'Frontend Developer',
            company: 'RemoteCorp',
            description: 'Remote frontend position',
            url: 'https://remoteok.io/remote-jobs/456',
            date: '2024-01-15',
            tags: ['react', 'javascript']
          }
        ]
      };

      mockedAxios.get.mockResolvedValue(mockRemoteOKResponse);

      const criteria = {
        jobTitles: ['Frontend Developer'],
        locations: ['Remote']
      };

      const jobs = await jobDiscoveryService.searchRemoteOK(criteria);

      expect(jobs).toHaveLength(1);
      expect(jobs[0].title).toBe('Frontend Developer');
      expect(jobs[0].company).toBe('RemoteCorp');
      expect(jobs[0].location).toBe('Remote');
      expect(jobs[0].sourcePlatform).toBe('remoteok');
    });

    test('should handle API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      const criteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco']
      };

      await expect(jobDiscoveryService.searchIndeed(criteria)).rejects.toThrow('Indeed API error');
    });

    test('should handle rate limiting', async () => {
      const error = new Error('Rate limited');
      error.response = { status: 429 };
      mockedAxios.get.mockRejectedValue(error);

      const criteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco']
      };

      await expect(jobDiscoveryService.searchIndeed(criteria)).rejects.toThrow('Indeed API rate limit exceeded');
    });
  });

  describe('Caching', () => {
    test('should cache API responses', async () => {
      const mockResponse = {
        data: {
          results: [
            {
              jobkey: '123',
              jobtitle: 'Software Engineer',
              company: 'TechCorp',
              formattedLocation: 'San Francisco, CA',
              snippet: 'Great opportunity',
              url: 'https://indeed.com/job/123',
              date: '2024-01-15'
            }
          ]
        }
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const criteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco']
      };

      // First call should hit API
      await jobDiscoveryService.searchPlatformWithRetry('indeed', criteria);
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);

      // Second call should use cache
      await jobDiscoveryService.searchPlatformWithRetry('indeed', criteria);
      expect(mockedAxios.get).toHaveBeenCalledTimes(1); // Still 1, not 2
      expect(jobDiscoveryService.stats.cacheHits).toBe(1);
    });

    test('should expire cached data', async () => {
      // Set a very short cache timeout for testing
      jobDiscoveryService.cacheTimeout = 1; // 1ms

      const mockResponse = {
        data: { results: [] }
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const criteria = {
        jobTitles: ['Software Engineer'],
        locations: ['San Francisco']
      };

      // First call
      await jobDiscoveryService.searchPlatformWithRetry('indeed', criteria);
      
      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Second call should hit API again
      await jobDiscoveryService.searchPlatformWithRetry('indeed', criteria);
      expect(mockedAxios.get).toHaveBeenCalledTimes(2);
    });
  });

  describe('Deduplication', () => {
    test('should remove duplicate jobs', () => {
      const jobs = [
        {
          externalId: 'indeed_123',
          title: 'Software Engineer',
          company: 'TechCorp',
          url: 'https://indeed.com/job/123'
        },
        {
          externalId: 'linkedin_456',
          title: 'Software Engineer',
          company: 'TechCorp',
          url: 'https://linkedin.com/job/456'
        },
        {
          externalId: 'indeed_789',
          title: 'Frontend Developer',
          company: 'WebCorp',
          url: 'https://indeed.com/job/789'
        }
      ];

      const uniqueJobs = jobDiscoveryService.removeDuplicates(jobs);
      
      expect(uniqueJobs).toHaveLength(2); // Should remove the duplicate TechCorp Software Engineer
      expect(uniqueJobs.find(job => job.company === 'WebCorp')).toBeDefined();
    });

    test('should detect similar job titles', () => {
      const similarity = jobDiscoveryService.calculateSimilarity(
        'Software Engineer',
        'Software Developer'
      );
      
      expect(similarity).toBeGreaterThan(0.7);
      
      const dissimilarity = jobDiscoveryService.calculateSimilarity(
        'Software Engineer',
        'Marketing Manager'
      );
      
      expect(dissimilarity).toBeLessThan(0.3);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits', async () => {
      const platform = 'indeed';
      const startTime = Date.now();

      // First request should go through immediately
      await jobDiscoveryService.enforceRateLimit(platform);
      const firstRequestTime = Date.now() - startTime;
      expect(firstRequestTime).toBeLessThan(100);

      // Second request should be delayed
      const secondStartTime = Date.now();
      await jobDiscoveryService.enforceRateLimit(platform);
      const secondRequestTime = Date.now() - secondStartTime;
      expect(secondRequestTime).toBeGreaterThan(900); // Should wait ~1000ms
    });
  });

  describe('Job Discovery Integration', () => {
    test('should discover jobs from multiple platforms', async () => {
      // Mock successful responses from multiple platforms
      const mockIndeedResponse = {
        data: {
          results: [
            {
              jobkey: '123',
              jobtitle: 'Software Engineer',
              company: 'TechCorp',
              formattedLocation: 'San Francisco, CA',
              snippet: 'Great opportunity',
              url: 'https://indeed.com/job/123',
              date: '2024-01-15'
            }
          ]
        }
      };

      const mockRemoteOKResponse = {
        data: [
          { id: 'header' },
          {
            id: '456',
            position: 'Frontend Developer',
            company: 'RemoteCorp',
            description: 'Remote position',
            url: 'https://remoteok.io/remote-jobs/456',
            date: '2024-01-15'
          }
        ]
      };

      mockedAxios.get
        .mockResolvedValueOnce(mockIndeedResponse)
        .mockResolvedValueOnce(mockRemoteOKResponse)
        .mockResolvedValue({ data: [] }); // For other platforms

      // Mock database operations
      jobDiscoveryService.scoreJobRelevance = jest.fn().mockResolvedValue([
        { title: 'Software Engineer', relevanceScore: 0.9 },
        { title: 'Frontend Developer', relevanceScore: 0.8 }
      ]);
      
      jobDiscoveryService.saveDiscoveredJobs = jest.fn().mockResolvedValue();

      const result = await jobDiscoveryService.discoverJobs(mockLoopConfig);

      expect(result.jobs).toBeDefined();
      expect(result.stats.totalFound).toBeGreaterThan(0);
      expect(jobDiscoveryService.stats.successfulRequests).toBeGreaterThan(0);
    });

    test('should handle complete API failures', async () => {
      // Mock all APIs to fail
      mockedAxios.get.mockRejectedValue(new Error('All APIs down'));

      await expect(jobDiscoveryService.discoverJobs(mockLoopConfig)).rejects.toThrow('No jobs found from any platform');
    });
  });

  describe('Statistics', () => {
    test('should track discovery statistics', () => {
      jobDiscoveryService.updateDiscoveryStats(5, 2000, []);
      
      expect(jobDiscoveryService.stats.totalRequests).toBe(1);
      expect(jobDiscoveryService.stats.successfulRequests).toBe(1);
      expect(jobDiscoveryService.stats.failedRequests).toBe(0);
    });

    test('should track failed discoveries', () => {
      jobDiscoveryService.updateDiscoveryStats(0, 1000, [{ platform: 'indeed', error: 'API down' }]);
      
      expect(jobDiscoveryService.stats.totalRequests).toBe(1);
      expect(jobDiscoveryService.stats.successfulRequests).toBe(0);
      expect(jobDiscoveryService.stats.failedRequests).toBe(1);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid loop configuration', async () => {
      const invalidConfig = {
        job_titles: 'invalid json',
        locations: '[]'
      };

      await expect(jobDiscoveryService.discoverJobs(invalidConfig)).rejects.toThrow('Invalid loop configuration');
    });

    test('should handle missing required fields', async () => {
      const incompleteConfig = {
        job_titles: '[]', // Empty job titles
        locations: '["San Francisco"]'
      };

      await expect(jobDiscoveryService.discoverJobs(incompleteConfig)).rejects.toThrow('At least one job title is required');
    });
  });
});
