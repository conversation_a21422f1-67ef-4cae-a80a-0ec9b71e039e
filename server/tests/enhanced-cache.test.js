/**
 * Enhanced Cache Service Tests
 * Tests for new enhanced caching features
 */

const assert = require('assert');
const cacheService = require('../cacheService');

/**
 * Simple test runner for enhanced cache features
 */
class EnhancedCacheTestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  test(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  async run() {
    console.log(`\n🧪 Running ${this.tests.length} enhanced cache tests...\n`);

    for (const { name, testFunction } of this.tests) {
      try {
        const startTime = Date.now();
        
        // Clear cache before each test
        cacheService.clear();
        
        await testFunction();
        const duration = Date.now() - startTime;
        
        this.passed++;
        this.results.push({ name, status: 'PASS', duration });
        console.log(`✅ ${name} (${duration}ms)`);
      } catch (error) {
        this.failed++;
        this.results.push({ name, status: 'FAIL', error: error.message });
        console.log(`❌ ${name} - ${error.message}`);
      }
    }

    this.printSummary();
    return this.results;
  }

  printSummary() {
    const total = this.passed + this.failed;
    const successRate = total > 0 ? (this.passed / total * 100).toFixed(1) : 0;
    
    console.log(`\n📊 Enhanced Cache Test Results:`);
    console.log(`Total: ${total}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${successRate}%\n`);
  }
}

const runner = new EnhancedCacheTestRunner();

// Test enhanced statistics
runner.test('Enhanced statistics should include performance metrics', () => {
  // Add some test data
  cacheService.set('test1', 'value1');
  cacheService.set('test2', 'value2');
  cacheService.get('test1'); // Create access
  
  const stats = cacheService.getStats();
  
  // Check new fields
  assert(stats.performance, 'Should have performance metrics');
  assert(stats.evictionStats, 'Should have eviction statistics');
  assert(stats.accessPatterns, 'Should have access patterns');
  assert(stats.efficiency.memoryFillRatio, 'Should have memory fill ratio');
  assert.strictEqual(typeof stats.maxMemoryBytes, 'number', 'Should have memory limit');
});

// Test eviction statistics tracking
runner.test('Eviction statistics should track different eviction types', () => {
  // Set a short TTL item that will expire
  cacheService.set('expiring', 'value', 1); // 1ms TTL
  
  // Wait for expiration
  setTimeout(() => {
    cacheService.get('expiring'); // This should trigger expired eviction
    
    const stats = cacheService.getStats();
    assert(stats.evictionStats.expiredEvictions >= 1, 'Should track expired evictions');
    assert(stats.evictionStats.totalEvictions >= 1, 'Should track total evictions');
  }, 10);
});

// Test memory-based eviction
runner.test('Memory-based eviction should work with configurable limits', () => {
  // Configure smaller memory limit for testing
  const originalConfig = cacheService.getConfiguration();
  cacheService.configure({ maxMemoryBytes: 1024 }); // 1KB limit
  
  // Add data to exceed memory limit
  for (let i = 0; i < 100; i++) {
    cacheService.set(`key${i}`, 'x'.repeat(100)); // Large values
  }
  
  const stats = cacheService.getStats();
  assert(stats.evictionStats.totalEvictions > 0, 'Should have triggered evictions due to memory');
  
  // Restore original config
  cacheService.configure(originalConfig);
});

// Test access pattern analysis
runner.test('Access pattern analysis should provide detailed insights', () => {
  // Create varied access patterns
  cacheService.set('hot1', 'value1');
  cacheService.set('hot2', 'value2');
  cacheService.set('cold1', 'value3');
  
  // Access hot keys multiple times
  for (let i = 0; i < 5; i++) {
    cacheService.get('hot1');
    cacheService.get('hot2');
  }
  
  // Access cold key once
  cacheService.get('cold1');
  
  const stats = cacheService.getStats();
  const patterns = stats.accessPatterns;
  
  assert(patterns.totalEntries === 3, 'Should count all entries');
  assert(patterns.accessDistribution, 'Should have access distribution');
  assert(patterns.frequencyTiers, 'Should have frequency tiers');
  assert(patterns.recentActivity, 'Should have recent activity data');
  
  // Check that we have hot and cold entries
  assert(patterns.frequencyTiers.hot > 0, 'Should have hot entries');
  assert(patterns.frequencyTiers.cold >= 0, 'Should track cold entries');
});

// Test enhanced hot key tracking
runner.test('Enhanced hot key tracking should provide detailed analytics', () => {
  // Create test data with different access patterns
  cacheService.set('popular', 'data1');
  cacheService.set('moderate', 'data2');
  cacheService.set('rare', 'data3');
  
  // Create access patterns
  for (let i = 0; i < 10; i++) cacheService.get('popular');
  for (let i = 0; i < 5; i++) cacheService.get('moderate');
  cacheService.get('rare');
  
  const hotKeys = cacheService.getHotKeys(3);
  
  assert(hotKeys.length === 3, 'Should return requested number of hot keys');
  assert(hotKeys[0].key === 'popular', 'Most accessed key should be first');
  assert(hotKeys[0].accessShare, 'Should include access share percentage');
  assert(hotKeys[0].lastAccessed, 'Should include last accessed time');
  assert(hotKeys[0].efficiency, 'Should include efficiency rating');
});

// Test performance timing
runner.test('Performance timing should measure cache operations', () => {
  // Perform several operations
  for (let i = 0; i < 10; i++) {
    cacheService.set(`perf${i}`, `value${i}`);
    cacheService.get(`perf${i}`);
  }
  
  const stats = cacheService.getStats();
  const perf = stats.performance;
  
  assert(perf.totalOperations > 0, 'Should track total operations');
  assert(typeof perf.avgOperationTime === 'string', 'Should have average operation time');
  assert(perf.avgOperationTime.includes('ms'), 'Should include time unit');
});

// Test configuration methods
runner.test('Configuration methods should allow runtime tuning', () => {
  const originalConfig = cacheService.getConfiguration();
  
  // Test configuration update
  cacheService.configure({
    maxCacheSize: 500,
    maxMemoryBytes: 10 * 1024 * 1024, // 10MB
    defaultTTL: 120000 // 2 minutes
  });
  
  const newConfig = cacheService.getConfiguration();
  assert.strictEqual(newConfig.maxCacheSize, 500, 'Should update max cache size');
  assert.strictEqual(newConfig.maxMemoryBytes, 10 * 1024 * 1024, 'Should update max memory');
  assert.strictEqual(newConfig.defaultTTL, 120000, 'Should update default TTL');
  assert.strictEqual(newConfig.maxMemoryMB, 10, 'Should calculate MB correctly');
  assert.strictEqual(newConfig.defaultTTLMinutes, 2, 'Should calculate minutes correctly');
  
  // Restore original config
  cacheService.configure(originalConfig);
});

// Test memory usage accuracy
runner.test('Memory usage estimation should be accurate and detailed', () => {
  // Add known data
  cacheService.set('small', 'abc');
  cacheService.set('medium', 'x'.repeat(100));
  cacheService.set('large', 'y'.repeat(1000));
  
  const memoryStats = cacheService.getStats().memoryUsage;
  
  assert(memoryStats.total.bytes > 0, 'Should calculate total bytes');
  assert(memoryStats.total.kilobytes, 'Should provide kilobytes');
  assert(memoryStats.total.megabytes, 'Should provide megabytes');
  assert(memoryStats.breakdown.keys, 'Should break down keys memory');
  assert(memoryStats.breakdown.values, 'Should break down values memory');
  assert(memoryStats.breakdown.metadata, 'Should break down metadata memory');
  assert(memoryStats.averageEntrySize, 'Should calculate average entry size');
});

// Test smart TTL enhancements
runner.test('Smart TTL should adapt to access patterns effectively', () => {
  // Set initial item
  cacheService.set('adaptive', 'data');
  
  // Access it multiple times to increase access count
  for (let i = 0; i < 10; i++) {
    cacheService.get('adaptive');
  }
  
  // Use smart TTL with base access count
  cacheService.setWithSmartTTL('adaptive', 'updated_data', 2);
  
  // Get item to verify it was updated
  const retrieved = cacheService.get('adaptive');
  assert.strictEqual(retrieved, 'updated_data', 'Should update with smart TTL');
  
  // Access patterns should show high access count
  const hotKeys = cacheService.getHotKeys(1);
  assert(hotKeys[0].accessCount > 10, 'Should maintain high access count');
});

// Test backward compatibility
runner.test('Enhanced cache should maintain backward compatibility', () => {
  // Test basic operations still work
  cacheService.set('compat_test', 'value', 5000);
  assert.strictEqual(cacheService.get('compat_test'), 'value', 'Basic get/set should work');
  assert.strictEqual(cacheService.has('compat_test'), true, 'Has method should work');
  
  // Test middleware still works
  const middleware = cacheService.middleware(1000);
  assert.strictEqual(typeof middleware, 'function', 'Middleware should return function');
  
  // Test utility methods
  assert.strictEqual(typeof cacheService.invalidatePattern, 'function', 'Pattern invalidation should exist');
  assert.strictEqual(typeof cacheService.getOrSet, 'function', 'GetOrSet should exist');
  assert.strictEqual(typeof cacheService.warmUp, 'function', 'WarmUp should exist');
});

// Run the tests
(async () => {
  try {
    await runner.run();
    process.exit(0);
  } catch (error) {
    console.error('Test runner error:', error);
    process.exit(1);
  }
})();