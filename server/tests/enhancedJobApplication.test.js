/**
 * Enhanced Job Application System Tests
 * Tests for the enhanced autonomous job submission features
 */

const assert = require('assert');
const EnhancedJobApplicationService = require('../enhancedJobApplicationService');
const EnhancedJobApplicationController = require('../enhancedJobApplicationController');
const BrowserAutomationEngine = require('../browserAutomationEngine');

class EnhancedJobApplicationTests {
  constructor() {
    this.testResults = [];
    this.service = new EnhancedJobApplicationService();
    this.controller = new EnhancedJobApplicationController();
    this.browserEngine = new BrowserAutomationEngine();
  }

  async runTest(testName, testFunction) {
    const startTime = Date.now();
    try {
      await testFunction();
      const duration = Date.now() - startTime;
      this.testResults.push({
        name: testName,
        status: 'PASSED',
        duration: `${duration}ms`
      });
      console.log(`✅ ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.testResults.push({
        name: testName,
        status: 'FAILED',
        duration: `${duration}ms`,
        error: error.message
      });
      console.log(`❌ ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('\n🧪 Running Enhanced Job Application System Tests...\n');

    // Browser Automation Engine Tests
    await this.runTest('BrowserAutomationEngine initialization', async () => {
      assert.strictEqual(typeof this.browserEngine.initialize, 'function');
      assert.strictEqual(typeof this.browserEngine.applyToJob, 'function');
      assert.strictEqual(typeof this.browserEngine.detectPlatform, 'function');
    });

    await this.runTest('Platform detection', async () => {
      assert.strictEqual(this.browserEngine.detectPlatform('https://www.linkedin.com/jobs/view/123'), 'linkedin');
      assert.strictEqual(this.browserEngine.detectPlatform('https://indeed.com/viewjob?jk=123'), 'indeed');
      assert.strictEqual(this.browserEngine.detectPlatform('https://glassdoor.com/job/123'), 'glassdoor');
      assert.strictEqual(this.browserEngine.detectPlatform('https://company.com/careers/123'), 'company_website');
    });

    await this.runTest('Browser configuration', async () => {
      const config = this.browserEngine.config;
      assert.ok(config.defaultTimeout > 0);
      assert.ok(config.viewport.width > 0);
      assert.ok(config.viewport.height > 0);
    });

    // Enhanced Service Tests
    await this.runTest('Enhanced service initialization', async () => {
      assert.ok(this.service.applicationQueue);
      assert.ok(this.service.config);
      assert.ok(this.service.rateLimits);
      assert.strictEqual(this.service.isProcessing, false);
    });

    await this.runTest('Application data validation', async () => {
      const validData = {
        userId: 'user123',
        jobId: 'job123',
        resumeId: 'resume123',
        jobUrl: 'https://linkedin.com/jobs/view/123',
        jobTitle: 'Software Engineer',
        company: 'Tech Corp'
      };

      const result = await this.service.validateApplicationData(validData);
      assert.strictEqual(result.valid, true);
      assert.strictEqual(result.errors.length, 0);
    });

    await this.runTest('Invalid application data rejection', async () => {
      const invalidData = {
        userId: 'user123',
        jobUrl: 'invalid-url',
        jobTitle: 'A', // Too short
        company: '' // Empty
      };

      const result = await this.service.validateApplicationData(invalidData);
      assert.strictEqual(result.valid, false);
      assert.ok(result.errors.length > 0);
    });

    await this.runTest('Compatibility score calculation', async () => {
      const testData = {
        jobTitle: 'Senior Software Engineer',
        company: 'Google',
        location: 'San Francisco'
      };

      const score = await this.service.calculateCompatibilityScore(testData);
      assert.ok(score >= 0 && score <= 1);
    });

    await this.runTest('Dynamic priority calculation', async () => {
      const testData = {
        jobTitle: 'Software Engineer',
        company: 'Apple'
      };

      const priority = this.service.calculateDynamicPriority(testData, 0.8);
      assert.ok(['critical', 'high', 'normal', 'low', 'very_low'].includes(priority));
    });

    await this.runTest('Rate limiting functionality', async () => {
      // Test rate limiting for LinkedIn
      assert.strictEqual(this.service.isRateLimited('linkedin'), false);
      
      // Simulate hitting rate limit
      for (let i = 0; i < 15; i++) {
        this.service.updateRateLimit('linkedin');
      }
      
      assert.strictEqual(this.service.isRateLimited('linkedin'), true);
    });

    await this.runTest('Optimal timing calculation', async () => {
      const testData = { jobTitle: 'Engineer', company: 'Test Corp' };
      const timing = this.service.getOptimalApplicationTiming(testData, 'linkedin');
      
      assert.ok(timing.delay >= 0);
      assert.ok(timing.reason);
    });

    await this.runTest('Queue management', async () => {
      const initialLength = this.service.applicationQueue.length;
      
      const testApplication = {
        userId: 'test-user',
        jobId: 'test-job',
        resumeId: 'test-resume',
        jobUrl: 'https://linkedin.com/jobs/view/test',
        jobTitle: 'Test Engineer',
        company: 'Test Company'
      };

      try {
        const queued = await this.service.queueApplication(testApplication);
        
        if (queued) { // Might be null if compatibility score is too low
          assert.ok(queued.id);
          assert.strictEqual(queued.status, 'queued');
          assert.ok(this.service.applicationQueue.length >= initialLength);
        }
      } catch (error) {
        // Expected if validation fails or score is too low
        assert.ok(error.message.includes('validation') || error.message.includes('compatibility'));
      }
    });

    await this.runTest('Queue status retrieval', async () => {
      const status = this.service.getEnhancedQueueStatus();
      
      assert.ok(typeof status.total === 'number');
      assert.ok(typeof status.isProcessing === 'boolean');
      assert.ok(typeof status.activeWorkers === 'number');
      assert.ok(status.statusBreakdown);
      assert.ok(status.platformBreakdown);
    });

    // Enhanced Controller Tests
    await this.runTest('Controller initialization', async () => {
      assert.ok(this.controller.legacyService);
      assert.ok(this.controller.enhancedService);
      assert.ok(typeof this.controller.useEnhancedService === 'boolean');
    });

    await this.runTest('Mock request handling', async () => {
      // Create mock request and response objects
      const mockReq = {
        body: {
          jobId: 'test-job',
          resumeId: 'test-resume',
          jobUrl: 'https://linkedin.com/jobs/view/test',
          jobTitle: 'Test Position',
          company: 'Test Corp'
        },
        user: { userId: 'test-user' },
        query: {}
      };

      let responseData = null;
      let statusCode = 200;

      const mockRes = {
        json: (data) => { responseData = data; },
        status: (code) => { 
          statusCode = code; 
          return { json: (data) => { responseData = data; } };
        }
      };

      try {
        await this.controller.getQueueStatus(mockReq, mockRes);
        assert.ok(responseData);
        assert.ok(responseData.status || responseData.error);
      } catch (error) {
        // Expected if service is not fully initialized
        assert.ok(error.message);
      }
    });

    await this.runTest('Health status check', async () => {
      const mockReq = { query: {} };
      let responseData = null;

      const mockRes = {
        json: (data) => { responseData = data; },
        status: (code) => ({ json: (data) => { responseData = data; } })
      };

      await this.controller.getHealthStatus(mockReq, mockRes);
      
      assert.ok(responseData);
      assert.ok(responseData.status);
      assert.ok(responseData.services);
    });

    // Configuration Tests
    await this.runTest('Rate limit configuration', async () => {
      const config = this.service.config.rateLimits;
      
      assert.ok(config.linkedin);
      assert.ok(config.indeed);
      assert.ok(config.glassdoor);
      assert.ok(config.company_website);
      
      Object.values(config).forEach(limit => {
        assert.ok(limit.requests > 0);
        assert.ok(limit.window > 0);
      });
    });

    await this.runTest('Anti-detection configuration', async () => {
      const antiDetection = this.service.config.antiDetection;
      
      assert.ok(antiDetection.randomDelayRange);
      assert.ok(antiDetection.userAgentRotationInterval > 0);
      assert.ok(antiDetection.maxApplicationsPerSession > 0);
    });

    await this.runTest('Quality filter configuration', async () => {
      const filters = this.service.config.qualityFilters;
      
      assert.ok(filters.minCompatibilityScore >= 0 && filters.minCompatibilityScore <= 1);
      assert.ok(typeof filters.skipLowQualityJobs === 'boolean');
      assert.ok(typeof filters.requiresSkillMatch === 'boolean');
    });

    // Performance Tests
    await this.runTest('Performance metrics collection', async () => {
      try {
        const stats = await this.service.getEnhancedApplicationStats('test-user');
        
        assert.ok(typeof stats.total === 'number');
        assert.ok(typeof stats.successful === 'number');
        assert.ok(typeof stats.failed === 'number');
        assert.ok(typeof stats.successRate === 'number');
        assert.ok(stats.platformBreakdown);
        assert.ok(stats.qualityMetrics);
      } catch (error) {
        // Expected if log file doesn't exist
        assert.ok(error.message || true);
      }
    });

    await this.runTest('Browser stats retrieval', async () => {
      const stats = this.browserEngine.getStats();
      
      assert.ok(typeof stats.browserActive === 'boolean');
      assert.ok(typeof stats.sessionsActive === 'number');
      assert.ok(stats.config);
    });

    // Error Handling Tests
    await this.runTest('Error handling for invalid URLs', async () => {
      const invalidApp = {
        userId: 'test-user',
        jobId: 'test-job',
        resumeId: 'test-resume',
        jobUrl: 'not-a-valid-url',
        jobTitle: 'Test Job',
        company: 'Test Company'
      };

      try {
        await this.service.queueApplication(invalidApp);
        assert.fail('Should have thrown validation error');
      } catch (error) {
        assert.ok(error.message.includes('validation') || error.message.includes('Invalid'));
      }
    });

    await this.runTest('Graceful degradation', async () => {
      // Test that system continues to work even if enhanced features fail
      const originalBrowserEngine = this.service.browserEngine;
      
      // Temporarily disable browser engine
      this.service.browserEngine = null;
      
      try {
        const status = this.service.getEnhancedQueueStatus();
        assert.ok(status); // Should still return status even without browser
      } finally {
        // Restore browser engine
        this.service.browserEngine = originalBrowserEngine;
      }
    });

    // Integration Tests
    await this.runTest('Service integration', async () => {
      // Test that controller properly integrates with both services
      assert.ok(this.controller.legacyService.applicationQueue);
      assert.ok(this.controller.enhancedService.applicationQueue);
      
      // Test service selection logic
      const mockReq = { body: { useEnhanced: true }, user: { userId: 'test' }, query: {} };
      let responseReceived = false;
      const mockRes = {
        json: () => { responseReceived = true; },
        status: () => ({ json: () => { responseReceived = true; } })
      };

      await this.controller.getHealthStatus(mockReq, mockRes);
      assert.ok(responseReceived);
    });

    // Feature Flag Tests
    await this.runTest('Feature flag handling', async () => {
      const originalEnv = process.env.USE_ENHANCED_AUTOMATION;
      
      // Test with feature enabled
      process.env.USE_ENHANCED_AUTOMATION = 'true';
      const controllerEnabled = new EnhancedJobApplicationController();
      assert.strictEqual(controllerEnabled.useEnhancedService, true);
      
      // Test with feature disabled
      process.env.USE_ENHANCED_AUTOMATION = 'false';
      const controllerDisabled = new EnhancedJobApplicationController();
      assert.strictEqual(controllerDisabled.useEnhancedService, false);
      
      // Restore original value
      process.env.USE_ENHANCED_AUTOMATION = originalEnv;
    });

    // Security Tests
    await this.runTest('Input sanitization', async () => {
      const maliciousData = {
        userId: 'test-user',
        jobId: '<script>alert("xss")</script>',
        resumeId: 'test-resume',
        jobUrl: 'javascript:alert("xss")',
        jobTitle: 'Test Job',
        company: 'Test Company'
      };

      try {
        await this.service.queueApplication(maliciousData);
        assert.fail('Should have rejected malicious input');
      } catch (error) {
        assert.ok(error.message.includes('validation') || error.message.includes('Invalid'));
      }
    });

    this.printTestSummary();
  }

  printTestSummary() {
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const total = this.testResults.length;
    const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;

    console.log('\n📊 Enhanced Job Application System Test Results:');
    console.log(`Total: ${total}, Passed: ${passed}, Failed: ${failed}`);
    console.log(`Success Rate: ${successRate}%\n`);

    if (failed > 0) {
      console.log('❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAILED')
        .forEach(result => {
          console.log(`  • ${result.name}: ${result.error}`);
        });
      console.log('');
    }

    // Test performance summary
    const totalDuration = this.testResults.reduce((sum, result) => {
      return sum + parseInt(result.duration.replace('ms', ''));
    }, 0);

    console.log(`⏱️  Total execution time: ${totalDuration}ms`);
    console.log(`📈 Average test duration: ${Math.round(totalDuration / total)}ms`);
  }

  // Helper method to test specific functionality
  async testSpecificFeature(featureName) {
    console.log(`\n🔍 Testing specific feature: ${featureName}\n`);
    
    switch (featureName) {
      case 'browser':
        await this.runTest('Browser engine test', async () => {
          const platform = this.browserEngine.detectPlatform('https://linkedin.com/jobs/view/123');
          assert.strictEqual(platform, 'linkedin');
        });
        break;
      
      case 'queue':
        await this.runTest('Queue management test', async () => {
          const status = this.service.getEnhancedQueueStatus();
          assert.ok(status.total >= 0);
        });
        break;
      
      case 'controller':
        await this.runTest('Controller test', async () => {
          assert.ok(this.controller.legacyService);
          assert.ok(this.controller.enhancedService);
        });
        break;
      
      default:
        console.log(`Unknown feature: ${featureName}`);
    }
    
    this.printTestSummary();
  }
}

// Export for use in other test files
module.exports = EnhancedJobApplicationTests;

// Run tests if this file is executed directly
if (require.main === module) {
  const tests = new EnhancedJobApplicationTests();
  
  // Check if specific test requested
  const args = process.argv.slice(2);
  if (args.length > 0) {
    tests.testSpecificFeature(args[0]);
  } else {
    tests.runAllTests().catch(console.error);
  }
}