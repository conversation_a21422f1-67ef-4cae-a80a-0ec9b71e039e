#!/usr/bin/env node

/**
 * Load Testing Infrastructure for CVleap
 * Comprehensive performance testing with metrics collection
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');
const fs = require('fs').promises;
const path = require('path');

class LoadTester {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'http://localhost:3000';
    this.concurrent = options.concurrent || 10;
    this.duration = options.duration || 60000; // 1 minute
    this.rampUpTime = options.rampUpTime || 10000; // 10 seconds
    this.thinkTime = options.thinkTime || 1000; // 1 second between requests
    
    this.stats = {
      requests: 0,
      responses: 0,
      errors: 0,
      timeouts: 0,
      responseTimes: [],
      statusCodes: {},
      startTime: null,
      endTime: null,
      bytesReceived: 0,
      bytesSent: 0
    };
    
    this.scenarios = [];
    this.results = [];
  }

  /**
   * Add test scenario
   */
  addScenario(name, requests, weight = 1) {
    this.scenarios.push({
      name,
      requests,
      weight,
      executed: 0,
      errors: 0,
      avgResponseTime: 0
    });
  }

  /**
   * Load test scenarios from configuration
   */
  loadScenarios() {
    // Health check scenario
    this.addScenario('Health Check', [
      { method: 'GET', path: '/health' },
      { method: 'GET', path: '/health/readiness' }
    ], 2);

    // API endpoints scenario
    this.addScenario('API Endpoints', [
      { method: 'GET', path: '/api/users/profile', auth: true },
      { method: 'GET', path: '/api/resumes', auth: true },
      { method: 'POST', path: '/api/resumes', auth: true, data: { title: 'Test Resume' } }
    ], 3);

    // Authentication scenario
    this.addScenario('Authentication', [
      { method: 'POST', path: '/api/auth/login', data: { email: '<EMAIL>', password: 'password' } },
      { method: 'POST', path: '/api/auth/refresh', auth: true }
    ], 1);

    // Static resources scenario
    this.addScenario('Static Resources', [
      { method: 'GET', path: '/favicon.ico' },
      { method: 'GET', path: '/manifest.json' }
    ], 1);

    // AI services scenario (if enabled)
    this.addScenario('AI Services', [
      { method: 'POST', path: '/api/ai/improve-resume', auth: true, data: { content: 'Test content' } },
      { method: 'POST', path: '/api/ai/generate-summary', auth: true, data: { text: 'Test text' } }
    ], 1);
  }

  /**
   * Generate authentication token for testing
   */
  async generateAuthToken() {
    try {
      const loginData = {
        email: '<EMAIL>',
        password: 'LoadTest123!'
      };

      const response = await this.makeRequest('POST', '/api/auth/login', loginData);
      if (response && response.data && response.data.accessToken) {
        return response.data.accessToken;
      }
    } catch (error) {
      console.warn('Could not generate auth token:', error.message);
    }
    
    // Return a mock token for testing
    return 'mock-token-for-load-testing';
  }

  /**
   * Make HTTP request
   */
  async makeRequest(method, urlPath, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(urlPath, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const httpModule = isHttps ? https : http;
      
      const requestData = data ? JSON.stringify(data) : null;
      
      const options = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method: method,
        headers: {
          'User-Agent': 'CVleap-LoadTester/1.0',
          'Accept': 'application/json',
          'Connection': 'keep-alive',
          ...headers
        },
        timeout: 30000
      };

      if (requestData) {
        options.headers['Content-Type'] = 'application/json';
        options.headers['Content-Length'] = Buffer.byteLength(requestData);
      }

      const startTime = Date.now();
      
      const req = httpModule.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
          this.stats.bytesReceived += chunk.length;
        });
        
        res.on('end', () => {
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          
          this.stats.responses++;
          this.stats.responseTimes.push(responseTime);
          this.stats.statusCodes[res.statusCode] = (this.stats.statusCodes[res.statusCode] || 0) + 1;
          
          let parsedData = null;
          try {
            if (responseData) {
              parsedData = JSON.parse(responseData);
            }
          } catch (parseError) {
            // Response is not JSON, that's okay
          }
          
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData,
            responseTime,
            size: responseData.length
          });
        });
      });

      req.on('error', (error) => {
        this.stats.errors++;
        reject(error);
      });

      req.on('timeout', () => {
        this.stats.timeouts++;
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (requestData) {
        this.stats.bytesSent += Buffer.byteLength(requestData);
        req.write(requestData);
      }
      
      this.stats.requests++;
      req.end();
    });
  }

  /**
   * Execute a single test scenario
   */
  async executeScenario(scenario, authToken) {
    const scenarioStart = Date.now();
    let scenarioErrors = 0;
    let totalResponseTime = 0;

    for (const request of scenario.requests) {
      try {
        const headers = {};
        
        if (request.auth && authToken) {
          headers.Authorization = `Bearer ${authToken}`;
        }

        const response = await this.makeRequest(
          request.method,
          request.path,
          request.data,
          headers
        );

        totalResponseTime += response.responseTime;
        
        // Simulate think time
        if (this.thinkTime > 0) {
          await new Promise(resolve => setTimeout(resolve, this.thinkTime));
        }
        
      } catch (error) {
        scenarioErrors++;
        console.error(`Scenario ${scenario.name} error:`, error.message);
      }
    }

    scenario.executed++;
    scenario.errors += scenarioErrors;
    scenario.avgResponseTime = totalResponseTime / scenario.requests.length;
  }

  /**
   * Select random scenario based on weights
   */
  selectScenario() {
    const totalWeight = this.scenarios.reduce((sum, s) => sum + s.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const scenario of this.scenarios) {
      random -= scenario.weight;
      if (random <= 0) {
        return scenario;
      }
    }
    
    return this.scenarios[0]; // Fallback
  }

  /**
   * Worker function for concurrent load generation
   */
  async worker(workerId, authToken) {
    const workerStart = Date.now();
    const workerStats = {
      requests: 0,
      errors: 0,
      scenarios: {}
    };

    while (Date.now() - this.stats.startTime < this.duration) {
      try {
        const scenario = this.selectScenario();
        
        if (!workerStats.scenarios[scenario.name]) {
          workerStats.scenarios[scenario.name] = 0;
        }
        workerStats.scenarios[scenario.name]++;
        
        await this.executeScenario(scenario, authToken);
        workerStats.requests++;
        
      } catch (error) {
        workerStats.errors++;
      }
      
      // Add some randomness to avoid synchronized requests
      const jitter = Math.random() * 100;
      await new Promise(resolve => setTimeout(resolve, jitter));
    }

    console.log(`Worker ${workerId} completed: ${workerStats.requests} requests, ${workerStats.errors} errors`);
    return workerStats;
  }

  /**
   * Calculate statistics
   */
  calculateStats() {
    if (this.stats.responseTimes.length === 0) {
      return null;
    }

    const sortedTimes = this.stats.responseTimes.sort((a, b) => a - b);
    const totalTime = this.stats.endTime - this.stats.startTime;
    
    return {
      duration: totalTime,
      totalRequests: this.stats.requests,
      totalResponses: this.stats.responses,
      totalErrors: this.stats.errors,
      totalTimeouts: this.stats.timeouts,
      requestsPerSecond: (this.stats.responses / (totalTime / 1000)).toFixed(2),
      errorRate: ((this.stats.errors / this.stats.requests) * 100).toFixed(2),
      
      responseTime: {
        min: sortedTimes[0],
        max: sortedTimes[sortedTimes.length - 1],
        avg: (sortedTimes.reduce((sum, time) => sum + time, 0) / sortedTimes.length).toFixed(2),
        p50: sortedTimes[Math.floor(sortedTimes.length * 0.5)],
        p90: sortedTimes[Math.floor(sortedTimes.length * 0.9)],
        p95: sortedTimes[Math.floor(sortedTimes.length * 0.95)],
        p99: sortedTimes[Math.floor(sortedTimes.length * 0.99)]
      },
      
      statusCodes: this.stats.statusCodes,
      
      throughput: {
        bytesPerSecond: ((this.stats.bytesReceived + this.stats.bytesSent) / (totalTime / 1000)).toFixed(2),
        requestSizeAvg: (this.stats.bytesSent / this.stats.requests).toFixed(2),
        responseSizeAvg: (this.stats.bytesReceived / this.stats.responses).toFixed(2)
      },
      
      scenarios: this.scenarios.map(scenario => ({
        name: scenario.name,
        executed: scenario.executed,
        errors: scenario.errors,
        errorRate: ((scenario.errors / Math.max(scenario.executed, 1)) * 100).toFixed(2),
        avgResponseTime: scenario.avgResponseTime.toFixed(2)
      }))
    };
  }

  /**
   * Run load test
   */
  async run() {
    console.log('Starting CVleap Load Test...');
    console.log(`Target: ${this.baseUrl}`);
    console.log(`Concurrent users: ${this.concurrent}`);
    console.log(`Duration: ${this.duration / 1000}s`);
    console.log(`Ramp-up time: ${this.rampUpTime / 1000}s`);
    
    // Load test scenarios
    this.loadScenarios();
    console.log(`Loaded ${this.scenarios.length} test scenarios`);
    
    // Generate auth token
    console.log('Generating authentication token...');
    const authToken = await this.generateAuthToken();
    
    // Initialize stats
    this.stats.startTime = Date.now();
    
    // Start workers with ramp-up
    const workers = [];
    const rampUpDelay = this.rampUpTime / this.concurrent;
    
    for (let i = 0; i < this.concurrent; i++) {
      setTimeout(() => {
        const worker = this.worker(i + 1, authToken);
        workers.push(worker);
      }, i * rampUpDelay);
    }
    
    // Wait for all workers to complete
    await Promise.all(workers);
    
    this.stats.endTime = Date.now();
    
    // Calculate and display results
    const results = this.calculateStats();
    await this.displayResults(results);
    await this.saveResults(results);
    
    return results;
  }

  /**
   * Display test results
   */
  async displayResults(results) {
    if (!results) {
      console.log('No results to display');
      return;
    }

    console.log('\n=== CVleap Load Test Results ===\n');
    
    console.log('📊 Summary:');
    console.log(`  Duration: ${(results.duration / 1000).toFixed(1)}s`);
    console.log(`  Total Requests: ${results.totalRequests}`);
    console.log(`  Total Responses: ${results.totalResponses}`);
    console.log(`  Total Errors: ${results.totalErrors}`);
    console.log(`  Requests/sec: ${results.requestsPerSecond}`);
    console.log(`  Error Rate: ${results.errorRate}%`);
    
    console.log('\n⏱️  Response Times (ms):');
    console.log(`  Min: ${results.responseTime.min}ms`);
    console.log(`  Avg: ${results.responseTime.avg}ms`);
    console.log(`  Max: ${results.responseTime.max}ms`);
    console.log(`  P50: ${results.responseTime.p50}ms`);
    console.log(`  P90: ${results.responseTime.p90}ms`);
    console.log(`  P95: ${results.responseTime.p95}ms`);
    console.log(`  P99: ${results.responseTime.p99}ms`);
    
    console.log('\n📈 Status Codes:');
    Object.entries(results.statusCodes).forEach(([code, count]) => {
      const percentage = ((count / results.totalResponses) * 100).toFixed(1);
      console.log(`  ${code}: ${count} (${percentage}%)`);
    });
    
    console.log('\n🚀 Throughput:');
    console.log(`  Bytes/sec: ${results.throughput.bytesPerSecond}`);
    console.log(`  Avg Request Size: ${results.throughput.requestSizeAvg} bytes`);
    console.log(`  Avg Response Size: ${results.throughput.responseSizeAvg} bytes`);
    
    console.log('\n🎯 Scenarios:');
    results.scenarios.forEach(scenario => {
      console.log(`  ${scenario.name}:`);
      console.log(`    Executed: ${scenario.executed}`);
      console.log(`    Errors: ${scenario.errors} (${scenario.errorRate}%)`);
      console.log(`    Avg Response Time: ${scenario.avgResponseTime}ms`);
    });
  }

  /**
   * Save results to file
   */
  async saveResults(results) {
    try {
      const resultsDir = path.join(__dirname, '..', 'test-results');
      await fs.mkdir(resultsDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `load-test-${timestamp}.json`;
      const filePath = path.join(resultsDir, filename);
      
      const fullResults = {
        testConfig: {
          baseUrl: this.baseUrl,
          concurrent: this.concurrent,
          duration: this.duration,
          rampUpTime: this.rampUpTime,
          thinkTime: this.thinkTime
        },
        timestamp: new Date().toISOString(),
        results
      };
      
      await fs.writeFile(filePath, JSON.stringify(fullResults, null, 2));
      console.log(`\n💾 Results saved to: ${filePath}`);
      
    } catch (error) {
      console.error('Failed to save results:', error.message);
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace(/^--/, '');
    const value = args[i + 1];
    
    switch (key) {
      case 'url':
        options.baseUrl = value;
        break;
      case 'concurrent':
      case 'c':
        options.concurrent = parseInt(value);
        break;
      case 'duration':
      case 'd':
        options.duration = parseInt(value) * 1000;
        break;
      case 'rampup':
        options.rampUpTime = parseInt(value) * 1000;
        break;
      case 'think':
        options.thinkTime = parseInt(value);
        break;
    }
  }
  
  const loadTester = new LoadTester(options);
  
  loadTester.run()
    .then(results => {
      const success = results.errorRate < 5 && results.responseTime.p95 < 2000;
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Load test failed:', error);
      process.exit(1);
    });
}

module.exports = LoadTester;