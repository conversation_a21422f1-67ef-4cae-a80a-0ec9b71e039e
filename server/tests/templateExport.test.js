const fs = require('fs').promises;
const path = require('path');
const templateExportService = require('../templateExportService');

// Mock dependencies
jest.mock('puppeteer');
jest.mock('docx');
jest.mock('googleapis');
jest.mock('pdf-lib');

describe('Template Export Service', () => {
  let mockUserData;

  beforeEach(() => {
    mockUserData = {
      personalInfo: {
        fullName: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'San Francisco, CA',
        title: 'Senior Software Engineer',
        website: 'https://johndoe.dev'
      },
      summary: 'Experienced software engineer with 8+ years of experience in full-stack development.',
      experience: [
        {
          position: 'Senior Software Engineer',
          company: 'Tech Corp',
          duration: '2020 - Present',
          location: 'San Francisco, CA',
          description: 'Lead development of scalable web applications',
          achievements: [
            'Improved application performance by 40%',
            'Led team of 5 developers',
            'Implemented CI/CD pipeline'
          ]
        },
        {
          position: 'Software Engineer',
          company: 'StartupXYZ',
          duration: '2018 - 2020',
          location: 'San Jose, CA',
          description: 'Full-stack development using React and Node.js',
          achievements: [
            'Built user authentication system',
            'Developed RESTful APIs'
          ]
        }
      ],
      education: [
        {
          degree: 'Bachelor of Science in Computer Science',
          school: 'University of California, Berkeley',
          year: '2018',
          location: 'Berkeley, CA',
          gpa: '3.8'
        }
      ],
      skills: {
        'Programming Languages': ['JavaScript', 'Python', 'Java', 'TypeScript'],
        'Frameworks': ['React', 'Node.js', 'Express', 'Django'],
        'Databases': ['PostgreSQL', 'MongoDB', 'Redis'],
        'Tools': ['Git', 'Docker', 'AWS', 'Jenkins']
      },
      projects: [
        {
          name: 'E-commerce Platform',
          description: 'Full-stack e-commerce application with payment integration',
          technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe'],
          url: 'https://github.com/johndoe/ecommerce'
        }
      ],
      certifications: [
        {
          name: 'AWS Certified Solutions Architect',
          issuer: 'Amazon Web Services',
          date: '2023',
          url: 'https://aws.amazon.com/certification/'
        }
      ]
    };

    // Clear any existing mocks
    jest.clearAllMocks();
  });

  describe('Data Sanitization', () => {
    test('should sanitize user input to prevent injection attacks', async () => {
      const maliciousData = {
        personalInfo: {
          fullName: 'John<script>alert("xss")</script>Doe',
          email: '<EMAIL>{{{malicious}}}',
          phone: '******-123-4567<>'
        },
        summary: 'Summary with <script>malicious code</script>'
      };

      const sanitized = await templateExportService.sanitizeUserData(maliciousData);

      expect(sanitized.personalInfo.fullName).toBe('JohnscriptalertxssscriptDoe');
      expect(sanitized.personalInfo.email).toBe('<EMAIL>');
      expect(sanitized.personalInfo.phone).toBe('******-123-4567');
      expect(sanitized.summary).toBe('Summary with scriptmalicious codescript');
    });

    test('should handle missing or null data gracefully', async () => {
      const incompleteData = {
        personalInfo: {
          fullName: 'John Doe'
          // Missing other fields
        }
        // Missing other sections
      };

      const sanitized = await templateExportService.sanitizeUserData(incompleteData);

      expect(sanitized.personalInfo.fullName).toBe('John Doe');
      expect(sanitized.personalInfo.email).toBe('');
      expect(sanitized.experience).toEqual([]);
      expect(sanitized.education).toEqual([]);
    });
  });

  describe('Template Data Preparation', () => {
    test('should prepare template data with customization', async () => {
      const customization = {
        colors: { primary: '#ff0000', secondary: '#00ff00' },
        primaryFont: 'Helvetica',
        layout: 'compact'
      };

      const templateData = await templateExportService.prepareTemplateData(
        mockUserData,
        'modern',
        customization
      );

      expect(templateData.user).toEqual(mockUserData);
      expect(templateData.template.name).toBe('modern');
      expect(templateData.template.colors.primary).toBe('#ff0000');
      expect(templateData.template.fonts.primary).toBe('Helvetica');
      expect(templateData.template.layout).toBe('compact');
    });

    test('should use default template when invalid template specified', async () => {
      const templateData = await templateExportService.prepareTemplateData(
        mockUserData,
        'nonexistent',
        {}
      );

      expect(templateData.template.name).toBe('nonexistent');
      expect(templateData.template.config).toEqual(templateExportService.templates.modern);
    });
  });

  describe('PDF Generation', () => {
    test('should generate PDF successfully', async () => {
      const puppeteer = require('puppeteer');
      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue({
          setContent: jest.fn().mockResolvedValue(),
          addStyleTag: jest.fn().mockResolvedValue(),
          pdf: jest.fn().mockResolvedValue(Buffer.from('mock-pdf-content'))
        }),
        close: jest.fn().mockResolvedValue()
      };

      puppeteer.launch.mockResolvedValue(mockBrowser);

      // Mock fs.writeFile
      const originalWriteFile = fs.writeFile;
      fs.writeFile = jest.fn().mockResolvedValue();

      const result = await templateExportService.exportToPDF('modern', mockUserData, {
        includeWatermark: false,
        quality: 'high'
      });

      expect(result.success).toBe(true);
      expect(result.format).toBe('pdf');
      expect(result.filename).toMatch(/resume_modern_\d+\.pdf/);
      expect(result.metadata.template).toBe('modern');
      expect(puppeteer.launch).toHaveBeenCalled();
      expect(mockBrowser.close).toHaveBeenCalled();

      // Restore original function
      fs.writeFile = originalWriteFile;
    });

    test('should handle PDF generation errors gracefully', async () => {
      const puppeteer = require('puppeteer');
      puppeteer.launch.mockRejectedValue(new Error('Puppeteer launch failed'));

      await expect(
        templateExportService.exportToPDF('modern', mockUserData)
      ).rejects.toThrow('PDF generation failed: Puppeteer launch failed');
    });

    test('should add watermark when requested', async () => {
      const { PDFDocument } = require('pdf-lib');
      const mockPdfDoc = {
        getPages: jest.fn().mockReturnValue([
          {
            getSize: jest.fn().mockReturnValue({ width: 612, height: 792 }),
            drawText: jest.fn()
          }
        ]),
        save: jest.fn().mockResolvedValue(Buffer.from('watermarked-pdf'))
      };

      PDFDocument.load = jest.fn().mockResolvedValue(mockPdfDoc);

      const watermarkedBuffer = await templateExportService.addWatermarkToPDF(
        Buffer.from('original-pdf')
      );

      expect(PDFDocument.load).toHaveBeenCalled();
      expect(mockPdfDoc.getPages).toHaveBeenCalled();
      expect(watermarkedBuffer).toEqual(Buffer.from('watermarked-pdf'));
    });
  });

  describe('Word Document Generation', () => {
    test('should generate Word document successfully', async () => {
      const { Document, Packer } = require('docx');
      const mockBuffer = Buffer.from('mock-docx-content');

      Packer.toBuffer = jest.fn().mockResolvedValue(mockBuffer);

      // Mock fs.writeFile
      const originalWriteFile = fs.writeFile;
      fs.writeFile = jest.fn().mockResolvedValue();

      const result = await templateExportService.exportToWord('classic', mockUserData, {});

      expect(result.success).toBe(true);
      expect(result.format).toBe('word');
      expect(result.filename).toMatch(/resume_classic_\d+\.docx/);
      expect(result.metadata.template).toBe('classic');
      expect(Packer.toBuffer).toHaveBeenCalled();

      // Restore original function
      fs.writeFile = originalWriteFile;
    });

    test('should handle Word generation errors', async () => {
      const { Packer } = require('docx');
      Packer.toBuffer = jest.fn().mockRejectedValue(new Error('DOCX generation failed'));

      await expect(
        templateExportService.exportToWord('classic', mockUserData)
      ).rejects.toThrow('Word generation failed: DOCX generation failed');
    });
  });

  describe('Google Docs Integration', () => {
    test('should create Google Doc successfully', async () => {
      const mockDocsService = {
        documents: {
          create: jest.fn().mockResolvedValue({
            data: { documentId: 'mock-doc-id' }
          }),
          batchUpdate: jest.fn().mockResolvedValue({})
        }
      };

      const mockDriveService = {
        permissions: {
          create: jest.fn().mockResolvedValue({})
        }
      };

      // Mock the services
      templateExportService.docsService = mockDocsService;
      templateExportService.driveService = mockDriveService;

      // Mock fs.writeFile
      const originalWriteFile = fs.writeFile;
      fs.writeFile = jest.fn().mockResolvedValue();

      const result = await templateExportService.exportToGoogleDocs('modern', mockUserData, {
        sharing: 'private',
        userAccessToken: 'mock-token'
      });

      expect(result.success).toBe(true);
      expect(result.format).toBe('google-docs');
      expect(result.metadata.documentId).toBe('mock-doc-id');
      expect(mockDocsService.documents.create).toHaveBeenCalled();

      // Restore original function
      fs.writeFile = originalWriteFile;
    });

    test('should handle Google Docs API errors', async () => {
      // Clear the services to simulate unconfigured state
      templateExportService.docsService = null;
      templateExportService.driveService = null;

      await expect(
        templateExportService.exportToGoogleDocs('modern', mockUserData)
      ).rejects.toThrow('Google Services not configured');
    });

    test('should generate Google Docs requests correctly', async () => {
      const templateData = await templateExportService.prepareTemplateData(
        mockUserData,
        'modern',
        {}
      );

      const requests = await templateExportService.generateGoogleDocsRequests(
        templateData,
        'modern'
      );

      expect(requests).toBeInstanceOf(Array);
      expect(requests.length).toBeGreaterThan(0);
      
      // Check that it includes text insertion requests
      const textRequests = requests.filter(req => req.insertText);
      expect(textRequests.length).toBeGreaterThan(0);
      
      // Check that it includes the user's name
      const nameRequest = textRequests.find(req => 
        req.insertText.text.includes('John Doe')
      );
      expect(nameRequest).toBeDefined();
    });
  });

  describe('Template Management', () => {
    test('should return available templates', () => {
      const templates = templateExportService.getAvailableTemplates();

      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      
      const modernTemplate = templates.find(t => t.id === 'modern');
      expect(modernTemplate).toBeDefined();
      expect(modernTemplate.name).toBe('Modern Professional');
      expect(modernTemplate.supportedFormats).toContain('pdf');
      expect(modernTemplate.supportedFormats).toContain('docx');
      expect(modernTemplate.supportedFormats).toContain('googledocs');
    });

    test('should validate template data correctly', () => {
      // Valid data
      const validResult = templateExportService.validateTemplateData({
        user: mockUserData
      });
      expect(validResult.valid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Invalid data - missing required fields
      const invalidResult = templateExportService.validateTemplateData({
        user: {
          personalInfo: {}
        }
      });
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Full name is required');
      expect(invalidResult.errors).toContain('Email is required');
    });
  });

  describe('Export Statistics', () => {
    test('should track export statistics', () => {
      const initialStats = { ...templateExportService.exportStats };

      templateExportService.updateExportStats(true, 1500);
      templateExportService.updateExportStats(false, 2000);
      templateExportService.updateExportStats(true, 1000);

      const stats = templateExportService.exportStats;
      expect(stats.totalExports).toBe(initialStats.totalExports + 3);
      expect(stats.successfulExports).toBe(initialStats.successfulExports + 2);
      expect(stats.failedExports).toBe(initialStats.failedExports + 1);
    });

    test('should get comprehensive export statistics', async () => {
      // Mock fs.readdir and fs.stat
      const originalReaddir = fs.readdir;
      const originalStat = fs.stat;

      fs.readdir = jest.fn().mockResolvedValue([
        'resume1.pdf',
        'resume2.docx',
        'resume3.gdoc'
      ]);

      fs.stat = jest.fn().mockResolvedValue({ size: 1024 });

      const stats = await templateExportService.getExportStatistics();

      expect(stats.files.totalFiles).toBe(3);
      expect(stats.files.formats.pdf).toBe(1);
      expect(stats.files.formats.docx).toBe(1);
      expect(stats.files.formats.googleDocs).toBe(1);
      expect(stats.performance).toBeDefined();
      expect(stats.templates.available).toContain('modern');

      // Restore original functions
      fs.readdir = originalReaddir;
      fs.stat = originalStat;
    });
  });

  describe('Error Handling', () => {
    test('should handle file system errors gracefully', async () => {
      const originalWriteFile = fs.writeFile;
      fs.writeFile = jest.fn().mockRejectedValue(new Error('Disk full'));

      await expect(
        templateExportService.exportToPDF('modern', mockUserData)
      ).rejects.toThrow('PDF generation failed');

      fs.writeFile = originalWriteFile;
    });

    test('should handle template compilation errors', async () => {
      const originalReadFile = fs.readFile;
      fs.readFile = jest.fn().mockRejectedValue(new Error('Template not found'));

      // Should fall back to default template
      const html = await templateExportService.generateHTMLTemplate(
        { user: mockUserData, template: { colors: {}, fonts: {} } },
        'nonexistent'
      );

      expect(html).toContain('John Doe');
      expect(html).toContain('<!DOCTYPE html>');

      fs.readFile = originalReadFile;
    });
  });

  describe('Security', () => {
    test('should prevent template injection attacks', async () => {
      const maliciousData = {
        personalInfo: {
          fullName: '{{#each this}}{{@key}}: {{this}}{{/each}}',
          email: '{{constructor.constructor("return process")().exit()}}'
        }
      };

      const sanitized = await templateExportService.sanitizeUserData(maliciousData);
      
      // Should remove template injection patterns
      expect(sanitized.personalInfo.fullName).not.toContain('{{');
      expect(sanitized.personalInfo.email).not.toContain('{{');
    });

    test('should validate file paths to prevent directory traversal', async () => {
      const maliciousFilename = '../../../etc/passwd';
      
      const result = await templateExportService.getExportFile(maliciousFilename);
      
      expect(result.exists).toBe(false);
      expect(result.error).toBe('File not found');
    });
  });
});
