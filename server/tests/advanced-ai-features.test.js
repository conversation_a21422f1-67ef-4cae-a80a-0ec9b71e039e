const AIService = require('../aiService');
const AIController = require('../aiController');

// Simple test runner
class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  test(name, fn) {
    this.tests.push({ name, fn });
  }

  async run() {
    console.log(`🧪 Running ${this.tests.length} advanced AI tests...\n`);

    for (const test of this.tests) {
      try {
        const start = Date.now();
        await test.fn();
        const duration = Date.now() - start;
        console.log(`✅ ${test.name} (${duration}ms)`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${test.name} - ${error.message}`);
        this.failed++;
      }
    }

    console.log(`\n📊 Test Results:`);
    console.log(`Total: ${this.tests.length}, Passed: ${this.passed}, Failed: ${this.failed}`);
    console.log(`Success Rate: ${((this.passed / this.tests.length) * 100).toFixed(1)}%`);

    return this.failed === 0;
  }
}

const runner = new TestRunner();

// Test data
const sampleResumeData = {
  name: "<PERSON>",
  title: "Software Engineer",
  summary: "Experienced software engineer with 5 years in web development",
  skills: ["JavaScript", "React", "Node.js", "Python"],
  experience: [
    {
      role: "Senior Software Engineer",
      company: "Tech Corp",
      start: "01/2020",
      end: "Present",
      description: "Led development of web applications"
    }
  ],
  education: [
    {
      degree: "Bachelor of Computer Science",
      institution: "University of Technology",
      year: "2018"
    }
  ]
};

// Test AI Service initialization
runner.test('AIService should initialize without errors', () => {
  const aiService = new AIService();
  if (!aiService) {
    throw new Error('AIService initialization failed');
  }
});

// Test AI Controller initialization
runner.test('AIController should initialize without errors', () => {
  const aiController = new AIController();
  if (!aiController || !aiController.aiService) {
    throw new Error('AIController initialization failed');
  }
});

// Test new method existence in AIService
runner.test('AIService should have advanced resume scoring method', () => {
  const aiService = new AIService();
  if (typeof aiService.advancedResumeScoring !== 'function') {
    throw new Error('advancedResumeScoring method not found');
  }
});

runner.test('AIService should have weakness detection method', () => {
  const aiService = new AIService();
  if (typeof aiService.detectResumeWeaknesses !== 'function') {
    throw new Error('detectResumeWeaknesses method not found');
  }
});

runner.test('AIService should have strength amplification method', () => {
  const aiService = new AIService();
  if (typeof aiService.amplifyResumeStrengths !== 'function') {
    throw new Error('amplifyResumeStrengths method not found');
  }
});

runner.test('AIService should have chat assistant method', () => {
  const aiService = new AIService();
  if (typeof aiService.chatAssistant !== 'function') {
    throw new Error('chatAssistant method not found');
  }
});

runner.test('AIService should have real-time suggestions method', () => {
  const aiService = new AIService();
  if (typeof aiService.generateRealTimeSuggestions !== 'function') {
    throw new Error('generateRealTimeSuggestions method not found');
  }
});

runner.test('AIService should have document parsing method', () => {
  const aiService = new AIService();
  if (typeof aiService.parseResumeContent !== 'function') {
    throw new Error('parseResumeContent method not found');
  }
});

runner.test('AIService should have data extraction method', () => {
  const aiService = new AIService();
  if (typeof aiService.intelligentDataExtraction !== 'function') {
    throw new Error('intelligentDataExtraction method not found');
  }
});

runner.test('AIService should have duplicate detection method', () => {
  const aiService = new AIService();
  if (typeof aiService.detectDuplicateContent !== 'function') {
    throw new Error('detectDuplicateContent method not found');
  }
});

runner.test('AIService should have version comparison method', () => {
  const aiService = new AIService();
  if (typeof aiService.compareResumeVersions !== 'function') {
    throw new Error('compareResumeVersions method not found');
  }
});

runner.test('AIService should have template recommendation method', () => {
  const aiService = new AIService();
  if (typeof aiService.recommendOptimalTemplate !== 'function') {
    throw new Error('recommendOptimalTemplate method not found');
  }
});

runner.test('AIService should have layout optimization method', () => {
  const aiService = new AIService();
  if (typeof aiService.optimizeResumeLayout !== 'function') {
    throw new Error('optimizeResumeLayout method not found');
  }
});

runner.test('AIService should have translation method', () => {
  const aiService = new AIService();
  if (typeof aiService.translateResume !== 'function') {
    throw new Error('translateResume method not found');
  }
});

runner.test('AIService should have regional adaptation method', () => {
  const aiService = new AIService();
  if (typeof aiService.adaptForRegionalMarket !== 'function') {
    throw new Error('adaptForRegionalMarket method not found');
  }
});

// Test new controller methods
runner.test('AIController should have advanced scoring method', () => {
  const aiController = new AIController();
  if (typeof aiController.advancedResumeScoring !== 'function') {
    throw new Error('advancedResumeScoring controller method not found');
  }
});

runner.test('AIController should have weakness detection method', () => {
  const aiController = new AIController();
  if (typeof aiController.detectWeaknesses !== 'function') {
    throw new Error('detectWeaknesses controller method not found');
  }
});

runner.test('AIController should have strength amplification method', () => {
  const aiController = new AIController();
  if (typeof aiController.amplifyStrengths !== 'function') {
    throw new Error('amplifyStrengths controller method not found');
  }
});

runner.test('AIController should have chat assistant method', () => {
  const aiController = new AIController();
  if (typeof aiController.chatAssistant !== 'function') {
    throw new Error('chatAssistant controller method not found');
  }
});

runner.test('AIController should have template recommendation method', () => {
  const aiController = new AIController();
  if (typeof aiController.recommendTemplate !== 'function') {
    throw new Error('recommendTemplate controller method not found');
  }
});

runner.test('AIController should have translation method', () => {
  const aiController = new AIController();
  if (typeof aiController.translateResume !== 'function') {
    throw new Error('translateResume controller method not found');
  }
});

// Test error handling for AI service not configured
runner.test('AI methods should handle unconfigured service gracefully', async () => {
  const aiService = new AIService();
  
  try {
    await aiService.advancedResumeScoring(sampleResumeData, 'Software Engineer');
    throw new Error('Should have thrown error for unconfigured AI service');
  } catch (error) {
    if (error.message !== 'AI service not configured') {
      throw new Error(`Expected "AI service not configured" but got: ${error.message}`);
    }
  }
});

// Test validation in controller methods
runner.test('Controller methods should validate required parameters', () => {
  const aiController = new AIController();
  
  // Mock request and response objects
  const req = { body: {} };
  const res = {
    status: (code) => ({
      json: (data) => {
        if (code === 400 && data.error) {
          return { statusCode: code, data };
        }
        throw new Error('Expected 400 status with error message');
      }
    })
  };

  // This should return 400 error for missing resume data
  aiController.advancedResumeScoring(req, res);
});

// Run the tests
runner.run().then(success => {
  if (!success) {
    process.exit(1);
  }
}).catch(error => {
  console.error('Test runner error:', error);
  process.exit(1);
});