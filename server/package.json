{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node tests/enhanced-functions.test.js", "test:watch": "nodemon tests/enhanced-functions.test.js", "migrate": "npx prisma migrate dev", "generate": "npx prisma generate", "generate-safe": "npx prisma generate --skip-validation || echo 'Prisma generate failed, continuing with SQLite fallback'", "db-setup": "npm run generate-safe && npm run migrate-safe", "migrate-safe": "npx prisma migrate dev --skip-generate || echo 'Prisma migrate failed, using SQLite fallback'", "check-prisma": "node -e \"try { require('@prisma/client'); console.log('Prisma client available'); } catch(e) { console.log('Prisma client not available, using SQLite fallback'); }\""}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@azure/storage-blob": "^12.17.0", "@google/generative-ai": "^0.12.0", "@prisma/client": "^5.7.1", "@sendgrid/mail": "^8.1.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "groq-sdk": "^0.25.0", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "novita-sdk": "^3.1.3", "openai": "^5.1.1", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "prisma": "^5.7.1", "puppeteer": "^21.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "puppeteer-extra-plugin-stealth": "^2.11.2", "redis": "^4.6.12", "sharp": "^0.33.1", "sqlite3": "^5.1.7", "stripe": "^14.12.0", "user-agents": "^1.1.152", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"@types/node": "^20.10.5", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}