// Enhanced PostgreSQL schema for CVleap
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   Int      @id @default(autoincrement())
  email                String   @unique
  password_hash        String
  first_name           String?  @db.VarChar(100)
  last_name            String?  @db.VarChar(100)
  profile_picture_url  String?
  subscription_tier    String   @default("free") @db.VarChar(50)
  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt

  resumes              Resume[]
  ai_generations       AiGeneration[]
  user_analytics       UserAnalytic[]

  @@map("users")
}

model Resume {
  id            Int      @id @default(autoincrement())
  user_id       Int
  title         String   @db.VarChar(255)
  template_id   String?  @db.VarChar(100)
  content       Json
  ats_score     Int?     @default(0)
  is_public     Boolean  @default(false)
  version       Int      @default(1)
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  user          User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  versions      ResumeVersion[]
  user_analytics UserAnalytic[]

  @@map("resumes")
}

model ResumeVersion {
  id               Int      @id @default(autoincrement())
  resume_id        Int
  version_number   Int
  content          Json
  changes_summary  String?
  created_at       DateTime @default(now())

  resume           Resume @relation(fields: [resume_id], references: [id], onDelete: Cascade)

  @@map("resume_versions")
}

model Template {
  id           String   @id @db.VarChar(100)
  name         String   @db.VarChar(255)
  description  String?
  category     String?  @db.VarChar(100)
  is_premium   Boolean  @default(false)
  preview_url  String?
  structure    Json
  created_at   DateTime @default(now())

  @@map("templates")
}

model AiGeneration {
  id           Int      @id @default(autoincrement())
  user_id      Int?
  type         String   @db.VarChar(100) // 'resume_enhance', 'cover_letter', 'ats_analysis'
  input_data   Json?
  output_data  Json?
  model_used   String?  @db.VarChar(100)
  tokens_used  Int?
  created_at   DateTime @default(now())

  user         User? @relation(fields: [user_id], references: [id])

  @@map("ai_generations")
}

model UserAnalytic {
  id          Int      @id @default(autoincrement())
  user_id     Int?
  resume_id   Int?
  event_type  String   @db.VarChar(100)
  event_data  Json?
  created_at  DateTime @default(now())

  user        User?   @relation(fields: [user_id], references: [id])
  resume      Resume? @relation(fields: [resume_id], references: [id])

  @@map("user_analytics")
}