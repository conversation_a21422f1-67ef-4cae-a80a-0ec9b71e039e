const database = require('./database');

/**
 * Seed default email templates for the email automation system
 */
class EmailTemplateSeeder {
  constructor() {
    this.db = database.get();
  }

  /**
   * Seed all default templates
   */
  async seedDefaultTemplates() {
    try {
      console.log('📧 Seeding default email templates...');
      
      const templates = this.getDefaultTemplates();
      let seededCount = 0;
      
      for (const template of templates) {
        try {
          await this.insertTemplate(template);
          seededCount++;
          console.log(`✅ Seeded template: ${template.name}`);
        } catch (error) {
          if (error.message.includes('UNIQUE constraint')) {
            console.log(`⚠️  Template already exists: ${template.name}`);
          } else {
            console.error(`❌ Failed to seed template ${template.name}:`, error.message);
          }
        }
      }
      
      console.log(`📧 Email template seeding completed: ${seededCount} templates seeded`);
      return seededCount;
      
    } catch (error) {
      console.error('Email template seeding error:', error);
      throw error;
    }
  }

  /**
   * Get default email templates
   */
  getDefaultTemplates() {
    return [
      {
        name: 'Introduction to Recruiter',
        type: 'introduction',
        subject: 'Exploring Opportunities at {{company}}',
        content: `
Hello {{name}},

I hope this email finds you well. My name is {{senderName}}, and I'm a professional with {{experience}} in the industry. I came across {{company}} and was impressed by your innovative work and company culture.

I'm currently exploring new opportunities in {{role}} positions and would love to learn more about potential openings at {{company}}. With my background in {{skills}}, I believe I could contribute significantly to your team.

I'd be delighted to schedule a brief call to discuss how my experience aligns with your current needs. I've attached my resume for your review.

Thank you for your time and consideration.

Best regards,
{{senderName}}
{{email}}
        `.trim(),
        variables: JSON.stringify([
          'name', 'company', 'senderName', 'experience', 'role', 'skills', 'email'
        ]),
        is_system_template: true
      },
      {
        name: 'Follow-up After Application',
        type: 'follow_up',
        subject: 'Following Up on My Application - {{role}} Position',
        content: `
Dear {{name}},

I hope you're doing well. I recently submitted my application for the {{role}} position at {{company}} and wanted to follow up to express my continued interest.

After researching {{company}} further, I'm even more excited about the possibility of joining your team. Your commitment to innovation and the positive culture you've built aligns perfectly with my career goals.

I believe my {{experience}} and passion for {{skills}} would make me a valuable addition to your team. I would welcome the opportunity to discuss how I can contribute to {{company}}'s continued success.

Thank you for considering my application. I look forward to hearing from you.

Best regards,
{{senderName}}
{{email}}
        `.trim(),
        variables: JSON.stringify([
          'name', 'role', 'company', 'experience', 'skills', 'senderName', 'email'
        ]),
        is_system_template: true
      },
      {
        name: 'Networking Outreach',
        type: 'networking',
        subject: 'Connecting with a Fellow Professional at {{company}}',
        content: `
Hi {{firstName}},

I hope this message finds you well. I'm {{senderName}}, a professional working in {{skills}} with {{experience}} in the field.

I've been following {{company}}'s work and am impressed by the innovative projects your team is working on. I'm particularly interested in learning more about the {{role}} opportunities and the culture at {{company}}.

I would love to connect and learn from your experience at {{company}}. Would you be open to a brief 15-minute chat over coffee or a virtual call?

I'm always eager to expand my professional network and learn from industry leaders like yourself.

Thank you for your time, and I hope to hear from you soon.

Best regards,
{{senderName}}
{{email}}
        `.trim(),
        variables: JSON.stringify([
          'firstName', 'company', 'senderName', 'skills', 'experience', 'role', 'email'
        ]),
        is_system_template: true
      },
      {
        name: 'Thank You Post-Interview',
        type: 'thank_you',
        subject: 'Thank You - {{role}} Interview',
        content: `
Dear {{name}},

Thank you for taking the time to interview me for the {{role}} position at {{company}} yesterday. I enjoyed our conversation and learning more about the exciting projects your team is working on.

Our discussion reinforced my enthusiasm for this opportunity. I'm particularly excited about {{companyName}}'s commitment to innovation and how my {{skills}} could contribute to your team's success.

If you need any additional information or have further questions, please don't hesitate to reach out. I look forward to the next steps in the process.

Thank you again for your time and consideration.

Best regards,
{{senderName}}
{{email}}
        `.trim(),
        variables: JSON.stringify([
          'name', 'role', 'company', 'companyName', 'skills', 'senderName', 'email'
        ]),
        is_system_template: true
      },
      {
        name: 'Cold Outreach to Hiring Manager',
        type: 'cold_outreach',
        subject: 'Experienced {{role}} Professional - Exploring Opportunities',
        content: `
Hello {{name}},

I hope this email finds you well. My name is {{senderName}}, and I'm a {{role}} professional with {{experience}} in the industry.

I've been following {{company}}'s growth and success, and I'm impressed by your innovative approach and company culture. I'm currently exploring new opportunities and believe my background in {{skills}} would be a valuable addition to your team.

Key highlights of my background include:
• {{experience}} in {{role}} positions
• Expertise in {{skills}}
• Track record of delivering results in dynamic environments

I would welcome the opportunity to discuss how I can contribute to {{company}}'s continued success. I've attached my resume for your review and would be happy to schedule a brief call at your convenience.

Thank you for your time and consideration.

Best regards,
{{senderName}}
{{email}}
        `.trim(),
        variables: JSON.stringify([
          'name', 'senderName', 'role', 'experience', 'company', 'skills', 'email'
        ]),
        is_system_template: true
      }
    ];
  }

  /**
   * Insert template into database
   */
  async insertTemplate(template) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO email_templates (
          name, type, subject, content, variables, is_system_template, 
          usage_count, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `;
      
      const params = [
        template.name,
        template.type,
        template.subject,
        template.content,
        template.variables,
        template.is_system_template ? 1 : 0
      ];
      
      this.db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  /**
   * Check if templates are already seeded
   */
  async checkIfSeeded() {
    return new Promise((resolve, reject) => {
      const query = 'SELECT COUNT(*) as count FROM email_templates WHERE is_system_template = 1';
      
      this.db.get(query, [], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count > 0);
        }
      });
    });
  }
}

/**
 * Run seeding if called directly
 */
if (require.main === module) {
  const seeder = new EmailTemplateSeeder();
  seeder.seedDefaultTemplates()
    .then((count) => {
      console.log(`\n🎉 Email template seeding completed successfully! ${count} templates seeded.`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Email template seeding failed:', error);
      process.exit(1);
    });
}

module.exports = EmailTemplateSeeder;