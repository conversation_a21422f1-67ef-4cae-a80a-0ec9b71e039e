const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const RecaptchaPlugin = require('puppeteer-extra-plugin-recaptcha');
const UserAgent = require('user-agents');

// Add stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

// Add reCAPTCHA plugin with 2captcha token (optional)
if (process.env.CAPTCHA_SOLVING_TOKEN) {
  puppeteer.use(RecaptchaPlugin({
    provider: {
      id: '2captcha',
      token: process.env.CAPTCHA_SOLVING_TOKEN
    },
    visualFeedback: true
  }));
}

/**
 * Enhanced Browser Automation Engine for CVleap
 * Handles real browser automation for job applications across multiple platforms
 */
class BrowserAutomationEngine {
  constructor() {
    this.browser = null;
    this.page = null;
    this.userAgent = new UserAgent();
    this.sessionData = new Map(); // Store session data per platform
    this.config = {
      headless: process.env.NODE_ENV === 'production' ? 'new' : false,
      slowMo: 100, // Slow down by 100ms to appear more human
      defaultTimeout: 30000,
      defaultWaitUntil: ['networkidle0', 'domcontentloaded'],
      viewport: { width: 1366, height: 768 },
      antiDetection: true
    };
  }

  /**
   * Initialize browser with anti-detection measures
   */
  async initialize() {
    if (this.browser) {
      return this.browser;
    }

    const launchOptions = {
      headless: this.config.headless,
      slowMo: this.config.slowMo,
      defaultViewport: this.config.viewport,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--window-size=1366,768',
        '--user-agent=' + this.userAgent.toString()
      ]
    };

    this.browser = await puppeteer.launch(launchOptions);
    console.log('Browser automation engine initialized');
    return this.browser;
  }

  /**
   * Create a new page with anti-detection setup
   */
  async createPage() {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser.newPage();
    
    // Set random user agent
    await page.setUserAgent(this.userAgent.toString());
    
    // Set extra HTTP headers to appear more human
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'DNT': '1',
      'Upgrade-Insecure-Requests': '1'
    });

    // Set default timeout
    page.setDefaultTimeout(this.config.defaultTimeout);

    // Random mouse movements and typing delays for human-like behavior
    await this.setupHumanBehavior(page);

    this.page = page;
    return page;
  }

  /**
   * Setup human-like behavior patterns
   */
  async setupHumanBehavior(page) {
    // Override the navigator.webdriver property
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
    });

    // Mock plugins and languages
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5]
      });
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en']
      });
    });
  }

  /**
   * Apply job on LinkedIn
   */
  async applyToLinkedInJob(application) {
    const page = await this.createPage();
    
    try {
      console.log(`Applying to LinkedIn job: ${application.jobTitle} at ${application.company}`);
      
      // Navigate to job URL
      await page.goto(application.jobUrl, { waitUntil: this.config.defaultWaitUntil });
      
      // Wait for job page to load
      await page.waitForSelector('.jobs-apply-button, .job-detail-action-btn', { timeout: 10000 });
      
      // Click apply button
      const applyButton = await page.$('.jobs-apply-button, .job-detail-action-btn');
      if (!applyButton) {
        throw new Error('Apply button not found');
      }
      
      await this.humanClick(page, applyButton);
      
      // Wait for application form
      await page.waitForSelector('.jobs-apply-form, .job-apply-modal', { timeout: 15000 });
      
      // Fill application form
      await this.fillLinkedInApplicationForm(page, application);
      
      // Submit application
      await this.submitLinkedInApplication(page);
      
      // Get confirmation
      const result = await this.getApplicationConfirmation(page);
      
      return {
        success: true,
        platform: 'linkedin',
        submissionId: result.submissionId || `LI_${Date.now()}`,
        submissionUrl: page.url(),
        timestamp: new Date().toISOString(),
        automationType: 'puppeteer_linkedin'
      };
      
    } catch (error) {
      console.error('LinkedIn application failed:', error.message);
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Apply job on Indeed
   */
  async applyToIndeedJob(application) {
    const page = await this.createPage();
    
    try {
      console.log(`Applying to Indeed job: ${application.jobTitle} at ${application.company}`);
      
      await page.goto(application.jobUrl, { waitUntil: this.config.defaultWaitUntil });
      
      // Look for apply button
      await page.waitForSelector('#indeedApplyButton, .indeed-apply-button', { timeout: 10000 });
      
      const applyButton = await page.$('#indeedApplyButton, .indeed-apply-button');
      await this.humanClick(page, applyButton);
      
      // Handle Indeed application flow
      await this.fillIndeedApplicationForm(page, application);
      
      return {
        success: true,
        platform: 'indeed',
        submissionId: `IN_${Date.now()}`,
        submissionUrl: page.url(),
        timestamp: new Date().toISOString(),
        automationType: 'puppeteer_indeed'
      };
      
    } catch (error) {
      console.error('Indeed application failed:', error.message);
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Apply to job on company website
   */
  async applyToCompanyWebsite(application) {
    const page = await this.createPage();
    
    try {
      console.log(`Applying to company website: ${application.jobTitle} at ${application.company}`);
      
      await page.goto(application.jobUrl, { waitUntil: this.config.defaultWaitUntil });
      
      // Try to detect and fill generic application forms
      await this.fillGenericApplicationForm(page, application);
      
      return {
        success: true,
        platform: 'company_website',
        submissionId: `CW_${Date.now()}`,
        submissionUrl: page.url(),
        timestamp: new Date().toISOString(),
        automationType: 'puppeteer_generic'
      };
      
    } catch (error) {
      console.error('Company website application failed:', error.message);
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Fill LinkedIn application form
   */
  async fillLinkedInApplicationForm(page, application) {
    // Look for common form fields
    const selectors = {
      firstName: 'input[name*="firstName"], #first-name, input[placeholder*="First name"]',
      lastName: 'input[name*="lastName"], #last-name, input[placeholder*="Last name"]',
      email: 'input[type="email"], input[name*="email"]',
      phone: 'input[type="tel"], input[name*="phone"]',
      coverLetter: 'textarea[name*="coverLetter"], textarea[placeholder*="cover letter"]',
      resume: 'input[type="file"][name*="resume"], input[type="file"][accept*="pdf"]'
    };

    // Fill basic information
    await this.fillFormField(page, selectors.firstName, application.userProfile?.firstName || 'John');
    await this.fillFormField(page, selectors.lastName, application.userProfile?.lastName || 'Doe');
    await this.fillFormField(page, selectors.email, application.userProfile?.email || '<EMAIL>');
    await this.fillFormField(page, selectors.phone, application.userProfile?.phone || '+1234567890');
    
    // Fill cover letter if provided
    if (application.coverLetter) {
      await this.fillFormField(page, selectors.coverLetter, application.coverLetter);
    }

    // Handle resume upload if needed
    await this.handleResumeUpload(page, selectors.resume, application.resumePath);
    
    // Answer screening questions
    await this.answerScreeningQuestions(page, application.customAnswers);
  }

  /**
   * Fill Indeed application form
   */
  async fillIndeedApplicationForm(page, application) {
    // Indeed-specific form handling
    const selectors = {
      email: 'input[type="email"]',
      phone: 'input[type="tel"]',
      resume: 'input[type="file"]'
    };

    await this.fillFormField(page, selectors.email, application.userProfile?.email);
    await this.fillFormField(page, selectors.phone, application.userProfile?.phone);
    
    // Handle Indeed's specific flow
    await this.handleIndeedSpecificFlow(page, application);
  }

  /**
   * Fill generic application form for company websites
   */
  async fillGenericApplicationForm(page, application) {
    // Use AI to detect form fields and fill them intelligently
    const formFields = await this.detectFormFields(page);
    
    for (const field of formFields) {
      await this.fillDetectedField(page, field, application);
    }
  }

  /**
   * Detect form fields using AI/heuristics
   */
  async detectFormFields(page) {
    return await page.evaluate(() => {
      const fields = [];
      const inputs = document.querySelectorAll('input, textarea, select');
      
      inputs.forEach(input => {
        const label = input.labels?.[0]?.textContent || 
                     input.placeholder || 
                     input.name || 
                     input.id;
        
        fields.push({
          element: input,
          selector: input.tagName.toLowerCase() + 
                   (input.id ? `#${input.id}` : '') + 
                   (input.className ? `.${input.className.split(' ').join('.')}` : ''),
          type: input.type,
          label: label?.toLowerCase(),
          name: input.name,
          id: input.id
        });
      });
      
      return fields;
    });
  }

  /**
   * Fill form field with human-like typing
   */
  async fillFormField(page, selector, value) {
    if (!value) return;
    
    try {
      const field = await page.$(selector);
      if (field) {
        await field.click();
        await this.humanType(page, value);
      }
    } catch (error) {
      console.log(`Could not fill field ${selector}:`, error.message);
    }
  }

  /**
   * Human-like clicking with random offset
   */
  async humanClick(page, element) {
    const box = await element.boundingBox();
    if (box) {
      const x = box.x + box.width / 2 + (Math.random() - 0.5) * 10;
      const y = box.y + box.height / 2 + (Math.random() - 0.5) * 10;
      await page.mouse.click(x, y, { delay: Math.random() * 100 + 50 });
    }
  }

  /**
   * Human-like typing with random delays
   */
  async humanType(page, text) {
    for (const char of text) {
      await page.keyboard.type(char, { delay: Math.random() * 100 + 50 });
    }
  }

  /**
   * Handle resume upload
   */
  async handleResumeUpload(page, selector, resumePath) {
    if (!resumePath) return;
    
    try {
      const fileInput = await page.$(selector);
      if (fileInput) {
        await fileInput.uploadFile(resumePath);
        await page.waitForTimeout(2000); // Wait for upload
      }
    } catch (error) {
      console.log('Resume upload failed:', error.message);
    }
  }

  /**
   * Answer screening questions
   */
  async answerScreeningQuestions(page, customAnswers = {}) {
    // Look for common screening questions
    const questions = await page.$$('[data-testid*="question"], .screening-question, .application-question');
    
    for (const question of questions) {
      try {
        const questionText = await question.$eval('label, .question-text', el => el.textContent.toLowerCase());
        
        // Try to match with custom answers
        const answer = this.findMatchingAnswer(questionText, customAnswers);
        
        if (answer) {
          const input = await question.$('input, select, textarea');
          if (input) {
            const inputType = await input.evaluate(el => el.type || el.tagName.toLowerCase());
            
            if (inputType === 'select') {
              await input.select(answer);
            } else {
              await input.click();
              await this.humanType(page, answer.toString());
            }
          }
        }
      } catch (error) {
        console.log('Error answering screening question:', error.message);
      }
    }
  }

  /**
   * Find matching answer for screening question
   */
  findMatchingAnswer(questionText, customAnswers) {
    // Simple keyword matching - could be enhanced with AI
    for (const [key, value] of Object.entries(customAnswers)) {
      if (questionText.includes(key.toLowerCase())) {
        return value;
      }
    }
    
    // Default answers for common questions
    if (questionText.includes('authorization') || questionText.includes('eligible to work')) {
      return 'yes';
    }
    if (questionText.includes('require sponsorship')) {
      return 'no';
    }
    if (questionText.includes('years of experience')) {
      return '3';
    }
    
    return null;
  }

  /**
   * Submit LinkedIn application
   */
  async submitLinkedInApplication(page) {
    const submitButton = await page.$('button[type="submit"], .jobs-apply-button--primary, [data-testid="jobs-apply-submit-button"]');
    if (submitButton) {
      await this.humanClick(page, submitButton);
      await page.waitForTimeout(3000);
    }
  }

  /**
   * Get application confirmation
   */
  async getApplicationConfirmation(page) {
    try {
      // Wait for success message
      await page.waitForSelector('.jobs-apply-success, .application-success, [data-testid="application-success"]', { timeout: 10000 });
      
      return {
        submissionId: `AUTO_${Date.now()}`,
        confirmed: true
      };
    } catch (error) {
      return {
        submissionId: `AUTO_${Date.now()}`,
        confirmed: false
      };
    }
  }

  /**
   * Handle Indeed-specific application flow
   */
  async handleIndeedSpecificFlow(page, application) {
    // Indeed has multi-step application process
    let step = 1;
    const maxSteps = 5;
    
    while (step <= maxSteps) {
      try {
        // Look for continue/next button
        const continueButton = await page.$('button[type="submit"], .icl-Button--primary, [data-testid="continue-button"]');
        
        if (continueButton) {
          await this.humanClick(page, continueButton);
          await page.waitForTimeout(2000);
          step++;
        } else {
          break;
        }
      } catch (error) {
        console.log(`Indeed step ${step} failed:`, error.message);
        break;
      }
    }
  }

  /**
   * Fill detected field based on its characteristics
   */
  async fillDetectedField(page, field, application) {
    const label = field.label || '';
    let value = null;
    
    // Map field types to application data
    if (label.includes('first name') || label.includes('firstname')) {
      value = application.userProfile?.firstName;
    } else if (label.includes('last name') || label.includes('lastname')) {
      value = application.userProfile?.lastName;
    } else if (label.includes('email')) {
      value = application.userProfile?.email;
    } else if (label.includes('phone')) {
      value = application.userProfile?.phone;
    } else if (label.includes('cover letter') || label.includes('message')) {
      value = application.coverLetter;
    }
    
    if (value) {
      await this.fillFormField(page, field.selector, value);
    }
  }

  /**
   * Detect job platform from URL
   */
  detectPlatform(url) {
    const hostname = new URL(url).hostname.toLowerCase();
    
    if (hostname.includes('linkedin.com')) return 'linkedin';
    if (hostname.includes('indeed.com')) return 'indeed';
    if (hostname.includes('glassdoor.com')) return 'glassdoor';
    if (hostname.includes('angel.co') || hostname.includes('wellfound.com')) return 'angellist';
    
    return 'company_website';
  }

  /**
   * Main application method - detects platform and routes accordingly
   */
  async applyToJob(application) {
    const platform = this.detectPlatform(application.jobUrl);
    
    console.log(`Detected platform: ${platform} for ${application.jobUrl}`);
    
    switch (platform) {
      case 'linkedin':
        return await this.applyToLinkedInJob(application);
      case 'indeed':
        return await this.applyToIndeedJob(application);
      case 'glassdoor':
        return await this.applyToGlassdoorJob(application);
      case 'company_website':
      default:
        return await this.applyToCompanyWebsite(application);
    }
  }

  /**
   * Apply to Glassdoor job (placeholder)
   */
  async applyToGlassdoorJob(application) {
    // Similar implementation to LinkedIn/Indeed
    return await this.applyToCompanyWebsite(application);
  }

  /**
   * Close browser and cleanup
   */
  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
      console.log('Browser automation engine cleaned up');
    }
  }

  /**
   * Handle CAPTCHA if present
   */
  async handleCaptcha(page) {
    try {
      // Check for reCAPTCHA
      const captcha = await page.$('.g-recaptcha, iframe[src*="recaptcha"]');
      if (captcha) {
        console.log('CAPTCHA detected, attempting to solve...');
        // The recaptcha plugin will handle this automatically if configured
        await page.waitForTimeout(10000); // Wait for potential solving
      }
    } catch (error) {
      console.log('CAPTCHA handling error:', error.message);
    }
  }

  /**
   * Rotate user agent for anti-detection
   */
  async rotateUserAgent() {
    this.userAgent = new UserAgent();
    if (this.page) {
      await this.page.setUserAgent(this.userAgent.toString());
    }
  }

  /**
   * Get automation statistics
   */
  getStats() {
    return {
      browserActive: !!this.browser,
      sessionsActive: this.sessionData.size,
      userAgent: this.userAgent.toString(),
      config: this.config
    };
  }
}

module.exports = BrowserAutomationEngine;