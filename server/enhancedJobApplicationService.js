const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const BrowserAutomationEngine = require('./browserAutomationEngine');
const ContainerService = require('./services/containerService');
const TerminalService = require('./services/terminalService');
const ApprovalService = require('./services/approvalService');
const JobExecutionAuditService = require('./services/jobExecutionAuditService');

/**
 * Enhanced Job Application Service
 * Provides production-grade autonomous job application capabilities with containerization
 */
class EnhancedJobApplicationService {
  constructor(notificationService = null) {
    this.applicationQueue = [];
    this.isProcessing = false;
    this.activeWorkers = 0;
    this.browserEngine = new BrowserAutomationEngine();
    this.rateLimits = new Map(); // Track rate limits per platform
    this.sessionData = new Map(); // Store session data per platform
    this.failurePatterns = new Map(); // Track failure patterns for learning
    
    // Initialize containerized execution services
    this.containerService = new ContainerService();
    this.terminalService = new TerminalService();
    this.approvalService = new ApprovalService(notificationService);
    this.auditService = new JobExecutionAuditService();
    this.notificationService = notificationService;
    
    this.config = {
      maxConcurrentWorkers: 2,
      delayBetweenApplications: 8000, // 8 seconds base delay
      maxRetries: 3,
      timeoutPerApplication: 600000, // 10 minutes
      rateLimits: {
        linkedin: { requests: 10, window: 3600000 }, // 10 per hour
        indeed: { requests: 20, window: 3600000 }, // 20 per hour
        glassdoor: { requests: 15, window: 3600000 }, // 15 per hour
        company_website: { requests: 30, window: 3600000 } // 30 per hour
      },
      antiDetection: {
        randomDelayRange: [5000, 15000], // Random delay between 5-15 seconds
        userAgentRotationInterval: 10, // Rotate every 10 applications
        maxApplicationsPerSession: 25 // Max applications before restarting browser
      },
      qualityFilters: {
        minCompatibilityScore: 0.6,
        skipLowQualityJobs: true,
        requiresSkillMatch: true
      },
      containerizedExecution: {
        enabled: true, // Enable containerized execution
        requireApproval: true, // Require human approval by default
        autoApprovalThreshold: 0.9, // Auto-approve high-confidence jobs
        maxConcurrentContainers: 3, // Max concurrent containerized jobs
        auditAll: true // Audit all containerized executions
      }
    };

    // Initialize scheduled jobs
    this.initializeScheduler();
    
    // Initialize persistent queue (would use Redis in production)
    this.initializePersistentQueue();
  }

  /**
   * Initialize cron jobs for automated processing
   */
  initializeScheduler() {
    // Process queue every 5 minutes
    cron.schedule('*/5 * * * *', () => {
      if (!this.isProcessing && this.applicationQueue.length > 0) {
        console.log('🕐 Scheduled queue processing started');
        this.processQueue();
      }
    });

    // Daily cleanup at 2 AM
    cron.schedule('0 2 * * *', () => {
      this.performDailyMaintenance();
    });

    // Reset rate limits every hour
    cron.schedule('0 * * * *', () => {
      this.resetRateLimits();
    });
  }

  /**
   * Initialize persistent queue storage
   */
  async initializePersistentQueue() {
    try {
      const queuePath = path.join(__dirname, 'persistent_queue.json');
      const queueData = await fs.readFile(queuePath, 'utf8');
      this.applicationQueue = JSON.parse(queueData);
      console.log(`Loaded ${this.applicationQueue.length} applications from persistent storage`);
    } catch (error) {
      console.log('No persistent queue found, starting fresh');
      this.applicationQueue = [];
    }
  }

  /**
   * Save queue to persistent storage
   */
  async savePersistentQueue() {
    try {
      const queuePath = path.join(__dirname, 'persistent_queue.json');
      await fs.writeFile(queuePath, JSON.stringify(this.applicationQueue, null, 2));
    } catch (error) {
      console.error('Failed to save persistent queue:', error);
    }
  }

  /**
   * Queue a job application with enhanced validation and AI filtering
   */
  async queueApplication(applicationData) {
    // Enhanced validation
    const validationResult = await this.validateApplicationData(applicationData);
    if (!validationResult.valid) {
      throw new Error(`Application validation failed: ${validationResult.errors.join(', ')}`);
    }

    // AI-powered job compatibility scoring
    const compatibilityScore = await this.calculateCompatibilityScore(applicationData);
    
    if (compatibilityScore < this.config.qualityFilters.minCompatibilityScore) {
      console.log(`Skipping low compatibility job (score: ${compatibilityScore}): ${applicationData.jobTitle}`);
      return null;
    }

    // Detect job platform
    const platform = this.detectPlatform(applicationData.jobUrl);
    
    // Check rate limits
    if (this.isRateLimited(platform)) {
      const resetTime = this.getRateLimitResetTime(platform);
      applicationData.scheduledFor = resetTime;
      console.log(`Rate limited for ${platform}, scheduling for ${resetTime}`);
    }

    // Apply timing optimization
    const optimalTiming = this.getOptimalApplicationTiming(applicationData, platform);
    if (optimalTiming.delay > 0) {
      applicationData.scheduledFor = new Date(Date.now() + optimalTiming.delay);
    }

    const queueItem = {
      id: this.generateApplicationId(),
      userId: applicationData.userId,
      jobId: applicationData.jobId,
      resumeId: applicationData.resumeId,
      jobUrl: applicationData.jobUrl,
      jobTitle: applicationData.jobTitle,
      company: applicationData.company,
      coverLetter: applicationData.coverLetter,
      customAnswers: applicationData.customAnswers || {},
      priority: this.calculateDynamicPriority(applicationData, compatibilityScore),
      scheduledFor: applicationData.scheduledFor || new Date(),
      platform: platform,
      compatibilityScore: compatibilityScore,
      status: 'queued',
      attempts: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      automationMetadata: {
        userAgent: null,
        sessionId: null,
        estimatedDuration: this.estimateApplicationDuration(platform),
        riskLevel: this.assessRiskLevel(applicationData)
      }
    };

    this.applicationQueue.push(queueItem);
    this.sortQueueByIntelligentPriority();
    
    // Save to persistent storage
    await this.savePersistentQueue();
    
    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    console.log(`✅ Queued application for ${applicationData.jobTitle} at ${applicationData.company} (score: ${compatibilityScore})`);
    return queueItem;
  }

  /**
   * Enhanced queue processing with worker pool management
   */
  async processQueue() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('🚀 Starting enhanced job application queue processing...');

    try {
      while (this.applicationQueue.length > 0 && this.activeWorkers < this.config.maxConcurrentWorkers) {
        const nextApplication = this.getNextOptimalApplication();
        if (!nextApplication) {
          await this.sleep(5000);
          continue;
        }

        // Check if we need to restart browser for anti-detection
        if (await this.shouldRestartBrowser()) {
          await this.browserEngine.cleanup();
          await this.browserEngine.initialize();
        }

        // Process application in worker
        this.processApplicationWorker(nextApplication);
        
        // Smart delay with randomization
        const delay = this.calculateSmartDelay(nextApplication.platform);
        await this.sleep(delay);
      }
    } finally {
      // Wait for all workers to complete
      while (this.activeWorkers > 0) {
        await this.sleep(1000);
      }
      
      this.isProcessing = false;
      console.log('✅ Enhanced queue processing completed');
    }
  }

  /**
   * Process individual application in worker
   */
  async processApplicationWorker(application) {
    this.activeWorkers++;
    
    try {
      await this.processEnhancedApplication(application);
    } catch (error) {
      console.error(`Worker error for ${application.id}:`, error);
    } finally {
      this.activeWorkers--;
    }
  }

  /**
   * Enhanced application processing with real browser automation
   */
  async processEnhancedApplication(application) {
    application.status = 'processing';
    application.attempts++;
    application.startedAt = new Date();

    console.log(`🤖 Processing application ${application.id} for ${application.jobTitle} at ${application.company} (Platform: ${application.platform})`);

    try {
      // Update rate limiting
      this.updateRateLimit(application.platform);
      
      // Pre-application setup
      await this.prepareApplicationSession(application);
      
      // Real browser automation
      const result = await this.browserEngine.applyToJob(application);
      
      if (result.success) {
        application.status = 'completed';
        application.completedAt = new Date();
        application.submissionId = result.submissionId;
        application.submissionUrl = result.submissionUrl;
        application.automationMetadata.actualDuration = Date.now() - application.startedAt.getTime();
        
        // Record successful application
        await this.recordEnhancedApplicationResult(application, 'success', result);
        
        // Update success patterns for learning
        this.updateSuccessPatterns(application, result);
        
        console.log(`✅ Successfully applied to ${application.jobTitle} at ${application.company} via ${application.platform}`);
      } else {
        throw new Error(result.error || 'Application failed');
      }
    } catch (error) {
      console.error(`❌ Failed to apply to ${application.jobTitle} at ${application.company}:`, error.message);
      
      // Analyze failure pattern
      this.analyzeFailurePattern(application, error);
      
      if (application.attempts < this.config.maxRetries) {
        // Intelligent retry with exponential backoff and jitter
        const delay = this.calculateRetryDelay(application.attempts, application.platform);
        application.status = 'retry_scheduled';
        application.retryAt = new Date(Date.now() + delay);
        
        setTimeout(() => {
          application.status = 'queued';
          this.sortQueueByIntelligentPriority();
        }, delay);
      } else {
        application.status = 'failed';
        application.failedAt = new Date();
        application.error = error.message;
        application.automationMetadata.failureReason = this.categorizeFailure(error);
        
        // Record failed application with detailed analysis
        await this.recordEnhancedApplicationResult(application, 'failed', { 
          error: error.message,
          failureCategory: application.automationMetadata.failureReason,
          attempts: application.attempts
        });
      }
    }

    // Remove from queue if completed or failed
    if (['completed', 'failed'].includes(application.status)) {
      this.removeFromQueue(application.id);
      await this.savePersistentQueue();
    }

    application.updatedAt = new Date();
  }

  /**
   * Validate application data with enhanced checks
   */
  async validateApplicationData(data) {
    const errors = [];
    
    if (!data.jobUrl || !this.isValidUrl(data.jobUrl)) {
      errors.push('Invalid job URL');
    }
    
    if (!data.jobTitle || data.jobTitle.trim().length < 3) {
      errors.push('Invalid job title');
    }
    
    if (!data.company || data.company.trim().length < 2) {
      errors.push('Invalid company name');
    }
    
    if (!data.userId) {
      errors.push('User ID required');
    }

    // Check for duplicate applications
    const isDuplicate = this.applicationQueue.some(app => 
      app.userId === data.userId && 
      app.jobUrl === data.jobUrl && 
      app.status !== 'failed'
    );
    
    if (isDuplicate) {
      errors.push('Duplicate application detected');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate job compatibility score using AI
   */
  async calculateCompatibilityScore(applicationData) {
    // Simulate AI-based compatibility scoring
    // In production, this would use actual AI models
    
    let score = 0.5; // Base score
    
    // Job title relevance (simulate ML analysis)
    const titleRelevance = this.analyzeJobTitleRelevance(applicationData.jobTitle);
    score += titleRelevance * 0.3;
    
    // Company culture fit (simulate analysis)
    const cultureFit = this.analyzeCompanyCultureFit(applicationData.company);
    score += cultureFit * 0.2;
    
    // Location preference
    const locationScore = this.analyzeLocationPreference(applicationData);
    score += locationScore * 0.1;
    
    // Skills match (simulate resume analysis)
    const skillsMatch = this.analyzeSkillsMatch(applicationData);
    score += skillsMatch * 0.3;
    
    // Salary range compatibility
    const salaryMatch = this.analyzeSalaryCompatibility(applicationData);
    score += salaryMatch * 0.1;
    
    return Math.min(1.0, Math.max(0.0, score));
  }

  /**
   * Calculate dynamic priority based on multiple factors
   */
  calculateDynamicPriority(applicationData, compatibilityScore) {
    let priorityScore = 0;
    
    // Compatibility score influence
    priorityScore += compatibilityScore * 40;
    
    // Job posting age (newer is better)
    const postingAge = this.estimateJobPostingAge(applicationData);
    priorityScore += (7 - Math.min(7, postingAge)) * 5; // Max 35 points
    
    // Company tier (simulate company ranking)
    const companyTier = this.getCompanyTier(applicationData.company);
    priorityScore += companyTier * 10; // Max 30 points
    
    // User preference alignment
    const preferenceAlignment = this.getUserPreferenceAlignment(applicationData);
    priorityScore += preferenceAlignment * 15; // Max 15 points
    
    // Competition level (simulate market analysis)
    const competitionLevel = this.analyzeCompetitionLevel(applicationData);
    priorityScore += (5 - competitionLevel) * 4; // Max 20 points
    
    // Convert to priority level
    if (priorityScore >= 80) return 'critical';
    if (priorityScore >= 60) return 'high';
    if (priorityScore >= 40) return 'normal';
    if (priorityScore >= 20) return 'low';
    return 'very_low';
  }

  /**
   * Get optimal application timing with advanced strategies
   */
  getOptimalApplicationTiming(applicationData, platform) {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();
    
    // Platform-specific optimal times
    const optimalTimes = {
      linkedin: { 
        days: [1, 2, 3], 
        hours: [9, 10, 11, 14, 15, 16],
        avoidHours: [0, 1, 2, 3, 4, 5, 6, 22, 23]
      },
      indeed: { 
        days: [1, 2, 3, 4], 
        hours: [8, 9, 10, 13, 14, 15],
        avoidHours: [0, 1, 2, 3, 4, 5, 6, 18, 19, 20, 21, 22, 23]
      },
      glassdoor: { 
        days: [1, 2, 3, 4], 
        hours: [9, 10, 11, 14, 15],
        avoidHours: [0, 1, 2, 3, 4, 5, 6, 17, 18, 19, 20, 21, 22, 23]
      },
      company_website: { 
        days: [1, 2, 3, 4, 5], 
        hours: [9, 10, 11, 13, 14, 15, 16],
        avoidHours: [0, 1, 2, 3, 4, 5, 6, 18, 19, 20, 21, 22, 23]
      }
    };

    const timing = optimalTimes[platform] || optimalTimes.company_website;
    
    // Check if current time is optimal
    if (timing.days.includes(currentDay) && 
        timing.hours.includes(currentHour) && 
        !timing.avoidHours.includes(currentHour)) {
      return { delay: 0, reason: 'Current time is optimal' };
    }
    
    // Find next optimal time
    const nextOptimalTime = this.findNextOptimalTime(now, timing);
    const delay = nextOptimalTime.getTime() - now.getTime();
    
    return {
      delay: delay,
      scheduledTime: nextOptimalTime,
      reason: `Optimized for ${platform} platform timing`
    };
  }

  /**
   * Check if platform is rate limited
   */
  isRateLimited(platform) {
    const limit = this.config.rateLimits[platform];
    if (!limit) return false;
    
    const platformData = this.rateLimits.get(platform);
    if (!platformData) return false;
    
    const now = Date.now();
    const windowStart = now - limit.window;
    
    // Clean old requests
    platformData.requests = platformData.requests.filter(timestamp => timestamp > windowStart);
    
    return platformData.requests.length >= limit.requests;
  }

  /**
   * Update rate limit tracking
   */
  updateRateLimit(platform) {
    if (!this.rateLimits.has(platform)) {
      this.rateLimits.set(platform, { requests: [] });
    }
    
    const platformData = this.rateLimits.get(platform);
    platformData.requests.push(Date.now());
  }

  /**
   * Get next optimal application from queue
   */
  getNextOptimalApplication() {
    const now = new Date();
    
    return this.applicationQueue.find(app => {
      if (app.status !== 'queued') return false;
      if (new Date(app.scheduledFor) > now) return false;
      if (this.isRateLimited(app.platform)) return false;
      
      return true;
    });
  }

  /**
   * Calculate smart delay between applications
   */
  calculateSmartDelay(platform) {
    const baseDelay = this.config.delayBetweenApplications;
    const platformMultiplier = {
      linkedin: 1.5, // LinkedIn is more sensitive
      indeed: 1.0,
      glassdoor: 1.2,
      company_website: 0.8
    };
    
    const multiplier = platformMultiplier[platform] || 1.0;
    const randomFactor = 0.5 + Math.random(); // 0.5 to 1.5
    
    return Math.floor(baseDelay * multiplier * randomFactor);
  }

  /**
   * Calculate retry delay with exponential backoff and jitter
   */
  calculateRetryDelay(attempts, platform) {
    const baseDelay = 60000; // 1 minute
    const exponentialDelay = baseDelay * Math.pow(2, attempts - 1);
    const jitter = Math.random() * 30000; // Up to 30 seconds jitter
    const platformMultiplier = {
      linkedin: 2.0, // LinkedIn needs longer delays
      indeed: 1.5,
      glassdoor: 1.5,
      company_website: 1.0
    };
    
    const multiplier = platformMultiplier[platform] || 1.0;
    return Math.floor((exponentialDelay + jitter) * multiplier);
  }

  /**
   * Sort queue by intelligent priority
   */
  sortQueueByIntelligentPriority() {
    const priorityOrder = { 
      critical: 5, 
      high: 4, 
      normal: 3, 
      low: 2, 
      very_low: 1 
    };
    
    this.applicationQueue.sort((a, b) => {
      // Primary: Priority level
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Secondary: Compatibility score
      const scoreDiff = b.compatibilityScore - a.compatibilityScore;
      if (Math.abs(scoreDiff) > 0.1) return scoreDiff;
      
      // Tertiary: Scheduled time
      return new Date(a.scheduledFor) - new Date(b.scheduledFor);
    });
  }

  /**
   * Enhanced application result recording with analytics
   */
  async recordEnhancedApplicationResult(application, status, result) {
    const logEntry = {
      applicationId: application.id,
      userId: application.userId,
      jobId: application.jobId,
      jobTitle: application.jobTitle,
      company: application.company,
      platform: application.platform,
      status: status,
      submittedAt: new Date().toISOString(),
      attempts: application.attempts,
      compatibilityScore: application.compatibilityScore,
      priority: application.priority,
      automationType: 'enhanced_puppeteer',
      automationMetadata: application.automationMetadata,
      result: result,
      analytics: {
        queueTime: application.startedAt.getTime() - application.createdAt.getTime(),
        processingTime: application.automationMetadata.actualDuration || 0,
        platformUsage: await this.getPlatformUsageStats(),
        timeOfApplication: {
          hour: new Date().getHours(),
          dayOfWeek: new Date().getDay(),
          timestamp: new Date().toISOString()
        }
      }
    };

    try {
      const logPath = path.join(__dirname, 'enhanced_application_logs.json');
      let logs = [];
      
      try {
        const existingLogs = await fs.readFile(logPath, 'utf8');
        logs = JSON.parse(existingLogs);
      } catch (err) {
        // File doesn't exist or is empty
      }
      
      logs.push(logEntry);
      
      // Keep only last 10000 entries to prevent file from growing too large
      if (logs.length > 10000) {
        logs = logs.slice(-10000);
      }
      
      await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Failed to record enhanced application result:', error);
    }
  }

  /**
   * Get enhanced application statistics
   */
  async getEnhancedApplicationStats(userId) {
    try {
      const logPath = path.join(__dirname, 'enhanced_application_logs.json');
      const logs = JSON.parse(await fs.readFile(logPath, 'utf8'));
      
      const userLogs = logs.filter(log => log.userId === userId);
      const last24Hours = userLogs.filter(log => 
        new Date(log.submittedAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      );
      const last7Days = userLogs.filter(log => 
        new Date(log.submittedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      );

      const stats = {
        total: userLogs.length,
        successful: userLogs.filter(log => log.status === 'success').length,
        failed: userLogs.filter(log => log.status === 'failed').length,
        pending: this.applicationQueue.filter(app => app.userId === userId).length,
        successRate: 0,
        averageProcessingTime: 0,
        last24Hours: {
          total: last24Hours.length,
          successful: last24Hours.filter(log => log.status === 'success').length
        },
        last7Days: {
          total: last7Days.length,
          successful: last7Days.filter(log => log.status === 'success').length
        },
        platformBreakdown: this.getPlatformBreakdown(userLogs),
        qualityMetrics: this.calculateQualityMetrics(userLogs),
        recentApplications: userLogs.slice(-10).map(log => ({
          jobTitle: log.jobTitle,
          company: log.company,
          platform: log.platform,
          status: log.status,
          submittedAt: log.submittedAt,
          compatibilityScore: log.compatibilityScore
        }))
      };

      if (stats.total > 0) {
        stats.successRate = (stats.successful / stats.total) * 100;
        const processingTimes = userLogs
          .filter(log => log.analytics?.processingTime)
          .map(log => log.analytics.processingTime);
        
        if (processingTimes.length > 0) {
          stats.averageProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
        }
      }

      return stats;
    } catch (error) {
      return {
        total: 0,
        successful: 0,
        failed: 0,
        pending: 0,
        successRate: 0,
        averageProcessingTime: 0,
        platformBreakdown: {},
        qualityMetrics: {},
        recentApplications: []
      };
    }
  }

  /**
   * Perform daily maintenance tasks
   */
  async performDailyMaintenance() {
    console.log('🔧 Performing daily maintenance...');
    
    // Clean up old queue items
    const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days
    this.applicationQueue = this.applicationQueue.filter(app => 
      new Date(app.createdAt) > cutoffDate
    );
    
    // Rotate browser session for security
    await this.browserEngine.cleanup();
    await this.browserEngine.rotateUserAgent();
    
    // Save cleaned queue
    await this.savePersistentQueue();
    
    // Clear old failure patterns
    this.failurePatterns.clear();
    
    console.log('✅ Daily maintenance completed');
  }

  // Helper methods for simulation (would be replaced with real AI in production)
  analyzeJobTitleRelevance(title) { return 0.7 + Math.random() * 0.3; }
  analyzeCompanyCultureFit(company) { return 0.6 + Math.random() * 0.4; }
  analyzeLocationPreference(data) { return 0.8 + Math.random() * 0.2; }
  analyzeSkillsMatch(data) { return 0.5 + Math.random() * 0.5; }
  analyzeSalaryCompatibility(data) { return 0.7 + Math.random() * 0.3; }
  estimateJobPostingAge(data) { return Math.floor(Math.random() * 7); }
  getCompanyTier(company) { return Math.floor(Math.random() * 4); }
  getUserPreferenceAlignment(data) { return 0.6 + Math.random() * 0.4; }
  analyzeCompetitionLevel(data) { return Math.floor(Math.random() * 5) + 1; }

  // Existing helper methods adapted
  generateApplicationId() {
    return `enhanced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  detectPlatform(url) {
    const hostname = new URL(url).hostname.toLowerCase();
    if (hostname.includes('linkedin.com')) return 'linkedin';
    if (hostname.includes('indeed.com')) return 'indeed';
    if (hostname.includes('glassdoor.com')) return 'glassdoor';
    return 'company_website';
  }

  isValidUrl(string) {
    try {
      const url = new URL(string);
      // Basic security check - reject javascript: and data: protocols
      if (url.protocol === 'javascript:' || url.protocol === 'data:') {
        return false;
      }
      return true;
    } catch (_) {
      return false;
    }
  }

  removeFromQueue(applicationId) {
    this.applicationQueue = this.applicationQueue.filter(app => app.id !== applicationId);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async shouldRestartBrowser() {
    const stats = this.browserEngine.getStats();
    // Restart browser every 25 applications for anti-detection
    return this.applicationsProcessed >= this.config.antiDetection.maxApplicationsPerSession;
  }

  findNextOptimalTime(from, timing) {
    // Implementation similar to original but enhanced
    return new Date(from.getTime() + 3600000); // 1 hour default
  }

  resetRateLimits() {
    this.rateLimits.clear();
  }

  getRateLimitResetTime(platform) {
    return new Date(Date.now() + 3600000); // 1 hour from now
  }

  prepareApplicationSession(application) {
    // Set up session data, user agent, etc.
    return Promise.resolve();
  }

  updateSuccessPatterns(application, result) {
    // Update ML patterns for learning
  }

  analyzeFailurePattern(application, error) {
    // Analyze and categorize failure
  }

  categorizeFailure(error) {
    if (error.message.includes('timeout')) return 'timeout';
    if (error.message.includes('captcha')) return 'captcha';
    if (error.message.includes('rate limit')) return 'rate_limit';
    return 'unknown';
  }

  estimateApplicationDuration(platform) {
    const durations = {
      linkedin: 120000, // 2 minutes
      indeed: 90000,    // 1.5 minutes
      glassdoor: 100000, // 1.67 minutes
      company_website: 180000 // 3 minutes
    };
    return durations[platform] || 120000;
  }

  assessRiskLevel(applicationData) {
    // Assess automation risk level
    return 'low';
  }

  getPlatformUsageStats() {
    // Get platform usage statistics
    return {};
  }

  getPlatformBreakdown(logs) {
    const breakdown = {};
    logs.forEach(log => {
      if (!breakdown[log.platform]) {
        breakdown[log.platform] = { total: 0, successful: 0 };
      }
      breakdown[log.platform].total++;
      if (log.status === 'success') {
        breakdown[log.platform].successful++;
      }
    });
    return breakdown;
  }

  calculateQualityMetrics(logs) {
    const scores = logs.map(log => log.compatibilityScore).filter(score => score);
    return {
      averageCompatibilityScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      highQualityApplications: scores.filter(score => score > 0.8).length,
      totalScoredApplications: scores.length
    };
  }

  /**
   * Get queue status with enhanced information
   */
  getEnhancedQueueStatus() {
    const statusCounts = {};
    const platformCounts = {};
    
    this.applicationQueue.forEach(app => {
      statusCounts[app.status] = (statusCounts[app.status] || 0) + 1;
      platformCounts[app.platform] = (platformCounts[app.platform] || 0) + 1;
    });

    const nextApplication = this.getNextOptimalApplication();

    return {
      total: this.applicationQueue.length,
      isProcessing: this.isProcessing,
      activeWorkers: this.activeWorkers,
      statusBreakdown: statusCounts,
      platformBreakdown: platformCounts,
      nextApplication: nextApplication ? {
        id: nextApplication.id,
        jobTitle: nextApplication.jobTitle,
        company: nextApplication.company,
        platform: nextApplication.platform,
        scheduledFor: nextApplication.scheduledFor
      } : null,
      rateLimitStatus: this.getRateLimitStatus(),
      browserStatus: this.browserEngine ? this.browserEngine.getStats() : { browserActive: false, error: 'Browser engine not available' }
    };
  }

  getRateLimitStatus() {
    const status = {};
    for (const [platform, data] of this.rateLimits.entries()) {
      const limit = this.config.rateLimits[platform];
      status[platform] = {
        current: data.requests.length,
        limit: limit.requests,
        resetTime: new Date(Math.max(...data.requests) + limit.window)
      };
    }
    return status;
  }

  /**
   * Submit job for containerized execution with approval workflow
   */
  async submitJobForContainerizedExecution(applicationData, userId, metadata = {}) {
    if (!this.config.containerizedExecution.enabled) {
      throw new Error('Containerized execution is not enabled');
    }

    try {
      // Log job submission
      this.auditService.logJobApproval('SUBMITTED', userId, applicationData, {
        submissionType: 'containerized',
        metadata
      });

      // Create approval request
      const approval = await this.approvalService.createApprovalRequest(
        applicationData,
        userId,
        {
          ...metadata,
          source: 'enhanced_job_service',
          platform: applicationData.platform,
          confidenceScore: this.calculateConfidenceScore(applicationData)
        }
      );

      console.log(`📋 Submitted job ${applicationData.id} for containerized execution, approval: ${approval.id}`);

      // If auto-approved, start execution immediately
      if (approval.status === 'auto_approved') {
        const executionResult = await this.startContainerizedExecution(applicationData, userId);
        return {
          success: true,
          jobId: applicationData.id,
          approval,
          execution: executionResult,
          message: 'Job auto-approved and execution started'
        };
      }

      return {
        success: true,
        jobId: applicationData.id,
        approval,
        requiresApproval: true,
        message: 'Job submitted for approval'
      };

    } catch (error) {
      console.error('Error submitting job for containerized execution:', error);
      this.auditService.logJobApproval('SUBMISSION_FAILED', userId, applicationData, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Start containerized execution for approved job
   */
  async startContainerizedExecution(applicationData, userId) {
    try {
      // Create terminal session for monitoring
      const terminalId = this.terminalService.createTerminal(
        `container_${applicationData.id}`,
        userId
      );

      // Set up terminal callback for real-time output
      const terminalCallback = (data) => {
        this.terminalService.sendOutput(terminalId, data);
      };

      // Prepare containerized job data
      const containerJobData = {
        ...applicationData,
        executionMode: 'containerized',
        browserConfig: this.getBrowserConfigForContainer(),
        platform: applicationData.platform,
        automationSettings: this.getAutomationSettings(applicationData.platform)
      };

      // Start container
      const containerId = await this.containerService.createJobContainer(
        containerJobData,
        terminalCallback
      );

      // Log container start
      this.auditService.logContainerExecution(
        'STARTED',
        userId,
        containerId,
        applicationData,
        {
          name: `cvleap-job-${containerId}`,
          image: this.containerService.config.baseImage,
          startTime: new Date(),
          executionMode: 'enhanced_automation'
        }
      );

      // Monitor execution
      this.monitorContainerizedExecution(containerId, applicationData, userId, terminalId);

      console.log(`🐳 Started containerized execution for job ${applicationData.id}, container: ${containerId}`);

      return {
        containerId,
        terminalId,
        status: 'started',
        startTime: new Date(),
        monitorUrl: `/terminal/terminal.html?terminalId=${terminalId}`
      };

    } catch (error) {
      console.error('Error starting containerized execution:', error);
      this.auditService.logContainerExecution(
        'FAILED',
        userId,
        null,
        applicationData,
        { error: error.message }
      );
      throw error;
    }
  }

  /**
   * Monitor containerized execution progress
   */
  async monitorContainerizedExecution(containerId, applicationData, userId, terminalId) {
    const checkInterval = setInterval(async () => {
      try {
        const containerInfo = this.containerService.getContainerInfo(containerId);
        
        if (!containerInfo) {
          clearInterval(checkInterval);
          return;
        }

        if (containerInfo.status === 'completed' || containerInfo.status === 'failed') {
          clearInterval(checkInterval);
          
          // Update terminal status
          this.terminalService.updateTerminalStatus(
            terminalId,
            containerInfo.status,
            {
              exitCode: containerInfo.exitCode,
              duration: containerInfo.endTime - containerInfo.startTime,
              jobId: applicationData.id
            }
          );

          // Log completion
          this.auditService.logContainerExecution(
            containerInfo.status === 'completed' ? 'COMPLETED' : 'FAILED',
            userId,
            containerId,
            applicationData,
            {
              exitCode: containerInfo.exitCode,
              executionTime: containerInfo.endTime - containerInfo.startTime,
              endTime: containerInfo.endTime,
              logs: containerInfo.logs
            }
          );

          // Log performance metrics
          this.auditService.logPerformanceMetrics(applicationData.id, {
            executionTime: containerInfo.endTime - containerInfo.startTime,
            success: containerInfo.status === 'completed',
            memoryUsage: 'unknown', // Would be populated by actual container stats
            cpuUsage: 'unknown',
            platform: applicationData.platform,
            company: applicationData.company
          });

          // Update application in queue if it exists
          const queueApplication = this.applicationQueue.find(app => app.id === applicationData.id);
          if (queueApplication) {
            queueApplication.status = containerInfo.status === 'completed' ? 'completed' : 'failed';
            queueApplication.completedAt = containerInfo.endTime;
            queueApplication.containerExecution = {
              containerId,
              terminalId,
              exitCode: containerInfo.exitCode,
              executionTime: containerInfo.endTime - containerInfo.startTime
            };
          }

          // Notify user of completion
          if (this.notificationService) {
            this.notificationService.sendToUser(userId, {
              type: 'containerized_job_completed',
              title: `Job Execution ${containerInfo.status === 'completed' ? 'Completed' : 'Failed'}`,
              message: `Containerized job application for ${applicationData.jobTitle} at ${applicationData.company} has ${containerInfo.status}`,
              data: {
                jobId: applicationData.id,
                containerId,
                terminalId,
                status: containerInfo.status,
                exitCode: containerInfo.exitCode,
                executionTime: containerInfo.endTime - containerInfo.startTime
              }
            });
          }

          console.log(`🐳 Containerized execution ${containerInfo.status} for job ${applicationData.id}`);
        }
      } catch (error) {
        console.error('Error monitoring containerized execution:', error);
        clearInterval(checkInterval);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Calculate confidence score for job application
   */
  calculateConfidenceScore(applicationData) {
    let score = 0.5; // Base score

    // Increase score based on job matching
    if (applicationData.skillMatch && applicationData.skillMatch > 0.7) {
      score += 0.2;
    }

    // Increase score for known platforms
    if (['linkedin', 'indeed', 'glassdoor'].includes(applicationData.platform)) {
      score += 0.1;
    }

    // Increase score if we have custom answers
    if (applicationData.customAnswers && Object.keys(applicationData.customAnswers).length > 0) {
      score += 0.1;
    }

    // Decrease score for new companies
    if (this.approvalService.isNewCompany(applicationData.company)) {
      score -= 0.2;
    }

    return Math.min(Math.max(score, 0), 1); // Clamp between 0 and 1
  }

  /**
   * Get browser configuration for container
   */
  getBrowserConfigForContainer() {
    return {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    };
  }

  /**
   * Get automation settings for platform
   */
  getAutomationSettings(platform) {
    const settings = {
      timeout: this.estimateApplicationDuration(platform),
      retries: this.config.maxRetries,
      antiDetection: this.config.antiDetection
    };

    // Platform-specific settings
    if (platform === 'linkedin') {
      settings.specialHandling = ['captcha', 'skill_questions'];
    } else if (platform === 'indeed') {
      settings.specialHandling = ['quick_apply', 'phone_screening'];
    }

    return settings;
  }

  /**
   * Get enhanced queue status including containerized executions
   */
  getEnhancedQueueStatusWithContainers() {
    const baseStatus = this.getEnhancedQueueStatus();
    
    return {
      ...baseStatus,
      containerized: {
        activeContainers: this.containerService.listActiveContainers().length,
        terminals: this.terminalService.getStats(),
        pendingApprovals: this.approvalService.getStats().pendingApprovals,
        audit: {
          totalEvents: this.auditService.getStats().totalEvents,
          recentActivity: this.auditService.getStats().recentActivity
        }
      },
      config: {
        ...baseStatus.config,
        containerizedExecution: this.config.containerizedExecution
      }
    };
  }
}

module.exports = EnhancedJobApplicationService;