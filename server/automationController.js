const AutomationService = require('./automationService');

class AutomationController {
  constructor() {
    this.automationService = new AutomationService();
  }

  async optimizeApplicationTiming(req, res) {
    try {
      const userId = req.user.id;

      const optimization = await this.automationService.optimizeApplicationTiming(userId);
      
      res.json({
        message: 'Application timing optimization completed successfully',
        optimization
      });
    } catch (error) {
      console.error('Optimize application timing error:', error);
      res.status(500).json({ error: 'Failed to optimize application timing' });
    }
  }

  async analyzeGeographicOpportunities(req, res) {
    try {
      const userId = req.user.id;
      const { targetLocations } = req.body;

      const analysis = await this.automationService.analyzeGeographicOpportunities(
        userId, 
        targetLocations || []
      );
      
      res.json({
        message: 'Geographic opportunities analysis completed successfully',
        analysis
      });
    } catch (error) {
      console.error('Analyze geographic opportunities error:', error);
      res.status(500).json({ error: 'Failed to analyze geographic opportunities' });
    }
  }

  async analyzeCompanyCultureFit(req, res) {
    try {
      const { resumeData, companyData } = req.body;

      if (!resumeData || !companyData) {
        return res.status(400).json({ 
          error: 'Resume data and company data are required' 
        });
      }

      const cultureFit = await this.automationService.analyzeCompanyCultureFit(
        resumeData, 
        companyData
      );
      
      res.json({
        message: 'Company culture fit analysis completed successfully',
        cultureFit
      });
    } catch (error) {
      console.error('Analyze company culture fit error:', error);
      res.status(500).json({ error: 'Failed to analyze company culture fit' });
    }
  }

  async getAntiDetectionStrategy(req, res) {
    try {
      const userId = req.user.id;

      const strategy = await this.automationService.implementAntiDetectionMeasures(userId);
      
      res.json({
        message: 'Anti-detection strategy generated successfully',
        strategy
      });
    } catch (error) {
      console.error('Get anti-detection strategy error:', error);
      res.status(500).json({ error: 'Failed to generate anti-detection strategy' });
    }
  }

  async getApplicationInsights(req, res) {
    try {
      const userId = req.user.id;

      // Get comprehensive application insights
      const [timingOptimization, geographicAnalysis, antiDetectionStrategy] = await Promise.all([
        this.automationService.optimizeApplicationTiming(userId),
        this.automationService.analyzeGeographicOpportunities(userId, []),
        this.automationService.implementAntiDetectionMeasures(userId)
      ]);

      const insights = {
        timing: timingOptimization,
        geographic: geographicAnalysis,
        antiDetection: antiDetectionStrategy,
        summary: this.generateInsightsSummary({
          timing: timingOptimization,
          geographic: geographicAnalysis,
          antiDetection: antiDetectionStrategy
        })
      };
      
      res.json({
        message: 'Application insights generated successfully',
        insights
      });
    } catch (error) {
      console.error('Get application insights error:', error);
      res.status(500).json({ error: 'Failed to generate application insights' });
    }
  }

  async optimizeApplicationStrategy(req, res) {
    try {
      const userId = req.user.id;
      const { targetRole, preferredLocations, companyPreferences } = req.body;

      if (!targetRole) {
        return res.status(400).json({ error: 'Target role is required' });
      }

      // Generate comprehensive optimization strategy
      const strategy = await this.generateOptimizationStrategy(
        userId,
        targetRole,
        preferredLocations || [],
        companyPreferences || {}
      );
      
      res.json({
        message: 'Application strategy optimization completed successfully',
        strategy
      });
    } catch (error) {
      console.error('Optimize application strategy error:', error);
      res.status(500).json({ error: 'Failed to optimize application strategy' });
    }
  }

  async generateOptimizationStrategy(userId, targetRole, preferredLocations, companyPreferences) {
    try {
      // Get timing optimization
      const timingOptimization = await this.automationService.optimizeApplicationTiming(userId);
      
      // Analyze geographic opportunities
      const geographicAnalysis = await this.automationService.analyzeGeographicOpportunities(
        userId, 
        preferredLocations
      );
      
      // Get anti-detection strategy
      const antiDetectionStrategy = await this.automationService.implementAntiDetectionMeasures(userId);

      // Generate personalized recommendations
      const recommendations = this.generatePersonalizedRecommendations({
        targetRole,
        timing: timingOptimization,
        geographic: geographicAnalysis,
        antiDetection: antiDetectionStrategy,
        companyPreferences
      });

      return {
        targetRole,
        timingStrategy: timingOptimization,
        geographicStrategy: geographicAnalysis,
        antiDetectionStrategy,
        recommendations,
        implementationPlan: this.createImplementationPlan(recommendations)
      };
    } catch (error) {
      console.error('Error generating optimization strategy:', error);
      throw error;
    }
  }

  generatePersonalizedRecommendations(data) {
    const recommendations = [];

    // Timing recommendations
    if (data.timing.optimalTiming.length > 0) {
      const bestTiming = data.timing.optimalTiming[0];
      recommendations.push({
        category: 'timing',
        priority: 'high',
        title: `Optimize Application Timing`,
        description: `Apply on ${bestTiming.dayName}s for ${bestTiming.successRate.toFixed(1)}% success rate`,
        actionItems: [
          `Schedule applications for ${bestTiming.dayName}s`,
          'Set up automated reminders for optimal timing',
          'Track results to validate timing effectiveness'
        ],
        estimatedImpact: 'high'
      });
    }

    // Geographic recommendations
    if (data.geographic.recommendations.length > 0) {
      const topGeoRec = data.geographic.recommendations[0];
      recommendations.push({
        category: 'geographic',
        priority: topGeoRec.priority,
        title: `Geographic Targeting Strategy`,
        description: topGeoRec.reason,
        actionItems: [
          `Focus applications on ${topGeoRec.location}`,
          'Research local market conditions',
          'Network with professionals in target location'
        ],
        estimatedImpact: 'medium'
      });
    }

    // Anti-detection recommendations
    if (data.antiDetection.behaviorRecommendations.length > 0) {
      recommendations.push({
        category: 'anti_detection',
        priority: 'medium',
        title: 'Application Pattern Optimization',
        description: 'Improve application patterns to avoid detection',
        actionItems: data.antiDetection.behaviorRecommendations.map(rec => rec.suggestion),
        estimatedImpact: 'medium'
      });
    }

    // Role-specific recommendations
    recommendations.push({
      category: 'role_optimization',
      priority: 'high',
      title: `${data.targetRole} Strategy`,
      description: `Tailored approach for ${data.targetRole} positions`,
      actionItems: [
        'Customize resume for role requirements',
        'Research company-specific requirements',
        'Prepare role-specific interview questions'
      ],
      estimatedImpact: 'high'
    });

    return recommendations;
  }

  createImplementationPlan(recommendations) {
    const plan = {
      immediate: [], // Next 24 hours
      shortTerm: [], // Next week
      mediumTerm: [], // Next month
      longTerm: [] // 1-3 months
    };

    recommendations.forEach(rec => {
      if (rec.priority === 'high') {
        plan.immediate.push({
          task: rec.title,
          description: rec.description,
          actionItems: rec.actionItems.slice(0, 2) // First 2 action items
        });
        
        if (rec.actionItems.length > 2) {
          plan.shortTerm.push({
            task: `Continue ${rec.title}`,
            actionItems: rec.actionItems.slice(2)
          });
        }
      } else if (rec.priority === 'medium') {
        plan.shortTerm.push({
          task: rec.title,
          description: rec.description,
          actionItems: rec.actionItems
        });
      } else {
        plan.mediumTerm.push({
          task: rec.title,
          description: rec.description,
          actionItems: rec.actionItems
        });
      }
    });

    // Add long-term strategic items
    plan.longTerm.push({
      task: 'Strategic Review and Optimization',
      description: 'Review and refine application strategy based on results',
      actionItems: [
        'Analyze performance metrics',
        'Adjust strategy based on market changes',
        'Expand to new markets or roles if successful'
      ]
    });

    return plan;
  }

  generateInsightsSummary(data) {
    const summary = {
      overallHealthScore: 0,
      keyInsights: [],
      actionPriorities: [],
      riskFactors: []
    };

    // Calculate overall health score
    let score = 0;
    let factors = 0;

    if (data.timing.currentSuccessRate) {
      score += Math.min(data.timing.currentSuccessRate / 20 * 100, 100); // Normalize to 100
      factors++;
    }

    if (data.geographic.currentLocationPerformance.overallSuccessRate) {
      score += Math.min(data.geographic.currentLocationPerformance.overallSuccessRate / 20 * 100, 100);
      factors++;
    }

    summary.overallHealthScore = factors > 0 ? score / factors : 50;

    // Generate key insights
    if (data.timing.insights.length > 0) {
      summary.keyInsights.push(`Timing optimization potential: ${data.timing.insights[0].message}`);
    }

    if (data.geographic.recommendations.length > 0) {
      summary.keyInsights.push(`Geographic opportunity: ${data.geographic.recommendations[0].reason}`);
    }

    // Determine action priorities
    if (summary.overallHealthScore < 60) {
      summary.actionPriorities.push('Improve overall application strategy');
    }

    if (data.antiDetection.riskLevel === 'high') {
      summary.riskFactors.push('High detection risk - vary application patterns');
    }

    return summary;
  }
}

module.exports = AutomationController;