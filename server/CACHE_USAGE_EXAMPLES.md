# Enhanced Cache Service Usage Examples

This document provides comprehensive usage examples for the enhanced cache service implementation.

## Basic Usage (Backward Compatible)

```javascript
const cacheService = require('./cacheService');

// Basic operations work exactly as before
cacheService.set('user:123', userData, 300000); // 5 minutes TTL
const user = cacheService.get('user:123');
const exists = cacheService.has('user:123');
```

## Enhanced Statistics and Analytics

```javascript
// Get comprehensive cache statistics
const stats = cacheService.getStats();

console.log(stats.hitRate);                    // "85.2%"
console.log(stats.memoryUsage.total.megabytes); // "2.5"
console.log(stats.efficiency.hotKeys);         // Top 5 accessed keys

// Performance metrics
console.log(stats.performance.avgOperationTime);  // "0.045ms"
console.log(stats.performance.totalOperations);   // 1250

// Eviction statistics
console.log(stats.evictionStats.totalEvictions);  // 45
console.log(stats.evictionStats.lruEvictions);    // 30
console.log(stats.evictionStats.memoryEvictions); // 15

// Access pattern analysis
console.log(stats.accessPatterns.frequencyTiers.hot);  // 15 (25.0%)
console.log(stats.accessPatterns.accessDistribution.median); // 3
```

## Configuration and Memory Management

```javascript
// Configure cache limits at runtime
cacheService.configure({
  maxCacheSize: 2000,              // Maximum number of entries
  maxMemoryBytes: 100 * 1024 * 1024, // 100MB memory limit
  defaultTTL: 600000               // 10 minutes default TTL
});

// Get current configuration
const config = cacheService.getConfiguration();
console.log(config.maxMemoryMB);      // 100
console.log(config.defaultTTLMinutes); // 10
```

## Hot Key Tracking and Analytics

```javascript
// Get detailed hot key analytics
const hotKeys = cacheService.getHotKeys(10);

hotKeys.forEach(key => {
  console.log(`Key: ${key.key}`);
  console.log(`Access Count: ${key.accessCount}`);
  console.log(`Access Share: ${key.accessShare}`);      // "15.2%"
  console.log(`Last Accessed: ${key.lastAccessed}`);    // "2s ago"
  console.log(`Efficiency: ${key.efficiency}`);         // "High"
  console.log(`Avg Time Between Access: ${key.avgTimeBetweenAccess}`); // "30s"
});
```

## Smart TTL Based on Access Patterns

```javascript
// Smart TTL automatically adjusts based on access frequency
cacheService.setWithSmartTTL('user:123', userData, 3);

// Frequently accessed items get longer TTL automatically
// baseAccessCount of 3 means items accessed more than 3 times get extended TTL
// TTL multiplier caps at 5x for highly accessed items
```

## Memory Usage Monitoring

```javascript
const memoryStats = cacheService.getStats().memoryUsage;

console.log(memoryStats.total.bytes);           // Total memory usage
console.log(memoryStats.total.kilobytes);       // "2,048 KB"
console.log(memoryStats.total.megabytes);       // "2.0 MB"

// Detailed breakdown
console.log(memoryStats.breakdown.keys);        // "45.2 KB"
console.log(memoryStats.breakdown.values);      // "1,890.5 KB"
console.log(memoryStats.breakdown.metadata);    // "112.3 KB"
console.log(memoryStats.averageEntrySize);      // "1,024 bytes"
```

## Access Pattern Analysis

```javascript
const accessPatterns = cacheService.getStats().accessPatterns;

// Overall statistics
console.log(accessPatterns.totalEntries);       // 150

// Access distribution
console.log(accessPatterns.accessDistribution.min);    // 0
console.log(accessPatterns.accessDistribution.max);    // 45
console.log(accessPatterns.accessDistribution.median); // 3
console.log(accessPatterns.accessDistribution.p75);    // 8
console.log(accessPatterns.accessDistribution.mean);   // 5.2

// Frequency tiers
console.log(accessPatterns.frequencyTiers.hot);        // 25
console.log(accessPatterns.frequencyTiers.warm);       // 80
console.log(accessPatterns.frequencyTiers.cold);       // 45
console.log(accessPatterns.frequencyTiers.percentages.hot); // "16.7%"

// Recent activity
console.log(accessPatterns.recentActivity.avgTimeSinceAccess); // "45s"
console.log(accessPatterns.recentActivity.avgEntryAge);        // "5m"
```

## Advanced Cache Operations

```javascript
// Pattern-based invalidation
const removed = cacheService.invalidatePattern('^user:');
console.log(`Removed ${removed} user cache entries`);

// User-specific invalidation
cacheService.invalidateUser('123'); // Removes all cache entries for user 123

// Cache with automatic refresh
const data = await cacheService.getOrSet('expensive:data', async () => {
  return await expensiveApiCall();
}, 300000);

// Warm up cache with frequently accessed data
await cacheService.warmUp([
  { key: 'config:app', fetchFunction: () => getAppConfig(), ttl: 3600000 },
  { key: 'users:active', fetchFunction: () => getActiveUsers(), ttl: 600000 }
]);
```

## Express Middleware Integration

```javascript
const express = require('express');
const app = express();

// Use cache middleware for GET requests
app.use('/api/users', cacheService.middleware(300000)); // 5 minutes

// The middleware automatically:
// - Caches successful responses (status 200)
// - Sets X-Cache header (HIT or MISS)
// - Uses URL + user ID as cache key
```

## Performance Monitoring

```javascript
// Monitor cache performance in real-time
setInterval(() => {
  const stats = cacheService.getStats();
  
  // Log key metrics
  console.log('Cache Performance Report:');
  console.log(`- Hit Rate: ${stats.hitRate}`);
  console.log(`- Memory Usage: ${stats.memoryUsage.total.megabytes} MB`);
  console.log(`- Avg Operation Time: ${stats.performance.avgOperationTime}`);
  console.log(`- Recent Evictions: ${stats.evictionStats.totalEvictions}`);
  
  // Alert on low hit rates
  const hitRate = parseFloat(stats.hitRate);
  if (hitRate < 70) {
    console.warn(`Low cache hit rate detected: ${hitRate}%`);
  }
  
  // Alert on high memory usage
  const memoryFill = parseFloat(stats.efficiency.memoryFillRatio);
  if (memoryFill > 90) {
    console.warn(`High memory usage: ${memoryFill}%`);
  }
}, 60000); // Every minute
```

## Cache Health Monitoring

```javascript
// Health check function for monitoring systems
function getCacheHealth() {
  const stats = cacheService.getStats();
  const config = cacheService.getConfiguration();
  
  return {
    status: 'healthy',
    metrics: {
      hitRate: parseFloat(stats.hitRate),
      memoryUsage: {
        current: stats.memoryUsage.total.bytes,
        limit: config.maxMemoryBytes,
        percentage: parseFloat(stats.efficiency.memoryFillRatio)
      },
      performance: {
        avgOperationTime: parseFloat(stats.performance.avgOperationTime),
        totalOperations: stats.performance.totalOperations
      },
      evictions: {
        total: stats.evictionStats.totalEvictions,
        recent: stats.evictionStats.lastEvictionAge !== 'N/A'
      }
    },
    recommendations: getCacheRecommendations(stats)
  };
}

function getCacheRecommendations(stats) {
  const recommendations = [];
  const hitRate = parseFloat(stats.hitRate);
  
  if (hitRate < 70) {
    recommendations.push('Consider increasing cache size or TTL values');
  }
  
  if (stats.evictionStats.memoryEvictions > stats.evictionStats.lruEvictions) {
    recommendations.push('Consider increasing memory limits');
  }
  
  if (stats.accessPatterns.frequencyTiers.cold > stats.accessPatterns.frequencyTiers.hot) {
    recommendations.push('Review caching strategy for better key selection');
  }
  
  return recommendations;
}
```

## Error Handling and Cleanup

```javascript
// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down cache service...');
  const stats = cacheService.getStats();
  console.log(`Final stats - Entries: ${stats.size}, Hit Rate: ${stats.hitRate}`);
  
  cacheService.destroy(); // Cleans up intervals and clears cache
  process.exit(0);
});

// Error handling for cache operations
try {
  const data = cacheService.get('potentially:invalid:key');
  if (data === null) {
    // Handle cache miss
    const freshData = await fetchFromDatabase();
    cacheService.set('potentially:invalid:key', freshData);
  }
} catch (error) {
  console.error('Cache operation failed:', error);
  // Fallback to direct data source
}
```

## Best Practices

1. **Configure Appropriate Limits**: Set memory and size limits based on your system resources
2. **Monitor Hit Rates**: Aim for >80% hit rate for optimal performance
3. **Use Smart TTL**: Let the cache adapt TTL based on access patterns
4. **Regular Health Checks**: Monitor eviction patterns and memory usage
5. **Pattern-Based Invalidation**: Use consistent key patterns for easy invalidation
6. **Graceful Degradation**: Always have fallbacks when cache operations fail

## Migration from Basic Cache

```javascript
// Before (basic cache)
cache.set('key', value);
const data = cache.get('key');

// After (enhanced cache - same interface!)
cacheService.set('key', value);  // Now includes performance tracking
const data = cacheService.get('key');  // Now includes hit/miss statistics

// Plus new capabilities
const insights = cacheService.getStats();  // Rich analytics
cacheService.configure({ maxMemoryBytes: 50 * 1024 * 1024 }); // Runtime tuning
```

The enhanced cache service maintains 100% backward compatibility while providing powerful new capabilities for monitoring, optimization, and performance analysis.