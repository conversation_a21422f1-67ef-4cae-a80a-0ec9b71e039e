const database = require('./database');
const notificationService = require('./notificationService');

/**
 * Comment and Review Controller
 * Handles comments, reviews, suggestions, and collaborative feedback
 */
class CommentController {

  /**
   * Add comment to resume
   */
  async addComment(req, res) {
    try {
      const { resumeId } = req.params;
      const { content, sectionId, positionData, commentType = 'comment' } = req.body;
      const userId = req.user.userId;

      if (!content || content.trim().length === 0) {
        return res.status(400).json({ error: 'Comment content is required' });
      }

      // Check if user has permission to comment on this resume
      const hasPermission = await this.checkCommentPermission(resumeId, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'No permission to comment on this resume' });
      }

      // Create comment
      const commentId = await this.createComment(
        resumeId, 
        userId, 
        content, 
        sectionId, 
        positionData, 
        commentType
      );

      // Get comment details with user info
      const comment = await this.getCommentWithUser(commentId);

      // Get resume details for notification
      const resume = await this.getResumeDetails(resumeId);
      const commenter = await this.getUserById(userId);

      // Notify resume owner and collaborators
      await this.notifyCommentAdded(resume, commenter, sectionId, comment);

      res.status(201).json({
        message: 'Comment added successfully',
        comment
      });
    } catch (error) {
      console.error('Add comment error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Reply to comment
   */
  async replyToComment(req, res) {
    try {
      const { commentId } = req.params;
      const { content } = req.body;
      const userId = req.user.userId;

      if (!content || content.trim().length === 0) {
        return res.status(400).json({ error: 'Reply content is required' });
      }

      // Get parent comment
      const parentComment = await this.getCommentById(commentId);
      if (!parentComment) {
        return res.status(404).json({ error: 'Parent comment not found' });
      }

      // Check permission
      const hasPermission = await this.checkCommentPermission(parentComment.resume_id, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'No permission to reply to this comment' });
      }

      // Create reply
      const replyId = await this.createComment(
        parentComment.resume_id,
        userId,
        content,
        parentComment.section_id,
        parentComment.position_data,
        'reply',
        commentId
      );

      const reply = await this.getCommentWithUser(replyId);

      res.status(201).json({
        message: 'Reply added successfully',
        reply
      });
    } catch (error) {
      console.error('Reply to comment error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get comments for resume
   */
  async getResumeComments(req, res) {
    try {
      const { resumeId } = req.params;
      const userId = req.user.userId;

      // Check permission
      const hasPermission = await this.checkViewPermission(resumeId, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'No permission to view comments' });
      }

      const comments = await this.getCommentsForResume(resumeId);
      
      res.json({ comments });
    } catch (error) {
      console.error('Get resume comments error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update comment
   */
  async updateComment(req, res) {
    try {
      const { commentId } = req.params;
      const { content } = req.body;
      const userId = req.user.userId;

      // Check if user owns the comment
      const comment = await this.getCommentById(commentId);
      if (!comment) {
        return res.status(404).json({ error: 'Comment not found' });
      }

      if (comment.user_id !== userId) {
        return res.status(403).json({ error: 'Can only edit your own comments' });
      }

      // Update comment
      await this.updateCommentContent(commentId, content);

      res.json({ message: 'Comment updated successfully' });
    } catch (error) {
      console.error('Update comment error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete comment
   */
  async deleteComment(req, res) {
    try {
      const { commentId } = req.params;
      const userId = req.user.userId;

      // Check if user owns the comment
      const comment = await this.getCommentById(commentId);
      if (!comment) {
        return res.status(404).json({ error: 'Comment not found' });
      }

      if (comment.user_id !== userId) {
        return res.status(403).json({ error: 'Can only delete your own comments' });
      }

      // Soft delete comment
      await this.softDeleteComment(commentId);

      res.json({ message: 'Comment deleted successfully' });
    } catch (error) {
      console.error('Delete comment error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Request review
   */
  async requestReview(req, res) {
    try {
      const { resumeId } = req.params;
      const { reviewerId, reviewType = 'general', message } = req.body;
      const userId = req.user.userId;

      // Check if user owns the resume
      const resume = await this.getResumeByIdAndUser(resumeId, userId);
      if (!resume) {
        return res.status(404).json({ error: 'Resume not found' });
      }

      // Check if reviewer exists and has permission
      const reviewer = await this.getUserById(reviewerId);
      if (!reviewer) {
        return res.status(404).json({ error: 'Reviewer not found' });
      }

      // Create review request
      const reviewId = await this.createReviewRequest(
        resumeId,
        reviewerId,
        reviewType,
        userId,
        message
      );

      // Notify reviewer
      const requester = await this.getUserById(userId);
      notificationService.sendNotification(reviewerId, {
        category: 'review_request',
        title: 'Review Request',
        message: `${requester.name} requested a review of "${resume.title}"`,
        data: {
          resumeId,
          reviewId,
          requesterName: requester.name,
          resumeTitle: resume.title,
          reviewType
        },
        priority: 'medium'
      });

      res.status(201).json({
        message: 'Review request sent successfully',
        reviewId
      });
    } catch (error) {
      console.error('Request review error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Submit review
   */
  async submitReview(req, res) {
    try {
      const { reviewId } = req.params;
      const { overallRating, feedback, suggestions } = req.body;
      const userId = req.user.userId;

      // Get review details
      const review = await this.getReviewById(reviewId);
      if (!review) {
        return res.status(404).json({ error: 'Review not found' });
      }

      if (review.reviewer_id !== userId) {
        return res.status(403).json({ error: 'Not authorized to submit this review' });
      }

      if (review.status !== 'pending') {
        return res.status(400).json({ error: 'Review already completed' });
      }

      // Update review
      await this.updateReview(reviewId, overallRating, feedback, suggestions);

      // Add suggestions as individual records
      if (suggestions && suggestions.length > 0) {
        await this.addReviewSuggestions(reviewId, suggestions);
      }

      // Get resume and reviewer details for notification
      const resume = await this.getResumeById(review.resume_id);
      const reviewer = await this.getUserById(userId);

      // Notify resume owner
      notificationService.notifyReviewCompleted(
        resume.user_id,
        reviewer.name,
        resume.title,
        overallRating
      );

      res.json({
        message: 'Review submitted successfully'
      });
    } catch (error) {
      console.error('Submit review error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get resume reviews
   */
  async getResumeReviews(req, res) {
    try {
      const { resumeId } = req.params;
      const userId = req.user.userId;

      // Check permission
      const hasPermission = await this.checkViewPermission(resumeId, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'No permission to view reviews' });
      }

      const reviews = await this.getReviewsForResume(resumeId);
      
      res.json({ reviews });
    } catch (error) {
      console.error('Get resume reviews error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get user's review requests
   */
  async getUserReviewRequests(req, res) {
    try {
      const userId = req.user.userId;
      const { status = 'pending' } = req.query;

      const reviews = await this.getUserReviews(userId, status);
      
      res.json({ reviews });
    } catch (error) {
      console.error('Get user review requests error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Database helper methods
   */
  async checkCommentPermission(resumeId, userId) {
    // Check if user owns the resume
    const ownsResume = await this.userOwnsResume(resumeId, userId);
    if (ownsResume) return true;

    // Check collaborative permissions
    return this.hasCollaborativePermission(resumeId, userId, ['view', 'edit', 'admin']);
  }

  async checkViewPermission(resumeId, userId) {
    // Check if user owns the resume
    const ownsResume = await this.userOwnsResume(resumeId, userId);
    if (ownsResume) return true;

    // Check collaborative permissions
    return this.hasCollaborativePermission(resumeId, userId, ['view', 'edit', 'admin']);
  }

  async userOwnsResume(resumeId, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id FROM resumes WHERE id = ? AND user_id = ? AND is_active = 1',
        [resumeId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(!!row);
        }
      );
    });
  }

  async hasCollaborativePermission(resumeId, userId, allowedPermissions) {
    return new Promise((resolve, reject) => {
      database.get().get(
        `SELECT permission_type FROM resume_permissions 
         WHERE resume_id = ? AND user_id = ?`,
        [resumeId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row && allowedPermissions.includes(row.permission_type));
        }
      );
    });
  }

  async createComment(resumeId, userId, content, sectionId, positionData, commentType, parentId = null) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `INSERT INTO resume_comments 
         (resume_id, user_id, parent_comment_id, content, section_id, position_data, comment_type) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [resumeId, userId, parentId, content, sectionId, JSON.stringify(positionData || {}), commentType],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
  }

  async getCommentWithUser(commentId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        `SELECT c.*, u.name as user_name, u.email as user_email 
         FROM resume_comments c 
         JOIN users u ON c.user_id = u.id 
         WHERE c.id = ?`,
        [commentId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getCommentById(commentId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resume_comments WHERE id = ? AND status = "active"',
        [commentId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getCommentsForResume(resumeId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT c.*, u.name as user_name, u.email as user_email 
         FROM resume_comments c 
         JOIN users u ON c.user_id = u.id 
         WHERE c.resume_id = ? AND c.status = 'active'
         ORDER BY c.created_at ASC`,
        [resumeId],
        (err, rows) => {
          if (err) reject(err);
          else {
            // Organize comments into threads
            const commentMap = new Map();
            const rootComments = [];

            // First pass: create comment objects
            rows.forEach(row => {
              commentMap.set(row.id, { ...row, replies: [] });
            });

            // Second pass: organize into threads
            rows.forEach(row => {
              if (row.parent_comment_id) {
                const parent = commentMap.get(row.parent_comment_id);
                if (parent) {
                  parent.replies.push(commentMap.get(row.id));
                }
              } else {
                rootComments.push(commentMap.get(row.id));
              }
            });

            resolve(rootComments);
          }
        }
      );
    });
  }

  async updateCommentContent(commentId, content) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE resume_comments SET content = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [content, commentId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async softDeleteComment(commentId) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE resume_comments SET status = "deleted", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [commentId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async getResumeDetails(resumeId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id, user_id, title FROM resumes WHERE id = ?',
        [resumeId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getUserById(userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT id, name, email FROM users WHERE id = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async notifyCommentAdded(resume, commenter, sectionId, comment) {
    // Notify resume owner if not the commenter
    if (resume.user_id !== commenter.id) {
      notificationService.notifyNewComment(
        resume.user_id,
        commenter.name,
        resume.title,
        sectionId || 'general'
      );
    }

    // Notify other collaborators
    const collaborators = await this.getResumeCollaborators(resume.id);
    collaborators.forEach(collaborator => {
      if (collaborator.user_id !== commenter.id && collaborator.user_id !== resume.user_id) {
        notificationService.notifyNewComment(
          collaborator.user_id,
          commenter.name,
          resume.title,
          sectionId || 'general'
        );
      }
    });
  }

  async getResumeCollaborators(resumeId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        'SELECT user_id FROM resume_permissions WHERE resume_id = ?',
        [resumeId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  async createReviewRequest(resumeId, reviewerId, reviewType, requestedBy, message) {
    return new Promise((resolve, reject) => {
      const reviewData = { message: message || '' };
      database.get().run(
        `INSERT INTO resume_reviews 
         (resume_id, reviewer_id, review_type, requested_by, review_data) 
         VALUES (?, ?, ?, ?, ?)`,
        [resumeId, reviewerId, reviewType, requestedBy, JSON.stringify(reviewData)],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
  }

  async getReviewById(reviewId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resume_reviews WHERE id = ?',
        [reviewId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getResumeById(resumeId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resumes WHERE id = ?',
        [resumeId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async getResumeByIdAndUser(resumeId, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM resumes WHERE id = ? AND user_id = ? AND is_active = 1',
        [resumeId, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  async updateReview(reviewId, rating, feedback, suggestions) {
    return new Promise((resolve, reject) => {
      database.get().run(
        `UPDATE resume_reviews 
         SET overall_rating = ?, feedback = ?, suggestions = ?, 
             status = 'completed', completed_at = CURRENT_TIMESTAMP 
         WHERE id = ?`,
        [rating, feedback, JSON.stringify(suggestions || []), reviewId],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  }

  async addReviewSuggestions(reviewId, suggestions) {
    const promises = suggestions.map(suggestion => {
      return new Promise((resolve, reject) => {
        database.get().run(
          `INSERT INTO review_suggestions 
           (review_id, section_id, suggestion_type, original_text, suggested_text, reasoning) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            reviewId,
            suggestion.sectionId,
            suggestion.type,
            suggestion.originalText,
            suggestion.suggestedText,
            suggestion.reasoning
          ],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    });

    return Promise.all(promises);
  }

  async getReviewsForResume(resumeId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT r.*, u.name as reviewer_name, u.email as reviewer_email 
         FROM resume_reviews r 
         JOIN users u ON r.reviewer_id = u.id 
         WHERE r.resume_id = ?
         ORDER BY r.completed_at DESC, r.requested_at DESC`,
        [resumeId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  async getUserReviews(userId, status) {
    return new Promise((resolve, reject) => {
      database.get().all(
        `SELECT r.*, res.title as resume_title, u.name as requester_name 
         FROM resume_reviews r 
         JOIN resumes res ON r.resume_id = res.id 
         JOIN users u ON r.requested_by = u.id 
         WHERE r.reviewer_id = ? AND r.status = ?
         ORDER BY r.requested_at DESC`,
        [userId, status],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }
}

module.exports = CommentController;