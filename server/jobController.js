const database = require('./database');

class JobController {
  async getJobs(req, res) {
    try {
      const userId = req.user.userId;
      const jobs = await this.getUserJobs(userId);
      res.json(jobs);
    } catch (error) {
      console.error('Get jobs error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async getJob(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const job = await this.getJobById(id, userId);
      if (!job) {
        return res.status(404).json({ error: 'Job not found' });
      }

      res.json(job);
    } catch (error) {
      console.error('Get job error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async createJob(req, res) {
    try {
      const { title, company, location, description, url } = req.body;
      const userId = req.user.userId;

      if (!title || !company) {
        return res.status(400).json({ error: 'Title and company are required' });
      }

      const jobId = await this.createJobRecord(userId, title, company, location, description, url);
      
      res.status(201).json({
        message: 'Job saved successfully',
        id: jobId,
        title,
        company,
        location,
        description,
        url
      });
    } catch (error) {
      console.error('Create job error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async updateJob(req, res) {
    try {
      const { id } = req.params;
      const { title, company, location, description, url, status } = req.body;
      const userId = req.user.userId;

      const job = await this.getJobById(id, userId);
      if (!job) {
        return res.status(404).json({ error: 'Job not found' });
      }

      await this.updateJobRecord(id, title, company, location, description, url, status);
      
      res.json({
        message: 'Job updated successfully',
        id,
        title,
        company,
        location,
        description,
        url,
        status
      });
    } catch (error) {
      console.error('Update job error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async deleteJob(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const job = await this.getJobById(id, userId);
      if (!job) {
        return res.status(404).json({ error: 'Job not found' });
      }

      await this.deleteJobRecord(id);
      
      res.json({ message: 'Job deleted successfully' });
    } catch (error) {
      console.error('Delete job error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  getUserJobs(userId) {
    return new Promise((resolve, reject) => {
      database.get().all(
        'SELECT * FROM jobs WHERE user_id = ? ORDER BY created_at DESC',
        [userId],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        }
      );
    });
  }

  getJobById(id, userId) {
    return new Promise((resolve, reject) => {
      database.get().get(
        'SELECT * FROM jobs WHERE id = ? AND user_id = ?',
        [id, userId],
        (err, row) => {
          if (err) {
            reject(err);
          } else {
            resolve(row);
          }
        }
      );
    });
  }

  createJobRecord(userId, title, company, location, description, url) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'INSERT INTO jobs (user_id, title, company, location, description, url) VALUES (?, ?, ?, ?, ?, ?)',
        [userId, title, company, location, description, url],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  updateJobRecord(id, title, company, location, description, url, status) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'UPDATE jobs SET title = ?, company = ?, location = ?, description = ?, url = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [title, company, location, description, url, status, id],
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  deleteJobRecord(id) {
    return new Promise((resolve, reject) => {
      database.get().run(
        'DELETE FROM jobs WHERE id = ?',
        [id],
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }
}

module.exports = JobController;