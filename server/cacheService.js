class CacheService {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 300000; // 5 minutes
    this.maxCacheSize = 1000; // Maximum number of cached items
    this.maxMemoryBytes = 50 * 1024 * 1024; // 50MB default memory limit
    this.hitCount = 0;
    this.missCount = 0;
    
    // Enhanced statistics tracking
    this.evictionStats = {
      totalEvictions: 0,
      lruEvictions: 0,
      expiredEvictions: 0,
      memoryEvictions: 0,
      lastEvictionTime: null
    };
    
    this.performanceStats = {
      totalOperations: 0,
      totalOperationTime: 0,
      avgOperationTime: 0
    };
    
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }

  /**
   * Measure performance of cache operations
   * @param {Function} operation - Operation to measure
   * @returns {*} Operation result
   */
  measureOperation(operation) {
    const startTime = process.hrtime.bigint();
    const result = operation();
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    this.performanceStats.totalOperations++;
    this.performanceStats.totalOperationTime += duration;
    this.performanceStats.avgOperationTime = this.performanceStats.totalOperationTime / this.performanceStats.totalOperations;
    
    return result;
  }

  // Set cache with TTL
  set(key, value, ttl = this.defaultTTL) {
    return this.measureOperation(() => {
      // Check memory limits before adding
      if (this.shouldEvictForMemory()) {
        this.evictForMemory();
      }
      
      // If cache is at max size, remove oldest entries
      if (this.cache.size >= this.maxCacheSize) {
        this.evictOldest();
      }

      const expiresAt = Date.now() + ttl;
      this.cache.set(key, {
        value,
        expiresAt,
        accessCount: 0,
        createdAt: Date.now()
      });
    });
  }

  /**
   * Get cached value with automatic hit/miss tracking
   * @param {string} key - Cache key
   * @returns {*} Cached value or null if not found/expired
   */
  get(key) {
    return this.measureOperation(() => {
      const item = this.cache.get(key);
      
      if (!item) {
        this.missCount++;
        return null;
      }

      // Check if expired
      if (Date.now() > item.expiresAt) {
        this.cache.delete(key);
        this.missCount++;
        this.evictionStats.expiredEvictions++;
        this.evictionStats.totalEvictions++;
        this.evictionStats.lastEvictionTime = Date.now();
        return null;
      }

      // Update access statistics
      item.accessCount++;
      item.lastAccessed = Date.now();
      this.hitCount++;
      
      return item.value;
    });
  }

  /**
   * Check if key exists and is not expired
   * @param {string} key - Cache key
   * @returns {boolean} True if key exists and not expired
   */
  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete specific cache entry
   * @param {string} key - Cache key
   * @returns {boolean} True if key was deleted
   */
  delete(key) {
    return this.cache.delete(key);
  }

  /**
   * Check if cache should evict entries due to memory limits
   * @returns {boolean} True if memory eviction is needed
   */
  shouldEvictForMemory() {
    const currentMemory = this.getCurrentMemoryUsage();
    return currentMemory > this.maxMemoryBytes;
  }

  /**
   * Get current memory usage in bytes
   * @returns {number} Current memory usage
   */
  getCurrentMemoryUsage() {
    return this.estimateMemoryUsage().total.bytes;
  }

  /**
   * Evict entries to free up memory
   */
  evictForMemory() {
    const initialMemory = this.getCurrentMemoryUsage();
    const targetMemory = this.maxMemoryBytes * 0.8; // Target 80% of max memory
    let evictedCount = 0;
    
    // Sort entries by LRU strategy for memory eviction
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => {
        const timeDiff = (a.lastAccessed || a.createdAt) - (b.lastAccessed || b.createdAt);
        return timeDiff !== 0 ? timeDiff : a.accessCount - b.accessCount;
      });
    
    for (const [key] of sortedEntries) {
      this.cache.delete(key);
      evictedCount++;
      
      if (this.getCurrentMemoryUsage() <= targetMemory) {
        break;
      }
    }
    
    this.evictionStats.memoryEvictions += evictedCount;
    this.evictionStats.totalEvictions += evictedCount;
    this.evictionStats.lastEvictionTime = Date.now();
    
    const freedMemory = initialMemory - this.getCurrentMemoryUsage();
    console.log(`Memory eviction: removed ${evictedCount} entries, freed ~${Math.round(freedMemory / 1024)}KB`);
  }

  /**
   * Clear all cache entries and reset statistics
   */
  clear() {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    // Reset eviction stats but keep performance stats for analysis
    this.evictionStats = {
      totalEvictions: 0,
      lruEvictions: 0,
      expiredEvictions: 0,
      memoryEvictions: 0,
      lastEvictionTime: null
    };
  }

  /**
   * Remove expired entries with detailed logging
   */
  cleanup() {
    const now = Date.now();
    let removedCount = 0;
    let freedMemory = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        // Estimate memory freed (rough calculation)
        freedMemory += this.estimateItemSize(item);
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      this.evictionStats.expiredEvictions += removedCount;
      this.evictionStats.totalEvictions += removedCount;
      this.evictionStats.lastEvictionTime = now;
      console.log(`Cache cleanup: removed ${removedCount} expired entries, freed ~${Math.round(freedMemory / 1024)}KB`);
    }
  }

  /**
   * Estimate memory size of cache item (rough calculation)
   * @param {Object} item - Cache item
   * @returns {number} Estimated size in bytes
   */
  estimateItemSize(item) {
    try {
      return JSON.stringify(item).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1024; // Default estimate
    }
  }

  /**
   * Evict oldest or least accessed entries when cache is full
   * Uses LRU (Least Recently Used) strategy
   */
  evictOldest() {
    const entriesToRemove = Math.ceil(this.maxCacheSize * 0.1); // Remove 10% of cache
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => {
        // Sort by last accessed time, then by access count
        const timeDiff = (a.lastAccessed || a.createdAt) - (b.lastAccessed || b.createdAt);
        return timeDiff !== 0 ? timeDiff : a.accessCount - b.accessCount;
      });
    
    for (let i = 0; i < entriesToRemove && i < sortedEntries.length; i++) {
      const [key] = sortedEntries[i];
      this.cache.delete(key);
    }
    
    this.evictionStats.lruEvictions += entriesToRemove;
    this.evictionStats.totalEvictions += entriesToRemove;
    this.evictionStats.lastEvictionTime = Date.now();
    
    console.log(`Cache eviction: removed ${entriesToRemove} oldest entries`);
  }
  /**
   * Get comprehensive cache statistics with performance metrics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0;
    const memoryUsage = this.estimateMemoryUsage();
    
    // Calculate additional performance metrics
    const averageAccessCount = this.getAverageAccessCount();
    const oldestEntry = this.getOldestEntry();
    const newestEntry = this.getNewestEntry();
    const accessPatterns = this.getAccessPatterns();
    
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      maxMemoryBytes: this.maxMemoryBytes,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: hitRate.toFixed(2) + '%',
      totalRequests,
      memoryUsage,
      averageAccessCount: averageAccessCount.toFixed(2),
      oldestEntryAge: oldestEntry ? Math.round((Date.now() - oldestEntry.createdAt) / 1000) + 's' : 'N/A',
      newestEntryAge: newestEntry ? Math.round((Date.now() - newestEntry.createdAt) / 1000) + 's' : 'N/A',
      performance: {
        totalOperations: this.performanceStats.totalOperations,
        avgOperationTime: this.performanceStats.avgOperationTime.toFixed(3) + 'ms',
        totalOperationTime: this.performanceStats.totalOperationTime.toFixed(2) + 'ms'
      },
      evictionStats: {
        ...this.evictionStats,
        lastEvictionAge: this.evictionStats.lastEvictionTime ? 
          Math.round((Date.now() - this.evictionStats.lastEvictionTime) / 1000) + 's' : 'N/A'
      },
      efficiency: {
        fillRatio: ((this.cache.size / this.maxCacheSize) * 100).toFixed(1) + '%',
        memoryFillRatio: ((memoryUsage.total.bytes / this.maxMemoryBytes) * 100).toFixed(1) + '%',
        avgTtl: this.getAverageTTL() + 'ms',
        hotKeys: this.getHotKeys(5)
      },
      accessPatterns
    };
  }

  /**
   * Get average access count across all cache entries
   * @returns {number} Average access count
   */
  getAverageAccessCount() {
    if (this.cache.size === 0) return 0;
    
    let totalAccess = 0;
    for (const [, item] of this.cache.entries()) {
      totalAccess += item.accessCount;
    }
    
    return totalAccess / this.cache.size;
  }

  /**
   * Get oldest cache entry
   * @returns {Object|null} Oldest entry or null
   */
  getOldestEntry() {
    let oldest = null;
    
    for (const [, item] of this.cache.entries()) {
      if (!oldest || item.createdAt < oldest.createdAt) {
        oldest = item;
      }
    }
    
    return oldest;
  }

  /**
   * Get newest cache entry
   * @returns {Object|null} Newest entry or null
   */
  getNewestEntry() {
    let newest = null;
    
    for (const [, item] of this.cache.entries()) {
      if (!newest || item.createdAt > newest.createdAt) {
        newest = item;
      }
    }
    
    return newest;
  }

  /**
   * Get average TTL of current cache entries
   * @returns {number} Average TTL in milliseconds
   */
  getAverageTTL() {
    if (this.cache.size === 0) return 0;
    
    const now = Date.now();
    let totalTTL = 0;
    
    for (const [, item] of this.cache.entries()) {
      totalTTL += Math.max(0, item.expiresAt - now);
    }
    
    return Math.round(totalTTL / this.cache.size);
  }

  /**
   * Get detailed access pattern analysis
   * @returns {Object} Access pattern statistics
   */
  getAccessPatterns() {
    if (this.cache.size === 0) {
      return {
        totalEntries: 0,
        accessDistribution: {},
        frequencyTiers: {},
        recentActivity: {}
      };
    }

    const now = Date.now();
    const accessCounts = [];
    const accessTimes = [];
    const entryAges = [];
    
    for (const [, item] of this.cache.entries()) {
      accessCounts.push(item.accessCount);
      if (item.lastAccessed) {
        accessTimes.push(now - item.lastAccessed);
      }
      entryAges.push(now - item.createdAt);
    }
    
    // Calculate access distribution
    const sortedAccess = [...accessCounts].sort((a, b) => a - b);
    const median = sortedAccess[Math.floor(sortedAccess.length / 2)];
    const p75 = sortedAccess[Math.floor(sortedAccess.length * 0.75)];
    const p90 = sortedAccess[Math.floor(sortedAccess.length * 0.90)];
    
    // Categorize entries by access frequency
    let cold = 0, warm = 0, hot = 0;
    for (const count of accessCounts) {
      if (count === 0) {
        cold++;
      } else if (count >= p75) {
        hot++;
      } else {
        warm++;
      }
    }
    
    return {
      totalEntries: this.cache.size,
      accessDistribution: {
        min: Math.min(...accessCounts),
        max: Math.max(...accessCounts),
        median: median,
        p75: p75,
        p90: p90,
        mean: accessCounts.reduce((a, b) => a + b, 0) / accessCounts.length
      },
      frequencyTiers: {
        cold: cold,
        warm: warm,
        hot: hot,
        percentages: {
          cold: ((cold / this.cache.size) * 100).toFixed(1) + '%',
          warm: ((warm / this.cache.size) * 100).toFixed(1) + '%',
          hot: ((hot / this.cache.size) * 100).toFixed(1) + '%'
        }
      },
      recentActivity: {
        avgTimeSinceAccess: accessTimes.length > 0 ? 
          Math.round(accessTimes.reduce((a, b) => a + b, 0) / accessTimes.length / 1000) + 's' : 'N/A',
        avgEntryAge: Math.round(entryAges.reduce((a, b) => a + b, 0) / entryAges.length / 1000) + 's'
      }
    };
  }

  /**
   * Get most frequently accessed cache keys
   * @param {number} count - Number of hot keys to return
   * @returns {Array} Array of hot keys with access counts
   */
  getHotKeys(count = 10) {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => b.accessCount - a.accessCount)
      .slice(0, count);
    
    const totalAccess = entries.reduce((sum, [, item]) => sum + item.accessCount, 0);
    
    return entries.map(([key, item]) => {
      const accessShare = totalAccess > 0 ? (item.accessCount / totalAccess * 100) : 0;
      const avgTimesBetweenAccess = item.accessCount > 1 && item.lastAccessed ? 
        (item.lastAccessed - item.createdAt) / item.accessCount : null;
      
      return {
        key: key.length > 50 ? key.substring(0, 50) + '...' : key,
        accessCount: item.accessCount,
        accessShare: accessShare.toFixed(1) + '%',
        hitRate: item.accessCount > 0 ? 100 : 0,
        avgTimeBetweenAccess: avgTimesBetweenAccess ? 
          Math.round(avgTimesBetweenAccess / 1000) + 's' : 'N/A',
        lastAccessed: item.lastAccessed ? 
          Math.round((Date.now() - item.lastAccessed) / 1000) + 's ago' : 'Never',
        efficiency: item.accessCount > 0 ? 'High' : 'Low'
      };
    });
  }

  /**
   * Enhanced memory usage estimation with detailed breakdown
   * @returns {Object} Memory usage statistics
   */
  estimateMemoryUsage() {
    let totalSize = 0;
    let keysSize = 0;
    let valuesSize = 0;
    let metadataSize = 0;
    
    for (const [key, item] of this.cache.entries()) {
      const keySize = key.length * 2; // UTF-16 characters
      const valueSize = this.estimateItemSize(item);
      const itemMetadataSize = 96; // Rough estimate for timestamps, counters, etc.
      
      keysSize += keySize;
      valuesSize += valueSize;
      metadataSize += itemMetadataSize;
      totalSize += keySize + valueSize + itemMetadataSize;
    }
    
    return {
      total: {
        bytes: totalSize,
        kilobytes: (totalSize / 1024).toFixed(2),
        megabytes: (totalSize / (1024 * 1024)).toFixed(3)
      },
      breakdown: {
        keys: (keysSize / 1024).toFixed(2) + ' KB',
        values: (valuesSize / 1024).toFixed(2) + ' KB',
        metadata: (metadataSize / 1024).toFixed(2) + ' KB'
      },
      averageEntrySize: this.cache.size > 0 ? (totalSize / this.cache.size).toFixed(0) + ' bytes' : '0 bytes'
    };
  }

  // Cache middleware for Express
  middleware(ttl = this.defaultTTL) {
    return (req, res, next) => {
      // Only cache GET requests
      if (req.method !== 'GET') {
        return next();
      }

      // Create cache key from URL and query parameters
      const cacheKey = `${req.originalUrl}:${req.user?.userId || 'anonymous'}`;
      
      // Try to get from cache
      const cachedData = this.get(cacheKey);
      if (cachedData) {
        res.setHeader('X-Cache', 'HIT');
        return res.json(cachedData);
      }

      // Store original json method
      const originalJson = res.json;
      
      // Override json method to cache response
      res.json = (data) => {
        // Cache successful responses only
        if (res.statusCode === 200) {
          this.set(cacheKey, data, ttl);
        }
        
        res.setHeader('X-Cache', 'MISS');
        return originalJson.call(res, data);
      };

      next();
    };
  }

  // Invalidate cache by pattern
  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    let removedCount = 0;
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    return removedCount;
  }

  // Invalidate cache for specific user
  invalidateUser(userId) {
    return this.invalidatePattern(`:${userId}$`);
  }

  // Cache with automatic refresh
  async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
    const cached = this.get(key);
    if (cached) {
      return cached;
    }

    try {
      const data = await fetchFunction();
      this.set(key, data, ttl);
      return data;
    } catch (error) {
      console.error('Cache getOrSet error:', error);
      throw error;
    }
  }

  // Warm up cache with frequently accessed data
  async warmUp(warmUpData) {
    console.log('Warming up cache...');
    
    for (const { key, fetchFunction, ttl } of warmUpData) {
      try {
        const data = await fetchFunction();
        this.set(key, data, ttl);
      } catch (error) {
        console.warn(`Failed to warm up cache for key ${key}:`, error.message);
      }
    }
    
    console.log(`Cache warmed up with ${warmUpData.length} entries`);
  }

  // Smart caching based on access patterns
  setWithSmartTTL(key, value, baseAccessCount = 1) {
    const existingItem = this.cache.get(key);
    let ttl = this.defaultTTL;
    
    // If item exists and is frequently accessed, increase TTL
    if (existingItem && existingItem.accessCount > baseAccessCount) {
      const multiplier = Math.min(existingItem.accessCount / baseAccessCount, 5); // Max 5x
      ttl = this.defaultTTL * multiplier;
    }
    
    // Check memory limits before adding
    if (this.shouldEvictForMemory()) {
      this.evictForMemory();
    }
    
    // If cache is at max size, remove oldest entries
    if (this.cache.size >= this.maxCacheSize && !existingItem) {
      this.evictOldest();
    }

    const expiresAt = Date.now() + ttl;
    const newItem = {
      value,
      expiresAt,
      accessCount: existingItem ? existingItem.accessCount : 0,
      createdAt: existingItem ? existingItem.createdAt : Date.now(),
      lastAccessed: existingItem ? existingItem.lastAccessed : undefined
    };
    
    this.cache.set(key, newItem);
  }

  /**
   * Configure cache limits
   * @param {Object} options - Configuration options
   */
  configure(options = {}) {
    if (options.maxCacheSize && options.maxCacheSize > 0) {
      this.maxCacheSize = options.maxCacheSize;
    }
    if (options.maxMemoryBytes && options.maxMemoryBytes > 0) {
      this.maxMemoryBytes = options.maxMemoryBytes;
    }
    if (options.defaultTTL && options.defaultTTL > 0) {
      this.defaultTTL = options.defaultTTL;
    }
    
    console.log(`Cache configured: maxSize=${this.maxCacheSize}, maxMemory=${Math.round(this.maxMemoryBytes / 1024 / 1024)}MB, defaultTTL=${this.defaultTTL}ms`);
  }

  /**
   * Get current cache configuration
   * @returns {Object} Current configuration
   */
  getConfiguration() {
    return {
      maxCacheSize: this.maxCacheSize,
      maxMemoryBytes: this.maxMemoryBytes,
      maxMemoryMB: Math.round(this.maxMemoryBytes / 1024 / 1024),
      defaultTTL: this.defaultTTL,
      defaultTTLMinutes: Math.round(this.defaultTTL / 60000)
    };
  }

  // Destroy cache service
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;