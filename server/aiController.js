const AIService = require('./aiService');

class AIController {
  constructor() {
    this.aiService = new AIService();
  }

  async enhanceResume(req, res) {
    try {
      const { resumeData, targetJob, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const enhancement = await this.aiService.enhanceResumeContent(
        resumeData, 
        targetJob,
        { preferredModel }
      );
      
      res.json({
        message: 'Resume enhanced successfully',
        enhancement
      });
    } catch (error) {
      console.error('Enhance resume error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to enhance resume' });
    }
  }

  async generateCoverLetter(req, res) {
    try {
      const { resumeData, jobDescription, companyName, preferredModel } = req.body;

      if (!resumeData || !jobDescription) {
        return res.status(400).json({ 
          error: 'Resume data and job description are required' 
        });
      }

      const result = await this.aiService.generateCoverLetter(
        resumeData, 
        jobDescription, 
        companyName || 'the company',
        { preferredModel }
      );
      
      res.json({
        message: 'Cover letter generated successfully',
        coverLetter: result.content,
        metadata: result._metadata
      });
    } catch (error) {
      console.error('Generate cover letter error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to generate cover letter' });
    }
  }

  async analyzeATS(req, res) {
    try {
      const { resumeData, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const analysis = await this.aiService.analyzeATSCompatibility(
        resumeData,
        { preferredModel }
      );
      
      res.json({
        message: 'ATS analysis completed successfully',
        analysis
      });
    } catch (error) {
      console.error('ATS analysis error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to analyze ATS compatibility' });
    }
  }

  async suggestSkills(req, res) {
    try {
      const { currentSkills, jobTitle, industry, preferredModel } = req.body;

      if (!currentSkills || !jobTitle) {
        return res.status(400).json({ 
          error: 'Current skills and job title are required' 
        });
      }

      const suggestions = await this.aiService.suggestSkills(
        currentSkills, 
        jobTitle, 
        industry,
        { preferredModel }
      );
      
      res.json({
        message: 'Skill suggestions generated successfully',
        suggestions
      });
    } catch (error) {
      console.error('Skill suggestion error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to suggest skills' });
    }
  }

  async simulateInterview(req, res) {
    try {
      const { resumeData, jobDescription, industry, difficulty } = req.body;

      if (!resumeData || !jobDescription) {
        return res.status(400).json({ 
          error: 'Resume data and job description are required' 
        });
      }

      const simulation = await this.aiService.simulateInterview(
        resumeData, 
        jobDescription, 
        industry || '', 
        difficulty || 'medium'
      );
      
      res.json({
        message: 'Interview simulation completed successfully',
        simulation
      });
    } catch (error) {
      console.error('Interview simulation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to simulate interview' });
    }
  }

  async predictJobSuccess(req, res) {
    try {
      const { resumeData, jobDescription, companyData } = req.body;

      if (!resumeData || !jobDescription) {
        return res.status(400).json({ 
          error: 'Resume data and job description are required' 
        });
      }

      const prediction = await this.aiService.predictJobSuccessScore(
        resumeData, 
        jobDescription, 
        companyData || {}
      );
      
      res.json({
        message: 'Job success prediction completed successfully',
        prediction
      });
    } catch (error) {
      console.error('Job success prediction error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to predict job success' });
    }
  }

  async generateIndustryPrompts(req, res) {
    try {
      const { industry, jobRole, promptType } = req.body;

      if (!industry || !jobRole) {
        return res.status(400).json({ 
          error: 'Industry and job role are required' 
        });
      }

      const prompts = await this.aiService.generateIndustrySpecificPrompt(
        industry, 
        jobRole, 
        promptType || 'enhancement'
      );
      
      res.json({
        message: 'Industry-specific prompts generated successfully',
        prompts
      });
    } catch (error) {
      console.error('Industry prompts generation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to generate industry prompts' });
    }
  }

  async generateResumeVariations(req, res) {
    try {
      const { resumeData, targetJob, variationCount } = req.body;

      if (!resumeData || !targetJob) {
        return res.status(400).json({ 
          error: 'Resume data and target job are required' 
        });
      }

      const variations = await this.aiService.generateResumeVariations(
        resumeData, 
        targetJob, 
        variationCount || 3
      );
      
      res.json({
        message: 'Resume variations generated successfully',
        variations
      });
    } catch (error) {
      console.error('Resume variations generation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to generate resume variations' });
    }
  }

  async getModelStatus(req, res) {
    try {
      const status = this.aiService.getModelStatus();
      res.json(status);
    } catch (error) {
      console.error('Get model status error:', error);
      res.status(500).json({ error: 'Failed to get model status' });
    }
  }

  async getAvailableModels(req, res) {
    try {
      const models = this.aiService.getAvailableModels();
      res.json({ models });
    } catch (error) {
      console.error('Get available models error:', error);
      res.status(500).json({ error: 'Failed to get available models' });
    }
  }

  // Advanced Resume Analysis Engine Controllers
  async advancedResumeScoring(req, res) {
    try {
      const { resumeData, targetJob, industry, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const analysis = await this.aiService.advancedResumeScoring(
        resumeData,
        targetJob || '',
        industry || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Advanced resume analysis completed successfully',
        analysis
      });
    } catch (error) {
      console.error('Advanced resume scoring error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to analyze resume' });
    }
  }

  async detectWeaknesses(req, res) {
    try {
      const { resumeData, targetJob, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const weaknesses = await this.aiService.detectResumeWeaknesses(
        resumeData,
        targetJob || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Resume weaknesses detected successfully',
        weaknesses
      });
    } catch (error) {
      console.error('Weakness detection error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to detect weaknesses' });
    }
  }

  async amplifyStrengths(req, res) {
    try {
      const { resumeData, targetJob, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const strengths = await this.aiService.amplifyResumeStrengths(
        resumeData,
        targetJob || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Resume strengths amplified successfully',
        strengths
      });
    } catch (error) {
      console.error('Strength amplification error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to amplify strengths' });
    }
  }

  // Real-time AI Assistant Controllers
  async chatAssistant(req, res) {
    try {
      const { message, context, conversationHistory, preferredModel } = req.body;

      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      const response = await this.aiService.chatAssistant(
        message,
        context || {},
        conversationHistory || [],
        { preferredModel }
      );
      
      res.json({
        message: 'Chat response generated successfully',
        response
      });
    } catch (error) {
      console.error('Chat assistant error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to process chat message' });
    }
  }

  async getRealTimeSuggestions(req, res) {
    try {
      const { currentText, sectionType, targetJob, preferredModel } = req.body;

      if (!currentText || !sectionType) {
        return res.status(400).json({ 
          error: 'Current text and section type are required' 
        });
      }

      const suggestions = await this.aiService.generateRealTimeSuggestions(
        currentText,
        sectionType,
        targetJob || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Real-time suggestions generated successfully',
        suggestions
      });
    } catch (error) {
      console.error('Real-time suggestions error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to generate suggestions' });
    }
  }

  // Document Processing Controllers
  async parseResumeContent(req, res) {
    try {
      const { rawText, format, preferredModel } = req.body;

      if (!rawText) {
        return res.status(400).json({ error: 'Raw text is required' });
      }

      const parsedData = await this.aiService.parseResumeContent(
        rawText,
        format || 'unknown',
        { preferredModel }
      );
      
      res.json({
        message: 'Resume content parsed successfully',
        parsedData
      });
    } catch (error) {
      console.error('Resume parsing error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to parse resume content' });
    }
  }

  async extractIntelligentData(req, res) {
    try {
      const { rawText, extractionType, preferredModel } = req.body;

      if (!rawText) {
        return res.status(400).json({ error: 'Raw text is required' });
      }

      const extractedData = await this.aiService.intelligentDataExtraction(
        rawText,
        extractionType || 'comprehensive',
        { preferredModel }
      );
      
      res.json({
        message: 'Data extraction completed successfully',
        extractedData
      });
    } catch (error) {
      console.error('Data extraction error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to extract data' });
    }
  }

  async detectDuplicates(req, res) {
    try {
      const { resumeData1, resumeData2, preferredModel } = req.body;

      if (!resumeData1 || !resumeData2) {
        return res.status(400).json({ 
          error: 'Two resume datasets are required for comparison' 
        });
      }

      const duplicateAnalysis = await this.aiService.detectDuplicateContent(
        resumeData1,
        resumeData2,
        { preferredModel }
      );
      
      res.json({
        message: 'Duplicate detection completed successfully',
        duplicateAnalysis
      });
    } catch (error) {
      console.error('Duplicate detection error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to detect duplicates' });
    }
  }

  async compareVersions(req, res) {
    try {
      const { oldVersion, newVersion, preferredModel } = req.body;

      if (!oldVersion || !newVersion) {
        return res.status(400).json({ 
          error: 'Both old and new resume versions are required' 
        });
      }

      const comparison = await this.aiService.compareResumeVersions(
        oldVersion,
        newVersion,
        { preferredModel }
      );
      
      res.json({
        message: 'Resume version comparison completed successfully',
        comparison
      });
    } catch (error) {
      console.error('Version comparison error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to compare versions' });
    }
  }

  // Template Selection and Design Optimization Controllers
  async recommendTemplate(req, res) {
    try {
      const { resumeData, targetJob, industry, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const recommendation = await this.aiService.recommendOptimalTemplate(
        resumeData,
        targetJob || '',
        industry || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Template recommendation generated successfully',
        recommendation
      });
    } catch (error) {
      console.error('Template recommendation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to recommend template' });
    }
  }

  async optimizeLayout(req, res) {
    try {
      const { resumeData, templateType, preferredModel } = req.body;

      if (!resumeData) {
        return res.status(400).json({ error: 'Resume data is required' });
      }

      const optimization = await this.aiService.optimizeResumeLayout(
        resumeData,
        templateType || 'professional',
        { preferredModel }
      );
      
      res.json({
        message: 'Layout optimization completed successfully',
        optimization
      });
    } catch (error) {
      console.error('Layout optimization error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to optimize layout' });
    }
  }

  // Multi-language and Cultural Adaptation Controllers
  async translateResume(req, res) {
    try {
      const { resumeData, targetLanguage, targetRegion, preferredModel } = req.body;

      if (!resumeData || !targetLanguage) {
        return res.status(400).json({ 
          error: 'Resume data and target language are required' 
        });
      }

      const translation = await this.aiService.translateResume(
        resumeData,
        targetLanguage,
        targetRegion || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Resume translation completed successfully',
        translation
      });
    } catch (error) {
      console.error('Resume translation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to translate resume' });
    }
  }

  async adaptForRegion(req, res) {
    try {
      const { resumeData, targetRegion, targetRole, preferredModel } = req.body;

      if (!resumeData || !targetRegion) {
        return res.status(400).json({ 
          error: 'Resume data and target region are required' 
        });
      }

      const adaptation = await this.aiService.adaptForRegionalMarket(
        resumeData,
        targetRegion,
        targetRole || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Regional adaptation completed successfully',
        adaptation
      });
    } catch (error) {
      console.error('Regional adaptation error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to adapt for region' });
    }
  }

  async quantifyAchievements(req, res) {
    try {
      const { achievements, jobContext, preferredModel } = req.body;

      if (!achievements) {
        return res.status(400).json({ error: 'Achievements are required' });
      }

      const result = await this.aiService.quantifyAchievements(
        achievements,
        jobContext || '',
        { preferredModel }
      );
      
      res.json({
        message: 'Achievements quantified successfully',
        result
      });
    } catch (error) {
      console.error('Achievement quantification error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to quantify achievements' });
    }
  }

  async personalizeForCompanyCulture(req, res) {
    try {
      const { resumeData, companyInfo, preferredModel } = req.body;

      if (!resumeData || !companyInfo) {
        return res.status(400).json({ error: 'Resume data and company information are required' });
      }

      const result = await this.aiService.personalizeForCompanyCulture(
        resumeData,
        companyInfo,
        { preferredModel }
      );
      
      res.json({
        message: 'Content personalized successfully',
        result
      });
    } catch (error) {
      console.error('Content personalization error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to personalize content' });
    }
  }

  async recommendResumeSections(req, res) {
    try {
      const { industry, jobRole, experienceLevel, preferredModel } = req.body;

      if (!industry || !jobRole) {
        return res.status(400).json({ error: 'Industry and job role are required' });
      }

      const result = await this.aiService.recommendResumeSections(
        industry,
        jobRole,
        experienceLevel || 'mid',
        { preferredModel }
      );
      
      res.json({
        message: 'Section recommendations generated successfully',
        result
      });
    } catch (error) {
      console.error('Section recommendations error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to generate section recommendations' });
    }
  }

  async analyzeKeywordDensity(req, res) {
    try {
      const { resumeContent, jobDescription, preferredModel } = req.body;

      if (!resumeContent || !jobDescription) {
        return res.status(400).json({ error: 'Resume content and job description are required' });
      }

      const result = await this.aiService.analyzeKeywordDensity(
        resumeContent,
        jobDescription,
        { preferredModel }
      );
      
      res.json({
        message: 'Keyword density analysis completed successfully',
        result
      });
    } catch (error) {
      console.error('Keyword density analysis error:', error);
      if (error.message === 'AI service not configured') {
        return res.status(503).json({ 
          error: 'AI service not available. Please configure AI API keys.' 
        });
      }
      res.status(500).json({ error: 'Failed to analyze keyword density' });
    }
  }
}

module.exports = AIController;