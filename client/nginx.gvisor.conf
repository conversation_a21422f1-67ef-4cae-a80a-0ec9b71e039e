# Nginx configuration optimized for gVisor runtime
# Reduces syscall overhead and improves performance

# Run as non-root user for enhanced security
user cvleap;

# Optimize worker processes for gVisor
worker_processes auto;
worker_rlimit_nofile 1024;

# PID file location
pid /var/run/nginx.pid;

# Events block optimized for gVisor
events {
    worker_connections 512;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # Basic settings optimized for gVisor
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    keepalive_requests 100;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Reduce syscall overhead
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging optimized for gVisor (reduced I/O)
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main buffer=16k flush=5s;
    error_log /var/log/nginx/error.log warn;

    # Gzip compression optimized for gVisor
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';" always;

    # Rate limiting for gVisor environment
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=static:10m rate=50r/s;

    # Upstream for backend API
    upstream cvleap_api {
        server server:3000;
        keepalive 32;
    }

    # Main server block
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Security: Hide Nginx version
        server_tokens off;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API proxy with optimizations for gVisor
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://cvleap_api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts optimized for gVisor
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Buffer settings for gVisor
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }

        # Static files with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            limit_req zone=static burst=100 nodelay;
            
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options nosniff;
            
            # Optimize for gVisor
            sendfile on;
            tcp_nopush on;
            
            # Security headers for static files
            add_header X-Frame-Options DENY;
            add_header X-XSS-Protection "1; mode=block";
        }

        # React app routing
        location / {
            try_files $uri $uri/ /index.html;
            
            # Security headers for HTML
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Referrer-Policy "strict-origin-when-cross-origin";
            
            # Cache control for HTML
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Security: Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ \.(env|config|log)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # Additional server block for HTTPS (when SSL is configured)
    server {
        listen 443 ssl http2;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # SSL configuration (when certificates are available)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS header
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Include the same location blocks as HTTP server
        include /etc/nginx/conf.d/locations.conf;
    }
}
