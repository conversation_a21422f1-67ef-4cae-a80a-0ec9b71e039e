# Multi-stage Dockerfile for React client optimized for gVisor runtime

# Build stage
FROM node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    git \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with gVisor optimizations
ENV NPM_CONFIG_CACHE=/tmp/.npm
ENV NODE_OPTIONS="--max-old-space-size=2048"
RUN npm ci --no-audit --no-fund \
    && npm cache clean --force

# Copy source code
COPY . .

# Build application with optimizations
ENV REACT_APP_API_URL=/api
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false
RUN npm run build \
    && apk del .build-deps

# Production stage with Nginx optimized for gVisor
FROM nginx:alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S cvleap && \
    adduser -S -D -H -u 1001 -s /sbin/nologin -G cvleap cvleap

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy gVisor-optimized Nginx configuration
COPY nginx.gvisor.conf /etc/nginx/nginx.conf

# Create necessary directories
RUN mkdir -p /var/cache/nginx /var/log/nginx /var/run \
    && chown -R cvleap:cvleap /var/cache/nginx /var/log/nginx /var/run \
    && chown -R cvleap:cvleap /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# gVisor optimizations
ENV GVISOR_RUNTIME=true

# Security: Switch to non-root user
USER cvleap

# Health check optimized for gVisor
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Start Nginx with gVisor optimizations
CMD ["nginx", "-g", "daemon off;"]

# Labels for gVisor runtime identification
LABEL runtime="gvisor"
LABEL security.profile="strict"
LABEL gvisor.optimized="true"
LABEL version="1.0.0"
LABEL description="CVLeap client optimized for gVisor runtime"
