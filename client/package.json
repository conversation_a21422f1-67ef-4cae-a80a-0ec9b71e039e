{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.80.6", "@types/lodash": "^4.17.17", "@types/node": "^22.15.30", "d3": "^7.8.5", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lucide-react": "^0.303.0", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-pdf": "^7.6.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.12.7", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@types/react": "^19.1.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}