# PWA Offline Sync Documentation

## Overview

The CVLeap PWA implementation provides comprehensive offline functionality with intelligent sync capabilities. The system automatically handles offline data storage, conflict resolution, and seamless synchronization when the network becomes available.

## Features

### 🔄 Automatic Sync
- Triggers sync when network connectivity is restored
- Background sync using Service Worker APIs
- Intelligent retry logic with exponential backoff

### 💾 Dual Storage Strategy
- **localStorage**: For small data and compatibility
- **IndexedDB**: For large data (>5MB) and better performance
- Automatic storage method selection based on data size

### 🔧 Conflict Resolution
- **SERVER_WINS**: Server data takes precedence
- **LOCAL_WINS**: Local data takes precedence
- **MERGE**: Smart merge of local and server data
- **MANUAL**: User intervention (defaults to server for now)

### 📊 Progress Tracking
- Real-time sync status updates
- Progress bars and error reporting
- Event-driven status notifications

## Usage

### Basic Data Storage

```typescript
import { OfflineStorage } from './hooks/usePWA';

// Store data (automatically selects optimal storage method)
await OfflineStorage.storeDataSmart('pending-resume-update-123', {
  title: 'Updated Resume',
  content: 'New content...'
});

// Store data with explicit storage method
await OfflineStorage.storeData('large-data-key', largeDataObject, true); // Use IndexedDB

// Retrieve data
const data = await OfflineStorage.getData('pending-resume-update-123');
```

### Sync Status Monitoring

```typescript
import { useSyncStatus } from './hooks/usePWA';

function MyComponent() {
  const syncStatus = useSyncStatus();
  
  return (
    <div>
      {syncStatus.isActive && (
        <div>
          Syncing {syncStatus.completedItems + 1}/{syncStatus.totalItems}
          <progress value={syncStatus.progress} max={100} />
        </div>
      )}
      
      {syncStatus.errors.length > 0 && (
        <div>Sync errors: {syncStatus.errors.length}</div>
      )}
    </div>
  );
}
```

### Manual Sync Triggering

```typescript
// Trigger immediate sync
const result = await OfflineStorage.syncPendingChanges();

// Request background sync
await OfflineStorage.requestBackgroundSync('custom-sync');
```

### Storage Statistics

```typescript
const stats = await OfflineStorage.getStorageStats();
console.log('localStorage used:', stats.localStorage.used);
console.log('IndexedDB supported:', stats.indexedDB.supported);
```

## UI Components

### Sync Status Indicator

```typescript
import SyncStatusIndicator from './components/sync/SyncStatusIndicator';

function Header() {
  return (
    <header>
      <h1>CVLeap</h1>
      <SyncStatusIndicator showDetails={true} />
    </header>
  );
}
```

## Data Types

The system automatically handles different types of offline data:

### Resume Data
- **pending-resume-update-{id}**: Resume updates
- **mobile-resume-draft**: Mobile resume drafts
- **pending-resume-create**: New resume creation

### Application Data
- **pending-application-{id}**: Job applications

### Profile Data
- **pending-profile-update**: User profile updates

## Service Worker Integration

The service worker automatically handles background sync:

```javascript
// Registers sync events
self.addEventListener('sync', event => {
  if (event.tag === 'cvleap-sync') {
    event.waitUntil(syncOfflineData());
  }
});
```

## Error Handling

The system includes comprehensive error handling:

1. **Network Errors**: Automatic retry with exponential backoff
2. **Storage Errors**: Fallback between IndexedDB and localStorage
3. **Conflict Errors**: Automatic resolution based on configured strategy
4. **Validation Errors**: Detailed error reporting with context

## Best Practices

### 1. Use Smart Storage
```typescript
// Let the system choose the optimal storage method
await OfflineStorage.storeDataSmart(key, data);
```

### 2. Monitor Sync Status
```typescript
// Always provide user feedback during sync
const syncStatus = useSyncStatus();
```

### 3. Handle Offline States
```typescript
const { isOffline } = usePWA();

if (isOffline) {
  // Store data for later sync
  await OfflineStorage.storeDataSmart(`pending-${operation}`, data);
} else {
  // Perform immediate API call
  await APIService.updateResume(id, data);
}
```

### 4. Clean Up Old Data
```typescript
// Clear specific data
await OfflineStorage.clearData('specific-key');

// Clear all offline data
await OfflineStorage.clearData();
```

## Testing

The system includes comprehensive tests covering:

- Data storage and retrieval
- Sync operations and error handling
- Conflict resolution strategies
- Retry logic and backoff
- Background sync integration

Run tests:
```bash
npm test -- src/tests/pwa-sync.test.ts
```

## Troubleshooting

### Common Issues

1. **Sync Not Triggering**
   - Check network connectivity
   - Verify service worker registration
   - Check browser console for errors

2. **Storage Quota Exceeded**
   - Use `getStorageStats()` to monitor usage
   - Clear old data regularly
   - Consider data compression for large objects

3. **Conflicts Not Resolving**
   - Check conflict resolution strategy
   - Verify server timestamps
   - Review merge logic for custom data types

### Debug Mode

Enable debug logging:
```typescript
// Set in browser console
localStorage.setItem('cvleap-debug', 'true');
```

## Browser Support

### Minimum Requirements
- localStorage: All modern browsers
- IndexedDB: IE 10+, Chrome 23+, Firefox 10+, Safari 7+
- Service Workers: Chrome 40+, Firefox 44+, Safari 11.1+

### Graceful Degradation
- Falls back to localStorage when IndexedDB unavailable
- Falls back to immediate sync when Service Workers unavailable
- Continues to function without PWA features in older browsers

## Performance Considerations

### Storage
- IndexedDB is used for data >5MB
- localStorage for smaller, frequently accessed data
- Automatic cleanup of stale data (>24 hours)

### Sync
- Batched operations for efficiency
- Exponential backoff prevents server overload
- Background sync minimizes UI blocking

### Memory
- Lazy initialization of IndexedDB
- Event listeners properly cleaned up
- Minimal memory footprint for status tracking