import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface Experience {
  company: string;
  role: string;
  start: string;
  end: string;
}

interface ResumeData {
  name: string;
  title: string;
  summary: string;
  skills: string[];
  experience: Experience[];
}

interface Resume {
  id: number;
  title: string;
  data: ResumeData;
  created_at: string;
  updated_at: string;
}

interface ResumeState {
  resumes: Resume[];
  currentResume: Resume | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: ResumeState = {
  resumes: [],
  currentResume: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchResumes = createAsyncThunk(
  'resume/fetchResumes',
  async (_, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch('/api/resumes', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch resumes');
    }

    return response.json();
  }
);

export const fetchResume = createAsyncThunk(
  'resume/fetchResume',
  async (id: number, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch(`/api/resumes/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch resume');
    }

    return response.json();
  }
);

export const createResume = createAsyncThunk(
  'resume/createResume',
  async ({ title, data }: { title: string; data: ResumeData }, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch('/api/resumes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ title, data }),
    });

    if (!response.ok) {
      throw new Error('Failed to create resume');
    }

    return response.json();
  }
);

export const updateResume = createAsyncThunk(
  'resume/updateResume',
  async ({ id, title, data }: { id: number; title: string; data: ResumeData }, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch(`/api/resumes/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ title, data }),
    });

    if (!response.ok) {
      throw new Error('Failed to update resume');
    }

    return response.json();
  }
);

const resumeSlice = createSlice({
  name: 'resume',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentResume: (state, action) => {
      state.currentResume = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch resumes
      .addCase(fetchResumes.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResumes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resumes = action.payload;
      })
      .addCase(fetchResumes.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch resumes';
      })
      // Fetch single resume
      .addCase(fetchResume.fulfilled, (state, action) => {
        state.currentResume = action.payload;
      })
      // Create resume
      .addCase(createResume.fulfilled, (state, action) => {
        state.resumes.unshift({
          id: action.payload.id,
          title: action.payload.title,
          data: action.payload.data,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      })
      // Update resume
      .addCase(updateResume.fulfilled, (state, action) => {
        const index = state.resumes.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.resumes[index] = {
            ...state.resumes[index],
            title: action.payload.title,
            data: action.payload.data,
            updated_at: new Date().toISOString(),
          };
        }
        if (state.currentResume && state.currentResume.id === action.payload.id) {
          state.currentResume = {
            ...state.currentResume,
            title: action.payload.title,
            data: action.payload.data,
            updated_at: new Date().toISOString(),
          };
        }
      });
  },
});

export const { clearError, setCurrentResume } = resumeSlice.actions;
export default resumeSlice.reducer;