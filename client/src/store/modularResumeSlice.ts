import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { ModularTemplate, ModularSection, ModularResumeState } from '../types/resumeModules';

const initialState: ModularResumeState = {
  currentTemplate: null,
  templates: [],
  isPreviewMode: false,
  selectedSectionId: null,
  undoStack: [],
  redoStack: [],
  isLoading: false,
  error: null,
};

// Async thunks for modular resume operations
export const fetchModularTemplates = createAsyncThunk(
  'modularResume/fetchTemplates',
  async (_, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch('/api/modular-templates', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch modular templates');
    }

    return response.json();
  }
);

export const createModularTemplate = createAsyncThunk(
  'modularResume/createTemplate',
  async (template: Omit<ModularTemplate, 'id' | 'metadata'>, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch('/api/modular-templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(template),
    });

    if (!response.ok) {
      throw new Error('Failed to create modular template');
    }

    return response.json();
  }
);

export const updateModularTemplate = createAsyncThunk(
  'modularResume/updateTemplate',
  async (template: ModularTemplate, { getState }) => {
    const state = getState() as any;
    const token = state.auth.token;

    const response = await fetch(`/api/modular-templates/${template.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(template),
    });

    if (!response.ok) {
      throw new Error('Failed to update modular template');
    }

    return response.json();
  }
);

const modularResumeSlice = createSlice({
  name: 'modularResume',
  initialState,
  reducers: {
    setCurrentTemplate: (state, action: PayloadAction<ModularTemplate>) => {
      // Save current state to undo stack before changing
      if (state.currentTemplate) {
        state.undoStack.push(structuredClone(state.currentTemplate));
        // Limit undo stack size
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }
      }
      state.currentTemplate = action.payload;
      state.redoStack = []; // Clear redo stack when new action is performed
    },

    updateSections: (state, action: PayloadAction<ModularSection[]>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }
        
        state.currentTemplate.sections = action.payload;
        state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        state.redoStack = [];
      }
    },

    updateSection: (state, action: PayloadAction<ModularSection>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        const index = state.currentTemplate.sections.findIndex(
          section => section.id === action.payload.id
        );
        if (index !== -1) {
          state.currentTemplate.sections[index] = action.payload;
          state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        }
        state.redoStack = [];
      }
    },

    addSection: (state, action: PayloadAction<ModularSection>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        state.currentTemplate.sections.push(action.payload);
        state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        state.redoStack = [];
      }
    },

    removeSection: (state, action: PayloadAction<string>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        state.currentTemplate.sections = state.currentTemplate.sections.filter(
          section => section.id !== action.payload
        );
        state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        state.redoStack = [];
      }
    },

    toggleSectionVisibility: (state, action: PayloadAction<string>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        const section = state.currentTemplate.sections.find(
          section => section.id === action.payload
        );
        if (section) {
          section.isVisible = !section.isVisible;
          state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        }
        state.redoStack = [];
      }
    },

    reorderSections: (state, action: PayloadAction<{ sourceIndex: number; destinationIndex: number }>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        const { sourceIndex, destinationIndex } = action.payload;
        const sections = [...state.currentTemplate.sections];
        const [reorderedSection] = sections.splice(sourceIndex, 1);
        sections.splice(destinationIndex, 0, reorderedSection);
        
        // Update order values
        sections.forEach((section, index) => {
          section.order = index;
        });
        
        state.currentTemplate.sections = sections;
        state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        state.redoStack = [];
      }
    },

    updateGlobalStyling: (state, action: PayloadAction<Partial<ModularTemplate['globalStyling']>>) => {
      if (state.currentTemplate) {
        // Save current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }

        state.currentTemplate.globalStyling = {
          ...state.currentTemplate.globalStyling,
          ...action.payload,
        };
        state.currentTemplate.metadata.updatedAt = new Date().toISOString();
        state.redoStack = [];
      }
    },

    setPreviewMode: (state, action: PayloadAction<boolean>) => {
      state.isPreviewMode = action.payload;
    },

    setSelectedSection: (state, action: PayloadAction<string | null>) => {
      state.selectedSectionId = action.payload;
    },

    undo: (state) => {
      if (state.undoStack.length > 0 && state.currentTemplate) {
        // Move current state to redo stack
        state.redoStack.push(structuredClone(state.currentTemplate));
        if (state.redoStack.length > 50) {
          state.redoStack.shift();
        }
        
        // Restore previous state
        const previousState = state.undoStack.pop();
        if (previousState) {
          state.currentTemplate = previousState;
        }
      }
    },

    redo: (state) => {
      if (state.redoStack.length > 0 && state.currentTemplate) {
        // Move current state to undo stack
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) {
          state.undoStack.shift();
        }
        
        // Restore next state
        const nextState = state.redoStack.pop();
        if (nextState) {
          state.currentTemplate = nextState;
        }
      }
    },

    clearError: (state) => {
      state.error = null;
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch templates
      .addCase(fetchModularTemplates.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchModularTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templates = action.payload;
      })
      .addCase(fetchModularTemplates.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch templates';
      })
      // Create template
      .addCase(createModularTemplate.fulfilled, (state, action) => {
        state.templates.unshift(action.payload);
        state.currentTemplate = action.payload;
      })
      // Update template
      .addCase(updateModularTemplate.fulfilled, (state, action) => {
        const index = state.templates.findIndex(t => t.id === action.payload.id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
        if (state.currentTemplate && state.currentTemplate.id === action.payload.id) {
          state.currentTemplate = action.payload;
        }
      });
  },
});

export const {
  setCurrentTemplate,
  updateSections,
  updateSection,
  addSection,
  removeSection,
  toggleSectionVisibility,
  reorderSections,
  updateGlobalStyling,
  setPreviewMode,
  setSelectedSection,
  undo,
  redo,
  clearError,
} = modularResumeSlice.actions;

export default modularResumeSlice.reducer;