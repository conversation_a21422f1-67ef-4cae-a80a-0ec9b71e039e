import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import resumeReducer from './resumeSlice';
import modularResumeReducer from './modularResumeSlice';
import enhancedModuleLayoutReducer from './slices/moduleLayoutSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    resume: resumeReducer,
    modularResume: modularResumeReducer,
    enhancedModuleLayout: enhancedModuleLayoutReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;