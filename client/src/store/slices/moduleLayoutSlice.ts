import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import type { ModularTemplate, ModularSection } from '../../types/resumeModules';

// Enhanced state interface with additional features
export interface EnhancedModuleLayoutState {
  currentTemplate: ModularTemplate | null;
  templates: ModularTemplate[];
  isPreviewMode: boolean;
  selectedSectionId: string | null;
  undoStack: ModularTemplate[];
  redoStack: ModularTemplate[];
  isLoading: boolean;
  error: string | null;
  
  // Enhanced features
  dragState: {
    isDragging: boolean;
    draggedSectionId: string | null;
    dragOverSectionId: string | null;
    snapToGrid: boolean;
    showGrid: boolean;
    gridSize: number;
  };
  
  performance: {
    mode: 'smooth' | 'fast';
    enableAnimations: boolean;
    lazyLoadThreshold: number;
  };
  
  accessibility: {
    enableAnnouncements: boolean;
    showKeyboardHints: boolean;
    highContrast: boolean;
    reducedMotion: boolean;
  };
  
  customization: {
    theme: 'light' | 'dark' | 'auto';
    accentColor: string;
    fontScale: number;
    compactMode: boolean;
  };
  
  collaboration: {
    isSharing: boolean;
    collaborators: Array<{
      id: string;
      name: string;
      email: string;
      role: 'viewer' | 'editor' | 'owner';
      isOnline: boolean;
      cursor?: { sectionId: string; timestamp: number };
    }>;
    shareSettings: {
      allowComments: boolean;
      allowEditing: boolean;
      expiresAt?: string;
    };
  };
  
  analytics: {
    interactions: Array<{
      type: 'drag' | 'edit' | 'add' | 'remove' | 'reorder';
      sectionId: string;
      timestamp: number;
      duration?: number;
    }>;
    performance: {
      averageDragTime: number;
      renderTimes: number[];
      errorCount: number;
    };
  };
}

const initialState: EnhancedModuleLayoutState = {
  currentTemplate: null,
  templates: [],
  isPreviewMode: false,
  selectedSectionId: null,
  undoStack: [],
  redoStack: [],
  isLoading: false,
  error: null,
  
  dragState: {
    isDragging: false,
    draggedSectionId: null,
    dragOverSectionId: null,
    snapToGrid: true,
    showGrid: false,
    gridSize: 20
  },
  
  performance: {
    mode: 'smooth',
    enableAnimations: true,
    lazyLoadThreshold: 0.1
  },
  
  accessibility: {
    enableAnnouncements: true,
    showKeyboardHints: false,
    highContrast: false,
    reducedMotion: false
  },
  
  customization: {
    theme: 'auto',
    accentColor: '#3B82F6',
    fontScale: 1,
    compactMode: false
  },
  
  collaboration: {
    isSharing: false,
    collaborators: [],
    shareSettings: {
      allowComments: true,
      allowEditing: false
    }
  },
  
  analytics: {
    interactions: [],
    performance: {
      averageDragTime: 0,
      renderTimes: [],
      errorCount: 0
    }
  }
};

// Enhanced async thunks
export const fetchEnhancedTemplates = createAsyncThunk(
  'moduleLayout/fetchEnhanced',
  async (params: { 
    includeAnalytics?: boolean;
    includeCollaborators?: boolean;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.includeAnalytics) queryParams.append('analytics', 'true');
    if (params.includeCollaborators) queryParams.append('collaborators', 'true');
    
    const response = await fetch(`/api/enhanced-templates?${queryParams}`);
    if (!response.ok) throw new Error('Failed to fetch enhanced templates');
    return response.json();
  }
);

export const saveTemplateWithAnalytics = createAsyncThunk(
  'moduleLayout/saveWithAnalytics',
  async ({ 
    template, 
    analytics 
  }: { 
    template: ModularTemplate; 
    analytics: EnhancedModuleLayoutState['analytics'] 
  }) => {
    const response = await fetch('/api/templates/save-enhanced', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ template, analytics })
    });
    
    if (!response.ok) throw new Error('Failed to save template with analytics');
    return response.json();
  }
);

export const shareTemplate = createAsyncThunk(
  'moduleLayout/share',
  async ({ 
    templateId, 
    shareSettings 
  }: { 
    templateId: string; 
    shareSettings: EnhancedModuleLayoutState['collaboration']['shareSettings'] 
  }) => {
    const response = await fetch(`/api/templates/${templateId}/share`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ shareSettings })
    });
    
    if (!response.ok) throw new Error('Failed to share template');
    return response.json();
  }
);

// Enhanced slice with comprehensive features
const enhancedModuleLayoutSlice = createSlice({
  name: 'moduleLayout',
  initialState,
  reducers: {
    // Core template operations (enhanced)
    setCurrentTemplate: (state, action: PayloadAction<ModularTemplate>) => {
      if (state.currentTemplate) {
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) state.undoStack.shift();
      }
      state.currentTemplate = action.payload;
      state.redoStack = [];
      state.error = null;
    },

    // Enhanced section operations
    addSectionEnhanced: (state, action: PayloadAction<{
      section: ModularSection;
      position?: number;
      trackInteraction?: boolean;
    }>) => {
      if (!state.currentTemplate) return;
      
      const { section, position, trackInteraction = true } = action.payload;
      
      // Save to undo stack
      state.undoStack.push(structuredClone(state.currentTemplate));
      if (state.undoStack.length > 50) state.undoStack.shift();
      
      // Add section
      if (position !== undefined) {
        state.currentTemplate.sections.splice(position, 0, section);
      } else {
        state.currentTemplate.sections.push(section);
      }
      
      // Update metadata
      state.currentTemplate.metadata.updatedAt = new Date().toISOString();
      state.redoStack = [];
      
      // Track interaction
      if (trackInteraction) {
        state.analytics.interactions.push({
          type: 'add',
          sectionId: section.id,
          timestamp: Date.now()
        });
      }
    },

    removeSectionEnhanced: (state, action: PayloadAction<{
      sectionId: string;
      trackInteraction?: boolean;
    }>) => {
      if (!state.currentTemplate) return;
      
      const { sectionId, trackInteraction = true } = action.payload;
      
      // Save to undo stack
      state.undoStack.push(structuredClone(state.currentTemplate));
      if (state.undoStack.length > 50) state.undoStack.shift();
      
      // Remove section
      state.currentTemplate.sections = state.currentTemplate.sections.filter(
        s => s.id !== sectionId
      );
      
      // Update metadata
      state.currentTemplate.metadata.updatedAt = new Date().toISOString();
      state.redoStack = [];
      
      // Clear selection if removed section was selected
      if (state.selectedSectionId === sectionId) {
        state.selectedSectionId = null;
      }
      
      // Track interaction
      if (trackInteraction) {
        state.analytics.interactions.push({
          type: 'remove',
          sectionId,
          timestamp: Date.now()
        });
      }
    },

    reorderSectionsEnhanced: (state, action: PayloadAction<{
      fromIndex: number;
      toIndex: number;
      trackInteraction?: boolean;
      duration?: number;
    }>) => {
      if (!state.currentTemplate) return;
      
      const { fromIndex, toIndex, trackInteraction = true, duration } = action.payload;
      
      // Save to undo stack
      state.undoStack.push(structuredClone(state.currentTemplate));
      if (state.undoStack.length > 50) state.undoStack.shift();
      
      // Reorder sections
      const sections = [...state.currentTemplate.sections];
      const [movedSection] = sections.splice(fromIndex, 1);
      sections.splice(toIndex, 0, movedSection);
      
      // Update order property
      sections.forEach((section, index) => {
        section.order = index;
      });
      
      state.currentTemplate.sections = sections;
      state.currentTemplate.metadata.updatedAt = new Date().toISOString();
      state.redoStack = [];
      
      // Track interaction and performance
      if (trackInteraction) {
        state.analytics.interactions.push({
          type: 'reorder',
          sectionId: movedSection.id,
          timestamp: Date.now(),
          duration
        });
        
        if (duration) {
          // Update average drag time
          const times = state.analytics.performance.renderTimes;
          times.push(duration);
          if (times.length > 100) times.shift();
          
          state.analytics.performance.averageDragTime = 
            times.reduce((sum, time) => sum + time, 0) / times.length;
        }
      }
    },

    // Drag state management
    startDragging: (state, action: PayloadAction<string>) => {
      state.dragState.isDragging = true;
      state.dragState.draggedSectionId = action.payload;
    },

    updateDragOver: (state, action: PayloadAction<string | null>) => {
      state.dragState.dragOverSectionId = action.payload;
    },

    endDragging: (state) => {
      state.dragState.isDragging = false;
      state.dragState.draggedSectionId = null;
      state.dragState.dragOverSectionId = null;
    },

    // Grid and snap settings
    toggleSnapToGrid: (state) => {
      state.dragState.snapToGrid = !state.dragState.snapToGrid;
    },

    toggleGrid: (state) => {
      state.dragState.showGrid = !state.dragState.showGrid;
    },

    setGridSize: (state, action: PayloadAction<number>) => {
      state.dragState.gridSize = Math.max(10, Math.min(50, action.payload));
    },

    // Performance settings
    setPerformanceMode: (state, action: PayloadAction<'smooth' | 'fast'>) => {
      state.performance.mode = action.payload;
      state.performance.enableAnimations = action.payload === 'smooth';
    },

    toggleAnimations: (state) => {
      state.performance.enableAnimations = !state.performance.enableAnimations;
    },

    setLazyLoadThreshold: (state, action: PayloadAction<number>) => {
      state.performance.lazyLoadThreshold = Math.max(0, Math.min(1, action.payload));
    },

    // Accessibility settings
    toggleAnnouncements: (state) => {
      state.accessibility.enableAnnouncements = !state.accessibility.enableAnnouncements;
    },

    toggleKeyboardHints: (state) => {
      state.accessibility.showKeyboardHints = !state.accessibility.showKeyboardHints;
    },

    toggleHighContrast: (state) => {
      state.accessibility.highContrast = !state.accessibility.highContrast;
    },

    setReducedMotion: (state, action: PayloadAction<boolean>) => {
      state.accessibility.reducedMotion = action.payload;
      if (action.payload) {
        state.performance.enableAnimations = false;
      }
    },

    // Customization
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.customization.theme = action.payload;
    },

    setAccentColor: (state, action: PayloadAction<string>) => {
      state.customization.accentColor = action.payload;
    },

    setFontScale: (state, action: PayloadAction<number>) => {
      state.customization.fontScale = Math.max(0.8, Math.min(1.5, action.payload));
    },

    toggleCompactMode: (state) => {
      state.customization.compactMode = !state.customization.compactMode;
    },

    // Collaboration
    setSharing: (state, action: PayloadAction<boolean>) => {
      state.collaboration.isSharing = action.payload;
    },

    updateCollaborators: (state, action: PayloadAction<EnhancedModuleLayoutState['collaboration']['collaborators']>) => {
      state.collaboration.collaborators = action.payload;
    },

    updateCollaboratorCursor: (state, action: PayloadAction<{
      collaboratorId: string;
      sectionId: string;
    }>) => {
      const collaborator = state.collaboration.collaborators.find(
        c => c.id === action.payload.collaboratorId
      );
      if (collaborator) {
        collaborator.cursor = {
          sectionId: action.payload.sectionId,
          timestamp: Date.now()
        };
      }
    },

    updateShareSettings: (state, action: PayloadAction<Partial<EnhancedModuleLayoutState['collaboration']['shareSettings']>>) => {
      state.collaboration.shareSettings = {
        ...state.collaboration.shareSettings,
        ...action.payload
      };
    },

    // Analytics
    trackInteraction: (state, action: PayloadAction<{
      type: 'drag' | 'edit' | 'add' | 'remove' | 'reorder';
      sectionId: string;
      duration?: number;
    }>) => {
      state.analytics.interactions.push({
        ...action.payload,
        timestamp: Date.now()
      });

      // Keep only last 1000 interactions
      if (state.analytics.interactions.length > 1000) {
        state.analytics.interactions = state.analytics.interactions.slice(-1000);
      }
    },

    recordPerformance: (state, action: PayloadAction<{
      renderTime?: number;
      dragTime?: number;
      error?: boolean;
    }>) => {
      const { renderTime, dragTime, error } = action.payload;
      
      if (renderTime) {
        state.analytics.performance.renderTimes.push(renderTime);
        if (state.analytics.performance.renderTimes.length > 100) {
          state.analytics.performance.renderTimes.shift();
        }
      }
      
      if (dragTime) {
        const times = state.analytics.performance.renderTimes;
        times.push(dragTime);
        state.analytics.performance.averageDragTime = 
          times.reduce((sum, time) => sum + time, 0) / times.length;
      }
      
      if (error) {
        state.analytics.performance.errorCount++;
      }
    },

    clearAnalytics: (state) => {
      state.analytics.interactions = [];
      state.analytics.performance = {
        averageDragTime: 0,
        renderTimes: [],
        errorCount: 0
      };
    },

    // Enhanced undo/redo with analytics
    undoEnhanced: (state) => {
      if (state.undoStack.length > 0 && state.currentTemplate) {
        state.redoStack.push(structuredClone(state.currentTemplate));
        if (state.redoStack.length > 50) state.redoStack.shift();
        
        const previousState = state.undoStack.pop();
        if (previousState) {
          state.currentTemplate = previousState;
        }
        
        // Track undo action
        state.analytics.interactions.push({
          type: 'edit',
          sectionId: 'template',
          timestamp: Date.now()
        });
      }
    },

    redoEnhanced: (state) => {
      if (state.redoStack.length > 0 && state.currentTemplate) {
        state.undoStack.push(structuredClone(state.currentTemplate));
        if (state.undoStack.length > 50) state.undoStack.shift();
        
        const nextState = state.redoStack.pop();
        if (nextState) {
          state.currentTemplate = nextState;
        }
        
        // Track redo action
        state.analytics.interactions.push({
          type: 'edit',
          sectionId: 'template',
          timestamp: Date.now()
        });
      }
    },

    // Error handling
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.analytics.performance.errorCount++;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Bulk operations for performance
    bulkUpdateSections: (state, action: PayloadAction<ModularSection[]>) => {
      if (!state.currentTemplate) return;
      
      state.undoStack.push(structuredClone(state.currentTemplate));
      if (state.undoStack.length > 50) state.undoStack.shift();
      
      state.currentTemplate.sections = action.payload;
      state.currentTemplate.metadata.updatedAt = new Date().toISOString();
      state.redoStack = [];
    },

    // Reset state
    resetState: () => initialState,

    // Selection management
    setSelectedSection: (state, action: PayloadAction<string | null>) => {
      state.selectedSectionId = action.payload;
    },

    // Preview mode
    setPreviewMode: (state, action: PayloadAction<boolean>) => {
      state.isPreviewMode = action.payload;
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(fetchEnhancedTemplates.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchEnhancedTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templates = action.payload.templates || [];
        if (action.payload.analytics) {
          state.analytics = { ...state.analytics, ...action.payload.analytics };
        }
        if (action.payload.collaborators) {
          state.collaboration.collaborators = action.payload.collaborators;
        }
      })
      .addCase(fetchEnhancedTemplates.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch templates';
        state.analytics.performance.errorCount++;
      })
      .addCase(shareTemplate.fulfilled, (state, action) => {
        state.collaboration.isSharing = true;
        state.collaboration.shareSettings = action.payload.shareSettings;
      })
      .addCase(saveTemplateWithAnalytics.fulfilled, (state, action) => {
        if (state.currentTemplate) {
          state.currentTemplate.id = action.payload.id;
          state.currentTemplate.metadata = action.payload.metadata;
        }
      });
  }
});

export const {
  setCurrentTemplate,
  addSectionEnhanced,
  removeSectionEnhanced,
  reorderSectionsEnhanced,
  startDragging,
  updateDragOver,
  endDragging,
  toggleSnapToGrid,
  toggleGrid,
  setGridSize,
  setPerformanceMode,
  toggleAnimations,
  setLazyLoadThreshold,
  toggleAnnouncements,
  toggleKeyboardHints,
  toggleHighContrast,
  setReducedMotion,
  setTheme,
  setAccentColor,
  setFontScale,
  toggleCompactMode,
  setSharing,
  updateCollaborators,
  updateCollaboratorCursor,
  updateShareSettings,
  trackInteraction,
  recordPerformance,
  clearAnalytics,
  undoEnhanced,
  redoEnhanced,
  setError,
  clearError,
  bulkUpdateSections,
  resetState,
  setSelectedSection,
  setPreviewMode
} = enhancedModuleLayoutSlice.actions;

export default enhancedModuleLayoutSlice.reducer;