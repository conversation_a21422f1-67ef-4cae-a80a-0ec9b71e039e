import { useCallback, useRef, useState } from 'react';
import type { DropResult } from 'react-beautiful-dnd';
import { useDispatch } from 'react-redux';
import type { ModularSection } from '../types/resumeModules';

// Import reorderSections from the existing slice for compatibility
import { reorderSections } from '../store/modularResumeSlice';

export interface DragDropState {
  isDragging: boolean;
  draggedItem: ModularSection | null;
  dropZone: string | null;
  snapToGrid: boolean;
}

export interface DragDropHandlers {
  onDragStart: (result: { draggableId: string }) => void;
  onDragUpdate: (result: { destination: { index: number } | null }) => void;
  onDragEnd: (result: DropResult) => void;
  toggleSnapToGrid: () => void;
  announceMove: (sectionName: string, fromIndex: number, toIndex: number) => void;
}

export interface UseDragDropOptions {
  sections: ModularSection[];
  enableSnapToGrid?: boolean;
  enableAnnouncements?: boolean;
  onSectionReorder?: (fromIndex: number, toIndex: number) => void;
}

export function useDragDrop({
  sections,
  enableSnapToGrid = true,
  enableAnnouncements = true,
  onSectionReorder
}: UseDragDropOptions) {
  const dispatch = useDispatch();
  const [state, setState] = useState<DragDropState>({
    isDragging: false,
    draggedItem: null,
    dropZone: null,
    snapToGrid: enableSnapToGrid
  });

  const announcerRef = useRef<HTMLDivElement>(null);

  const announceToScreenReader = useCallback((message: string) => {
    if (!enableAnnouncements || !announcerRef.current) return;
    
    announcerRef.current.textContent = message;
    
    // Clear after 1 second
    setTimeout(() => {
      if (announcerRef.current) {
        announcerRef.current.textContent = '';
      }
    }, 1000);
  }, [enableAnnouncements]);

  const onDragStart = useCallback((result: { draggableId: string }) => {
    const draggedSection = sections.find(s => s.id === result.draggableId);
    setState(prev => ({
      ...prev,
      isDragging: true,
      draggedItem: draggedSection || null
    }));

    if (draggedSection) {
      announceToScreenReader(`Dragging ${draggedSection.title} section`);
    }
  }, [sections, announceToScreenReader]);

  const onDragUpdate = useCallback((result: { destination: { index: number } | null }) => {
    setState(prev => ({
      ...prev,
      dropZone: result.destination ? `index-${result.destination.index}` : null
    }));
  }, []);

  const onDragEnd = useCallback((result: DropResult) => {
    setState(prev => ({
      ...prev,
      isDragging: false,
      draggedItem: null,
      dropZone: null
    }));

    if (!result.destination) {
      announceToScreenReader('Drag cancelled');
      return;
    }

    const fromIndex = result.source.index;
    const toIndex = result.destination.index;

    if (fromIndex === toIndex) {
      return;
    }

    // Reorder sections in Redux store
    dispatch(reorderSections({ sourceIndex: fromIndex, destinationIndex: toIndex }));

    // Call custom handler if provided
    if (onSectionReorder) {
      onSectionReorder(fromIndex, toIndex);
    }

    // Announce the move
    const movedSection = sections[fromIndex];
    if (movedSection) {
      announceToScreenReader(
        `${movedSection.title} moved from position ${fromIndex + 1} to position ${toIndex + 1}`
      );
    }
  }, [dispatch, sections, onSectionReorder, announceToScreenReader]);

  const toggleSnapToGrid = useCallback(() => {
    setState(prev => ({
      ...prev,
      snapToGrid: !prev.snapToGrid
    }));
  }, []);

  const announceMove = useCallback((sectionName: string, fromIndex: number, toIndex: number) => {
    announceToScreenReader(`${sectionName} moved from position ${fromIndex + 1} to position ${toIndex + 1}`);
  }, [announceToScreenReader]);

  const handlers: DragDropHandlers = {
    onDragStart,
    onDragUpdate,
    onDragEnd,
    toggleSnapToGrid,
    announceMove
  };

  return {
    state,
    handlers,
    announcerRef
  };
}

// Hook for keyboard navigation support
export function useKeyboardNavigation(sections: ModularSection[], onReorder: (fromIndex: number, toIndex: number) => void) {
  const handleKeyDown = useCallback((e: React.KeyboardEvent, sectionIndex: number) => {
    if (e.key === 'ArrowUp' && e.ctrlKey && e.shiftKey) {
      e.preventDefault();
      if (sectionIndex > 0) {
        onReorder(sectionIndex, sectionIndex - 1);
      }
    } else if (e.key === 'ArrowDown' && e.ctrlKey && e.shiftKey) {
      e.preventDefault();
      if (sectionIndex < sections.length - 1) {
        onReorder(sectionIndex, sectionIndex + 1);
      }
    }
  }, [sections.length, onReorder]);

  return { handleKeyDown };
}

// Hook for mobile touch enhancements
export function useTouchDragDrop() {
  const [touchState, setTouchState] = useState({
    isDragging: false,
    startY: 0,
    currentY: 0,
    dragThreshold: 10
  });

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchState(prev => ({
      ...prev,
      startY: touch.clientY,
      currentY: touch.clientY
    }));
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const deltaY = Math.abs(touch.clientY - touchState.startY);
    
    setTouchState(prev => ({
      ...prev,
      currentY: touch.clientY,
      isDragging: deltaY > prev.dragThreshold
    }));

    if (touchState.isDragging) {
      e.preventDefault(); // Prevent scrolling while dragging
    }
  }, [touchState.startY, touchState.isDragging]);

  const handleTouchEnd = useCallback(() => {
    setTouchState(prev => ({
      ...prev,
      isDragging: false,
      startY: 0,
      currentY: 0
    }));
  }, []);

  return {
    touchState,
    touchHandlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd
    }
  };
}