import { useEffect, useRef, useState } from 'react';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface CollaborationHookOptions {
  onUserJoined?: (data: any) => void;
  onUserLeft?: (data: any) => void;
  onOperation?: (data: any) => void;
  onCursorUpdate?: (data: any) => void;
  onError?: (error: string) => void;
}

export const useCollaboration = (resumeId: string | null, options: CollaborationHookOptions = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [activeUsers, setActiveUsers] = useState<any[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Generate unique session ID
  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // Connect to WebSocket
  const connect = () => {
    if (!resumeId) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/ws`;

    try {
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('WebSocket connected for collaboration');
        setIsConnected(true);
        reconnectAttempts.current = 0;
        
        // Authenticate
        const token = localStorage.getItem('token');
        if (token) {
          wsRef.current?.send(JSON.stringify({
            type: 'auth',
            token
          }));
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        
        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect... (${reconnectAttempts.current}/${maxReconnectAttempts})`);
            connect();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        options.onError?.('WebSocket connection error');
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      options.onError?.('Failed to establish connection');
    }
  };

  // Handle incoming messages
  const handleMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'auth_success':
        // Join editing session
        const newSessionId = generateSessionId();
        setSessionId(newSessionId);
        
        wsRef.current?.send(JSON.stringify({
          type: 'join_editing',
          resumeId,
          sessionId: newSessionId
        }));
        break;

      case 'session_joined':
        setActiveUsers(message.activeUsers || []);
        break;

      case 'user_joined':
        setActiveUsers(prev => {
          const existing = prev.find(u => u.userId === message.userId);
          if (existing) return prev;
          return [...prev, {
            userId: message.userId,
            sessionId: message.sessionId,
            name: message.name || 'Unknown User',
            cursor: { line: 0, column: 0 },
            selection: null
          }];
        });
        options.onUserJoined?.(message);
        break;

      case 'user_left':
        setActiveUsers(prev => prev.filter(u => u.sessionId !== message.sessionId));
        options.onUserLeft?.(message);
        break;

      case 'operation':
        options.onOperation?.(message.operation);
        break;

      case 'cursor_update':
        setActiveUsers(prev => prev.map(user => 
          user.sessionId === message.sessionId 
            ? { ...user, cursor: message.cursor, selection: message.selection }
            : user
        ));
        options.onCursorUpdate?.(message);
        break;

      case 'error':
        console.error('Collaboration error:', message.message);
        options.onError?.(message.message);
        break;

      default:
        console.log('Unhandled message type:', message.type);
    }
  };

  // Send editing operation
  const sendOperation = (operation: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN && sessionId) {
      wsRef.current.send(JSON.stringify({
        type: 'editing_operation',
        sessionId,
        operation
      }));
    }
  };

  // Update cursor position
  const updateCursor = (cursor: any, selection: any = null) => {
    if (wsRef.current?.readyState === WebSocket.OPEN && sessionId) {
      wsRef.current.send(JSON.stringify({
        type: 'cursor_update',
        sessionId,
        cursor,
        selection
      }));
    }
  };

  // Leave editing session
  const leaveSession = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN && sessionId) {
      wsRef.current.send(JSON.stringify({
        type: 'leave_editing',
        sessionId
      }));
    }
  };

  // Connect when resumeId changes
  useEffect(() => {
    if (resumeId) {
      connect();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      leaveSession();
      
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [resumeId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      leaveSession();
      wsRef.current?.close();
    };
  }, []);

  return {
    isConnected,
    activeUsers,
    sessionId,
    sendOperation,
    updateCursor,
    leaveSession
  };
};