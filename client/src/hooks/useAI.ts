import { useState, useCallback } from 'react';
import AIService, { ResumeData, CompanyInfo } from '../services/aiService';

export interface UseAIOptions {
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  preferredModel?: string;
}

export const useAI = (options: UseAIOptions = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);

  const executeAIRequest = useCallback(async (
    requestFn: () => Promise<any>,
    loadingMessage?: string
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await requestFn();
      setResult(response);
      options.onSuccess?.(response);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'AI request failed';
      setError(errorMessage);
      options.onError?.(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  // Core AI Enhancement Features
  const enhanceResume = useCallback(async (resumeData: ResumeData, targetJob?: string) => {
    return executeAIRequest(
      () => AIService.enhanceResume(resumeData, targetJob, options.preferredModel),
      'Enhancing resume...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const generateCoverLetter = useCallback(async (
    resumeData: ResumeData,
    jobDescription: string,
    companyName?: string
  ) => {
    return executeAIRequest(
      () => AIService.generateCoverLetter(resumeData, jobDescription, companyName, options.preferredModel),
      'Generating cover letter...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const analyzeATS = useCallback(async (resumeData: ResumeData) => {
    return executeAIRequest(
      () => AIService.analyzeATS(resumeData, options.preferredModel),
      'Analyzing ATS compatibility...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const suggestSkills = useCallback(async (
    currentSkills: string[],
    jobTitle: string,
    industry?: string
  ) => {
    return executeAIRequest(
      () => AIService.suggestSkills(currentSkills, jobTitle, industry, options.preferredModel),
      'Suggesting skills...'
    );
  }, [executeAIRequest, options.preferredModel]);

  // Phase 1 Enhancement Features
  const quantifyAchievements = useCallback(async (
    achievements: string[] | string,
    jobContext?: string
  ) => {
    return executeAIRequest(
      () => AIService.quantifyAchievements(achievements, jobContext, options.preferredModel),
      'Quantifying achievements...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const personalizeForCompanyCulture = useCallback(async (
    resumeData: ResumeData,
    companyInfo: CompanyInfo
  ) => {
    return executeAIRequest(
      () => AIService.personalizeForCompanyCulture(resumeData, companyInfo, options.preferredModel),
      'Personalizing content...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const recommendResumeSections = useCallback(async (
    industry: string,
    jobRole: string,
    experienceLevel?: string
  ) => {
    return executeAIRequest(
      () => AIService.recommendResumeSections(industry, jobRole, experienceLevel, options.preferredModel),
      'Analyzing section recommendations...'
    );
  }, [executeAIRequest, options.preferredModel]);

  const analyzeKeywordDensity = useCallback(async (
    resumeContent: string | ResumeData,
    jobDescription: string
  ) => {
    return executeAIRequest(
      () => AIService.analyzeKeywordDensity(resumeContent, jobDescription, options.preferredModel),
      'Analyzing keyword density...'
    );
  }, [executeAIRequest, options.preferredModel]);

  // Advanced Features
  const getAdvancedScoring = useCallback(async (
    resumeData: ResumeData,
    targetJob?: string,
    industry?: string
  ) => {
    return executeAIRequest(
      () => AIService.getAdvancedScoring(resumeData, targetJob, industry),
      'Calculating advanced scores...'
    );
  }, [executeAIRequest]);

  const detectWeaknesses = useCallback(async (resumeData: ResumeData, targetJob?: string) => {
    return executeAIRequest(
      () => AIService.detectWeaknesses(resumeData, targetJob),
      'Detecting weaknesses...'
    );
  }, [executeAIRequest]);

  const getRealTimeSuggestions = useCallback(async (
    currentText: string,
    sectionType: string,
    targetJob?: string
  ) => {
    return executeAIRequest(
      () => AIService.getRealTimeSuggestions(currentText, sectionType, targetJob),
      'Getting suggestions...'
    );
  }, [executeAIRequest]);

  // Reset state
  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setResult(null);
  }, []);

  return {
    // State
    isLoading,
    error,
    result,
    
    // Actions
    enhanceResume,
    generateCoverLetter,
    analyzeATS,
    suggestSkills,
    quantifyAchievements,
    personalizeForCompanyCulture,
    recommendResumeSections,
    analyzeKeywordDensity,
    getAdvancedScoring,
    detectWeaknesses,
    getRealTimeSuggestions,
    reset,
  };
};

// Specialized hooks for specific use cases
export const useResumeEnhancement = (options: UseAIOptions = {}) => {
  const { enhanceResume, analyzeATS, quantifyAchievements, isLoading, error, result } = useAI(options);
  
  const enhanceFullResume = useCallback(async (
    resumeData: ResumeData,
    targetJob?: string,
    includeATS: boolean = true
  ) => {
    const results: any = {};
    
    try {
      // Enhance resume content
      results.enhancement = await enhanceResume(resumeData, targetJob);
      
      // Analyze ATS if requested
      if (includeATS) {
        results.atsAnalysis = await analyzeATS(resumeData);
      }
      
      // Quantify achievements if they exist
      if (resumeData.experience && resumeData.experience.length > 0) {
        const achievements = resumeData.experience
          .map(exp => exp.description)
          .filter(desc => desc)
          .join('\n');
        
        if (achievements) {
          results.quantifiedAchievements = await quantifyAchievements(achievements, targetJob);
        }
      }
      
      return results;
    } catch (err) {
      console.error('Full resume enhancement failed:', err);
      throw err;
    }
  }, [enhanceResume, analyzeATS, quantifyAchievements]);
  
  return {
    enhanceFullResume,
    isLoading,
    error,
    result,
  };
};

export const useJobApplicationOptimization = (options: UseAIOptions = {}) => {
  const {
    personalizeForCompanyCulture,
    analyzeKeywordDensity,
    recommendResumeSections,
    generateCoverLetter,
    isLoading,
    error,
    result
  } = useAI(options);
  
  const optimizeForJob = useCallback(async (
    resumeData: ResumeData,
    jobDescription: string,
    companyInfo: CompanyInfo,
    industry: string,
    jobRole: string
  ) => {
    const results: any = {};
    
    try {
      // Get section recommendations
      results.sectionRecommendations = await recommendResumeSections(
        industry,
        jobRole,
        'mid' // Default experience level
      );
      
      // Analyze keyword density
      results.keywordAnalysis = await analyzeKeywordDensity(resumeData, jobDescription);
      
      // Personalize for company culture
      results.personalization = await personalizeForCompanyCulture(resumeData, companyInfo);
      
      // Generate tailored cover letter
      results.coverLetter = await generateCoverLetter(resumeData, jobDescription, companyInfo.name);
      
      return results;
    } catch (err) {
      console.error('Job application optimization failed:', err);
      throw err;
    }
  }, [personalizeForCompanyCulture, analyzeKeywordDensity, recommendResumeSections, generateCoverLetter]);
  
  return {
    optimizeForJob,
    isLoading,
    error,
    result,
  };
};

export default useAI;