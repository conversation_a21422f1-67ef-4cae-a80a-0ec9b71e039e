import { useEffect, useState } from 'react';
import APIService from './useAPI';

interface PWAInstallPrompt extends Event {
  prompt: () => void;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAHookReturn {
  isInstallable: boolean;
  isInstalled: boolean;
  isOffline: boolean;
  installApp: () => void;
  updateAvailable: boolean;
  reloadForUpdate: () => void;
}

export function usePWA(): PWAHookReturn {
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [installPrompt, setInstallPrompt] = useState<PWAInstallPrompt | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Check if app is installed
    setIsInstalled(window.matchMedia('(display-mode: standalone)').matches);

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setInstallPrompt(e as PWAInstallPrompt);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setInstallPrompt(null);
    };

    // Listen for online/offline status with sync triggering
    const handleOnline = async () => {
      setIsOffline(false);
      console.log('App came online, triggering sync...');
      
      // Trigger sync when coming back online
      try {
        await OfflineStorage.requestBackgroundSync('online-sync');
      } catch (error) {
        console.error('Failed to trigger sync on online:', error);
      }
    };
    
    const handleOffline = () => {
      setIsOffline(true);
      console.log('App went offline');
    };

    // Service Worker update available
    const handleUpdateAvailable = () => {
      setUpdateAvailable(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', handleUpdateAvailable);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', handleUpdateAvailable);
      }
    };
  }, []);

  const installApp = async () => {
    if (!installPrompt) return;

    installPrompt.prompt();
    const { outcome } = await installPrompt.userChoice;

    if (outcome === 'accepted') {
      setIsInstallable(false);
      setInstallPrompt(null);
    }
  };

  const reloadForUpdate = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration?.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          window.location.reload();
        }
      });
    }
  };

  return {
    isInstallable,
    isInstalled,
    isOffline,
    installApp,
    updateAvailable,
    reloadForUpdate,
  };
}

// Hook to track sync status
export function useSyncStatus(): SyncStatus {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(OfflineStorage.getSyncStatus());

  useEffect(() => {
    const unsubscribe = OfflineStorage.onSyncStatusChange(setSyncStatus);
    return unsubscribe;
  }, []);

  return syncStatus;
}

// Sync status interface for progress tracking
export interface SyncStatus {
  isActive: boolean;
  progress: number;
  currentItem: string | null;
  totalItems: number;
  completedItems: number;
  errors: Array<{ key: string; error: string }>;
}

// Conflict resolution strategies
export enum ConflictResolution {
  LOCAL_WINS = 'local',
  SERVER_WINS = 'server', 
  MERGE = 'merge',
  MANUAL = 'manual'
}

// Offline storage utilities
export class OfflineStorage {
  // IndexedDB configuration
  private static readonly DB_NAME = 'CVLeapOfflineDB';
  private static readonly DB_VERSION = 1;
  private static readonly STORE_NAME = 'offlineData';
  
  private static syncStatus: SyncStatus = {
    isActive: false,
    progress: 0,
    currentItem: null,
    totalItems: 0,
    completedItems: 0,
    errors: []
  };
  
  private static eventListeners: Array<(status: SyncStatus) => void> = [];

  // Initialize IndexedDB
  private static async initDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          const store = db.createObjectStore(this.STORE_NAME, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('dataType', 'dataType', { unique: false });
        }
      };
    });
  }

  // Store data for offline use with IndexedDB fallback
  static async storeData(key: string, data: any, useIndexedDB: boolean = false): Promise<void> {
    const wrappedData = {
      key,
      data,
      timestamp: Date.now(),
      version: 1,
      dataType: this.getDataTypeFromKey(key)
    };

    try {
      if (useIndexedDB && 'indexedDB' in window) {
        await this.storeInIndexedDB(wrappedData);
      } else {
        // Fallback to localStorage
        localStorage.setItem(key, JSON.stringify(wrappedData));
      }
    } catch (error) {
      console.error('Error storing offline data:', error);
      // Try localStorage as ultimate fallback
      try {
        localStorage.setItem(key, JSON.stringify(wrappedData));
      } catch (fallbackError) {
        console.error('Fallback storage also failed:', fallbackError);
        throw new Error('Unable to store data offline');
      }
    }
  }

  // Store data in IndexedDB
  private static async storeInIndexedDB(wrappedData: any): Promise<void> {
    const db = await this.initDB();
    const transaction = db.transaction([this.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(this.STORE_NAME);
    
    return new Promise((resolve, reject) => {
      const request = store.put(wrappedData);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Retrieve offline data with IndexedDB support
  static async getData(key: string, preferIndexedDB: boolean = false): Promise<any> {
    try {
      let stored: string | null = null;
      let parsedData: any = null;

      // Try IndexedDB first if preferred and available
      if (preferIndexedDB && 'indexedDB' in window) {
        parsedData = await this.getFromIndexedDB(key);
      }

      // Fallback to localStorage
      if (!parsedData) {
        stored = localStorage.getItem(key);
        if (stored) {
          parsedData = JSON.parse(stored);
        }
      }

      if (!parsedData) return null;

      // Check if data is stale (older than 24 hours)
      const isStale = Date.now() - parsedData.timestamp > 24 * 60 * 60 * 1000;
      if (isStale) {
        await this.clearData(key);
        return null;
      }

      return parsedData.data;
    } catch (error) {
      console.error('Error retrieving offline data:', error);
      return null;
    }
  }

  // Get data from IndexedDB
  private static async getFromIndexedDB(key: string): Promise<any> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([this.STORE_NAME], 'readonly');
      const store = transaction.objectStore(this.STORE_NAME);
      
      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Error accessing IndexedDB:', error);
      return null;
    }
  }

  // Get all pending keys from both localStorage and IndexedDB
  private static async getAllPendingKeys(): Promise<string[]> {
    const keys: string[] = [];

    // Get from localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pending-') || key.startsWith('mobile-resume-draft')) {
        keys.push(key);
      }
    });

    // Get from IndexedDB
    try {
      if ('indexedDB' in window) {
        const dbKeys = await this.getPendingKeysFromIndexedDB();
        keys.push(...dbKeys);
      }
    } catch (error) {
      console.warn('Failed to get keys from IndexedDB:', error);
    }

    return [...new Set(keys)]; // Remove duplicates
  }

  // Get pending keys from IndexedDB
  private static async getPendingKeysFromIndexedDB(): Promise<string[]> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([this.STORE_NAME], 'readonly');
      const store = transaction.objectStore(this.STORE_NAME);
      
      return new Promise((resolve, reject) => {
        const keys: string[] = [];
        const request = store.openCursor();
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            const key = cursor.value.key;
            if (key.startsWith('pending-') || key.startsWith('mobile-resume-draft')) {
              keys.push(key);
            }
            cursor.continue();
          } else {
            resolve(keys);
          }
        };
        
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Error getting pending keys from IndexedDB:', error);
      return [];
    }
  }

  // Clear offline data from both localStorage and IndexedDB
  static async clearData(key?: string): Promise<void> {
    if (key) {
      // Clear specific key
      localStorage.removeItem(key);
      
      try {
        if ('indexedDB' in window) {
          const db = await this.initDB();
          const transaction = db.transaction([this.STORE_NAME], 'readwrite');
          const store = transaction.objectStore(this.STORE_NAME);
          store.delete(key);
        }
      } catch (error) {
        console.warn('Failed to clear from IndexedDB:', error);
      }
    } else {
      // Clear all CVLeap related data
      Object.keys(localStorage).forEach(k => {
        if (k.startsWith('cvleap-') || k.startsWith('mobile-resume-') || k.startsWith('pending-')) {
          localStorage.removeItem(k);
        }
      });

      try {
        if ('indexedDB' in window) {
          const db = await this.initDB();
          const transaction = db.transaction([this.STORE_NAME], 'readwrite');
          const store = transaction.objectStore(this.STORE_NAME);
          store.clear();
        }
      } catch (error) {
        console.warn('Failed to clear IndexedDB:', error);
      }
    }
  }

  // Get data type from storage key
  private static getDataTypeFromKey(key: string): string {
    if (key.startsWith('pending-resume-update') || key.startsWith('mobile-resume-draft')) {
      return 'resume-update';
    } else if (key.startsWith('pending-resume-create')) {
      return 'resume-create';
    } else if (key.startsWith('pending-application')) {
      return 'application';
    } else if (key.startsWith('pending-profile')) {
      return 'profile';
    }
    return 'unknown';
  }

  // Subscribe to sync status updates
  static onSyncStatusChange(listener: (status: SyncStatus) => void): () => void {
    this.eventListeners.push(listener);
    return () => {
      const index = this.eventListeners.indexOf(listener);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  // Emit sync status update
  private static emitSyncStatus(): void {
    this.eventListeners.forEach(listener => listener({ ...this.syncStatus }));
  }

  // Get current sync status
  static getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  // Calculate retry delay with exponential backoff
  private static calculateRetryDelay(attempt: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 1000; // Up to 1 second jitter
    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  // Retry logic with exponential backoff
  private static async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    context: string = 'operation'
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`${context} failed (attempt ${attempt}/${maxAttempts}):`, error);
        
        if (attempt < maxAttempts) {
          const delay = this.calculateRetryDelay(attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }

  // Resolve conflicts between local and server data
  private static async resolveConflict(
    localData: any,
    serverData: any,
    strategy: ConflictResolution = ConflictResolution.SERVER_WINS
  ): Promise<any> {
    switch (strategy) {
      case ConflictResolution.LOCAL_WINS:
        return localData;
      
      case ConflictResolution.SERVER_WINS:
        return serverData;
      
      case ConflictResolution.MERGE:
        // Simple merge strategy - can be enhanced for specific data types
        if (typeof localData === 'object' && typeof serverData === 'object') {
          return {
            ...serverData,
            ...localData,
            // Prefer server timestamp if both exist
            updatedAt: serverData.updatedAt || localData.updatedAt || Date.now()
          };
        }
        return localData; // Fallback to local for non-objects
      
      case ConflictResolution.MANUAL:
        // Would require UI intervention - for now, default to server
        console.warn('Manual conflict resolution not implemented, using server data');
        return serverData;
      
      default:
        return serverData;
    }
  }

  // Sync a single data item with conflict resolution
  private static async syncDataItem(key: string, retryCount: number = 0): Promise<boolean> {
    try {
      // Try to get data from IndexedDB first, then localStorage
      let localData = await this.getData(key, true); // Prefer IndexedDB
      if (!localData) {
        localData = await this.getData(key, false); // Fallback to localStorage
      }
      
      if (!localData) {
        console.warn(`No data found for key: ${key}`);
        return true; // Consider empty data as successfully synced
      }

      let success = false;
      
      // Determine data type and sync accordingly
      if (key.startsWith('pending-resume-update') || key.startsWith('mobile-resume-draft')) {
        success = await this.syncResumeData(key, localData);
      } else if (key.startsWith('pending-resume-create')) {
        success = await this.syncResumeCreation(key, localData);
      } else if (key.startsWith('pending-application')) {
        success = await this.syncApplicationData(key, localData);
      } else if (key.startsWith('pending-profile')) {
        success = await this.syncProfileData(key, localData);
      } else {
        console.warn(`Unknown data type for key: ${key}`);
        success = true; // Skip unknown types
      }

      if (success) {
        await this.clearData(key); // Clear from both localStorage and IndexedDB
        return true;
      } else {
        throw new Error(`Failed to sync data for key: ${key}`);
      }
    } catch (error) {
      console.error(`Error syncing data item ${key}:`, error);
      this.syncStatus.errors.push({
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  // Sync resume data (update existing)
  private static async syncResumeData(key: string, localData: any): Promise<boolean> {
    const resumeId = this.extractIdFromKey(key);
    if (!resumeId) {
      console.error('No resume ID found in key:', key);
      return false;
    }

    return await this.retryWithBackoff(async () => {
      // Check for conflicts by fetching current server state
      try {
        const serverResume = await APIService.getResumes().then((resumes: any[]) => 
          resumes.find((r: any) => r.id === resumeId)
        );
        
        if (serverResume) {
          // Check for conflicts based on timestamps
          const serverTimestamp = new Date(serverResume.updatedAt || 0).getTime();
          const localTimestamp = localData.timestamp || localData.updatedAt || 0;
          
          if (serverTimestamp > localTimestamp) {
            console.warn('Server data is newer, resolving conflict...');
            const resolvedData = await this.resolveConflict(
              localData, 
              serverResume, 
              ConflictResolution.MERGE
            );
            await APIService.updateResume(resumeId, resolvedData);
          } else {
            await APIService.updateResume(resumeId, localData);
          }
        } else {
          // Resume not found on server, create it
          await APIService.createResume({ ...localData, id: resumeId });
        }
        return true;
      } catch (error) {
        console.error('Failed to sync resume data:', error);
        throw error;
      }
    }, 3, `Resume sync for ${resumeId}`);
  }

  // Sync resume creation
  private static async syncResumeCreation(key: string, localData: any): Promise<boolean> {
    return await this.retryWithBackoff(async () => {
      await APIService.createResume(localData);
      return true;
    }, 3, 'Resume creation');
  }

  // Sync application data
  private static async syncApplicationData(key: string, localData: any): Promise<boolean> {
    return await this.retryWithBackoff(async () => {
      await APIService.queueApplication(localData);
      return true;
    }, 3, 'Application sync');
  }

  // Sync profile data
  private static async syncProfileData(key: string, localData: any): Promise<boolean> {
    return await this.retryWithBackoff(async () => {
      // Assuming there's a profile update endpoint
      console.log('Profile sync not implemented, skipping:', key);
      return true;
    }, 3, 'Profile sync');
  }

  // Extract ID from storage key
  private static extractIdFromKey(key: string): string | null {
    const matches = key.match(/-([\w-]+)$/);
    return matches ? matches[1] : null;
  }

  // Sync offline changes when online
  static async syncPendingChanges(): Promise<SyncStatus> {
    if (this.syncStatus.isActive) {
      console.warn('Sync already in progress');
      return this.getSyncStatus();
    }

    // Find all pending data from both localStorage and IndexedDB
    const pendingKeys = await this.getAllPendingKeys();

    if (pendingKeys.length === 0) {
      console.log('No pending changes to sync');
      return this.getSyncStatus();
    }

    // Initialize sync status
    this.syncStatus = {
      isActive: true,
      progress: 0,
      currentItem: null,
      totalItems: pendingKeys.length,
      completedItems: 0,
      errors: []
    };

    this.emitSyncStatus();

    console.log(`Starting sync of ${pendingKeys.length} pending items...`);

    // Sync each item
    for (let i = 0; i < pendingKeys.length; i++) {
      const key = pendingKeys[i];
      this.syncStatus.currentItem = key;
      this.syncStatus.progress = (i / pendingKeys.length) * 100;
      this.emitSyncStatus();

      const success = await this.syncDataItem(key);
      if (success) {
        this.syncStatus.completedItems++;
      }
    }

    // Finalize sync status
    this.syncStatus.isActive = false;
    this.syncStatus.progress = 100;
    this.syncStatus.currentItem = null;
    this.emitSyncStatus();

    const successCount = this.syncStatus.completedItems;
    const errorCount = this.syncStatus.errors.length;

    console.log(`Sync completed: ${successCount} successful, ${errorCount} errors`);

    if (errorCount > 0) {
      console.error('Sync errors:', this.syncStatus.errors);
    }

    return this.getSyncStatus();
  }

  // Determine optimal storage method based on data size
  static getOptimalStorageMethod(data: any): boolean {
    const dataSize = JSON.stringify(data).length;
    const LARGE_DATA_THRESHOLD = 5 * 1024 * 1024; // 5MB
    
    // Use IndexedDB for large data if available
    return dataSize > LARGE_DATA_THRESHOLD && 'indexedDB' in window;
  }

  // Store data with automatic storage method selection
  static async storeDataSmart(key: string, data: any): Promise<void> {
    const useIndexedDB = this.getOptimalStorageMethod(data);
    await this.storeData(key, data, useIndexedDB);
  }

  // Get storage usage statistics
  static async getStorageStats(): Promise<{
    localStorage: { used: number; available: number };
    indexedDB: { supported: boolean; used?: number };
  }> {
    const stats: any = {
      localStorage: { used: 0, available: 0 },
      indexedDB: { supported: 'indexedDB' in window }
    };

    // Calculate localStorage usage
    let localStorageUsed = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        localStorageUsed += localStorage[key].length + key.length;
      }
    }
    stats.localStorage.used = localStorageUsed;
    
    // Estimate localStorage capacity (usually 5-10MB)
    try {
      const test = 'test';
      let i = 0;
      while (i < 10000) { // Reasonable limit to avoid hanging
        try {
          localStorage.setItem('test_' + i, test);
          i++;
        } catch (e) {
          break;
        }
      }
      // Clean up test data
      for (let j = 0; j < i; j++) {
        localStorage.removeItem('test_' + j);
      }
      stats.localStorage.available = i * test.length;
    } catch (e) {
      stats.localStorage.available = 5 * 1024 * 1024; // Default 5MB estimate
    }

    // Get IndexedDB usage if supported
    if ('indexedDB' in window && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        stats.indexedDB.used = estimate.usage || 0;
      } catch (e) {
        console.warn('Could not estimate IndexedDB usage:', e);
      }
    }

    return stats;
  }

  // Trigger background sync via service worker
  static async requestBackgroundSync(tag: string = 'cvleap-sync'): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready;
        // Check if sync is supported
        if ('sync' in registration) {
          await (registration as any).sync.register(tag);
          console.log('Background sync registered:', tag);
        } else {
          console.warn('Background sync not supported, falling back to immediate sync');
          await this.syncPendingChanges();
        }
      } catch (error) {
        console.warn('Background sync registration failed:', error);
        // Fallback to immediate sync
        await this.syncPendingChanges();
      }
    } else {
      console.warn('Service Worker not supported, falling back to immediate sync');
      await this.syncPendingChanges();
    }
  }
}