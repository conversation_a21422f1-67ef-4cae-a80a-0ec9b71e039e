import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../store';

interface APIResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

interface UseAPIOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

class APIService {
  private static async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(error || 'API request failed');
    }

    return response.json();
  }

  // Resume endpoints
  static async getResumes() {
    return this.request('/api/resumes');
  }

  static async createResume(resumeData: any) {
    return this.request('/api/resumes', {
      method: 'POST',
      body: JSON.stringify(resumeData),
    });
  }

  static async updateResume(id: string, resumeData: any) {
    return this.request(`/api/resumes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(resumeData),
    });
  }

  static async deleteResume(id: string) {
    return this.request(`/api/resumes/${id}`, {
      method: 'DELETE',
    });
  }

  // AI Enhancement endpoints
  static async enhanceResume(resumeData: any) {
    return this.request('/api/ai/enhance-resume', {
      method: 'POST',
      body: JSON.stringify(resumeData),
    });
  }

  static async generateCoverLetter(resumeId: string, jobDescription: string) {
    return this.request('/api/ai/generate-cover-letter', {
      method: 'POST',
      body: JSON.stringify({ resumeId, jobDescription }),
    });
  }

  static async analyzeATS(resumeData: any) {
    return this.request('/api/ai/analyze-ats', {
      method: 'POST',
      body: JSON.stringify(resumeData),
    });
  }

  // Job Application endpoints
  static async getApplicationStats() {
    return this.request('/api/applications/stats');
  }

  static async queueApplication(applicationData: any) {
    return this.request('/api/applications/queue', {
      method: 'POST',
      body: JSON.stringify(applicationData),
    });
  }

  static async getQueueStatus() {
    return this.request('/api/applications/queue-status');
  }

  // Analytics endpoints
  static async getEnhancedDashboard() {
    return this.request('/api/analytics/enhanced-dashboard');
  }

  static async getJobMarketInsights() {
    return this.request('/api/analytics/job-market');
  }

  static async getResumePerformance() {
    return this.request('/api/analytics/resume-performance');
  }

  static async getWeeklyTrends() {
    return this.request('/api/analytics/weekly-trends');
  }

  // Job endpoints
  static async getJobs(filters?: any) {
    const queryParams = filters ? new URLSearchParams(filters).toString() : '';
    return this.request(`/api/jobs${queryParams ? `?${queryParams}` : ''}`);
  }

  static async getJobById(id: string) {
    return this.request(`/api/jobs/${id}`);
  }
}

// Custom hook for API calls
export function useAPI<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = [],
  options: UseAPIOptions = {}
): APIResponse<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { immediate = true, onSuccess, onError } = options;

  const executeRequest = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiCall();
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (immediate) {
      executeRequest();
    }
  }, dependencies);

  return { data, loading, error, refetch: executeRequest };
}

// Specialized hooks for common operations
export function useResumes() {
  return useAPI(() => APIService.getResumes(), []);
}

export function useApplicationStats() {
  return useAPI(() => APIService.getApplicationStats(), []);
}

export function useJobMarketInsights() {
  return useAPI(() => APIService.getJobMarketInsights(), []);
}

export function useDashboardData() {
  return useAPI(() => APIService.getEnhancedDashboard(), []);
}

// Real-time data hook with WebSocket support
export function useRealTimeUpdates(eventTypes: string[] = []) {
  const [updates, setUpdates] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!user) return;

    const ws = new WebSocket(`ws://localhost:3001?token=${localStorage.getItem('token')}`);

    ws.onopen = () => {
      setIsConnected(true);
      // Subscribe to specific event types
      ws.send(JSON.stringify({ type: 'subscribe', events: eventTypes }));
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setUpdates(prev => [...prev, data]);
    };

    ws.onclose = () => {
      setIsConnected(false);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, [user, eventTypes.join(',')]);

  return { updates, isConnected };
}

export default APIService;