import React, { useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { updateResume } from '../store/resumeSlice';

// Simple debounce function with cancel
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) & { cancel: () => void } {
  let timeout: NodeJS.Timeout;
  
  const debouncedFunc = (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };

  debouncedFunc.cancel = () => {
    clearTimeout(timeout);
  };

  return debouncedFunc;
}

interface UseAutoSaveOptions {
  enabled?: boolean;
  delay?: number;
  resumeId?: number;
  onSaveSuccess?: () => void;
  onSaveError?: (error: Error) => void;
}

interface UseAutoSaveReturn {
  saveData: (data: any) => void;
  forceSave: () => void;
  lastSaved: Date | null;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
}

export const useAutoSave = (options: UseAutoSaveOptions = {}): UseAutoSaveReturn => {
  const {
    enabled = true,
    delay = 2000,
    resumeId,
    onSaveSuccess,
    onSaveError
  } = options;

  const dispatch = useDispatch();
  const pendingDataRef = useRef<any>(null);
  const lastSavedRef = useRef<Date | null>(null);
  const isSavingRef = useRef(false);
  const hasUnsavedChangesRef = useRef(false);

  const performSave = useCallback(async () => {
    if (!pendingDataRef.current || !resumeId || isSavingRef.current) {
      return;
    }

    isSavingRef.current = true;
    
    try {
      await dispatch(updateResume({
        id: resumeId,
        title: pendingDataRef.current.title || 'Untitled Resume',
        data: pendingDataRef.current
      }));

      lastSavedRef.current = new Date();
      hasUnsavedChangesRef.current = false;
      pendingDataRef.current = null;
      
      onSaveSuccess?.();
    } catch (error) {
      console.error('Auto-save failed:', error);
      onSaveError?.(error as Error);
    } finally {
      isSavingRef.current = false;
    }
  }, [dispatch, resumeId, onSaveSuccess, onSaveError]);

  const debouncedSave = useCallback(
    debounce(performSave, delay),
    [performSave, delay]
  );

  const saveData = useCallback((data: any) => {
    pendingDataRef.current = data;
    hasUnsavedChangesRef.current = true;
    
    if (enabled && resumeId) {
      debouncedSave();
    }
  }, [enabled, resumeId, debouncedSave]);

  const forceSave = useCallback(() => {
    debouncedSave.cancel();
    performSave();
  }, [debouncedSave, performSave]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedSave.cancel();
    };
  }, [debouncedSave]);

  // Save on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChangesRef.current) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        forceSave(); // Attempt final save
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [forceSave]);

  return {
    saveData,
    forceSave,
    lastSaved: lastSavedRef.current,
    isSaving: isSavingRef.current,
    hasUnsavedChanges: hasUnsavedChangesRef.current
  };
};

// Auto-save indicator component
interface AutoSaveIndicatorProps {
  lastSaved: Date | null;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  className?: string;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  lastSaved,
  isSaving,
  hasUnsavedChanges,
  className = ''
}) => {
  const getStatusMessage = () => {
    if (isSaving) {
      return { text: 'Saving...', color: 'text-blue-600' };
    }
    if (hasUnsavedChanges) {
      return { text: 'Unsaved changes', color: 'text-orange-600' };
    }
    if (lastSaved) {
      const timeAgo = Math.floor((Date.now() - lastSaved.getTime()) / 1000);
      if (timeAgo < 60) {
        return { text: 'Saved just now', color: 'text-green-600' };
      } else if (timeAgo < 3600) {
        return { text: `Saved ${Math.floor(timeAgo / 60)}m ago`, color: 'text-green-600' };
      } else {
        return { text: `Saved ${Math.floor(timeAgo / 3600)}h ago`, color: 'text-green-600' };
      }
    }
    return { text: 'Not saved', color: 'text-gray-500' };
  };

  const status = getStatusMessage();

  return (
    <div className={`flex items-center space-x-2 text-sm ${className}`}>
      {isSaving && (
        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500"></div>
      )}
      <span className={status.color}>
        {status.text}
      </span>
    </div>
  );
};

export default useAutoSave;