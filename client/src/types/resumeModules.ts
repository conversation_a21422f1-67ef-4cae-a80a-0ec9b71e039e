// Enhanced type definitions for modular resume sections

export interface ContactInfo {
  email: string;
  phone: string;
  location: string;
  website?: string;
  linkedin?: string;
  github?: string;
}

export interface HeaderContent {
  name: string;
  title: string;
  contact: ContactInfo;
  profilePhoto?: string;
}

export interface SummaryContent {
  summary: string;
  objective?: string;
}

export interface WorkExperience {
  id: string;
  company: string;
  position: string;
  location: string;
  startDate: string;
  endDate: string;
  current: boolean;
  achievements: string[];
  description?: string;
}

export interface ExperienceContent {
  experiences: WorkExperience[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  gpa?: string;
  honors?: string;
  relevantCourses?: string[];
}

export interface EducationContent {
  educations: Education[];
}

export interface Skill {
  id: string;
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  category: 'technical' | 'soft' | 'language' | 'other';
}

export interface SkillsContent {
  skills: Skill[];
  categories: {
    technical: Skill[];
    soft: Skill[];
    languages: Skill[];
    other: Skill[];
  };
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  startDate: string;
  endDate?: string;
  link?: string;
  repository?: string;
  achievements: string[];
}

export interface ProjectsContent {
  projects: Project[];
}

export interface Award {
  id: string;
  title: string;
  issuer: string;
  date: string;
  description?: string;
}

export interface Language {
  id: string;
  name: string;
  proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
}

export interface Publication {
  id: string;
  title: string;
  publisher: string;
  date: string;
  url?: string;
  description?: string;
}

export interface VolunteerWork {
  id: string;
  organization: string;
  role: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  achievements?: string[];
}

export interface AdditionalContent {
  awards?: Award[];
  languages?: Language[];
  publications?: Publication[];
  volunteerWork?: VolunteerWork[];
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
    expiryDate?: string;
    url?: string;
  }>;
}

export interface CustomContent {
  title: string;
  content: string;
  format: 'text' | 'list' | 'table';
  items?: string[];
}

export type SectionContent = 
  | HeaderContent 
  | SummaryContent 
  | ExperienceContent 
  | EducationContent 
  | SkillsContent 
  | ProjectsContent 
  | AdditionalContent 
  | CustomContent;

export interface ModularSection {
  id: string;
  type: 'header' | 'summary' | 'experience' | 'education' | 'skills' | 'projects' | 'awards' | 'languages' | 'publications' | 'volunteer' | 'certifications' | 'custom';
  title: string;
  content: SectionContent;
  isRequired: boolean;
  isVisible: boolean;
  order: number;
  styling?: {
    backgroundColor?: string;
    textColor?: string;
    accentColor?: string;
    fontSize?: string;
    fontWeight?: string;
    fontFamily?: string;
    lineHeight?: string;
    spacing?: string;
    layout?: string;
  };
}

export interface ModularTemplate {
  id: string;
  name: string;
  description: string;
  sections: ModularSection[];
  globalStyling: {
    fontFamily: string;
    fontSize: number;
    colorScheme: string;
    layout: 'single-column' | 'two-column' | 'hybrid';
    spacing: 'compact' | 'normal' | 'relaxed';
  };
  metadata: {
    createdAt: string;
    updatedAt: string;
    version: string;
  };
}

export interface ModularResumeState {
  currentTemplate: ModularTemplate | null;
  templates: ModularTemplate[];
  isPreviewMode: boolean;
  selectedSectionId: string | null;
  undoStack: ModularTemplate[];
  redoStack: ModularTemplate[];
  isLoading: boolean;
  error: string | null;
}