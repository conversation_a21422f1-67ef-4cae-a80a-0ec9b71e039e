// Enhanced type definitions for the modular template system
import type { ModularTemplate, ModularSection } from './resumeModules';

export interface ModuleMetadata {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'showcase' | 'additional' | 'professional' | 'custom';
  icon: string;
  color: string;
  isRequired: boolean;
  defaultVisible: boolean;
  supportedLayouts: string[];
  version: string;
}

export interface DragDropSettings {
  snapToGrid: boolean;
  gridSize: number;
  showGrid: boolean;
  autoSpacing: boolean;
  collisionDetection: boolean;
  smoothAnimations: boolean;
  touchOptimized: boolean;
}

export interface AccessibilitySettings {
  enableScreenReaderAnnouncements: boolean;
  showKeyboardHints: boolean;
  highContrastMode: boolean;
  reducedMotion: boolean;
  focusManagement: boolean;
  ariaLabels: {
    [key: string]: string;
  };
}

export interface PerformanceSettings {
  mode: 'performance' | 'quality';
  lazyLoading: boolean;
  virtualScrolling: boolean;
  debounceTime: number;
  throttleTime: number;
  cacheSize: number;
}

export interface CustomizationOptions {
  theme: 'light' | 'dark' | 'auto' | 'custom';
  accentColor: string;
  fontScale: number;
  spacing: 'compact' | 'normal' | 'comfortable';
  borderRadius: 'none' | 'small' | 'medium' | 'large';
  shadows: 'none' | 'subtle' | 'medium' | 'strong';
}

export interface CollaborationFeatures {
  realTimeEditing: boolean;
  commentSystem: boolean;
  versionHistory: boolean;
  shareSettings: {
    visibility: 'private' | 'shared' | 'public';
    permissions: {
      view: boolean;
      edit: boolean;
      comment: boolean;
      share: boolean;
    };
    expiresAt?: Date;
  };
  cursors: {
    [userId: string]: {
      sectionId: string;
      position: { x: number; y: number };
      timestamp: number;
    };
  };
}

export interface AnalyticsData {
  userInteractions: Array<{
    type: 'drag_start' | 'drag_end' | 'section_add' | 'section_remove' | 'section_edit' | 'reorder';
    sectionId: string;
    timestamp: number;
    duration?: number;
    metadata?: Record<string, any>;
  }>;
  performanceMetrics: {
    averageDragTime: number;
    renderTimes: number[];
    errorCount: number;
    memoryUsage?: number;
    bundleSize?: number;
  };
  usagePatterns: {
    mostUsedSections: string[];
    commonLayouts: string[];
    timeSpentEditing: number;
    completionRate: number;
  };
}

export interface ModularSectionEnhanced extends ModularSection {
  metadata: ModuleMetadata;
  dragSettings?: Partial<DragDropSettings>;
  customStyles?: {
    layout: 'single-column' | 'two-column' | 'grid' | 'flex';
    alignment: 'left' | 'center' | 'right' | 'justify';
    spacing: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
    background?: {
      color?: string;
      image?: string;
      gradient?: string;
    };
    border?: {
      width: number;
      color: string;
      style: 'solid' | 'dashed' | 'dotted';
      radius: number;
    };
    shadow?: {
      enabled: boolean;
      color: string;
      blur: number;
      spread: number;
      x: number;
      y: number;
    };
  };
  responsive?: {
    mobile: Partial<ModularSectionEnhanced['customStyles']>;
    tablet: Partial<ModularSectionEnhanced['customStyles']>;
    desktop: Partial<ModularSectionEnhanced['customStyles']>;
  };
  animations?: {
    entrance: 'none' | 'fade' | 'slide' | 'zoom' | 'bounce';
    duration: number;
    delay: number;
    easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  };
  interactions?: {
    hover: {
      scale?: number;
      opacity?: number;
      shadow?: boolean;
    };
    focus: {
      outline: boolean;
      scale?: number;
    };
    active: {
      scale?: number;
      feedback: boolean;
    };
  };
}

export interface ModularTemplateEnhanced extends ModularTemplate {
  enhancedSections: ModularSectionEnhanced[];
  settings: {
    dragDrop: DragDropSettings;
    accessibility: AccessibilitySettings;
    performance: PerformanceSettings;
    customization: CustomizationOptions;
    collaboration: CollaborationFeatures;
  };
  analytics: AnalyticsData;
  viewport: {
    width: number;
    height: number;
    scale: number;
    device: 'mobile' | 'tablet' | 'desktop';
  };
  exports: {
    supportedFormats: ('pdf' | 'docx' | 'html' | 'json' | 'png')[];
    lastExported?: {
      format: string;
      timestamp: Date;
      size: number;
    };
  };
}

// Enhanced drag and drop types
export interface DragDropContext {
  isDragging: boolean;
  draggedSection: ModularSectionEnhanced | null;
  dropZone: {
    id: string;
    position: 'before' | 'after' | 'inside';
    isValid: boolean;
  } | null;
  ghostElement: HTMLElement | null;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  snapPoints: Array<{ x: number; y: number; id: string }>;
}

export interface DropZoneInfo {
  id: string;
  type: 'section' | 'container' | 'placeholder';
  bounds: DOMRect;
  isActive: boolean;
  accepts: string[];
  metadata?: Record<string, any>;
}

export interface DragGestureInfo {
  startTime: number;
  endTime?: number;
  distance: number;
  velocity: { x: number; y: number };
  direction: 'up' | 'down' | 'left' | 'right';
  isSwipe: boolean;
  isTap: boolean;
}

// Mobile touch enhancement types
export interface TouchEnhancement {
  vibration: boolean;
  hapticFeedback: boolean;
  gestureRecognition: boolean;
  multitouch: boolean;
  pressureDetection: boolean;
}

export interface MobileGesture {
  type: 'tap' | 'long-press' | 'swipe' | 'pinch' | 'rotate';
  startPoint: { x: number; y: number };
  endPoint?: { x: number; y: number };
  duration: number;
  force?: number;
  scale?: number;
  rotation?: number;
}

// Accessibility enhancement types
export interface AccessibilityFeatures {
  ariaLive: 'off' | 'polite' | 'assertive';
  screenReaderContent: string;
  keyboardNavigation: {
    enabled: boolean;
    shortcuts: Record<string, string>;
    tabIndex: number;
  };
  highContrast: {
    enabled: boolean;
    ratio: number;
    colors: {
      foreground: string;
      background: string;
      border: string;
    };
  };
  reducedMotion: {
    enabled: boolean;
    fallbackAnimations: boolean;
  };
}

// Performance optimization types
export interface PerformanceOptimizations {
  lazyLoading: {
    enabled: boolean;
    threshold: number;
    placeholder: string;
  };
  virtualization: {
    enabled: boolean;
    itemHeight: number;
    bufferSize: number;
  };
  memoization: {
    enabled: boolean;
    cacheSize: number;
    ttl: number;
  };
  debouncing: {
    dragUpdates: number;
    resizeEvents: number;
    scrollEvents: number;
  };
}

// Advanced styling types
export interface AdvancedStyling {
  css: {
    variables: Record<string, string>;
    classes: string[];
    inline: Record<string, string>;
  };
  animations: {
    keyframes: Record<string, any>;
    transitions: Record<string, string>;
    transforms: string[];
  };
  responsive: {
    breakpoints: Record<string, number>;
    rules: Record<string, Record<string, string>>;
  };
  themes: {
    current: string;
    available: string[];
    custom: Record<string, any>;
  };
}

// Export configuration types
export interface ExportConfiguration {
  format: 'pdf' | 'docx' | 'html' | 'json' | 'png' | 'svg';
  quality: 'low' | 'medium' | 'high' | 'print';
  size: 'a4' | 'letter' | 'legal' | 'custom';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  options: {
    includeMetadata: boolean;
    embedFonts: boolean;
    compressImages: boolean;
    watermark?: string;
  };
}

// Template validation types
export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  validator: (template: ModularTemplateEnhanced) => boolean;
  message: string;
  autoFix?: (template: ModularTemplateEnhanced) => ModularTemplateEnhanced;
}

export interface ValidationResult {
  isValid: boolean;
  score: number;
  issues: Array<{
    ruleId: string;
    severity: 'error' | 'warning' | 'info';
    message: string;
    sectionId?: string;
    autoFixable: boolean;
  }>;
  suggestions: Array<{
    type: 'improvement' | 'optimization' | 'accessibility';
    message: string;
    impact: 'low' | 'medium' | 'high';
  }>;
}

// Template analytics types
export interface TemplateAnalytics {
  usage: {
    viewCount: number;
    editCount: number;
    shareCount: number;
    exportCount: number;
    lastAccessed: Date;
  };
  performance: {
    loadTime: number;
    renderTime: number;
    interactionTime: number;
    errorRate: number;
  };
  engagement: {
    timeSpent: number;
    bounceRate: number;
    completionRate: number;
    returnVisits: number;
  };
  conversion: {
    previewToEdit: number;
    editToExport: number;
    shareToView: number;
  };
}

// Search and filter types
export interface SearchFilters {
  category?: string[];
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  author?: string;
  rating?: {
    min: number;
    max: number;
  };
  complexity?: 'simple' | 'moderate' | 'complex';
  industry?: string[];
  experience?: 'entry' | 'mid' | 'senior' | 'executive';
}

export interface SearchResult {
  template: ModularTemplateEnhanced;
  score: number;
  matches: Array<{
    field: string;
    value: string;
    highlight: string;
  }>;
  relevance: number;
}

// Integration types
export interface IntegrationConfig {
  type: 'api' | 'webhook' | 'export' | 'import';
  provider: string;
  settings: Record<string, any>;
  credentials?: Record<string, string>;
  mapping?: Record<string, string>;
  enabled: boolean;
}

// Backup and versioning types
export interface TemplateVersion {
  id: string;
  version: string;
  timestamp: Date;
  author: string;
  changes: string[];
  template: ModularTemplateEnhanced;
  metadata: {
    size: number;
    checksum: string;
    tags: string[];
  };
}

export interface BackupConfiguration {
  enabled: boolean;
  frequency: 'manual' | 'hourly' | 'daily' | 'weekly';
  retention: number;
  compression: boolean;
  encryption: boolean;
  location: 'local' | 'cloud' | 'both';
}

// Type guards and utilities
export function isModularSectionEnhanced(section: any): section is ModularSectionEnhanced {
  return section && typeof section === 'object' && 'metadata' in section;
}

export function isModularTemplateEnhanced(template: any): template is ModularTemplateEnhanced {
  return template && typeof template === 'object' && 'enhancedSections' in template && 'settings' in template;
}

// Default configurations
export const DEFAULT_DRAG_DROP_SETTINGS: DragDropSettings = {
  snapToGrid: true,
  gridSize: 20,
  showGrid: false,
  autoSpacing: true,
  collisionDetection: true,
  smoothAnimations: true,
  touchOptimized: true
};

export const DEFAULT_ACCESSIBILITY_SETTINGS: AccessibilitySettings = {
  enableScreenReaderAnnouncements: true,
  showKeyboardHints: false,
  highContrastMode: false,
  reducedMotion: false,
  focusManagement: true,
  ariaLabels: {
    dragHandle: 'Drag to reorder section',
    addSection: 'Add new section',
    removeSection: 'Remove section',
    editSection: 'Edit section content'
  }
};

export const DEFAULT_PERFORMANCE_SETTINGS: PerformanceSettings = {
  mode: 'quality',
  lazyLoading: true,
  virtualScrolling: false,
  debounceTime: 300,
  throttleTime: 16,
  cacheSize: 50
};

export const DEFAULT_CUSTOMIZATION_OPTIONS: CustomizationOptions = {
  theme: 'auto',
  accentColor: '#3B82F6',
  fontScale: 1,
  spacing: 'normal',
  borderRadius: 'medium',
  shadows: 'medium'
};