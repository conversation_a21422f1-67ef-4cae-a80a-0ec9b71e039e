// Unified Template interfaces supporting both simple and advanced features

export interface TemplateSection {
  id: string;
  name: string;
  type: string;
  required: boolean;
  config?: Record<string, any>;
}

export interface TemplateStyle {
  fontFamily: string;
  fontSize: number;
  colorScheme: string;
  spacing?: Record<string, any>;
  borders?: Record<string, any>;
}

export interface TemplateLayout {
  type: 'single-column' | 'two-column' | 'three-column';
  dimensions?: Record<string, any>;
  margins?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface TemplateStructure {
  sections: TemplateSection[];
  styling: TemplateStyle;
  layout: TemplateLayout;
}

export interface TemplateAnalytics {
  usageCount: number;
  successRate: number;
  averageAtsScore: number;
  userRating: number;
}

// Unified Template interface supporting both current and PR #10 structures
export interface Template {
  // Base fields (compatible with current implementation) 
  id: number | string; // Support both number (current) and string (PR #10)
  name: string;
  description?: string; // Optional for backward compatibility
  category: string;
  industry: string[]; // Always array format
  ats_score: number;
  is_premium?: boolean; // Current implementation field
  is_customizable?: boolean; // Current implementation field
  preview_url?: string; // Current implementation field
  usage_count?: number; // Current implementation field
  rating?: number; // Current implementation field

  // Advanced fields (from PR #10)
  customizable?: boolean; // Alias for is_customizable
  structure?: any | TemplateStructure; // Support both simple and complex structures
  analytics?: TemplateAnalytics; // Optional analytics data
  created_at?: string;
  updated_at?: string;
}

// API Response types
export interface APIResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface TemplateUsageData {
  selectedSections?: string[];
  customizations?: Record<string, any>;
  completionTime?: number;
}

// Career Coach related interfaces (from PR #10)
export interface CareerStep {
  title: string;
  description: string;
  timeframe: string;
  requirements: string[];
}

export interface SkillRequirement {
  skill: string;
  level: string;
  importance: 'High' | 'Medium' | 'Low';
}

export interface MarketAnalysis {
  outlook: string;
  demandScore: number;
  growthTrend: 'Growing' | 'Stable' | 'Declining';
  salaryRange: {
    min: number;
    max: number;
    median: number;
  };
}

export interface CareerRecommendation {
  suggestedPath: CareerStep[];
  timelineEstimate: string;
  skillRequirements: SkillRequirement[];
  marketOutlook: MarketAnalysis;
}

export interface LearningResource {
  title: string;
  type: string;
  url: string;
  rating: number;
  provider: string;
}

export interface SkillGap {
  skill: string;
  currentLevel: number;
  requiredLevel: number;
  learningResources: LearningResource[];
  estimatedTimeToAcquire: string;
}

export interface InterviewQuestion {
  question: string;
  type: 'behavioral' | 'technical' | 'situational';
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  sampleAnswers: string[];
}

export interface Feedback {
  score: number;
  summary: string;
  strengths: string[];
  improvements: string[];
  suggestions: string[];
}

export interface CareerGoals {
  targetRole: string;
  targetIndustry: string;
  timeframe: string;
  preferences: string[];
  salaryExpectations: {
    min: number;
    max: number;
  };
}

export interface CoachingSession {
  id: string;
  user_id: string;
  session_type: string;
  input_data: Record<string, any>;
  recommendations: Record<string, any>;
  created_at: string;
}

export interface SalaryNegotiationAdvice {
  strategy: string;
  keyPoints: string[];
  marketData: {
    averageSalary: number;
    salaryRange: {
      min: number;
      max: number;
    };
  };
  negotiationTips: string[];
  timing: string;
  alternatives: string[];
}

export interface JobData {
  jobTitle: string;
  company: string;
  currentSalary?: number;
  targetSalary: number;
  experience: number;
}