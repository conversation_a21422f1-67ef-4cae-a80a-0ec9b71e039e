import React from 'react';
import MobileDashboard from '../components/mobile/MobileDashboard';
import MobileResumeBuilder from '../components/mobile/MobileResumeBuilder';
import MobileInput from '../components/mobile/MobileInput';
import { useTouchGestures } from '../components/mobile/TouchGestures';

// Demo component to showcase mobile features
export default function MobileDemo() {
  const [currentView, setCurrentView] = React.useState<'dashboard' | 'builder' | 'input'>('dashboard');
  const [capturedText, setCapturedText] = React.useState('');

  // Sample data for demonstration
  const mockStats = {
    activeApplications: 12,
    responseRate: 24,
    interviewsScheduled: 3,
    resumesCreated: 5
  };

  const mockApplications = [
    {
      jobTitle: 'Senior Software Engineer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      status: 'interview',
      appliedDate: '2 days ago'
    },
    {
      jobTitle: 'Full Stack Developer',
      company: 'StartupXYZ',
      location: 'Remote',
      status: 'pending',
      appliedDate: '5 days ago'
    },
    {
      jobTitle: 'Frontend Engineer',
      company: 'DesignStudio',
      location: 'New York, NY',
      status: 'pending',
      appliedDate: '1 week ago'
    }
  ];

  const mockSections = [
    {
      id: 'summary',
      type: 'summary' as const,
      title: 'Professional Summary',
      content: { text: 'Experienced software engineer with 5+ years...' },
      order: 0
    },
    {
      id: 'experience',
      type: 'experience' as const,
      title: 'Work Experience',
      content: { text: 'Senior Developer at TechCorp (2020-2024)...' },
      order: 1
    }
  ];

  const gestureRef = useTouchGestures({
    onSwipeLeft: () => {
      if (currentView === 'dashboard') setCurrentView('builder');
      else if (currentView === 'builder') setCurrentView('input');
    },
    onSwipeRight: () => {
      if (currentView === 'input') setCurrentView('builder');
      else if (currentView === 'builder') setCurrentView('dashboard');
    }
  });

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900" ref={gestureRef}>
      {/* Navigation */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            Mobile Demo
          </h1>
          <div className="flex gap-2">
            <button
              onClick={() => setCurrentView('dashboard')}
              className={`px-3 py-1 text-sm rounded ${
                currentView === 'dashboard' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-200 text-gray-700'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setCurrentView('builder')}
              className={`px-3 py-1 text-sm rounded ${
                currentView === 'builder' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-200 text-gray-700'
              }`}
            >
              Builder
            </button>
            <button
              onClick={() => setCurrentView('input')}
              className={`px-3 py-1 text-sm rounded ${
                currentView === 'input' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-200 text-gray-700'
              }`}
            >
              Input
            </button>
          </div>
        </div>
      </div>

      {/* Gesture Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900 border-b border-blue-200 dark:border-blue-700 p-3">
        <p className="text-sm text-blue-800 dark:text-blue-200 text-center">
          💡 Swipe left/right to navigate between views
        </p>
      </div>

      {/* Content */}
      <div className="transition-all duration-300 ease-in-out">
        {currentView === 'dashboard' && (
          <MobileDashboard
            stats={mockStats}
            recentApplications={mockApplications}
            onCreateResume={() => setCurrentView('builder')}
            onViewApplications={() => console.log('View applications')}
            onViewAnalytics={() => console.log('View analytics')}
          />
        )}

        {currentView === 'builder' && (
          <MobileResumeBuilder
            sections={mockSections}
            onSectionUpdate={(sections) => console.log('Sections updated:', sections)}
            onSave={() => console.log('Resume saved')}
          />
        )}

        {currentView === 'input' && (
          <div className="max-w-2xl mx-auto p-4 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Mobile Input Demo
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Test voice recording and camera capture functionality
              </p>
              
              <MobileInput
                onTextCapture={(text) => {
                  setCapturedText(text);
                  console.log('Captured text:', text);
                }}
                onError={(error) => {
                  console.error('Input error:', error);
                  alert(`Error: ${error}`);
                }}
                className="mb-6"
              />

              {capturedText && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    Captured Text:
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {capturedText}
                  </p>
                </div>
              )}
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Mobile Features Available:
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>✅ Touch gesture navigation</li>
                <li>✅ Voice-to-text input</li>
                <li>✅ Camera OCR capture</li>
                <li>✅ PWA install prompt</li>
                <li>✅ Offline functionality</li>
                <li>✅ Background sync</li>
                <li>✅ Push notifications</li>
                <li>✅ Native sharing</li>
                <li>✅ Location-based features</li>
                <li>✅ Mobile-optimized UI</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}