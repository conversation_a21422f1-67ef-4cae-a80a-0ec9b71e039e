import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ViewColumnsIcon,
  EyeIcon,
  PaintBrushIcon,
  WrenchScrewdriverIcon,
  DocumentArrowDownIcon,
  ArrowLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import TemplateGallery from '../components/templates/TemplateGallery';
import ModularTemplateBuilder from '../components/templates/ModularTemplateBuilder';
import TemplatePreview from '../components/templates/TemplatePreview';
import TemplateCustomizer from '../components/templates/TemplateCustomizer';
import type { ResumeTemplate } from '../constants/resumeTemplates';
import { templateAPI } from '../services/templateAPI';
import { generatePreviewData } from '../utils/templateUtils';

type ViewMode = 'gallery' | 'builder' | 'preview' | 'customize';

interface TemplateWorkflowStep {
  id: ViewMode;
  title: string;
  description: string;
  icon: React.ElementType;
  completed: boolean;
}

export default function AdvancedTemplatesPage() {
  const [currentView, setCurrentView] = useState<ViewMode>('gallery');
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [previewData, setPreviewData] = useState(generatePreviewData());
  
  const workflowSteps: TemplateWorkflowStep[] = [
    {
      id: 'gallery',
      title: 'Choose Template',
      description: 'Browse 100+ professional templates',
      icon: ViewColumnsIcon,
      completed: !!selectedTemplate
    },
    {
      id: 'customize',
      title: 'Customize Design',
      description: 'Personalize colors, fonts & layout',
      icon: PaintBrushIcon,
      completed: currentView === 'customize' || ['builder', 'preview'].includes(currentView)
    },
    {
      id: 'builder',
      title: 'Build Content',
      description: 'Add sections & organize layout',
      icon: WrenchScrewdriverIcon,
      completed: currentView === 'builder' || currentView === 'preview'
    },
    {
      id: 'preview',
      title: 'Preview & Export',
      description: 'Review & download your resume',
      icon: EyeIcon,
      completed: currentView === 'preview'
    }
  ];

  const handleTemplateSelect = (template: ResumeTemplate) => {
    setSelectedTemplate(template);
    setCurrentView('customize');
  };

  const handleTemplatePreview = (template: ResumeTemplate) => {
    setSelectedTemplate(template);
    setCurrentView('preview');
  };

  const handleTemplateUpdate = (updatedTemplate: ResumeTemplate) => {
    setSelectedTemplate(updatedTemplate);
  };

  const handleSaveTemplate = async () => {
    if (!selectedTemplate) return;
    
    setIsLoading(true);
    try {
      // If template has been customized, save as new custom template
      const result = await templateAPI.createTemplate({
        ...selectedTemplate,
        name: `${selectedTemplate.name} (Custom)`,
        isPremium: false
      });
      
      if (result.success) {
        console.log('Template saved successfully');
      }
    } catch (error) {
      console.error('Failed to save template:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'word' | 'google-docs') => {
    if (!selectedTemplate) return;
    
    setIsLoading(true);
    try {
      const result = await templateAPI.exportTemplate(selectedTemplate.id, format, previewData);
      
      if (result.success && result.data.downloadUrl) {
        // Create a temporary link to download the file
        const link = document.createElement('a');
        link.href = result.data.downloadUrl;
        link.download = result.data.filename || `resume.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const goBack = () => {
    switch (currentView) {
      case 'customize':
        setCurrentView('gallery');
        break;
      case 'builder':
        setCurrentView('customize');
        break;
      case 'preview':
        setCurrentView('builder');
        break;
      default:
        setCurrentView('gallery');
    }
  };

  const getCurrentStepIndex = () => {
    return workflowSteps.findIndex(step => step.id === currentView);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {currentView !== 'gallery' && (
                <button
                  onClick={goBack}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                  <span>Back</span>
                </button>
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Advanced Resume Templates</h1>
                <p className="text-gray-600">
                  {selectedTemplate ? `Working on: ${selectedTemplate.name}` : 'Create professional resumes with our advanced template system'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {selectedTemplate && currentView !== 'gallery' && (
                <>
                  <button
                    onClick={() => handleExport('pdf')}
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4" />
                    <span>Export PDF</span>
                  </button>
                  <select
                    onChange={(e) => {
                      const format = e.target.value as 'pdf' | 'word' | 'google-docs';
                      if (format) handleExport(format);
                      e.target.value = '';
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="">More Formats</option>
                    <option value="word">Word Document</option>
                    <option value="google-docs">Google Docs</option>
                  </select>
                </>
              )}
            </div>
          </div>

          {/* Workflow Progress */}
          {selectedTemplate && (
            <div className="mt-6">
              <div className="flex items-center space-x-4">
                {workflowSteps.map((step, index) => {
                  const IconComponent = step.icon;
                  const isActive = step.id === currentView;
                  const isCompleted = step.completed;
                  const isAccessible = index <= getCurrentStepIndex() + 1;

                  return (
                    <React.Fragment key={step.id}>
                      <button
                        onClick={() => isAccessible && setCurrentView(step.id)}
                        disabled={!isAccessible}
                        className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all ${
                          isActive
                            ? 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                            : isCompleted
                            ? 'bg-green-100 text-green-700 border-2 border-green-300 hover:bg-green-200'
                            : isAccessible
                            ? 'bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200'
                            : 'bg-gray-50 text-gray-400 border-2 border-gray-200 cursor-not-allowed'
                        }`}
                      >
                        <div className={`p-2 rounded-full ${
                          isActive 
                            ? 'bg-blue-200' 
                            : isCompleted 
                            ? 'bg-green-200' 
                            : 'bg-gray-200'
                        }`}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-sm">{step.title}</div>
                          <div className="text-xs opacity-75">{step.description}</div>
                        </div>
                      </button>
                      {index < workflowSteps.length - 1 && (
                        <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                      )}
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentView}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            style={{ height: '100%' }}
          >
            {currentView === 'gallery' && (
              <TemplateGallery
                onTemplateSelect={handleTemplateSelect}
                onTemplatePreview={handleTemplatePreview}
                selectedTemplateId={selectedTemplate?.id}
              />
            )}

            {currentView === 'customize' && selectedTemplate && (
              <div className="h-screen flex">
                <div className="w-96 border-r border-gray-200">
                  <TemplateCustomizer
                    template={selectedTemplate}
                    onTemplateUpdate={handleTemplateUpdate}
                    onPreview={() => setCurrentView('preview')}
                    onSave={handleSaveTemplate}
                  />
                </div>
                <div className="flex-1">
                  <TemplatePreview
                    template={selectedTemplate}
                    data={previewData}
                    onExport={handleExport}
                  />
                </div>
              </div>
            )}

            {currentView === 'builder' && selectedTemplate && (
              <ModularTemplateBuilder
                template={selectedTemplate}
                onTemplateUpdate={handleTemplateUpdate}
                onPreview={() => setCurrentView('preview')}
                onSave={handleSaveTemplate}
              />
            )}

            {currentView === 'preview' && selectedTemplate && (
              <TemplatePreview
                template={selectedTemplate}
                data={previewData}
                onEdit={() => setCurrentView('builder')}
                onExport={handleExport}
                isEditable={true}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
}