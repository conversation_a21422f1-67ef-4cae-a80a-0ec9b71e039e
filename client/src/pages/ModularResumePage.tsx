import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '../store';
import { setCurrentTemplate } from '../store/modularResumeSlice';
import type { ModularTemplate } from '../types/resumeModules';
import EnhancedDragDropResumeBuilder from '../components/resume/EnhancedDragDropResumeBuilder';
import ResumePreview from '../components/resume/ResumePreview';
import RealTimePreview from '../components/resume/RealTimePreview';
import { Monitor, Smartphone, Tablet } from 'lucide-react';
import Button from '../components/ui/Button';

const ModularResumePage: React.FC = () => {
  const dispatch = useDispatch();
  const { currentTemplate } = useSelector(
    (state: RootState) => state.modularResume
  );

  const [previewSize, setPreviewSize] = React.useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [showLivePreview, setShowLivePreview] = React.useState(true);

  useEffect(() => {
    // Initialize with a demo template if none exists
    if (!currentTemplate) {
      const demoTemplate: ModularTemplate = {
        id: 'demo-template',
        name: 'Professional Resume Template',
        description: 'A clean, modern template perfect for professional roles',
        sections: [
          {
            id: 'header-1',
            type: 'header',
            title: 'Personal Information',
            content: {
              name: 'John Doe',
              title: 'Senior Software Engineer',
              contact: {
                email: '<EMAIL>',
                phone: '+****************',
                location: 'San Francisco, CA',
                website: 'https://johndoe.dev',
                linkedin: 'https://linkedin.com/in/johndoe',
                github: 'https://github.com/johndoe'
              }
            },
            isRequired: true,
            isVisible: true,
            order: 0
          },
          {
            id: 'summary-1',
            type: 'summary',
            title: 'Professional Summary',
            content: {
              summary: 'Experienced software engineer with 8+ years developing scalable web applications. Passionate about clean code, performance optimization, and mentoring junior developers. Proven track record of leading cross-functional teams and delivering high-quality software solutions.',
              objective: ''
            },
            isRequired: false,
            isVisible: true,
            order: 1
          },
          {
            id: 'experience-1',
            type: 'experience',
            title: 'Work Experience',
            content: {
              experiences: [
                {
                  id: 'exp-1',
                  company: 'Tech Solutions Inc.',
                  position: 'Senior Software Engineer',
                  location: 'San Francisco, CA',
                  startDate: '2020-03',
                  endDate: '',
                  current: true,
                  achievements: [
                    'Led development of microservices architecture serving 1M+ daily users',
                    'Improved application performance by 40% through code optimization',
                    'Mentored 5 junior developers and established coding best practices'
                  ],
                  description: 'Lead engineer responsible for designing and implementing scalable backend systems.'
                },
                {
                  id: 'exp-2',
                  company: 'Digital Innovations LLC',
                  position: 'Software Engineer',
                  location: 'San Francisco, CA',
                  startDate: '2018-06',
                  endDate: '2020-02',
                  current: false,
                  achievements: [
                    'Developed RESTful APIs used by 500K+ monthly active users',
                    'Reduced deployment time by 60% through CI/CD pipeline improvements',
                    'Collaborated with product team to deliver 15+ major features'
                  ],
                  description: 'Full-stack developer working on customer-facing web applications.'
                }
              ]
            },
            isRequired: false,
            isVisible: true,
            order: 2
          },
          {
            id: 'skills-1',
            type: 'skills',
            title: 'Technical Skills',
            content: {
              skills: [
                { id: 'skill-1', name: 'JavaScript', level: 'expert', category: 'technical' },
                { id: 'skill-2', name: 'React', level: 'expert', category: 'technical' },
                { id: 'skill-3', name: 'Node.js', level: 'advanced', category: 'technical' },
                { id: 'skill-4', name: 'Python', level: 'advanced', category: 'technical' },
                { id: 'skill-5', name: 'Leadership', level: 'advanced', category: 'soft' },
                { id: 'skill-6', name: 'Communication', level: 'expert', category: 'soft' },
                { id: 'skill-7', name: 'English', level: 'expert', category: 'language' },
                { id: 'skill-8', name: 'Spanish', level: 'intermediate', category: 'language' }
              ],
              categories: {
                technical: [
                  { id: 'skill-1', name: 'JavaScript', level: 'expert', category: 'technical' },
                  { id: 'skill-2', name: 'React', level: 'expert', category: 'technical' },
                  { id: 'skill-3', name: 'Node.js', level: 'advanced', category: 'technical' },
                  { id: 'skill-4', name: 'Python', level: 'advanced', category: 'technical' }
                ],
                soft: [
                  { id: 'skill-5', name: 'Leadership', level: 'advanced', category: 'soft' },
                  { id: 'skill-6', name: 'Communication', level: 'expert', category: 'soft' }
                ],
                languages: [
                  { id: 'skill-7', name: 'English', level: 'expert', category: 'language' },
                  { id: 'skill-8', name: 'Spanish', level: 'intermediate', category: 'language' }
                ],
                other: []
              }
            },
            isRequired: false,
            isVisible: true,
            order: 3
          }
        ],
        globalStyling: {
          fontFamily: 'Arial',
          fontSize: 12,
          colorScheme: 'professional',
          layout: 'single-column',
          spacing: 'normal'
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          version: '1.0.0'
        }
      };

      dispatch(setCurrentTemplate(demoTemplate));
    }
  }, [dispatch, currentTemplate]);

  const getPreviewContainerClass = () => {
    switch (previewSize) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      default:
        return 'max-w-4xl mx-auto';
    }
  };

  if (!currentTemplate) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Dynamic Modular Resume Builder
          </h1>
          <p className="text-gray-600 text-lg">
            Build your perfect resume with drag-and-drop functionality and real-time preview
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Resume Builder */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Resume Editor</h2>
              <Button
                onClick={() => setShowLivePreview(!showLivePreview)}
                variant="outline"
                size="sm"
              >
                {showLivePreview ? 'Hide Preview' : 'Show Preview'}
              </Button>
            </div>
            
            <EnhancedDragDropResumeBuilder />
          </div>

          {/* Live Preview */}
          {showLivePreview && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Live Preview</h2>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={previewSize === 'desktop' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewSize('desktop')}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewSize === 'tablet' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewSize('tablet')}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewSize === 'mobile' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewSize('mobile')}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className={`transition-all duration-300 ${getPreviewContainerClass()}`}>
                <RealTimePreview 
                  isVisible={showLivePreview}
                  className="border rounded-lg overflow-hidden"
                />
              </div>

              <div className="mt-4 text-sm text-gray-500 text-center">
                Preview Size: {previewSize.charAt(0).toUpperCase() + previewSize.slice(1)}
              </div>
            </div>
          )}
        </div>

        {/* Features Overview */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Key Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-semibold">🎯</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Drag & Drop Interface</h3>
                <p className="text-gray-600 text-sm">
                  Easily reorder sections by dragging and dropping them into your preferred layout.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 font-semibold">👁️</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Real-time Preview</h3>
                <p className="text-gray-600 text-sm">
                  See your changes instantly with live preview across desktop, tablet, and mobile views.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 font-semibold">🔧</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Modular Sections</h3>
                <p className="text-gray-600 text-sm">
                  Add, remove, and customize individual sections with specialized content editors.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 font-semibold">↩️</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Undo/Redo</h3>
                <p className="text-gray-600 text-sm">
                  Experiment freely with undo/redo functionality to try different layouts.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-red-600 font-semibold">📱</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Responsive Design</h3>
                <p className="text-gray-600 text-sm">
                  Your resume looks great on all devices with automatic responsive adjustments.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <span className="text-indigo-600 font-semibold">🎨</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Custom Styling</h3>
                <p className="text-gray-600 text-sm">
                  Customize fonts, colors, spacing, and layout to match your personal style.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModularResumePage;