import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  DocumentArrowDownIcon,
  StarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { templateAPI } from '../services/templateAPI';

interface TemplateAnalytics {
  templateId: string;
  templateName: string;
  category: string;
  metrics: {
    views: number;
    selections: number;
    exports: number;
    rating: number;
    usageGrowth: number;
    conversionRate: number;
  };
  performance: {
    daily: Array<{
      date: string;
      views: number;
      selections: number;
      exports: number;
    }>;
  };
}

interface OverallStats {
  totalTemplates: number;
  totalViews: number;
  totalSelections: number;
  totalExports: number;
  averageRating: number;
  topPerforming: string[];
}

export default function TemplateAnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<TemplateAnalytics[]>([]);
  const [overallStats, setOverallStats] = useState<OverallStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange, selectedCategory]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      // Fetch overall statistics
      const statsResult = await templateAPI.getTemplateStatistics();
      if (statsResult.success) {
        setOverallStats(statsResult.data);
      }

      // Mock template analytics data - in real implementation, fetch from API
      const mockAnalytics: TemplateAnalytics[] = [
        {
          templateId: 'modern-professional',
          templateName: 'Modern Professional',
          category: 'professional',
          metrics: {
            views: 15420,
            selections: 2845,
            exports: 1623,
            rating: 4.7,
            usageGrowth: 15.3,
            conversionRate: 18.5
          },
          performance: {
            daily: generateMockDailyData(30)
          }
        },
        {
          templateId: 'creative-designer',
          templateName: 'Creative Designer',
          category: 'creative',
          metrics: {
            views: 12680,
            selections: 1972,
            exports: 1205,
            rating: 4.5,
            usageGrowth: 8.7,
            conversionRate: 15.6
          },
          performance: {
            daily: generateMockDailyData(30)
          }
        },
        {
          templateId: 'ats-optimized',
          templateName: 'ATS Optimized',
          category: 'ats-optimized',
          metrics: {
            views: 18950,
            selections: 3421,
            exports: 2156,
            rating: 4.8,
            usageGrowth: 22.1,
            conversionRate: 18.1
          },
          performance: {
            daily: generateMockDailyData(30)
          }
        },
        {
          templateId: 'tech-engineer',
          templateName: 'Tech Engineer',
          category: 'technology',
          metrics: {
            views: 9845,
            selections: 1534,
            exports: 892,
            rating: 4.4,
            usageGrowth: -2.3,
            conversionRate: 15.6
          },
          performance: {
            daily: generateMockDailyData(30)
          }
        },
        {
          templateId: 'executive-classic',
          templateName: 'Executive Classic',
          category: 'executive',
          metrics: {
            views: 7632,
            selections: 987,
            exports: 654,
            rating: 4.6,
            usageGrowth: 5.2,
            conversionRate: 12.9
          },
          performance: {
            daily: generateMockDailyData(30)
          }
        }
      ];

      // Filter by category if selected
      const filteredAnalytics = selectedCategory === 'all' 
        ? mockAnalytics 
        : mockAnalytics.filter(a => a.category === selectedCategory);

      setAnalytics(filteredAnalytics);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateMockDailyData = (days: number) => {
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        views: Math.floor(Math.random() * 500) + 100,
        selections: Math.floor(Math.random() * 100) + 20,
        exports: Math.floor(Math.random() * 50) + 10
      });
    }
    return data;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string;
    value: number;
    change?: number;
    icon: React.ElementType;
    format?: 'number' | 'percentage' | 'rating';
  }) => (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">
            {format === 'percentage' && '%'}
            {format === 'rating' ? value.toFixed(1) : formatNumber(value)}
            {format === 'percentage' && value}
          </p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              {change > 0 ? (
                <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(change)}%
              </span>
            </div>
          )}
        </div>
        <div className="p-3 bg-blue-100 rounded-lg">
          <Icon className="h-6 w-6 text-blue-600" />
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  const totalViews = analytics.reduce((sum, a) => sum + a.metrics.views, 0);
  const totalSelections = analytics.reduce((sum, a) => sum + a.metrics.selections, 0);
  const totalExports = analytics.reduce((sum, a) => sum + a.metrics.exports, 0);
  const averageRating = analytics.reduce((sum, a) => sum + a.metrics.rating, 0) / analytics.length;

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Template Analytics</h1>
          <p className="text-gray-600 mt-2">Performance insights for your resume templates</p>
        </div>

        <div className="flex items-center space-x-4">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="all">All Categories</option>
            <option value="professional">Professional</option>
            <option value="creative">Creative</option>
            <option value="executive">Executive</option>
            <option value="ats-optimized">ATS Optimized</option>
            <option value="technology">Technology</option>
          </select>

          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title="Total Views"
          value={totalViews}
          icon={EyeIcon}
        />
        <MetricCard
          title="Total Selections"
          value={totalSelections}
          icon={CursorArrowRaysIcon}
        />
        <MetricCard
          title="Total Exports"
          value={totalExports}
          icon={DocumentArrowDownIcon}
        />
        <MetricCard
          title="Average Rating"
          value={averageRating}
          icon={StarIcon}
          format="rating"
        />
      </div>

      {/* Template Performance Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Template Performance</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Template
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Views
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Selections
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Exports
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conversion Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Growth
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analytics.map((template) => (
                <motion.tr
                  key={template.templateId}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {template.templateName}
                      </div>
                      <div className="text-sm text-gray-500 capitalize">
                        {template.category}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(template.metrics.views)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(template.metrics.selections)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(template.metrics.exports)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {template.metrics.conversionRate}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 text-yellow-400 mr-1" />
                      <span className="text-sm text-gray-900">
                        {template.metrics.rating.toFixed(1)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {template.metrics.usageGrowth > 0 ? (
                        <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
                      )}
                      <span className={`text-sm ${
                        template.metrics.usageGrowth > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {Math.abs(template.metrics.usageGrowth)}%
                      </span>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Insights and Recommendations */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Templates</h3>
          <div className="space-y-3">
            {analytics
              .sort((a, b) => b.metrics.conversionRate - a.metrics.conversionRate)
              .slice(0, 3)
              .map((template, index) => (
                <div key={template.templateId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                      index === 0 ? 'bg-yellow-100 text-yellow-800' :
                      index === 1 ? 'bg-gray-100 text-gray-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {index + 1}
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {template.templateName}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">
                    {template.metrics.conversionRate}% conversion
                  </span>
                </div>
              ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Improve Low-Performing Templates</p>
                <p className="text-sm text-gray-600">
                  Consider updating templates with conversion rates below 15%
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Expand Popular Categories</p>
                <p className="text-sm text-gray-600">
                  ATS-optimized templates show highest engagement rates
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">A/B Test New Designs</p>
                <p className="text-sm text-gray-600">
                  Test variations of top-performing templates
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}