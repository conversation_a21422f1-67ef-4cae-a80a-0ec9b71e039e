import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  Sparkles, 
  Smartphone, 
  Monitor, 
  Accessibility, 
  Zap,
  Grid,
  Play,
  BookOpen
} from 'lucide-react';

import type { RootState } from '../store';
import { setCurrentTemplate } from '../store/slices/moduleLayoutSlice';
import type { ModularTemplate } from '../types/resumeModules';

import EnhancedModularTemplateBuilder from '../components/ResumeBuilder/ModularTemplate/EnhancedModularTemplateBuilder';
import { EnhancedDragDropProvider } from '../components/DragDrop/EnhancedDragDropComponents';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

// Demo template for showcase
const createDemoTemplate = (): ModularTemplate => ({
  id: 'demo-template-1',
  name: 'Enhanced Resume Template',
  description: 'A modern, responsive resume template with advanced drag-and-drop capabilities',
  sections: [
    {
      id: 'header-demo',
      type: 'header',
      title: 'Header',
      content: {
        name: '<PERSON>',
        title: 'Full <PERSON>ack <PERSON>eloper',
        contact: {
          email: '<EMAIL>',
          phone: '+****************',
          location: 'Seattle, WA',
          linkedin: 'linkedin.com/in/alexjohnson',
          github: 'github.com/alexjohnson'
        }
      },
      isRequired: true,
      isVisible: true,
      order: 0
    },
    {
      id: 'summary-demo',
      type: 'summary',
      title: 'Professional Summary',
      content: {
        summary: 'Innovative Full Stack Developer with 5+ years of experience building scalable web applications. Passionate about creating user-centric solutions and leveraging modern technologies to drive business growth.'
      },
      isRequired: false,
      isVisible: true,
      order: 1
    },
    {
      id: 'experience-demo',
      type: 'experience',
      title: 'Work Experience',
      content: {
        experiences: [
          {
            id: 'exp-1',
            company: 'TechCorp Inc.',
            position: 'Senior Full Stack Developer',
            location: 'Seattle, WA',
            startDate: '2021-03',
            endDate: '2024-01',
            current: false,
            achievements: [
              'Led development of microservices architecture, improving system performance by 40%',
              'Mentored junior developers and established code review best practices',
              'Implemented CI/CD pipelines reducing deployment time by 60%'
            ]
          },
          {
            id: 'exp-2',
            company: 'StartupXYZ',
            position: 'Frontend Developer',
            location: 'Remote',
            startDate: '2019-06',
            endDate: '2021-02',
            current: false,
            achievements: [
              'Built responsive React applications with 99.9% uptime',
              'Optimized bundle size by 50% through code splitting',
              'Collaborated with UX team to improve user engagement by 25%'
            ]
          }
        ]
      },
      isRequired: true,
      isVisible: true,
      order: 2
    },
    {
      id: 'skills-demo',
      type: 'skills',
      title: 'Technical Skills',
      content: {
        skills: [
          {
            id: 'skill-1',
            name: 'React/TypeScript',
            level: 'Expert',
            category: 'Frontend',
            years: 5
          },
          {
            id: 'skill-2',
            name: 'Node.js/Express',
            level: 'Advanced',
            category: 'Backend',
            years: 4
          },
          {
            id: 'skill-3',
            name: 'PostgreSQL/MongoDB',
            level: 'Advanced',
            category: 'Database',
            years: 4
          },
          {
            id: 'skill-4',
            name: 'AWS/Docker',
            level: 'Intermediate',
            category: 'DevOps',
            years: 3
          }
        ]
      },
      isRequired: false,
      isVisible: true,
      order: 3
    }
  ],
  globalStyling: {
    fontFamily: 'Inter',
    fontSize: 14,
    colorScheme: 'blue',
    layout: 'single-column',
    spacing: 'normal'
  },
  metadata: {
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: '2.0.0'
  }
});

const EnhancedModularTemplatePage: React.FC = () => {
  const dispatch = useDispatch();
  const { currentTemplate } = useSelector((state: RootState) => state.enhancedModuleLayout);
  
  const [showDemo, setShowDemo] = useState(false);
  const [features] = useState([
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: 'Enhanced Drag & Drop',
      description: 'Intuitive section reordering with visual feedback, snap-to-grid, and smooth animations'
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: 'Mobile Optimized',
      description: 'Touch-friendly interactions with haptic feedback and gesture recognition'
    },
    {
      icon: <Accessibility className="h-6 w-6" />,
      title: 'WCAG 2.1 Compliant',
      description: 'Full accessibility support with screen readers, keyboard navigation, and high contrast'
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: 'Performance First',
      description: 'Optimized rendering with lazy loading, virtualization, and smart caching'
    },
    {
      icon: <Grid className="h-6 w-6" />,
      title: 'Smart Grid System',
      description: 'Automatic spacing, collision detection, and magnetic snap positioning'
    },
    {
      icon: <Monitor className="h-6 w-6" />,
      title: 'Real-time Preview',
      description: 'Live preview with device views, responsive breakpoints, and instant updates'
    }
  ]);

  useEffect(() => {
    // Initialize with demo template if none exists
    if (!currentTemplate && !showDemo) {
      const demoTemplate = createDemoTemplate();
      dispatch(setCurrentTemplate(demoTemplate));
    }
  }, [currentTemplate, dispatch, showDemo]);

  const handleStartDemo = () => {
    const demoTemplate = createDemoTemplate();
    dispatch(setCurrentTemplate(demoTemplate));
    setShowDemo(true);
  };

  const handleSaveTemplate = (template: ModularTemplate) => {
    console.log('Saving template:', template);
    // In a real app, this would save to the backend
  };

  const handleExportTemplate = (format: 'pdf' | 'docx' | 'json') => {
    console.log('Exporting template as:', format);
    // In a real app, this would trigger the export process
  };

  if (showDemo) {
    return (
      <div className="min-h-screen bg-gray-50">
        <EnhancedDragDropProvider
          options={{
            snapToGrid: true,
            showGrid: false,
            gridSize: 20,
            autoSpacing: true,
            performance: 'smooth',
            accessibility: true
          }}
        >
          <EnhancedModularTemplateBuilder
            template={currentTemplate!}
            className="max-w-7xl mx-auto px-4 py-6"
            onSave={handleSaveTemplate}
            onExport={handleExportTemplate}
          />
        </EnhancedDragDropProvider>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-6"
          >
            <Sparkles className="h-10 w-10 text-blue-600" />
          </motion.div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Enhanced Modular
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {' '}Template System
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Build beautiful, professional resumes with our advanced drag-and-drop system. 
            Features mobile optimization, accessibility compliance, and real-time collaboration.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={handleStartDemo}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4"
            >
              <Play className="h-5 w-5 mr-2" />
              Try Interactive Demo
            </Button>
            
            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4"
              onClick={() => {
                // Scroll to features section
                document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              <BookOpen className="h-5 w-5 mr-2" />
              Learn More
            </Button>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          id="features"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Powerful Features
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <Card className="p-6 h-full hover:shadow-lg transition-shadow">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                    <div className="text-blue-600">
                      {feature.icon}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Technical Highlights */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Technical Excellence
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">
                Built for Performance & Accessibility
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">WCAG 2.1 AA Compliant</h4>
                    <p className="text-gray-600">Full screen reader support, keyboard navigation, and high contrast modes</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">Mobile-First Design</h4>
                    <p className="text-gray-600">Touch-optimized with haptic feedback and gesture recognition</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">Performance Optimized</h4>
                    <p className="text-gray-600">Lazy loading, virtual scrolling, and intelligent caching</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">Real-time Collaboration</h4>
                    <p className="text-gray-600">Multi-user editing with live cursors and conflict resolution</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-900 rounded-xl p-6 text-green-400 font-mono text-sm overflow-hidden">
              <div className="mb-4">
                <span className="text-gray-500">// Enhanced features</span>
              </div>
              <div className="space-y-2">
                <div><span className="text-blue-400">const</span> features = {'{'};</div>
                <div className="pl-4">dragDrop: <span className="text-yellow-400">'react-beautiful-dnd'</span>,</div>
                <div className="pl-4">animations: <span className="text-yellow-400">'framer-motion'</span>,</div>
                <div className="pl-4">accessibility: <span className="text-yellow-400">'WCAG 2.1 AA'</span>,</div>
                <div className="pl-4">mobile: <span className="text-yellow-400">'touch-optimized'</span>,</div>
                <div className="pl-4">performance: <span className="text-yellow-400">'lazy-loading'</span>,</div>
                <div className="pl-4">collaboration: <span className="text-yellow-400">'real-time'</span></div>
                <div>{'}'};</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center"
        >
          <Card className="p-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Build Your Perfect Resume?
            </h2>
            <p className="text-xl mb-8 text-blue-100">
              Experience the future of resume building with our enhanced modular template system.
            </p>
            <Button
              size="lg"
              onClick={handleStartDemo}
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
            >
              <Play className="h-5 w-5 mr-2" />
              Start Building Now
            </Button>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedModularTemplatePage;