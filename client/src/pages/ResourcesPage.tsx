import React from 'react';
import ContactForm from '../components/ContactForm';
import { trackPageView } from '../utils/analytics';
import { BookOpenIcon, ChartBarIcon, DocumentTextIcon, BriefcaseIcon, StarIcon, UserGroupIcon } from '@heroicons/react/24/outline';

const ResourcesPage: React.FC = () => {
  React.useEffect(() => {
    // Track page view for resources
    trackPageView({
      page_title: 'Resources - CVleap',
      content_group1: 'Resources',
    });
  }, []);

  const guidesData = [
    {
      title: "Complete CV Writing Guide 2024",
      description: "Master the art of resume writing with our comprehensive guide covering modern ATS-friendly formats, keyword optimization, and industry-specific tips.",
      category: "Guide",
      readTime: "15 min read",
      featured: true,
      icon: DocumentTextIcon
    },
    {
      title: "ATS Optimization Checklist",
      description: "Ensure your resume passes Applicant Tracking Systems with our detailed checklist. Learn about formatting, keywords, and common ATS pitfalls.",
      category: "Checklist",
      readTime: "8 min read",
      featured: true,
      icon: ChartBarIcon
    },
    {
      title: "Industry-Specific Resume Templates",
      description: "Access professionally designed templates tailored for different industries including tech, healthcare, finance, marketing, and more.",
      category: "Templates",
      readTime: "5 min read",
      featured: false,
      icon: BriefcaseIcon
    },
    {
      title: "Interview Preparation Guide",
      description: "From common questions to salary negotiation, prepare for every aspect of the interview process with our comprehensive guide.",
      category: "Guide",
      readTime: "20 min read",
      featured: false,
      icon: UserGroupIcon
    },
    {
      title: "LinkedIn Profile Optimization",
      description: "Transform your LinkedIn presence to attract recruiters and build your professional network with proven optimization strategies.",
      category: "Guide",
      readTime: "12 min read",
      featured: true,
      icon: StarIcon
    },
    {
      title: "Career Change Strategy",
      description: "Navigate career transitions successfully with our step-by-step guide to changing industries or roles while maximizing your experience.",
      category: "Strategy",
      readTime: "18 min read",
      featured: false,
      icon: BookOpenIcon
    }
  ];

  const latestUpdates = [
    {
      date: "2024-01-15",
      title: "New AI-Powered Resume Scoring Feature",
      description: "Get instant feedback on your resume with our advanced AI scoring system that analyzes content, formatting, and ATS compatibility."
    },
    {
      date: "2024-01-08",
      title: "2024 Job Market Trends Report",
      description: "Discover the latest hiring trends, in-demand skills, and salary insights across different industries for 2024."
    },
    {
      date: "2024-01-01",
      title: "Updated ATS Guidelines for 2024",
      description: "Stay ahead with the latest ATS requirements and formatting guidelines that major companies are using in 2024."
    }
  ];

  const stats = [
    { label: "Successful Resumes", value: "50,000+", description: "Resumes optimized and approved" },
    { label: "Job Placements", value: "15,000+", description: "Users who landed their dream jobs" },
    { label: "ATS Pass Rate", value: "94%", description: "Of our optimized resumes pass ATS screening" },
    { label: "Average Salary Increase", value: "23%", description: "Salary boost after using our platform" }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="responsive-container">
          <div className="content-container text-center">
          <h1 className="text-5xl font-bold mb-6">
            CV Building Resources & Career Guidance
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Everything you need to create winning resumes, optimize for ATS systems, and land your dream job. 
            Stay updated with the latest trends, tips, and strategies from career experts.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => document.getElementById('guides')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
            >
              Browse Guides
            </button>
            <button 
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Get Expert Help
            </button>
          </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="responsive-container">
          <div className="content-container">
            <div className="responsive-grid responsive-grid-4">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {stat.value}
                </div>
                <div className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {stat.description}
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </section>

      {/* Latest Updates Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Latest Updates & News
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Stay informed with the latest developments in CV building and job search strategies
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {latestUpdates.map((update, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div className="text-sm text-blue-600 dark:text-blue-400 font-medium mb-2">
                  {new Date(update.date).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {update.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {update.description}
                </p>
                <button className="mt-4 text-blue-600 dark:text-blue-400 font-medium hover:underline">
                  Read More →
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Guides & Resources Section */}
      <section id="guides" className="py-20 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Comprehensive CV Building Guides
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Expert-crafted resources to help you create compelling resumes, optimize for ATS systems, 
              and stand out in today's competitive job market.
            </p>
          </div>

          {/* Featured Resources */}
          <div className="mb-12">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              ⭐ Featured Resources
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {guidesData.filter(guide => guide.featured).map((guide, index) => (
                <div key={index} className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-700 dark:to-gray-600 p-6 rounded-lg border-2 border-blue-200 dark:border-blue-400">
                  <div className="flex items-center mb-4">
                    <guide.icon className="icon-lg text-blue-600 dark:text-blue-400 mr-3" />
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                      {guide.category}
                    </span>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {guide.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {guide.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {guide.readTime}
                    </span>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200">
                      Read Guide
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* All Resources */}
          <div>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              All Resources
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {guidesData.map((guide, index) => (
                <div key={index} className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center mb-4">
                    <guide.icon className="icon-base text-gray-600 dark:text-gray-400 mr-3" />
                    <span className="bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full font-medium">
                      {guide.category}
                    </span>
                    {guide.featured && (
                      <StarIcon className="icon-sm text-yellow-500 ml-2" />
                    )}
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {guide.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                    {guide.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {guide.readTime}
                    </span>
                    <button className="text-blue-600 dark:text-blue-400 font-medium hover:underline text-sm">
                      Read More →
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Need Personalized CV Help?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Get expert guidance tailored to your specific industry and career goals. 
              Our career specialists are here to help you create a resume that stands out.
            </p>
          </div>
          
          <div className="max-w-2xl mx-auto">
            <ContactForm />
          </div>

          {/* Additional Contact Info */}
          <div className="mt-12 text-center">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Expert Consultation</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  One-on-one sessions with career specialists
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Resume Review</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Professional feedback on your existing resume
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Industry Insights</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Latest trends and requirements for your field
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">CVleap</h3>
              <p className="text-gray-400">
                Your trusted partner for creating professional resumes and advancing your career through AI-powered optimization.
              </p>
            </div>
            
            <div>
              <h4 className="text-md font-semibold mb-4">Features</h4>
              <ul className="space-y-2 text-gray-400">
                <li>AI Resume Builder</li>
                <li>ATS Optimization</li>
                <li>Resume Templates</li>
                <li>Career Analytics</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-md font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-gray-400">
                <li>CV Writing Guide</li>
                <li>Interview Preparation</li>
                <li>Career Change Tips</li>
                <li>Industry Insights</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-md font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Help Center</li>
                <li>Contact Us</li>
                <li>Expert Consultation</li>
                <li>Community Forum</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 CVleap. All rights reserved. Empowering careers through intelligent resume optimization.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ResourcesPage;