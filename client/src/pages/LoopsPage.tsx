import React, { useState, useEffect } from 'react';
import { 
  LoopCreator, 
  LoopDashboard, 
  LoopPerformance, 
  LoopSettings 
} from '../components/loops';
import { loopApiService } from '../services/loopApiService';

interface JobLoop {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed' | 'draft';
  configuration: any;
  createdAt: string;
  updatedAt: string;
  lastRunAt?: string;
  nextRunAt?: string;
  jobTitles: string[];
  locations: string[];
  industries: string[];
  maxApplicationsPerDay: number;
}

type ViewMode = 'dashboard' | 'create' | 'performance' | 'settings';

export default function LoopsPage() {
  const [loops, setLoops] = useState<JobLoop[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('dashboard');
  const [selectedLoop, setSelectedLoop] = useState<JobLoop | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load loops on component mount
  useEffect(() => {
    loadLoops();
  }, []);

  const loadLoops = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await loopApiService.getLoops();
      if (response.success && response.data) {
        setLoops(response.data);
      } else {
        // Fallback to mock data if API fails
        setLoops(getMockLoops());
        setError('Using mock data - API not available');
      }
    } catch (err) {
      console.error('Failed to load loops:', err);
      setLoops(getMockLoops());
      setError('Using mock data - API not available');
    } finally {
      setIsLoading(false);
    }
  };

  const getMockLoops = (): JobLoop[] => [
    {
      id: '1',
      name: 'Senior Frontend Developer',
      description: 'Looking for senior frontend positions with React and TypeScript',
      status: 'active',
      configuration: {},
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      lastRunAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      jobTitles: ['Senior Frontend Developer', 'React Developer', 'Frontend Engineer'],
      locations: ['San Francisco, CA', 'Remote', 'New York, NY'],
      industries: ['Technology', 'Fintech'],
      maxApplicationsPerDay: 5
    },
    {
      id: '2',
      name: 'Full Stack Engineer',
      description: 'Full stack positions with modern tech stack',
      status: 'paused',
      configuration: {},
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      jobTitles: ['Full Stack Engineer', 'Full Stack Developer'],
      locations: ['Remote', 'Seattle, WA'],
      industries: ['Technology'],
      maxApplicationsPerDay: 3
    }
  ];

  const createLoop = async (loopData: any) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await loopApiService.createLoop(loopData);
      if (response.success && response.data) {
        await loadLoops(); // Reload loops to get the updated list
        setViewMode('dashboard');
      } else {
        throw new Error(response.error || 'Failed to create loop');
      }
    } catch (err) {
      console.error('Failed to create loop:', err);
      setError(err instanceof Error ? err.message : 'Failed to create loop');
    } finally {
      setIsLoading(false);
    }
  };

  const updateLoop = async (loopId: string, updateData: any) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await loopApiService.updateLoop(loopId, updateData);
      if (response.success) {
        await loadLoops(); // Reload loops to get the updated list
      } else {
        throw new Error(response.error || 'Failed to update loop');
      }
    } catch (err) {
      console.error('Failed to update loop:', err);
      setError(err instanceof Error ? err.message : 'Failed to update loop');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteLoop = async (loopId: string) => {
    if (!confirm('Are you sure you want to delete this loop?')) {
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const response = await loopApiService.deleteLoop(loopId);
      if (response.success) {
        await loadLoops(); // Reload loops to get the updated list
      } else {
        throw new Error(response.error || 'Failed to delete loop');
      }
    } catch (err) {
      console.error('Failed to delete loop:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete loop');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLoop = async (loopId: string, action: 'pause' | 'resume') => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await loopApiService.toggleLoop(loopId, action);
      if (response.success) {
        await loadLoops(); // Reload loops to get the updated list
      } else {
        throw new Error(response.error || `Failed to ${action} loop`);
      }
    } catch (err) {
      console.error(`Failed to ${action} loop:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${action} loop`);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (settings: any) => {
    if (!selectedLoop) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Update the loop configuration
      const response = await loopApiService.updateLoop(selectedLoop.id, {
        configuration: settings
      });
      
      if (response.success) {
        await loadLoops(); // Reload loops to get the updated list
        setViewMode('dashboard');
      } else {
        throw new Error(response.error || 'Failed to save settings');
      }
    } catch (err) {
      console.error('Failed to save settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentView = () => {
    switch (viewMode) {
      case 'create':
        return (
          <LoopCreator
            onCreateLoop={createLoop}
            onCancel={() => setViewMode('dashboard')}
          />
        );
        
      case 'performance':
        return selectedLoop ? (
          <LoopPerformance
            loopId={selectedLoop.id}
            loopName={selectedLoop.name}
            onBack={() => setViewMode('dashboard')}
            isLoading={isLoading}
          />
        ) : null;
        
      case 'settings':
        return selectedLoop ? (
          <LoopSettings
            loopId={selectedLoop.id}
            currentSettings={selectedLoop.configuration}
            onSave={saveSettings}
            onCancel={() => setViewMode('dashboard')}
            isLoading={isLoading}
          />
        ) : null;
        
      case 'dashboard':
      default:
        return (
          <LoopDashboard
            loops={loops}
            onCreateLoop={() => setViewMode('create')}
            onEditLoop={(loop) => {
              setSelectedLoop(loop);
              setViewMode('settings');
            }}
            onDeleteLoop={deleteLoop}
            onToggleLoop={toggleLoop}
            onViewPerformance={(loopId) => {
              const loop = loops.find(l => l.id === loopId);
              if (loop) {
                setSelectedLoop(loop);
                setViewMode('performance');
              }
            }}
            isLoading={isLoading}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-yellow-800 dark:text-yellow-200 text-sm">
              ⚠️ {error}
            </p>
          </div>
        )}
        
        {renderCurrentView()}
      </div>
    </div>
  );
}