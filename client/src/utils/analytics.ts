/**
 * Google Analytics Integration
 * Provides comprehensive analytics tracking for user interactions, page views, and events
 */

// Analytics configuration
interface AnalyticsConfig {
  trackingId: string;
  debug: boolean;
  anonymizeIp: boolean;
  siteSpeedSampleRate: number;
}

// Event tracking interface
interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

// Page view tracking interface
interface PageView {
  page_title?: string;
  page_location?: string;
  page_path?: string;
  content_group1?: string;
  custom_parameters?: Record<string, any>;
}

// User properties interface
interface UserProperties {
  user_id?: string;
  user_type?: 'authenticated' | 'anonymous';
  subscription_tier?: 'free' | 'premium' | 'enterprise';
  registration_date?: string;
  custom_properties?: Record<string, any>;
}

class Analytics {
  private config: AnalyticsConfig;
  private initialized: boolean = false;
  private debug: boolean = false;

  constructor() {
    this.config = {
      trackingId: this.getTrackingId(),
      debug: this.isDebugMode(),
      anonymizeIp: true,
      siteSpeedSampleRate: 10, // 10% sampling
    };

    this.debug = this.config.debug;
    this.initializeAnalytics();
  }

  /**
   * Get Google Analytics tracking ID from environment variables
   */
  private getTrackingId(): string {
    const trackingId = import.meta.env.VITE_GA_TRACKING_ID || 
                      import.meta.env.VITE_GOOGLE_ANALYTICS_ID ||
                      process.env.GOOGLE_ANALYTICS_ID;
    
    if (!trackingId) {
      console.warn('Google Analytics tracking ID not found. Set VITE_GA_TRACKING_ID environment variable.');
      return '';
    }

    return trackingId;
  }

  /**
   * Check if we're in debug mode
   */
  private isDebugMode(): boolean {
    return import.meta.env.MODE === 'development' || 
           import.meta.env.VITE_GA_DEBUG === 'true';
  }

  /**
   * Initialize Google Analytics
   */
  private async initializeAnalytics(): Promise<void> {
    if (!this.config.trackingId) {
      this.logDebug('Analytics not initialized: missing tracking ID');
      return;
    }

    try {
      // Load Google Analytics script
      await this.loadGoogleAnalytics();
      
      // Configure gtag
      this.configureGtag();
      
      this.initialized = true;
      this.logDebug('Google Analytics initialized successfully');
      
      // Track initial page view
      this.trackPageView();
      
    } catch (error) {
      console.error('Failed to initialize Google Analytics:', error);
    }
  }

  /**
   * Load Google Analytics script dynamically
   */
  private loadGoogleAnalytics(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if gtag is already loaded
      if (typeof window.gtag === 'function') {
        resolve();
        return;
      }

      // Create and load the GA script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.trackingId}`;
      
      script.onload = () => {
        // Initialize gtag function
        window.dataLayer = window.dataLayer || [];
        window.gtag = function(...args: any[]) {
          window.dataLayer.push(...args);
        };
        
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error('Failed to load Google Analytics script'));
      };

      document.head.appendChild(script);
    });
  }

  /**
   * Configure gtag with initial settings
   */
  private configureGtag(): void {
    if (!window.gtag) return;

    // Initialize gtag with tracking ID
    window.gtag('js', new Date());
    window.gtag('config', this.config.trackingId, {
      anonymize_ip: this.config.anonymizeIp,
      site_speed_sample_rate: this.config.siteSpeedSampleRate,
      debug_mode: this.config.debug,
      send_page_view: false, // We'll handle page views manually
    });
  }

  /**
   * Track page views
   */
  public trackPageView(pageData?: Partial<PageView>): void {
    if (!this.initialized || !window.gtag) {
      this.logDebug('Analytics not initialized, queuing page view');
      return;
    }

    const defaultPageData: PageView = {
      page_title: document.title,
      page_location: window.location.href,
      page_path: window.location.pathname,
      content_group1: this.getContentGroup(),
    };

    const trackingData = { ...defaultPageData, ...pageData };
    
    window.gtag('event', 'page_view', trackingData);
    
    this.logDebug('Page view tracked:', trackingData);
  }

  /**
   * Track custom events
   */
  public trackEvent(event: AnalyticsEvent): void {
    if (!this.initialized || !window.gtag) {
      this.logDebug('Analytics not initialized, queuing event:', event);
      return;
    }

    const eventData = {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters,
    };

    window.gtag('event', event.action, eventData);
    
    this.logDebug('Event tracked:', event);
  }

  /**
   * Set user properties
   */
  public setUserProperties(properties: UserProperties): void {
    if (!this.initialized || !window.gtag) {
      this.logDebug('Analytics not initialized, queuing user properties');
      return;
    }

    // Set user ID if provided
    if (properties.user_id) {
      window.gtag('config', this.config.trackingId, {
        user_id: properties.user_id,
      });
    }

    // Set custom user properties
    const userProps = {
      user_type: properties.user_type,
      subscription_tier: properties.subscription_tier,
      registration_date: properties.registration_date,
      ...properties.custom_properties,
    };

    window.gtag('set', { user_properties: userProps });
    
    this.logDebug('User properties set:', userProps);
  }

  /**
   * Track conversion events
   */
  public trackConversion(conversionData: {
    transaction_id?: string;
    value?: number;
    currency?: string;
    items?: any[];
  }): void {
    if (!this.initialized || !window.gtag) {
      this.logDebug('Analytics not initialized, queuing conversion');
      return;
    }

    window.gtag('event', 'purchase', {
      transaction_id: conversionData.transaction_id,
      value: conversionData.value || 0,
      currency: conversionData.currency || 'USD',
      items: conversionData.items || [],
    });

    this.logDebug('Conversion tracked:', conversionData);
  }

  /**
   * Track timing events (for performance monitoring)
   */
  public trackTiming(category: string, variable: string, value: number, label?: string): void {
    this.trackEvent({
      action: 'timing_complete',
      category: `timing_${category}`,
      label: label || variable,
      value: Math.round(value),
      custom_parameters: {
        timing_category: category,
        timing_variable: variable,
      },
    });
  }

  /**
   * Track errors and exceptions
   */
  public trackException(error: Error, fatal: boolean = false): void {
    this.trackEvent({
      action: 'exception',
      category: 'javascript_error',
      label: error.message,
      custom_parameters: {
        description: error.message,
        fatal: fatal,
        stack_trace: error.stack?.substring(0, 500), // Limit stack trace length
      },
    });
  }

  /**
   * Determine content group based on current page
   */
  private getContentGroup(): string {
    const path = window.location.pathname;
    
    if (path.includes('/dashboard')) return 'Dashboard';
    if (path.includes('/resume')) return 'Resume Builder';
    if (path.includes('/templates')) return 'Templates';
    if (path.includes('/jobs')) return 'Job Search';
    if (path.includes('/analytics')) return 'Analytics';
    if (path.includes('/settings')) return 'Settings';
    if (path.includes('/auth') || path.includes('/login') || path.includes('/register')) return 'Authentication';
    
    return 'Other';
  }

  /**
   * Debug logging
   */
  private logDebug(message: string, data?: any): void {
    if (this.debug) {
      console.log(`[Analytics] ${message}`, data || '');
    }
  }

  /**
   * Check if analytics is ready
   */
  public isReady(): boolean {
    return this.initialized && !!window.gtag;
  }

  /**
   * Get analytics configuration
   */
  public getConfig(): AnalyticsConfig {
    return { ...this.config };
  }
}

// Create singleton instance
const analytics = new Analytics();

// Predefined tracking functions for common events
export const trackPageView = (pageData?: Partial<PageView>) => {
  analytics.trackPageView(pageData);
};

export const trackEvent = (event: AnalyticsEvent) => {
  analytics.trackEvent(event);
};

export const trackUserLogin = (userId: string, method: string = 'email') => {
  analytics.trackEvent({
    action: 'login',
    category: 'authentication',
    label: method,
    custom_parameters: { login_method: method },
  });
  
  analytics.setUserProperties({
    user_id: userId,
    user_type: 'authenticated',
  });
};

export const trackUserRegistration = (userId: string, method: string = 'email') => {
  analytics.trackEvent({
    action: 'sign_up',
    category: 'authentication',
    label: method,
    custom_parameters: { signup_method: method },
  });
  
  analytics.setUserProperties({
    user_id: userId,
    user_type: 'authenticated',
    registration_date: new Date().toISOString(),
  });
};

export const trackResumeCreated = (templateId?: string) => {
  analytics.trackEvent({
    action: 'resume_created',
    category: 'resume_builder',
    label: templateId,
    custom_parameters: { template_id: templateId },
  });
};

export const trackResumeDownloaded = (format: string, resumeId?: string) => {
  analytics.trackEvent({
    action: 'resume_downloaded',
    category: 'resume_builder',
    label: format,
    custom_parameters: { 
      download_format: format,
      resume_id: resumeId,
    },
  });
};

export const trackJobApplicationSubmitted = (platform: string, jobId?: string) => {
  analytics.trackEvent({
    action: 'job_application_submitted',
    category: 'job_search',
    label: platform,
    custom_parameters: {
      platform: platform,
      job_id: jobId,
    },
  });
};

export const trackAIFeatureUsed = (feature: string, success: boolean = true) => {
  analytics.trackEvent({
    action: 'ai_feature_used',
    category: 'ai_features',
    label: feature,
    custom_parameters: {
      feature_name: feature,
      success: success,
    },
  });
};

export const trackContactFormSubmitted = (success: boolean = true) => {
  analytics.trackEvent({
    action: 'contact_form_submitted',
    category: 'engagement',
    label: success ? 'success' : 'error',
    custom_parameters: { success },
  });
};

export const trackSubscriptionUpgrade = (tier: string, value?: number) => {
  analytics.trackEvent({
    action: 'subscription_upgrade',
    category: 'monetization',
    label: tier,
    value: value,
    custom_parameters: { subscription_tier: tier },
  });
  
  analytics.setUserProperties({
    subscription_tier: tier as any,
  });
};

export const trackPerformance = (metric: string, value: number, category: string = 'performance') => {
  analytics.trackTiming(category, metric, value);
};

export const trackError = (error: Error, fatal: boolean = false) => {
  analytics.trackException(error, fatal);
};

export const setUserProperties = (properties: UserProperties) => {
  analytics.setUserProperties(properties);
};

// Export analytics instance for advanced usage
export default analytics;

// Extend window object for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}