import type { ResumeTemplate, ColorScheme } from '../constants/resumeTemplates';

/**
 * Utility functions for template management and manipulation
 */

export interface TemplateExportOptions {
  format: 'pdf' | 'word' | 'google-docs';
  colorScheme?: string;
  fontSize?: number;
  spacing?: 'compact' | 'normal' | 'spacious';
}

export interface TemplatePreviewData {
  personalInfo: {
    fullName: string;
    title: string;
    email: string;
    phone: string;
    location: string;
    website?: string;
  };
  summary: string;
  experience: Array<{
    position: string;
    company: string;
    duration: string;
    achievements: string[];
  }>;
  education: Array<{
    degree: string;
    school: string;
    year: string;
  }>;
  skills: {
    [category: string]: string[];
  };
  projects?: Array<{
    name: string;
    description: string;
    technologies: string[];
  }>;
}

/**
 * Apply a color scheme to a template
 */
export function applyColorScheme(template: ResumeTemplate, colorScheme: ColorScheme): ResumeTemplate {
  return {
    ...template,
    styles: {
      ...template.styles,
      primaryColor: colorScheme.primaryColor,
      secondaryColor: colorScheme.secondaryColor,
      accentColor: colorScheme.accentColor
    }
  };
}

/**
 * Get template export metadata for different formats
 */
export function getTemplateExportMetadata(template: ResumeTemplate, options: TemplateExportOptions) {
  const baseMetadata = {
    templateId: template.id,
    templateName: template.name,
    format: options.format,
    atsCompatible: template.atsCompatible,
    timestamp: new Date().toISOString()
  };

  switch (options.format) {
    case 'pdf':
      return {
        ...baseMetadata,
        pdfSettings: {
          pageSize: 'A4',
          margins: template.styles.spacing === 'compact' ? '0.5in' : '1in',
          fontSize: options.fontSize || 12,
          fontFamily: template.styles.fontFamily
        }
      };
    
    case 'word':
      return {
        ...baseMetadata,
        wordSettings: {
          version: 'docx',
          compatibility: 'Office 2016+',
          styleMapping: true,
          preserveFormatting: true
        }
      };
    
    case 'google-docs':
      return {
        ...baseMetadata,
        googleDocsSettings: {
          shareSettings: 'private',
          allowComments: true,
          suggestMode: false
        }
      };
    
    default:
      return baseMetadata;
  }
}

/**
 * Generate sample data for template previews
 */
export function generatePreviewData(): TemplatePreviewData {
  return {
    personalInfo: {
      fullName: 'John Alexander Smith',
      title: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'San Francisco, CA',
      website: 'www.johnsmith.dev'
    },
    summary: 'Experienced software engineer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable solutions and driving technical innovation in fast-paced environments.',
    experience: [
      {
        position: 'Senior Software Engineer',
        company: 'TechCorp Inc.',
        duration: '2020 - Present',
        achievements: [
          'Led development of microservices architecture serving 10M+ users daily',
          'Mentored 5 junior developers and established CI/CD best practices',
          'Improved system performance by 45% through optimization initiatives',
          'Implemented automated testing frameworks improving code coverage by 40%'
        ]
      },
      {
        position: 'Software Engineer',
        company: 'StartupXYZ',
        duration: '2018 - 2020',
        achievements: [
          'Developed full-stack web applications using React and Node.js',
          'Collaborated with product team to deliver features ahead of schedule',
          'Reduced deployment time by 60% through Docker containerization',
          'Built RESTful APIs serving 100K+ requests per hour'
        ]
      },
      {
        position: 'Junior Developer',
        company: 'Innovation Labs',
        duration: '2016 - 2018',
        achievements: [
          'Built responsive user interfaces using modern JavaScript frameworks',
          'Participated in agile development processes and code reviews',
          'Contributed to open-source projects and technical documentation',
          'Gained expertise in database design and API development'
        ]
      }
    ],
    education: [
      {
        degree: 'Master of Science in Computer Science',
        school: 'University of Technology',
        year: '2018'
      },
      {
        degree: 'Bachelor of Science in Software Engineering',
        school: 'State University',
        year: '2016'
      }
    ],
    skills: {
      'Programming Languages': ['JavaScript', 'Python', 'TypeScript', 'Java', 'Go'],
      'Frontend Technologies': ['React', 'Vue.js', 'Angular', 'HTML5', 'CSS3'],
      'Backend Technologies': ['Node.js', 'Express', 'Django', 'Spring Boot'],
      'Database Systems': ['PostgreSQL', 'MongoDB', 'Redis', 'MySQL'],
      'Cloud & DevOps': ['AWS', 'Docker', 'Kubernetes', 'Jenkins', 'Terraform']
    },
    projects: [
      {
        name: 'E-commerce Platform',
        description: 'Full-stack e-commerce solution with real-time inventory management, payment processing, and advanced analytics dashboard',
        technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'AWS']
      },
      {
        name: 'Microservices Architecture',
        description: 'Scalable microservices system with service discovery, load balancing, and distributed tracing capabilities',
        technologies: ['Go', 'Docker', 'Kubernetes', 'Consul', 'Prometheus']
      },
      {
        name: 'Real-time Chat Application',
        description: 'WebSocket-based chat application with file sharing, user presence, and message encryption features',
        technologies: ['Vue.js', 'Socket.io', 'Node.js', 'MongoDB']
      }
    ]
  };
}

/**
 * Validate template structure and requirements
 */
export function validateTemplate(template: ResumeTemplate): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields validation
  if (!template.id) errors.push('Template ID is required');
  if (!template.name) errors.push('Template name is required');
  if (!template.description) errors.push('Template description is required');
  if (!template.category) errors.push('Template category is required');
  if (!template.industry || template.industry.length === 0) errors.push('At least one industry is required');

  // Styles validation
  if (!template.styles) {
    errors.push('Template styles are required');
  } else {
    if (!template.styles.layout) errors.push('Layout is required');
    if (!template.styles.fontFamily) errors.push('Font family is required');
    if (!template.styles.primaryColor) errors.push('Primary color is required');
  }

  // Sections validation
  if (!template.sections || template.sections.length === 0) {
    errors.push('At least one section is required');
  } else {
    const hasHeader = template.sections.some(section => section.type === 'header');
    if (!hasHeader) errors.push('Header section is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get template compatibility score for ATS systems
 */
export function calculateATSCompatibilityScore(template: ResumeTemplate): number {
  let score = 0;

  // Layout scoring
  if (template.styles.layout === 'single-column') score += 30;
  else if (template.styles.layout === 'two-column') score += 20;
  else score += 10;

  // Font scoring
  const atsFriendlyFonts = ['Arial', 'Calibri', 'Times New Roman', 'Helvetica', 'Georgia'];
  const fontFamily = template.styles.fontFamily.toLowerCase();
  if (atsFriendlyFonts.some(font => fontFamily.includes(font.toLowerCase()))) {
    score += 25;
  } else {
    score += 10;
  }

  // Color scheme scoring
  const isConservativeColor = template.styles.primaryColor === '#000000' || 
                             template.styles.primaryColor.startsWith('#1') ||
                             template.styles.primaryColor.startsWith('#2');
  if (isConservativeColor) score += 20;
  else score += 15;

  // Section structure scoring
  const requiredSections = ['header', 'experience', 'education'] as const;
  const presentSections = template.sections.map(s => s.type);
  const hasAllRequired = requiredSections.every(req => presentSections.includes(req as any));
  if (hasAllRequired) score += 15;
  else score += 5;

  // Features scoring
  if (template.features.includes('ATS-Optimized')) score += 10;

  return Math.min(score, 100);
}

/**
 * Get recommended templates based on user criteria
 */
export function getRecommendedTemplates(
  templates: ResumeTemplate[],
  criteria: {
    industry?: string;
    experienceLevel?: string;
    preferATS?: boolean;
    isPremium?: boolean;
  }
): ResumeTemplate[] {
  let filtered = templates;

  // Filter by industry
  if (criteria.industry && criteria.industry !== 'all') {
    filtered = filtered.filter(template => 
      template.industry.some(ind => 
        ind.toLowerCase().includes(criteria.industry!.toLowerCase())
      )
    );
  }

  // Filter by experience level
  if (criteria.experienceLevel && criteria.experienceLevel !== 'all') {
    filtered = filtered.filter(template => 
      template.experienceLevel === criteria.experienceLevel ||
      template.category === criteria.experienceLevel
    );
  }

  // Filter by ATS preference
  if (criteria.preferATS) {
    filtered = filtered.filter(template => template.atsCompatible);
  }

  // Filter by premium preference
  if (criteria.isPremium !== undefined) {
    filtered = filtered.filter(template => template.isPremium === criteria.isPremium);
  }

  // Sort by ATS compatibility score
  return filtered.sort((a, b) => {
    const scoreA = calculateATSCompatibilityScore(a);
    const scoreB = calculateATSCompatibilityScore(b);
    return scoreB - scoreA;
  });
}