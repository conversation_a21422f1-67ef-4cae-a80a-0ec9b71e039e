// Template preview utilities
export const generateTemplatePreviewUrl = (templateId: string): string => {
  // Check if a custom preview image exists
  const customPreviewUrl = `/templates/previews/${templateId}.png`;
  
  // For now, return a placeholder URL that will be handled by the component
  return customPreviewUrl;
};

export const getTemplatePlaceholderSvg = (templateName: string, category: string): string => {
  const categoryColors: { [key: string]: { bg: string; text: string } } = {
    professional: { bg: '#2563eb', text: '#ffffff' },
    creative: { bg: '#ec4899', text: '#ffffff' },
    executive: { bg: '#1e293b', text: '#ffffff' },
    technical: { bg: '#059669', text: '#ffffff' },
    classic: { bg: '#374151', text: '#ffffff' },
    modern: { bg: '#7c3aed', text: '#ffffff' },
    default: { bg: '#64748b', text: '#ffffff' }
  };
  
  const colors = categoryColors[category] || categoryColors.default;
  
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="400" fill="${colors.bg}"/>
      <rect x="20" y="20" width="260" height="60" fill="rgba(255,255,255,0.1)" rx="4"/>
      <rect x="30" y="30" width="100" height="8" fill="${colors.text}" opacity="0.8" rx="2"/>
      <rect x="30" y="45" width="150" height="6" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="55" width="120" height="6" fill="${colors.text}" opacity="0.6" rx="2"/>
      
      <rect x="20" y="100" width="260" height="80" fill="rgba(255,255,255,0.1)" rx="4"/>
      <rect x="30" y="110" width="80" height="6" fill="${colors.text}" opacity="0.8" rx="2"/>
      <rect x="30" y="125" width="200" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="135" width="180" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="145" width="160" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="155" width="140" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      
      <rect x="20" y="200" width="260" height="100" fill="rgba(255,255,255,0.1)" rx="4"/>
      <rect x="30" y="210" width="70" height="6" fill="${colors.text}" opacity="0.8" rx="2"/>
      <rect x="30" y="230" width="90" height="5" fill="${colors.text}" opacity="0.7" rx="2"/>
      <rect x="30" y="240" width="180" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="250" width="160" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="265" width="90" height="5" fill="${colors.text}" opacity="0.7" rx="2"/>
      <rect x="30" y="275" width="170" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      <rect x="30" y="285" width="150" height="4" fill="${colors.text}" opacity="0.6" rx="2"/>
      
      <text x="150" y="350" text-anchor="middle" fill="${colors.text}" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
        ${templateName}
      </text>
      <text x="150" y="370" text-anchor="middle" fill="${colors.text}" font-family="Arial, sans-serif" font-size="12" opacity="0.6">
        ${category.charAt(0).toUpperCase() + category.slice(1)} Template
      </text>
    </svg>
  `)}`;
};

export const handleImageError = (event: Event, templateName: string, category: string) => {
  const img = event.target as HTMLImageElement;
  if (img) {
    img.src = getTemplatePlaceholderSvg(templateName, category);
  }
};