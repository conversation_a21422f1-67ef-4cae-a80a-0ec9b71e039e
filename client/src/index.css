@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Design System */
:root {
  /* Typography */
  --font-heading: 'Inter', system-ui, sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;
  
  /* Spacing scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 400ms ease;
  
  /* Shadows */
  --shadow-elevation-1: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-elevation-2: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-elevation-3: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-elevation-4: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-elevation-5: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Dark mode custom properties */
.dark {
  --shadow-elevation-1: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-elevation-2: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-elevation-3: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-elevation-4: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-elevation-5: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* Enhanced base styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px; /* Base font size for accessibility */
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-size: 16px; /* Ensure minimum readable font size */
  line-height: 1.5; /* Improve readability */
}

body {
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Inter', system-ui, sans-serif;
}

#root {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  font-size: 1rem; /* 16px base */
}

/* Better selection colors */
::selection {
  background-color: rgb(59 130 246 / 0.2);
  color: rgb(37 99 235);
}

/* Component utilities */
.btn-enhanced {
  transition: all 250ms ease;
  border-radius: 0.75rem;
  font-weight: 500;
  box-shadow: var(--shadow-elevation-1);
}

.btn-enhanced:hover {
  box-shadow: var(--shadow-elevation-2);
  transform: translateY(-1px);
}

.btn-enhanced:active {
  transform: translateY(0);
  box-shadow: var(--shadow-elevation-1);
}

.card-enhanced {
  border-radius: 1rem;
  box-shadow: var(--shadow-elevation-1);
  transition: all 300ms ease;
  border: 1px solid rgb(226 232 240);
  background: white;
}

.dark .card-enhanced {
  border-color: rgb(55 65 81);
  background: rgb(31 41 55);
}

.card-enhanced:hover {
  box-shadow: var(--shadow-elevation-3);
  transform: translateY(-2px);
}

.nav-tab-enhanced {
  position: relative;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 200ms ease;
  color: rgb(107 114 128);
}

.nav-tab-enhanced:hover {
  color: rgb(55 65 81);
  background-color: rgb(249 250 251);
}

.dark .nav-tab-enhanced:hover {
  color: rgb(209 213 219);
  background-color: rgb(55 65 81);
}

.nav-tab-enhanced.active {
  color: rgb(37 99 235);
  background-color: rgb(239 246 255);
  box-shadow: var(--shadow-elevation-1);
}

.dark .nav-tab-enhanced.active {
  color: rgb(96 165 250);
  background-color: rgb(30 58 138 / 0.2);
}

.nav-tab-enhanced.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 1.5rem;
  right: 1.5rem;
  height: 2px;
  background-color: rgb(37 99 235);
  border-radius: 1px;
}

.dark .nav-tab-enhanced.active::after {
  background-color: rgb(96 165 250);
}

.loading-enhanced {
  background: linear-gradient(90deg, 
    rgb(248 250 252) 0%, 
    rgb(241 245 249) 50%, 
    rgb(248 250 252) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 0.5rem;
}

.dark .loading-enhanced {
  background: linear-gradient(90deg, 
    rgb(55 65 81) 0%, 
    rgb(75 85 99) 50%, 
    rgb(55 65 81) 100%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes wiggle {
  0%, 7% { transform: rotateZ(0); }
  15% { transform: rotateZ(-15deg); }
  20% { transform: rotateZ(10deg); }
  25% { transform: rotateZ(-10deg); }
  30% { transform: rotateZ(6deg); }
  35% { transform: rotateZ(-4deg); }
  40%, 100% { transform: rotateZ(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}

.animate-wiggle {
  animation: wiggle 0.8s ease-in-out;
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.2s ease;
}

.micro-bounce:hover {
  transform: translateY(-2px) scale(1.02);
}

.micro-bounce:active {
  transform: translateY(0) scale(0.98);
}

.micro-pulse {
  transition: all 0.2s ease;
}

.micro-pulse:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.micro-glow {
  transition: all 0.3s ease;
}

.micro-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.micro-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Enhanced focus styles */
.focus-enhanced:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246), 0 0 0 4px rgb(59 130 246 / 0.2);
  border-radius: 0.5rem;
}

/* Modern glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-heavy {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark .glass-heavy {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient effects */
.gradient-primary {
  background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(107 114 128) 0%, rgb(75 85 99) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, rgb(34 197 94) 0%, rgb(21 128 61) 100%);
}

.gradient-rainbow {
  background: linear-gradient(135deg, 
    rgb(59 130 246) 0%, 
    rgb(147 51 234) 25%, 
    rgb(219 39 119) 50%, 
    rgb(239 68 68) 75%, 
    rgb(245 158 11) 100%);
}

.gradient-text {
  background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(147 51 234) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .gradient-text {
  background: linear-gradient(135deg, rgb(96 165 250) 0%, rgb(168 85 247) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive typography utilities */
.text-fluid-sm {
  font-size: clamp(0.875rem, 0.8rem + 0.25vw, 1rem);
}

.text-fluid-base {
  font-size: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
}

.text-fluid-lg {
  font-size: clamp(1.125rem, 1rem + 0.75vw, 1.5rem);
}

.text-fluid-xl {
  font-size: clamp(1.25rem, 1rem + 1vw, 2rem);
}

/* Advanced accessibility features */
.focus-visible-only {
  outline: none;
}

.focus-visible-only:focus-visible {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
  border-radius: 4px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading-enhanced {
    animation: none;
    background: rgb(248 250 252);
  }
  
  .dark .loading-enhanced {
    background: rgb(55 65 81);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-enhanced {
    border-width: 2px;
    border-color: currentColor;
  }
  
  .btn-enhanced {
    border-width: 2px;
    border-style: solid;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .card-enhanced {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Container queries for component-level responsiveness */
@container (min-width: 768px) {
  .container-responsive {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@container (min-width: 1024px) {
  .container-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile-first touch optimizations */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Touch-friendly button sizes */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

/* Safe area insets for mobile devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Mobile viewport height adjustments */
.min-h-screen-mobile {
  min-height: 100vh;
  min-height: 100svh; /* Small viewport height for mobile browsers */
}

.h-screen-mobile {
  height: 100vh;
  height: 100svh;
}

/* Mobile-specific spacing */
.mobile-spacing {
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-spacing {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-spacing {
    padding: 2rem;
  }
}

/* Enhanced mobile gestures */
.swipe-indicator {
  position: relative;
}

.swipe-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(156, 163, 175, 0.5), transparent);
  border-radius: 2px;
}

/* Mobile card animations */
.mobile-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* CSS Boundary Fixes */
.container-bounded {
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

@media (min-width: 640px) {
  .content-wrapper {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .content-wrapper {
    padding: 0 2rem;
  }
}

/* Prevent horizontal overflow */
.no-overflow {
  overflow-x: hidden;
  max-width: 100%;
}

/* Responsive grid fixes */
.grid-responsive {
  display: grid;
  gap: 1rem;
  width: 100%;
  max-width: 100%;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* Flex container fixes */
.flex-bounded {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.flex-item {
  flex: 1;
  min-width: 0; /* Prevents flex items from overflowing */
  max-width: 100%;
}

/* Text overflow fixes */
.text-bounded {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* Image responsive fixes */
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Table responsive fixes */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 600px;
  width: 100%;
}

/* Modal and overlay fixes */
.modal-bounded {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  z-index: 1000;
}

.modal-content {
  max-width: calc(100vw - 2rem);
  max-height: calc(100vh - 2rem);
  margin: 1rem auto;
  overflow: auto;
}

/* Sidebar and navigation fixes */
.sidebar-bounded {
  width: 100%;
  max-width: 300px;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

@media (max-width: 768px) {
  .sidebar-bounded {
    width: 100vw;
    max-width: 100vw;
  }
}

/* Form element fixes */
.form-bounded {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.form-bounded input,
.form-bounded textarea,
.form-bounded select {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Button group fixes */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  max-width: 100%;
}

.button-group button {
  flex: 1;
  min-width: 0;
  max-width: 100%;
}

@media (max-width: 640px) {
  .button-group {
    flex-direction: column;
  }

  .button-group button {
    width: 100%;
  }
}

/* ===== UI SCALING FIXES ===== */

/* Typography Scale - Accessible and Balanced */
.text-xs { font-size: 0.75rem; line-height: 1rem; } /* 12px */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; } /* 14px */
.text-base { font-size: 1rem; line-height: 1.5rem; } /* 16px - Accessibility minimum */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; } /* 18px */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; } /* 20px */
.text-2xl { font-size: 1.5rem; line-height: 2rem; } /* 24px */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px */

/* Icon Sizing - Proportional to Text */
.icon-xs { width: 0.75rem; height: 0.75rem; } /* 12px - for 12px text */
.icon-sm { width: 1rem; height: 1rem; } /* 16px - for 14px text */
.icon-base { width: 1.25rem; height: 1.25rem; } /* 20px - for 16px text */
.icon-lg { width: 1.5rem; height: 1.5rem; } /* 24px - for 18px text */
.icon-xl { width: 1.75rem; height: 1.75rem; } /* 28px - for 20px text */
.icon-2xl { width: 2rem; height: 2rem; } /* 32px - for 24px text */

/* Button Scaling - Balanced Icon and Text */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.btn-xs svg, .btn-xs .icon {
  width: 0.75rem;
  height: 0.75rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-sm svg, .btn-sm .icon {
  width: 1rem;
  height: 1rem;
}

.btn-base {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.btn-base svg, .btn-base .icon {
  width: 1.25rem;
  height: 1.25rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.btn-lg svg, .btn-lg .icon {
  width: 1.5rem;
  height: 1.5rem;
}

/* Navigation Scaling */
.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.nav-item svg, .nav-item .icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.nav-item-sm {
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.nav-item-sm svg, .nav-item-sm .icon {
  width: 1rem;
  height: 1rem;
}

/* Form Element Scaling */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  margin-bottom: 0.5rem;
  color: rgb(55 65 81);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.5rem;
  background-color: white;
}

.form-input-with-icon {
  padding-left: 2.75rem;
}

.form-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: rgb(107 114 128);
}

/* Card Content Scaling */
.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  margin-bottom: 0.5rem;
}

.card-subtitle {
  font-size: 0.875rem;
  color: rgb(107 114 128);
  line-height: 1.25rem;
  margin-bottom: 1rem;
}

.card-content {
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgb(55 65 81);
}

/* Dashboard Scaling */
.dashboard-card {
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: white;
  border: 1px solid rgb(229 231 235);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.dashboard-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.dashboard-card-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: rgb(59 130 246);
}

.dashboard-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
  color: rgb(17 24 39);
}

.dashboard-stat {
  font-size: 2rem;
  font-weight: 700;
  line-height: 2.25rem;
  color: rgb(17 24 39);
}

.dashboard-stat-label {
  font-size: 0.875rem;
  color: rgb(107 114 128);
  line-height: 1.25rem;
}

/* Resume Builder Scaling */
.resume-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid rgb(229 231 235);
}

.resume-toolbar-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.5rem;
  background: white;
  color: rgb(55 65 81);
  cursor: pointer;
  transition: all 0.2s ease;
}

.resume-toolbar-button:hover {
  background: rgb(249 250 251);
  border-color: rgb(156 163 175);
}

.resume-toolbar-button svg, .resume-toolbar-button .icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.resume-toolbar-button-primary {
  background: rgb(59 130 246);
  color: white;
  border-color: rgb(59 130 246);
}

.resume-toolbar-button-primary:hover {
  background: rgb(37 99 235);
  border-color: rgb(37 99 235);
}

.resume-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid rgb(229 231 235);
}

.resume-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
  color: rgb(17 24 39);
}

.resume-section-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.resume-section-control {
  padding: 0.375rem;
  border: none;
  background: transparent;
  color: rgb(107 114 128);
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.resume-section-control:hover {
  background: rgb(243 244 246);
  color: rgb(55 65 81);
}

.resume-section-control svg, .resume-section-control .icon {
  width: 1rem;
  height: 1rem;
}

.resume-content {
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgb(17 24 39);
}

.resume-content h1 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 2.25rem;
  margin-bottom: 0.5rem;
}

.resume-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
  margin-bottom: 0.75rem;
}

.resume-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  margin-bottom: 0.5rem;
}

.resume-content p {
  font-size: 1rem;
  line-height: 1.5rem;
  margin-bottom: 0.75rem;
}

.resume-content ul, .resume-content ol {
  font-size: 1rem;
  line-height: 1.5rem;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.resume-content li {
  margin-bottom: 0.25rem;
}

/* Sidebar Scaling */
.sidebar {
  width: 16rem;
  background: white;
  border-right: 1px solid rgb(229 231 235);
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgb(229 231 235);
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.75rem;
  color: rgb(17 24 39);
}

.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(55 65 81);
  text-decoration: none;
  transition: all 0.2s ease;
}

.sidebar-nav-item:hover {
  background: rgb(249 250 251);
  color: rgb(17 24 39);
}

.sidebar-nav-item.active {
  background: rgb(239 246 255);
  color: rgb(37 99 235);
  border-right: 2px solid rgb(37 99 235);
}

.sidebar-nav-item svg, .sidebar-nav-item .icon {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
}

/* Mobile Responsive Scaling */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .resume-toolbar {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .resume-toolbar-button {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .resume-toolbar-button svg, .resume-toolbar-button .icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .dashboard-card {
    padding: 1rem;
  }

  .dashboard-card-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  .dashboard-card-title {
    font-size: 1rem;
  }
}

/* Utility Classes for Consistent Icon-Text Pairing */
.icon-text-xs {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.icon-text-xs svg, .icon-text-xs .icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.icon-text-sm {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.icon-text-sm svg, .icon-text-sm .icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.icon-text-base {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.icon-text-base svg, .icon-text-base .icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.icon-text-lg {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.icon-text-lg svg, .icon-text-lg .icon {
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
}

.icon-text-xl {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.icon-text-xl svg, .icon-text-xl .icon {
  width: 1.75rem;
  height: 1.75rem;
  flex-shrink: 0;
}

/* Override Tailwind classes for consistent scaling */
.h-4 { height: 1rem !important; }
.w-4 { width: 1rem !important; }
.h-5 { height: 1.25rem !important; }
.w-5 { width: 1.25rem !important; }
.h-6 { height: 1.5rem !important; }
.w-6 { width: 1.5rem !important; }

/* Text size overrides for accessibility */
.text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
.text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
.text-base { font-size: 1rem !important; line-height: 1.5rem !important; }
.text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
.text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
.text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }

/* Button text and icon consistency */
button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

button svg, button .icon {
  width: 1rem;
  height: 1rem;
}

button.btn-lg {
  font-size: 1rem;
  line-height: 1.5rem;
}

button.btn-lg svg, button.btn-lg .icon {
  width: 1.25rem;
  height: 1.25rem;
}

button.btn-sm {
  font-size: 0.75rem;
  line-height: 1rem;
}

button.btn-sm svg, button.btn-sm .icon {
  width: 0.875rem;
  height: 0.875rem;
}

/* Input and form scaling */
input, textarea, select {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

label {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500;
}

/* Link scaling */
a {
  font-size: inherit;
  line-height: inherit;
}

/* Ensure proper scaling on high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Focus states with proper scaling */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
a:focus-visible {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* Force proper Tailwind color application */
.bg-white {
  background-color: rgb(255 255 255) !important;
}

.bg-gray-50 {
  background-color: rgb(249 250 251) !important;
}

.bg-gray-100 {
  background-color: rgb(243 244 246) !important;
}

.bg-blue-600 {
  background-color: rgb(37 99 235) !important;
}

.bg-blue-100 {
  background-color: rgb(219 234 254) !important;
}

.text-blue-600 {
  color: rgb(37 99 235) !important;
}

.text-gray-900 {
  color: rgb(17 24 39) !important;
}

.text-gray-600 {
  color: rgb(75 85 99) !important;
}

.text-white {
  color: rgb(255 255 255) !important;
}

.border-gray-200 {
  border-color: rgb(229 231 235) !important;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
}

/* Ensure gradients work */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

.from-blue-600 {
  --tw-gradient-from: rgb(37 99 235) !important;
  --tw-gradient-to: rgb(37 99 235 / 0) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.to-indigo-700 {
  --tw-gradient-to: rgb(67 56 202) !important;
}

/* Navigation specific fixes */
nav {
  background-color: rgb(255 255 255) !important;
  border-bottom: 1px solid rgb(229 231 235) !important;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06) !important;
}

/* Button fixes */
button {
  cursor: pointer !important;
}

.btn-primary {
  background-color: rgb(37 99 235) !important;
  color: rgb(255 255 255) !important;
  border: none !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.btn-primary:hover {
  background-color: rgb(29 78 216) !important;
  transform: translateY(-1px) !important;
}

/* Card fixes */
.card {
  background-color: rgb(255 255 255) !important;
  border: 1px solid rgb(229 231 235) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06) !important;
}

/* Ensure proper spacing */
.p-4 {
  padding: 1rem !important;
}

.p-6 {
  padding: 1.5rem !important;
}

.p-8 {
  padding: 2rem !important;
}

.mb-4 {
  margin-bottom: 1rem !important;
}

.mb-6 {
  margin-bottom: 1.5rem !important;
}

.mb-8 {
  margin-bottom: 2rem !important;
}

/* Grid fixes */
.grid {
  display: grid !important;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
}

.gap-4 {
  gap: 1rem !important;
}

.gap-6 {
  gap: 1.5rem !important;
}

.gap-8 {
  gap: 2rem !important;
}

/* Flex fixes */
.flex {
  display: flex !important;
}

.items-center {
  align-items: center !important;
}

.justify-between {
  justify-content: space-between !important;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 1rem !important;
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.75rem !important;
}

/* Typography fixes */
.text-3xl {
  font-size: 1.875rem !important;
  line-height: 2.25rem !important;
}

.text-xl {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}

.text-lg {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}

.text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.font-bold {
  font-weight: 700 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-medium {
  font-weight: 500 !important;
}

.mobile-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Improved mobile form inputs */
.mobile-input {
  font-size: 16px; /* Prevents zoom on iOS */
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.mobile-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dark .mobile-input {
  background: #374151;
  border-color: #4b5563;
  color: white;
}

.dark .mobile-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-optimized loading states */
.mobile-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: mobile-spin 1s ease-in-out infinite;
}

@keyframes mobile-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile-specific button states */
.mobile-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.mobile-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.mobile-btn:active::before {
  width: 300px;
  height: 300px;
}

/* PWA specific styles */
.pwa-prompt {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Voice recording animation */
.voice-recording {
  animation: voice-pulse 1.5s ease-in-out infinite;
}

@keyframes voice-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Camera capture overlay */
.camera-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Mobile swipe cards */
.swipe-card {
  touch-action: pan-y;
  user-select: none;
  -webkit-user-select: none;
}

.swipe-card.swiping {
  transition: none;
}

.swipe-card.not-swiping {
  transition: transform 0.3s ease-out;
}

/* Enhanced mobile accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-card,
  .mobile-btn,
  .swipe-card {
    transition: none !important;
  }
  
  .voice-recording {
    animation: none !important;
  }
}

/* High contrast mode for mobile */
@media (prefers-contrast: high) {
  .mobile-input {
    border-width: 2px;
  }
  
  .mobile-btn {
    border: 2px solid currentColor;
  }
}

/* Mobile dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .mobile-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .mobile-input {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }
}
