import { 
  RESUME_TEMPLATES, 
  getTemplateStats, 
  getTemplatesByCategory,
  getAvailableCategories,
  getTemplateById 
} from './constants/resumeTemplates';

// Test the template system
console.log('=== Template System Test ===');

const stats = getTemplateStats();
console.log('\n📊 Template Statistics:');
console.log(`Total Templates: ${stats.total}`);
console.log(`Free: ${stats.free}, Premium: ${stats.premium}`);
console.log(`ATS Optimized: ${stats.atsOptimized}`);

console.log('\n📂 Categories:');
Object.entries(stats.byCategory).forEach(([category, count]) => {
  console.log(`  ${category}: ${count} templates`);
});

console.log('\n🔍 Available Categories:');
console.log(getAvailableCategories());

console.log('\n🧪 Sample Templates:');
const sampleTemplates = RESUME_TEMPLATES.slice(0, 5);
sampleTemplates.forEach(template => {
  console.log(`  ${template.name} (${template.category}) - Premium: ${template.isPremium}, ATS: ${template.atsCompatible}`);
});

console.log('\n✅ Professional Templates:');
const professionalTemplates = getTemplatesByCategory('professional');
console.log(`Professional templates found: ${professionalTemplates.length}`);
professionalTemplates.slice(0, 3).forEach(template => {
  console.log(`  - ${template.name}: ${template.description}`);
});

console.log('\n🎨 Creative Templates:');
const creativeTemplates = getTemplatesByCategory('creative');
console.log(`Creative templates found: ${creativeTemplates.length}`);
creativeTemplates.slice(0, 3).forEach(template => {
  console.log(`  - ${template.name}: ${template.description}`);
});

console.log('\n📋 Template Lookup Test:');
const testTemplate = getTemplateById('professional-1');
if (testTemplate) {
  console.log(`Found template: ${testTemplate.name}`);
  console.log(`Color schemes: ${testTemplate.colorSchemes?.length || 0}`);
  console.log(`Features: ${testTemplate.features.join(', ')}`);
} else {
  console.log('Template lookup failed');
}

console.log('\n🔧 Template Structure Validation:');
const hasRequiredFields = RESUME_TEMPLATES.every(template => 
  template.id && 
  template.name && 
  template.category && 
  template.industry && 
  template.styles &&
  template.sections
);
console.log(`All templates have required fields: ${hasRequiredFields}`);

const hasColorSchemes = RESUME_TEMPLATES.filter(template => 
  template.colorSchemes && template.colorSchemes.length > 0
).length;
console.log(`Templates with color schemes: ${hasColorSchemes}/${RESUME_TEMPLATES.length}`);

console.log('\n✨ Test Complete - Template System is working!');