import { useEffect, useRef } from 'react';

interface TouchGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onPinch?: (scale: number) => void;
  threshold?: number;
  velocityThreshold?: number;
}

export function useTouchGestures(options: TouchGestureOptions) {
  const elementRef = useRef<HTMLDivElement>(null);
  const startTouchRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const lastTouchesRef = useRef<TouchList | null>(null);

  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onPinch,
    threshold = 50,
    velocityThreshold = 0.3
  } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    let initialDistance = 0;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        // Single touch - potential swipe
        const touch = e.touches[0];
        startTouchRef.current = {
          x: touch.clientX,
          y: touch.clientY,
          time: Date.now()
        };
      } else if (e.touches.length === 2 && onPinch) {
        // Two touches - potential pinch
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        initialDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );
      }
      lastTouchesRef.current = e.touches;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2 && onPinch && initialDistance > 0) {
        // Handle pinch gesture
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const currentDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );
        const scale = currentDistance / initialDistance;
        onPinch(scale);
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (!startTouchRef.current || e.touches.length > 0) return;

      const endTouch = e.changedTouches[0];
      const deltaX = endTouch.clientX - startTouchRef.current.x;
      const deltaY = endTouch.clientY - startTouchRef.current.y;
      const deltaTime = Date.now() - startTouchRef.current.time;
      
      const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / deltaTime;
      const absX = Math.abs(deltaX);
      const absY = Math.abs(deltaY);

      // Check if gesture meets thresholds
      if (absX > threshold || absY > threshold || velocity > velocityThreshold) {
        if (absX > absY) {
          // Horizontal swipe
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight();
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft();
          }
        } else {
          // Vertical swipe
          if (deltaY > 0 && onSwipeDown) {
            onSwipeDown();
          } else if (deltaY < 0 && onSwipeUp) {
            onSwipeUp();
          }
        }
      }

      startTouchRef.current = null;
      initialDistance = 0;
    };

    // Add event listeners with passive option for better performance
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onPinch, threshold, velocityThreshold]);

  return elementRef;
}