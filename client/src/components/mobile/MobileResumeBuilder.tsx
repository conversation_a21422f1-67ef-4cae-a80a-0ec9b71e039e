import { useState, useEffect } from 'react';
import { Plus, GripVertical, Edit3, Trash2, Save, Share, Download, Eye } from 'lucide-react';
import MobileInput from './MobileInput';
import { useTouchGestures } from './TouchGestures';
import { OfflineStorage } from '../../hooks/usePWA';
import APIService from '../../hooks/useAPI';

interface ResumeSection {
  id: string;
  type: 'summary' | 'experience' | 'education' | 'skills' | 'projects';
  title: string;
  content: any;
  order: number;
}

interface MobileResumeBuilderProps {
  sections: ResumeSection[];
  onSectionUpdate: (sections: ResumeSection[]) => void;
  onSave: () => void;
  autoSave?: boolean;
  offlineMode?: boolean;
  className?: string;
}

export default function MobileResumeBuilder({ 
  sections, 
  onSectionUpdate, 
  onSave,
  autoSave = true,
  offlineMode = false,
  className = ''
}: MobileResumeBuilderProps) {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isOffline, setIsOffline] = useState(offlineMode);
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'offline' | 'error'>('synced');

  // Touch-optimized drag and drop (placeholder for future implementation)
  // const handleSectionReorder = useCallback((fromIndex: number, toIndex: number) => {
  //   const updatedSections = [...sections];
  //   const [moved] = updatedSections.splice(fromIndex, 1);
  //   updatedSections.splice(toIndex, 0, moved);
  //   
  //   // Update order
  //   const reorderedSections = updatedSections.map((section, index) => ({
  //     ...section,
  //     order: index
  //   }));
  //   
  //   onSectionUpdate(reorderedSections);
  // }, [sections, onSectionUpdate]);

  // Load offline data on initialization
  useEffect(() => {
    const loadOfflineData = async () => {
      if (sections.length === 0) {
        const offlineData = await OfflineStorage.getData('mobile-resume-draft');
        if (offlineData) {
          onSectionUpdate(offlineData);
        }
      }
    };

    loadOfflineData();
  }, []);

  // Offline detection
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      setSyncStatus('syncing');
      // Sync pending changes when coming back online
      setTimeout(() => setSyncStatus('synced'), 1000);
    };

    const handleOffline = () => {
      setIsOffline(true);
      setSyncStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && hasUnsavedChanges && !isOffline) {
      const timer = setTimeout(() => {
        handleSave();
      }, 3000); // Auto-save after 3 seconds on mobile

      return () => clearTimeout(timer);
    }
  }, [sections, hasUnsavedChanges, autoSave, isOffline]);

  const handleSave = async () => {
    if (isOffline) {
      // Save to local storage when offline
      await OfflineStorage.storeData('mobile-resume-draft', sections);
      setSyncStatus('offline');
    } else {
      setSyncStatus('syncing');
      try {
        // Try to save to server
        await APIService.updateResume(resumeId, { sections });
        setSyncStatus('synced');
        setHasUnsavedChanges(false);
      } catch (error) {
        // Fall back to offline storage
        await OfflineStorage.storeData('pending-resume-update', sections);
        setSyncStatus('error');
        console.error('Failed to save resume:', error);
      }
    }
    onSave();
  };

  const handleTextCapture = (sectionId: string, text: string) => {
    const updatedSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          content: { ...section.content, text }
        };
      }
      return section;
    });
    
    onSectionUpdate(updatedSections);
    setHasUnsavedChanges(true);
  };

  const addNewSection = () => {
    const newSection: ResumeSection = {
      id: `section-${Date.now()}`,
      type: 'summary',
      title: 'New Section',
      content: { text: '' },
      order: sections.length
    };
    onSectionUpdate([...sections, newSection]);
  };

  const deleteSection = (sectionId: string) => {
    const updatedSections = sections.filter(section => section.id !== sectionId);
    onSectionUpdate(updatedSections);
  };

  const SectionCard = ({ section }: { section: ResumeSection }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    const gestureRef = useTouchGestures({
      onSwipeLeft: () => deleteSection(section.id),
      onSwipeRight: () => setIsExpanded(!isExpanded),
      threshold: 100
    });

    return (
      <div
        ref={gestureRef}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 overflow-hidden transition-all duration-200 ${
          activeSection === section.id ? 'ring-2 ring-primary-500' : ''
        }`}
      >
        {/* Section Header */}
        <div
          className="flex items-center justify-between p-4 cursor-pointer"
          onClick={() => setActiveSection(activeSection === section.id ? null : section.id)}
        >
          <div className="flex items-center gap-3">
            <div className="cursor-move touch-manipulation">
              <GripVertical className="h-5 w-5 text-gray-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {section.title}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                {section.type}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors touch-manipulation"
            >
              <Edit3 className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deleteSection(section.id);
              }}
              className="p-2 text-red-400 hover:text-red-600 transition-colors touch-manipulation"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Section Content */}
        {(isExpanded || activeSection === section.id) && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="space-y-4">
              {/* Content Preview */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {section.content?.text || 'No content added yet'}
                </p>
              </div>

              {/* Mobile Input Controls */}
              <div className="flex items-center justify-between">
                <MobileInput
                  onTextCapture={(text) => handleTextCapture(section.id, text)}
                  onError={(error) => console.error('Input error:', error)}
                  className="flex-1"
                />
                
                <button
                  onClick={() => {
                    // Toggle editing mode for this section
                    setIsEditing(!isEditing);
                  }}
                  className="ml-3 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors touch-manipulation"
                >
                  Edit
                </button>
              </div>

              {/* Quick Edit Form (when in editing mode) */}
              {isEditing && (
                <div className="space-y-3">
                  <textarea
                    value={section.content?.text || ''}
                    onChange={(e) => handleTextCapture(section.id, e.target.value)}
                    placeholder="Enter content here..."
                    className="w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors touch-manipulation"
                    >
                      Done
                    </button>
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors touch-manipulation"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`max-w-2xl mx-auto p-4 ${className}`}>
      {/* Enhanced Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Mobile Resume Builder
          </h1>
          {/* Sync Status Indicator */}
          <div className="flex items-center space-x-2 mt-1">
            {syncStatus === 'synced' && (
              <div className="flex items-center space-x-1 text-green-600 text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>All changes saved</span>
              </div>
            )}
            {syncStatus === 'syncing' && (
              <div className="flex items-center space-x-1 text-blue-600 text-xs">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Syncing...</span>
              </div>
            )}
            {syncStatus === 'offline' && (
              <div className="flex items-center space-x-1 text-yellow-600 text-xs">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>Working offline</span>
              </div>
            )}
            {hasUnsavedChanges && (
              <span className="text-xs text-gray-500">• Unsaved changes</span>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Action Buttons */}
          <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors touch-manipulation">
            <Eye className="h-5 w-5" />
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors touch-manipulation">
            <Share className="h-5 w-5" />
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors touch-manipulation">
            <Download className="h-5 w-5" />
          </button>
          <button
            onClick={handleSave}
            disabled={syncStatus === 'syncing'}
            className={`flex items-center gap-2 px-4 py-2 font-medium rounded-lg transition-colors touch-manipulation ${
              syncStatus === 'syncing' 
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            <Save className="h-4 w-4" />
            {syncStatus === 'syncing' ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
        <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Touch Gestures
        </h3>
        <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Swipe left on a section to delete it</li>
          <li>• Swipe right to expand/collapse</li>
          <li>• Drag sections by the grip handle to reorder</li>
          <li>• Use camera or voice input for quick content addition</li>
        </ul>
      </div>

      {/* Sections */}
      <div className="space-y-4">
        {sections
          .sort((a, b) => a.order - b.order)
          .map((section) => (
            <SectionCard
              key={section.id}
              section={section}
            />
          ))}
      </div>

      {/* Add Section Button */}
      <button
        onClick={addNewSection}
        className="w-full mt-6 flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-primary-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors touch-manipulation"
      >
        <Plus className="h-5 w-5" />
        Add New Section
      </button>
    </div>
  );
}