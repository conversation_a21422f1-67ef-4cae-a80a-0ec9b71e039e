import React from 'react';
import { 
  FileText, 
  Briefcase, 
  TrendingUp, 
  MapPin, 
  Share2, 
  Plus, 
  Download,
  Clock
} from 'lucide-react';
import { useTouchGestures } from './TouchGestures';

interface DashboardStats {
  activeApplications: number;
  responseRate: number;
  interviewsScheduled: number;
  resumesCreated: number;
}

interface QuickAction {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}

interface MobileDashboardProps {
  stats: DashboardStats;
  recentApplications: any[];
  onCreateResume: () => void;
  onViewApplications: () => void;
  onViewAnalytics: () => void;
}

export default function MobileDashboard({
  stats,
  recentApplications,
  onCreateResume,
  onViewApplications,
  onViewAnalytics
}: MobileDashboardProps) {
  // Touch gesture for quick actions
  const gestureRef = useTouchGestures({
    onSwipeLeft: () => {
      // Navigate to next view
    },
    onSwipeRight: () => {
      // Navigate to previous view  
    }
  });

  const quickActions: QuickAction[] = [
    {
      id: 'new-resume',
      title: 'New Resume',
      icon: <Plus className="h-5 w-5" />,
      color: 'bg-blue-500',
      action: onCreateResume
    },
    {
      id: 'applications',
      title: 'Applications',
      icon: <Briefcase className="h-5 w-5" />,
      color: 'bg-green-500',
      action: onViewApplications
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'bg-purple-500',
      action: onViewAnalytics
    },
    {
      id: 'share',
      title: 'Share Resume',
      icon: <Share2 className="h-5 w-5" />,
      color: 'bg-orange-500',
      action: () => {
        if (navigator.share) {
          navigator.share({
            title: 'My Resume - CVleap',
            text: 'Check out my professional resume created with CVleap',
            url: window.location.origin + '/resume/shared'
          });
        }
      }
    }
  ];

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color,
    description,
    trend 
  }: { 
    title: string; 
    value: string | number; 
    icon: React.ReactNode; 
    color: string;
    description: string;
    trend?: number;
  }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${color}`}>
          {icon}
        </div>
        {trend !== undefined && (
          <div className={`text-xs font-medium px-2 py-1 rounded-full ${
            trend >= 0 
              ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
          }`}>
            {trend >= 0 ? '+' : ''}{trend}%
          </div>
        )}
      </div>
      <div className="space-y-1">
        <p className="text-2xl font-bold text-gray-900 dark:text-white">
          {value}
        </p>
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {title}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500">
          {description}
        </p>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto p-4 space-y-6" ref={gestureRef}>
      {/* Welcome Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Welcome back! 👋
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Here's your job search progress
        </p>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              className="flex flex-col items-center gap-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors touch-manipulation"
            >
              <div className={`p-3 rounded-full text-white ${action.color}`}>
                {action.icon}
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {action.title}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <StatCard
          title="Active Applications"
          value={stats.activeApplications}
          icon={<Briefcase className="h-5 w-5 text-white" />}
          color="bg-blue-500"
          description="In progress"
          trend={12}
        />
        <StatCard
          title="Response Rate"
          value={`${stats.responseRate}%`}
          icon={<TrendingUp className="h-5 w-5 text-white" />}
          color="bg-green-500"
          description="This month"
          trend={5}
        />
        <StatCard
          title="Interviews"
          value={stats.interviewsScheduled}
          icon={<Clock className="h-5 w-5 text-white" />}
          color="bg-purple-500"
          description="Scheduled"
        />
        <StatCard
          title="Resumes"
          value={stats.resumesCreated}
          icon={<FileText className="h-5 w-5 text-white" />}
          color="bg-orange-500"
          description="Created"
        />
      </div>

      {/* Recent Applications */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Applications
            </h2>
            <button
              onClick={onViewApplications}
              className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              View All
            </button>
          </div>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {recentApplications.slice(0, 3).map((application, index) => (
            <div key={index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {application.jobTitle || 'Software Engineer'}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {application.company || 'Tech Company'}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <MapPin className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {application.location || 'Remote'}
                    </span>
                  </div>
                </div>
                
                <div className="flex flex-col items-end gap-1">
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                    application.status === 'pending' 
                      ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
                      : application.status === 'interview'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                  }`}>
                    {application.status || 'pending'}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {application.appliedDate || '2 days ago'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Location-based Job Suggestions */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-4 text-white">
        <div className="flex items-center gap-3 mb-3">
          <MapPin className="h-6 w-6" />
          <div>
            <h3 className="font-semibold">Jobs Near You</h3>
            <p className="text-blue-100 text-sm">Based on your location</p>
          </div>
        </div>
        <button
          onClick={() => {
            if (navigator.geolocation) {
              navigator.geolocation.getCurrentPosition((position) => {
                console.log('User location:', position.coords);
                // Implement location-based job search
              });
            }
          }}
          className="w-full mt-2 px-4 py-2 bg-white bg-opacity-20 rounded-lg font-medium hover:bg-opacity-30 transition-colors touch-manipulation"
        >
          Find Local Jobs
        </button>
      </div>

      {/* Share Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
          Share Your Success
        </h3>
        <div className="flex gap-2">
          <button
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: 'CVleap Resume Builder',
                  text: 'I just created an amazing resume with CVleap!',
                  url: window.location.origin
                });
              }
            }}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors touch-manipulation"
          >
            <Share2 className="h-4 w-4" />
            Share App
          </button>
          <button
            onClick={() => {
              // Implement resume sharing
              console.log('Share resume');
            }}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors touch-manipulation"
          >
            <Download className="h-4 w-4" />
            Export Resume
          </button>
        </div>
      </div>
    </div>
  );
}