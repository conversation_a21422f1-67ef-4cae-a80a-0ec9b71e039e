import { useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { MenuIcon, XIcon, BellIcon, UserIcon } from 'lucide-react';
import Button from '../ui/Button';
import P<PERSON>InstallPrompt from './PWAInstallPrompt';
import { useTouchGestures } from './TouchGestures';

interface MobileLayoutProps {
  children: ReactNode;
  user?: { name: string; email: string };
  onLogout?: () => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
  tabs: Array<{ id: string; name: string; icon: string }>;
}

export default function MobileLayout({ 
  children, 
  user, 
  onLogout, 
  activeTab, 
  onTabChange, 
  tabs 
}: MobileLayoutProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [notifications, setNotifications] = useState<string[]>([]);

  // Find current tab index for gesture navigation
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);

  // Touch gesture support for tab navigation
  const gestureRef = useTouchGestures({
    onSwipeLeft: () => {
      // Navigate to next tab
      if (currentTabIndex < tabs.length - 1) {
        onTabChange(tabs[currentTabIndex + 1].id);
      }
    },
    onSwipeRight: () => {
      // Navigate to previous tab
      if (currentTabIndex > 0) {
        onTabChange(tabs[currentTabIndex - 1].id);
      }
    },
    threshold: 75,
    velocityThreshold: 0.5
  });

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Close menu when tab changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [activeTab]);

  // Add notification when going offline/online
  useEffect(() => {
    if (!isOnline) {
      setNotifications(['You are offline. Some features may be limited.']);
    } else {
      setNotifications([]);
    }
  }, [isOnline]);

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900" ref={gestureRef}>
      {/* Mobile Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Menu Button */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 lg:hidden"
                aria-label="Toggle menu"
              >
                {isMenuOpen ? (
                  <XIcon className="h-6 w-6" />
                ) : (
                  <MenuIcon className="h-6 w-6" />
                )}
              </button>
              
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-primary-600">CV</span>
                <span className="text-2xl font-bold text-gray-900 dark:text-white">leap</span>
              </div>
            </div>

            {/* Current Tab Indicator (Mobile) */}
            <div className="flex items-center gap-2 lg:hidden">
              {activeTabData && (
                <>
                  <span className="text-lg">{activeTabData.icon}</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden xs:block">
                    {activeTabData.name}
                  </span>
                </>
              )}
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center gap-2">
              {/* Offline Indicator */}
              {!isOnline && (
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" title="Offline" />
              )}
              
              {/* Notifications */}
              <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <BellIcon className="h-5 w-5" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {notifications.length}
                  </span>
                )}
              </button>

              {/* User Menu */}
              <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <UserIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsMenuOpen(false)} />
          <div className="fixed top-16 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 max-h-[calc(100vh-4rem)] overflow-y-auto">
            <div className="p-4">
              {/* User Info */}
              {user && (
                <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{user.name}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{user.email}</p>
                    </div>
                  </div>
                  <Button
                    onClick={onLogout}
                    variant="outline"
                    size="sm"
                    className="w-full mt-3"
                  >
                    Sign Out
                  </Button>
                </div>
              )}

              {/* Navigation Tabs */}
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => onTabChange(tab.id)}
                    className={`w-full text-left p-3 rounded-lg flex items-center gap-3 transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <span className="text-xl">{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:block lg:w-72 lg:overflow-y-auto lg:bg-white lg:dark:bg-gray-800 lg:shadow-lg lg:border-r lg:border-gray-200 lg:dark:border-gray-700">
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center gap-2 p-6 border-b border-gray-200 dark:border-gray-700">
            <span className="text-2xl font-bold text-primary-600">CV</span>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">leap</span>
          </div>

          {/* User Info */}
          {user && (
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-lg">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">{user.name}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{user.email}</p>
                </div>
              </div>
              <Button
                onClick={onLogout}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Sign Out
              </Button>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`w-full text-left p-3 rounded-lg flex items-center gap-3 transition-colors ${
                  activeTab === tab.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className="text-xl">{tab.icon}</span>
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="lg:pl-72">
        {/* Offline Banner */}
        {!isOnline && (
          <div className="bg-yellow-50 dark:bg-yellow-900 border-b border-yellow-200 dark:border-yellow-700 p-3">
            <div className="flex items-center justify-center gap-2 text-sm text-yellow-800 dark:text-yellow-200">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
              <span>You're offline. Some features may be limited.</span>
            </div>
          </div>
        )}

        {/* Page Content */}
        <main className="flex-1 min-h-screen-mobile">
          <div className="p-4 sm:p-6 lg:p-8">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile Bottom Navigation (Optional - for key actions) */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 lg:hidden z-30">
        <div className="grid grid-cols-4 gap-1 p-2">
          {tabs.slice(0, 4).map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`p-3 rounded-lg flex flex-col items-center gap-1 text-xs transition-colors ${
                activeTab === tab.id
                  ? 'text-primary-600 dark:text-primary-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
              }`}
            >
              <span className="text-lg">{tab.icon}</span>
              <span className="font-medium truncate w-full text-center">{tab.name.split(' ')[0]}</span>
            </button>
          ))}
        </div>
      </div>

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </div>
  );
}