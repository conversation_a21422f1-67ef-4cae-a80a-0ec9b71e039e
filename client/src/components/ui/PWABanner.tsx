import { useState } from 'react';
import { Download, X, Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { usePWA } from '../../hooks/usePWA';

interface PWABannerProps {
  className?: string;
}

export default function PWABanner({ className = '' }: PWABannerProps) {
  const [dismissed, setDismissed] = useState(false);
  const { isInstallable, isOffline, installApp, updateAvailable, reloadForUpdate } = usePWA();

  if (dismissed) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Install App Banner */}
      {isInstallable && (
        <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-100 dark:bg-primary-800 rounded-lg">
                <Download className="h-5 w-5 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-primary-900 dark:text-primary-100">
                  Install CVLeap App
                </h4>
                <p className="text-sm text-primary-700 dark:text-primary-300">
                  Get the full experience with offline access and push notifications
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={installApp}
                className="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors"
              >
                Install
              </button>
              <button
                onClick={() => setDismissed(true)}
                className="p-2 text-primary-500 hover:text-primary-700 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Offline Status Banner */}
      {isOffline && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-800 rounded-lg">
              <WifiOff className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                Working Offline
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Your changes are being saved locally and will sync when you're back online
              </p>
            </div>
            <div className="flex items-center text-yellow-600 dark:text-yellow-400">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse mr-2"></div>
              <span className="text-xs font-medium">Offline Mode</span>
            </div>
          </div>
        </div>
      )}

      {/* Update Available Banner */}
      {updateAvailable && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                <RefreshCw className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Update Available
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  A new version of CVLeap is ready with improved features
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={reloadForUpdate}
                className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                Update Now
              </button>
              <button
                onClick={() => setDismissed(true)}
                className="p-2 text-blue-500 hover:text-blue-700 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Connection Restored Banner */}
      {!isOffline && !isInstallable && !updateAvailable && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4 animate-fade-in">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
              <Wifi className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100">
                Connection Restored
              </h4>
              <p className="text-sm text-green-700 dark:text-green-300">
                You're back online. Syncing your latest changes...
              </p>
            </div>
            <button
              onClick={() => setDismissed(true)}
              className="p-2 text-green-500 hover:text-green-700 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}