import { useState } from 'react';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  if (items.length === 0) return null;

  const handleItemClick = (item: BreadcrumbItem) => {
    if (item.onClick) {
      item.onClick();
    } else if (item.href) {
      window.location.href = item.href;
    }
  };

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          
          return (
            <li key={index} className="flex items-center">
              {index === 0 && (
                <Home className="h-4 w-4 text-gray-400 mr-2" />
              )}
              
              {item.icon && index > 0 && (
                <span className="mr-2">{item.icon}</span>
              )}
              
              {isLast ? (
                <span 
                  className="text-gray-900 dark:text-white font-medium truncate max-w-48"
                  title={item.label}
                >
                  {item.label}
                </span>
              ) : (
                <button
                  onClick={() => handleItemClick(item)}
                  className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors truncate max-w-32"
                  title={item.label}
                >
                  {item.label}
                </button>
              )}
              
              {!isLast && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-1 flex-shrink-0" />
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Hook for managing breadcrumb state
export function useBreadcrumb() {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);

  const addBreadcrumb = (item: BreadcrumbItem) => {
    setBreadcrumbs(prev => [...prev, item]);
  };

  const setBreadcrumbPath = (items: BreadcrumbItem[]) => {
    setBreadcrumbs(items);
  };

  const goBack = () => {
    setBreadcrumbs(prev => prev.slice(0, -1));
  };

  const goToIndex = (index: number) => {
    setBreadcrumbs(prev => prev.slice(0, index + 1));
  };

  const clear = () => {
    setBreadcrumbs([]);
  };

  return {
    breadcrumbs,
    addBreadcrumb,
    setBreadcrumbPath,
    goBack,
    goToIndex,
    clear
  };
}