import { Loader2, Re<PERSON>resh<PERSON><PERSON>, AlertCircle, CheckCircle } from 'lucide-react';

interface LoadingStateProps {
  type?: 'spinner' | 'dots' | 'pulse' | 'progress';
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  className?: string;
}

export function LoadingSpinner({ 
  type = 'spinner', 
  size = 'md', 
  message, 
  className = '' 
}: LoadingStateProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const containerSizeClasses = {
    sm: 'py-2',
    md: 'py-4',
    lg: 'py-8'
  };

  const renderSpinner = () => {
    switch (type) {
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`bg-primary-600 rounded-full animate-pulse ${
                  size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4'
                }`}
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        );
      
      case 'pulse':
        return (
          <div className={`bg-primary-600 rounded-full animate-pulse ${sizeClasses[size]}`} />
        );
      
      case 'progress':
        return (
          <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-primary-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }} />
          </div>
        );
      
      default:
        return <Loader2 className={`animate-spin text-primary-600 ${sizeClasses[size]}`} />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${containerSizeClasses[size]} ${className}`}>
      {renderSpinner()}
      {message && (
        <p className={`text-gray-600 dark:text-gray-400 mt-2 ${
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
        }`}>
          {message}
        </p>
      )}
    </div>
  );
}

interface ProgressIndicatorProps {
  progress: number; // 0-100
  label?: string;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'green' | 'blue' | 'red' | 'yellow';
  className?: string;
}

export function ProgressIndicator({ 
  progress, 
  label, 
  showPercentage = true, 
  size = 'md',
  color = 'primary',
  className = '' 
}: ProgressIndicatorProps) {
  const heightClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colorClasses = {
    primary: 'bg-primary-600',
    green: 'bg-green-600',
    blue: 'bg-blue-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <div className={`w-full ${className}`}>
      {(label || showPercentage) && (
        <div className={`flex justify-between items-center mb-1 ${textSizeClasses[size]}`}>
          {label && <span className="text-gray-700 dark:text-gray-300">{label}</span>}
          {showPercentage && (
            <span className="text-gray-600 dark:text-gray-400 font-medium">
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}
      <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full ${heightClasses[size]}`}>
        <div
          className={`${colorClasses[color]} ${heightClasses[size]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
}

interface StatusIndicatorProps {
  status: 'loading' | 'success' | 'error' | 'warning' | 'info';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function StatusIndicator({ 
  status, 
  message, 
  size = 'md', 
  showIcon = true,
  className = '' 
}: StatusIndicatorProps) {
  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const getStatusConfig = () => {
    switch (status) {
      case 'loading':
        return {
          icon: <RefreshCw className={`animate-spin ${iconSizeClasses[size]}`} />,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-700'
        };
      case 'success':
        return {
          icon: <CheckCircle className={iconSizeClasses[size]} />,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-700'
        };
      case 'error':
        return {
          icon: <AlertCircle className={iconSizeClasses[size]} />,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-700'
        };
      case 'warning':
        return {
          icon: <AlertCircle className={iconSizeClasses[size]} />,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-700'
        };
      case 'info':
        return {
          icon: <AlertCircle className={iconSizeClasses[size]} />,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-700'
        };
      default:
        return {
          icon: <AlertCircle className={iconSizeClasses[size]} />,
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-700'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center space-x-2 p-3 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      {showIcon && (
        <div className={config.color}>
          {config.icon}
        </div>
      )}
      {message && (
        <span className={`${config.color} ${textSizeClasses[size]} font-medium`}>
          {message}
        </span>
      )}
    </div>
  );
}

// Shimmer loading effect for content areas
export function ShimmerPlaceholder({ 
  lines = 3, 
  className = '',
  animate = true 
}: { 
  lines?: number; 
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`h-4 bg-gray-200 dark:bg-gray-700 rounded ${
            animate ? 'animate-pulse' : ''
          }`}
          style={{ 
            width: index === lines - 1 ? '75%' : '100%' 
          }}
        />
      ))}
    </div>
  );
}