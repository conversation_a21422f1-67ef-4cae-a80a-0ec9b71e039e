import React, { useRef } from 'react';

// Experience interface
interface Experience {
  id: string;
  title: string;
  company: string;
  location: string;
  type: 'Full-time' | 'Part-time' | 'Contract' | 'Freelance' | 'Internship';
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  achievements: string[];
  technologies: string[];
  companyUrl?: string;
  companyLogo?: string;
}

// Sample experience data
const experienceData: Experience[] = [
  {
    id: '1',
    title: 'Senior Full Stack Developer',
    company: 'CVleap Technologies',
    location: 'San Francisco, CA',
    type: 'Full-time',
    startDate: '2023-01-15',
    current: true,
    description: 'Leading the development of an AI-powered resume building platform that helps professionals create ATS-optimized resumes and automate their job search process.',
    achievements: [
      'Architected and developed a comprehensive resume builder with 50+ professional templates',
      'Implemented AI-powered resume optimization increasing user success rate by 65%',
      'Built real-time collaboration features supporting 10,000+ concurrent users',
      'Designed and deployed job application automation system processing 100,000+ applications monthly',
      'Led a team of 8 developers following agile methodologies and modern DevOps practices'
    ],
    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Python', 'AI/ML', 'Docker', 'Kubernetes'],
    companyUrl: 'https://cvleap.com',
    companyLogo: '/api/placeholder/80/80'
  },
  {
    id: '2',
    title: 'Lead Frontend Developer',
    company: 'TechFlow Solutions',
    location: 'Austin, TX',
    type: 'Full-time',
    startDate: '2021-03-10',
    endDate: '2022-12-20',
    current: false,
    description: 'Spearheaded frontend development for enterprise SaaS applications, focusing on performance optimization and user experience enhancement.',
    achievements: [
      'Reduced application load time by 70% through advanced optimization techniques',
      'Implemented micro-frontend architecture supporting independent team deployments',
      'Mentored 5 junior developers and established frontend best practices',
      'Built reusable component library adopted across 15+ internal applications',
      'Achieved 98% test coverage using Jest and React Testing Library'
    ],
    technologies: ['React', 'Redux', 'TypeScript', 'Webpack', 'Sass', 'Jest', 'Storybook'],
    companyUrl: 'https://techflow.com',
    companyLogo: '/api/placeholder/80/80'
  },
  {
    id: '3',
    title: 'Full Stack Developer',
    company: 'StartupHub Inc.',
    location: 'Remote',
    type: 'Contract',
    startDate: '2020-06-01',
    endDate: '2021-02-28',
    current: false,
    description: 'Developed MVP for multiple early-stage startups, from concept to production deployment, working directly with founders and stakeholders.',
    achievements: [
      'Built 8 MVPs from scratch, 6 of which successfully raised seed funding',
      'Implemented real-time features using WebSocket and Socket.io',
      'Designed scalable backend architectures handling 50,000+ daily active users',
      'Established CI/CD pipelines reducing deployment time by 80%',
      'Collaborated with design teams to create pixel-perfect responsive interfaces'
    ],
    technologies: ['Vue.js', 'Node.js', 'Express', 'MongoDB', 'Socket.io', 'AWS', 'Docker'],
    companyUrl: 'https://startuphub.com',
    companyLogo: '/api/placeholder/80/80'
  },
  {
    id: '4',
    title: 'Software Developer',
    company: 'Digital Innovations Corp',
    location: 'New York, NY',
    type: 'Full-time',
    startDate: '2019-01-15',
    endDate: '2020-05-30',
    current: false,
    description: 'Contributed to large-scale web applications serving millions of users, focusing on backend development and API design.',
    achievements: [
      'Optimized database queries improving response time by 60%',
      'Designed RESTful APIs serving 1M+ requests per day',
      'Implemented caching strategies reducing server load by 45%',
      'Participated in code reviews and maintained 95%+ code quality score',
      'Contributed to open-source projects gaining 500+ GitHub stars'
    ],
    technologies: ['Python', 'Django', 'PostgreSQL', 'Redis', 'Elasticsearch', 'Apache Kafka'],
    companyUrl: 'https://digitalinnovations.com',
    companyLogo: '/api/placeholder/80/80'
  },
  {
    id: '5',
    title: 'Junior Web Developer',
    company: 'WebCraft Agency',
    location: 'Los Angeles, CA',
    type: 'Full-time',
    startDate: '2017-09-01',
    endDate: '2018-12-31',
    current: false,
    description: 'Started my professional journey building websites for small businesses and learning modern web development practices.',
    achievements: [
      'Delivered 25+ responsive websites for diverse client industries',
      'Learned modern JavaScript frameworks and best practices',
      'Improved website performance scores by average of 40%',
      'Collaborated with designers to implement creative solutions',
      'Provided ongoing maintenance and support for client websites'
    ],
    technologies: ['HTML', 'CSS', 'JavaScript', 'jQuery', 'WordPress', 'PHP', 'MySQL'],
    companyUrl: 'https://webcraftagency.com',
    companyLogo: '/api/placeholder/80/80'
  }
];

interface ExperienceSectionProps {
  className?: string;
}

const ExperienceSection: React.FC<ExperienceSectionProps> = ({ className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    });
  };

  const calculateDuration = (startDate: string, endDate?: string) => {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return months > 0 ? `${years}y ${months}m` : `${years}y`;
    }
    return months > 0 ? `${months}m` : '< 1m';
  };

  return (
    <div ref={containerRef} className={`${className}`}>
      {/* Header */}
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Professional Experience
        </h2>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          A journey through innovative projects, cutting-edge technologies, and impactful solutions 
          that have shaped my career in software development.
        </p>
      </div>

      {/* Experience Timeline */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute left-8 md:left-1/2 transform md:-translate-x-px w-0.5 bg-gradient-to-b from-primary-500 to-purple-600 h-full"></div>

        {/* Experience Items */}
        <div className="space-y-12">
          {experienceData.map((experience, index) => {
            const isEven = index % 2 === 0;
            
            return (
              <ExperienceCard
                key={experience.id}
                experience={experience}
                isEven={isEven}
                formatDate={formatDate}
                calculateDuration={calculateDuration}
              />
            );
          })}
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-16 text-center">
        <div className="bg-gradient-to-r from-primary-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8">
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Let's Build Something Amazing Together
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
            With years of experience in full-stack development and a passion for innovation, 
            I'm ready to tackle your next challenging project.
          </p>
          <button
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200"
          >
            Get in Touch
          </button>
        </div>
      </div>
    </div>
  );
};

// Individual Experience Card Component
interface ExperienceCardProps {
  experience: Experience;
  isEven: boolean;
  formatDate: (date: string) => string;
  calculateDuration: (startDate: string, endDate?: string) => string;
}

const ExperienceCard: React.FC<ExperienceCardProps> = ({
  experience,
  isEven,
  formatDate,
  calculateDuration
}) => {
  const cardRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={cardRef}
      className={`relative flex items-center ${
        isEven ? 'md:flex-row' : 'md:flex-row-reverse'
      } flex-col md:gap-8`}
    >
      {/* Timeline Dot */}
      <div 
        className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary-600 border-4 border-white dark:border-gray-900 rounded-full shadow-lg z-10"
      />

      {/* Experience Card */}
      <div 
        className={`w-full md:w-5/12 ml-16 md:ml-0 ${
          isEven ? 'md:mr-8' : 'md:ml-8'
        }`}
      >
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                {experience.companyLogo && (
                  <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-purple-600 rounded"></div>
                  </div>
                )}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {experience.title}
                  </h3>
                  <div className="flex items-center gap-2 text-primary-600 dark:text-primary-400">
                    <span className="font-medium">{experience.company}</span>
                    {experience.companyUrl && (
                      <button
                        onClick={() => window.open(experience.companyUrl, '_blank')}
                        className="text-sm hover:text-primary-700 dark:hover:text-primary-300"
                      >
                        🔗
                      </button>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-3">
                <span className="flex items-center gap-1">
                  📍 {experience.location}
                </span>
                <span className="flex items-center gap-1">
                  💼 {experience.type}
                </span>
                {experience.current && (
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                    Current
                  </span>
                )}
              </div>

              <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                {formatDate(experience.startDate)} - {experience.current ? 'Present' : formatDate(experience.endDate!)} 
                <span className="ml-2 font-medium">
                  ({calculateDuration(experience.startDate, experience.endDate)})
                </span>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
            {experience.description}
          </p>

          {/* Achievements */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Key Achievements:
            </h4>
            <ul className="space-y-1">
              {experience.achievements.slice(0, 3).map((achievement, idx) => (
                <li
                  key={idx}
                  className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-2"
                >
                  <span className="text-primary-500 mt-1 text-xs">▸</span>
                  <span>{achievement}</span>
                </li>
              ))}
              {experience.achievements.length > 3 && (
                <li className="text-sm text-gray-500 dark:text-gray-500 ml-4">
                  +{experience.achievements.length - 3} more achievements
                </li>
              )}
            </ul>
          </div>

          {/* Technologies */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Technologies:
            </h4>
            <div className="flex flex-wrap gap-1">
              {experience.technologies.map((tech) => (
                <span
                  key={tech}
                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExperienceSection;