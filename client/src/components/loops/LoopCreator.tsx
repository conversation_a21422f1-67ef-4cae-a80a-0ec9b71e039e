import React, { useState } from 'react';
import { Plus, MapPin, Briefcase, DollarSign, Settings, Play } from 'lucide-react';

interface LoopConfiguration {
  jobTitles: string[];
  locations: string[];
  industries: string[];
  experienceLevels: string[];
  salaryMin?: number;
  salaryMax?: number;
  remoteOptions: string;
  excludedCompanies: string[];
  keywords: string[];
  scheduleType: string;
  maxApplicationsPerDay: number;
}

interface LoopCreatorProps {
  onCreateLoop: (loopData: any) => void;
  onCancel: () => void;
}

export default function LoopCreator({ onCreateLoop, onCancel }: LoopCreatorProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [configuration, setConfiguration] = useState<LoopConfiguration>({
    jobTitles: [''],
    locations: [''],
    industries: [],
    experienceLevels: [],
    remoteOptions: 'any',
    excludedCompanies: [],
    keywords: [],
    scheduleType: 'continuous',
    maxApplicationsPerDay: 5
  });

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const experienceLevelOptions = [
    'Entry Level',
    'Mid Level',
    'Senior Level',
    'Executive',
    'Internship'
  ];

  const industryOptions = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'Manufacturing',
    'Retail',
    'Marketing',
    'Consulting',
    'Government',
    'Non-profit'
  ];

  const remoteOptions = [
    { value: 'any', label: 'Any' },
    { value: 'remote_only', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'on_site', label: 'On-site Only' }
  ];

  const addArrayItem = (field: keyof LoopConfiguration, value: string) => {
    if (value.trim()) {
      setConfiguration(prev => ({
        ...prev,
        [field]: [...(prev[field] as string[]), value.trim()]
      }));
    }
  };

  const removeArrayItem = (field: keyof LoopConfiguration, index: number) => {
    setConfiguration(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }));
  };

  const updateArrayItem = (field: keyof LoopConfiguration, index: number, value: string) => {
    setConfiguration(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).map((item, i) => i === index ? value : item)
    }));
  };

  const toggleIndustry = (industry: string) => {
    setConfiguration(prev => ({
      ...prev,
      industries: prev.industries.includes(industry)
        ? prev.industries.filter(i => i !== industry)
        : [...prev.industries, industry]
    }));
  };

  const toggleExperienceLevel = (level: string) => {
    setConfiguration(prev => ({
      ...prev,
      experienceLevels: prev.experienceLevels.includes(level)
        ? prev.experienceLevels.filter(l => l !== level)
        : [...prev.experienceLevels, level]
    }));
  };

  const handleSubmit = () => {
    const loopData = {
      name,
      description,
      configuration: {
        ...configuration,
        jobTitles: configuration.jobTitles.filter(title => title.trim()),
        locations: configuration.locations.filter(location => location.trim()),
        excludedCompanies: configuration.excludedCompanies.filter(company => company.trim()),
        keywords: configuration.keywords.filter(keyword => keyword.trim())
      }
    };

    onCreateLoop(loopData);
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return name.trim() && configuration.jobTitles.some(title => title.trim());
      case 2:
        return configuration.locations.some(location => location.trim());
      case 3:
        return true; // Optional step
      case 4:
        return true; // Settings step
      default:
        return false;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Basic Information
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Loop Name *
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Senior Frontend Developer Search"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what type of positions this loop should find..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Job Titles *
              </label>
              <div className="space-y-2">
                {configuration.jobTitles.map((title, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={title}
                      onChange={(e) => updateArrayItem('jobTitles', index, e.target.value)}
                      placeholder="e.g., Frontend Developer, React Developer"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                    {configuration.jobTitles.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeArrayItem('jobTitles', index)}
                        className="px-3 py-2 text-red-600 hover:text-red-700 transition-colors"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addArrayItem('jobTitles', '')}
                  className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Job Title</span>
                </button>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Location Preferences</span>
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Preferred Locations *
              </label>
              <div className="space-y-2">
                {configuration.locations.map((location, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={location}
                      onChange={(e) => updateArrayItem('locations', index, e.target.value)}
                      placeholder="e.g., San Francisco, CA, Remote, New York"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                    {configuration.locations.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeArrayItem('locations', index)}
                        className="px-3 py-2 text-red-600 hover:text-red-700 transition-colors"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addArrayItem('locations', '')}
                  className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Location</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Remote Work Preference
              </label>
              <div className="grid grid-cols-2 gap-3">
                {remoteOptions.map((option) => (
                  <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="remoteOptions"
                      value={option.value}
                      checked={configuration.remoteOptions === option.value}
                      onChange={(e) => setConfiguration(prev => ({ ...prev, remoteOptions: e.target.value }))}
                      className="w-4 h-4 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{option.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
              <Briefcase className="h-5 w-5" />
              <span>Job Preferences</span>
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Industries
              </label>
              <div className="grid grid-cols-2 gap-3">
                {industryOptions.map((industry) => (
                  <label key={industry} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={configuration.industries.includes(industry)}
                      onChange={() => toggleIndustry(industry)}
                      className="w-4 h-4 text-primary-600 focus:ring-primary-500 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{industry}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Experience Levels
              </label>
              <div className="grid grid-cols-2 gap-3">
                {experienceLevelOptions.map((level) => (
                  <label key={level} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={configuration.experienceLevels.includes(level)}
                      onChange={() => toggleExperienceLevel(level)}
                      className="w-4 h-4 text-primary-600 focus:ring-primary-500 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{level}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Minimum Salary
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <input
                    type="number"
                    value={configuration.salaryMin || ''}
                    onChange={(e) => setConfiguration(prev => ({ 
                      ...prev, 
                      salaryMin: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="50000"
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Salary
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <input
                    type="number"
                    value={configuration.salaryMax || ''}
                    onChange={(e) => setConfiguration(prev => ({ 
                      ...prev, 
                      salaryMax: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="150000"
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Loop Settings</span>
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Maximum Applications Per Day
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={configuration.maxApplicationsPerDay}
                onChange={(e) => setConfiguration(prev => ({ 
                  ...prev, 
                  maxApplicationsPerDay: parseInt(e.target.value) || 5 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Recommended: 5-10 applications per day for best results
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Keywords to Include
              </label>
              <div className="space-y-2">
                {configuration.keywords.map((keyword, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={keyword}
                      onChange={(e) => updateArrayItem('keywords', index, e.target.value)}
                      placeholder="e.g., React, TypeScript, AWS"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                    <button
                      type="button"
                      onClick={() => removeArrayItem('keywords', index)}
                      className="px-3 py-2 text-red-600 hover:text-red-700 transition-colors"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addArrayItem('keywords', '')}
                  className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Keyword</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Excluded Companies
              </label>
              <div className="space-y-2">
                {configuration.excludedCompanies.map((company, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={company}
                      onChange={(e) => updateArrayItem('excludedCompanies', index, e.target.value)}
                      placeholder="e.g., Company Name"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                    <button
                      type="button"
                      onClick={() => removeArrayItem('excludedCompanies', index)}
                      className="px-3 py-2 text-red-600 hover:text-red-700 transition-colors"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addArrayItem('excludedCompanies', '')}
                  className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Excluded Company</span>
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Create New Loop
          </h2>
          <span className="text-sm text-gray-500">
            Step {currentStep} of {totalSteps}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      {/* Step Content */}
      <div className="mb-8">
        {renderStep()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <div>
          {currentStep > 1 && (
            <button
              type="button"
              onClick={() => setCurrentStep(prev => prev - 1)}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Previous
            </button>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            Cancel
          </button>

          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={() => setCurrentStep(prev => prev + 1)}
              disabled={!isStepValid()}
              className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              Next
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              disabled={!isStepValid()}
              className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <Play className="h-4 w-4" />
              <span>Create Loop</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}