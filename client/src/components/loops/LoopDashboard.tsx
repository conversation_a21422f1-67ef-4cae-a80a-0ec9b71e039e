import React, { useState, useEffect } from 'react';
import { 
  Plus, Play, Pause, Settings, BarChart3, Target, Clock, 
  MapPin, Briefcase, DollarSign, Search, Filter, Edit,
  Trash2, Eye, TrendingUp
} from 'lucide-react';

interface JobLoop {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed' | 'draft';
  configuration: any;
  createdAt: string;
  updatedAt: string;
  lastRunAt?: string;
  nextRunAt?: string;
  jobTitles: string[];
  locations: string[];
  industries: string[];
  maxApplicationsPerDay: number;
}

interface LoopDashboardProps {
  loops: JobLoop[];
  onCreateLoop: () => void;
  onEditLoop: (loop: JobLoop) => void;
  onDeleteLoop: (loopId: string) => void;
  onToggleLoop: (loopId: string, action: 'pause' | 'resume') => void;
  onViewPerformance: (loopId: string) => void;
  isLoading?: boolean;
}

export default function LoopDashboard({
  loops,
  onCreateLoop,
  onEditLoop,
  onDelete<PERSON>oop,
  onToggleLoop,
  onViewPerformance,
  isLoading = false
}: LoopDashboardProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('updated');

  const filteredLoops = loops.filter(loop => {
    const matchesSearch = loop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         loop.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || loop.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const sortedLoops = [...filteredLoops].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'status':
        return a.status.localeCompare(b.status);
      case 'created':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'updated':
      default:
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    }
  });

  const getStatusIcon = (status: JobLoop['status']) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4 text-green-600" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      case 'completed':
        return <Target className="h-4 w-4 text-blue-600" />;
      case 'draft':
        return <Settings className="h-4 w-4 text-gray-600" />;
      default:
        return <Settings className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: JobLoop['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) {
        return `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
      }
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Job Search Loops
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your automated job search campaigns
          </p>
        </div>
        <button
          onClick={onCreateLoop}
          className="mt-4 sm:mt-0 flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Create Loop</span>
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Loops</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {loops.length}
              </p>
            </div>
            <Target className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Loops</p>
              <p className="text-2xl font-semibold text-green-600">
                {loops.filter(l => l.status === 'active').length}
              </p>
            </div>
            <Play className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Paused Loops</p>
              <p className="text-2xl font-semibold text-yellow-600">
                {loops.filter(l => l.status === 'paused').length}
              </p>
            </div>
            <Pause className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Draft Loops</p>
              <p className="text-2xl font-semibold text-gray-600">
                {loops.filter(l => l.status === 'draft').length}
              </p>
            </div>
            <Settings className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search loops..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="paused">Paused</option>
          <option value="draft">Draft</option>
          <option value="completed">Completed</option>
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="updated">Last Updated</option>
          <option value="created">Date Created</option>
          <option value="name">Name</option>
          <option value="status">Status</option>
        </select>
      </div>

      {/* Loops Grid */}
      {sortedLoops.length === 0 ? (
        <div className="text-center py-12">
          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {loops.length === 0 ? 'No loops created yet' : 'No loops match your filters'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {loops.length === 0 
              ? 'Create your first job search loop to start automating your job applications.'
              : 'Try adjusting your search terms or filters.'
            }
          </p>
          {loops.length === 0 && (
            <button
              onClick={onCreateLoop}
              className="flex items-center space-x-2 mx-auto px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Create Your First Loop</span>
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedLoops.map((loop) => (
            <div
              key={loop.id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow"
            >
              {/* Loop Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {getStatusIcon(loop.status)}
                    <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                      {loop.name}
                    </h3>
                  </div>
                  <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(loop.status)}`}>
                    {loop.status.charAt(0).toUpperCase() + loop.status.slice(1)}
                  </span>
                </div>
                
                <div className="flex items-center space-x-1 ml-2">
                  <button
                    onClick={() => onViewPerformance(loop.id)}
                    className="p-1 text-gray-500 hover:text-primary-600 transition-colors"
                    title="View Performance"
                  >
                    <BarChart3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onEditLoop(loop)}
                    className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                    title="Edit Loop"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDeleteLoop(loop.id)}
                    className="p-1 text-gray-500 hover:text-red-600 transition-colors"
                    title="Delete Loop"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Loop Description */}
              {loop.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                  {loop.description}
                </p>
              )}

              {/* Loop Configuration Preview */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center space-x-2 text-sm">
                  <Briefcase className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {loop.jobTitles.slice(0, 2).join(', ')}
                    {loop.jobTitles.length > 2 && ` +${loop.jobTitles.length - 2} more`}
                  </span>
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {loop.locations.slice(0, 2).join(', ')}
                    {loop.locations.length > 2 && ` +${loop.locations.length - 2} more`}
                  </span>
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <Target className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Max {loop.maxApplicationsPerDay} applications/day
                  </span>
                </div>
              </div>

              {/* Loop Timing */}
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                <div className="flex items-center space-x-1 mb-1">
                  <Clock className="h-3 w-3" />
                  <span>Updated {formatRelativeTime(loop.updatedAt)}</span>
                </div>
                {loop.lastRunAt && (
                  <div className="flex items-center space-x-1">
                    <Play className="h-3 w-3" />
                    <span>Last run {formatRelativeTime(loop.lastRunAt)}</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                {loop.status === 'active' ? (
                  <button
                    onClick={() => onToggleLoop(loop.id, 'pause')}
                    className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors"
                  >
                    <Pause className="h-4 w-4" />
                    <span>Pause</span>
                  </button>
                ) : (
                  <button
                    onClick={() => onToggleLoop(loop.id, 'resume')}
                    className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    <Play className="h-4 w-4" />
                    <span>Start</span>
                  </button>
                )}

                <button
                  onClick={() => onViewPerformance(loop.id)}
                  className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  <TrendingUp className="h-4 w-4" />
                  <span>Analytics</span>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}