import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3, TrendingUp, Target, Clock, MapPin, Briefcase,
  Eye, CheckCircle, XCircle, AlertCircle, Calendar, ArrowLeft,
  Download, Filter, RefreshCw
} from 'lucide-react';

interface PerformanceMetric {
  metricType: string;
  value: number;
  data: any;
  recordedAt: string;
}

interface LoopPerformanceData {
  analytics: PerformanceMetric[];
  summary: {
    totalDiscovered: number;
    totalApplied: number;
  };
}

interface DiscoveredJob {
  id: string;
  loopId: string;
  loopName: string;
  title: string;
  company: string;
  location: string;
  description: string;
  url: string;
  sourcePlatform: string;
  relevanceScore: number;
  matchFactors: any;
  status: string;
  discoveredAt: string;
  appliedAt?: string;
}

interface LoopPerformanceProps {
  loopId: string;
  loopName: string;
  onBack: () => void;
  isLoading?: boolean;
}

export default function LoopPerformance({ 
  loopId, 
  loopName, 
  onBack, 
  isLoading = false 
}: LoopPerformanceProps) {
  const [performanceData, setPerformanceData] = useState<LoopPerformanceData | null>(null);
  const [discoveredJobs, setDiscoveredJobs] = useState<DiscoveredJob[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'jobs' | 'analytics'>('overview');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  // Mock data for demonstration
  useEffect(() => {
    if (!isLoading) {
      setPerformanceData({
        analytics: [
          { metricType: 'jobs_discovered', value: 47, data: {}, recordedAt: new Date().toISOString() },
          { metricType: 'applications_sent', value: 23, data: {}, recordedAt: new Date().toISOString() },
          { metricType: 'response_rate', value: 12.8, data: {}, recordedAt: new Date().toISOString() },
          { metricType: 'interview_rate', value: 8.7, data: {}, recordedAt: new Date().toISOString() },
        ],
        summary: {
          totalDiscovered: 47,
          totalApplied: 23
        }
      });

      setDiscoveredJobs([
        {
          id: '1',
          loopId,
          loopName,
          title: 'Senior Frontend Developer',
          company: 'TechCorp',
          location: 'San Francisco, CA',
          description: 'We are looking for a talented developer...',
          url: 'https://example.com/job/1',
          sourcePlatform: 'LinkedIn',
          relevanceScore: 0.92,
          matchFactors: { titleMatch: 0.95, keywordMatch: 0.88 },
          status: 'applied',
          discoveredAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          appliedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          loopId,
          loopName,
          title: 'React Developer',
          company: 'InnovateSoft',
          location: 'Remote',
          description: 'Join our dynamic team...',
          url: 'https://example.com/job/2',
          sourcePlatform: 'Indeed',
          relevanceScore: 0.87,
          matchFactors: { titleMatch: 0.90, keywordMatch: 0.85 },
          status: 'discovered',
          discoveredAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]);
    }
  }, [isLoading, loopId, loopName]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'applied':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'discovered':
        return <Eye className="h-4 w-4 text-blue-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'applied':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'discovered':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getPlatformIcon = (platform: string) => {
    const platformLower = platform.toLowerCase();
    if (platformLower.includes('linkedin')) return '💼';
    if (platformLower.includes('indeed')) return '🔍';
    if (platformLower.includes('glassdoor')) return '🏢';
    return '🌐';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {loopName} Performance
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Analytics and insights for your job search loop
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>

          <button className="flex items-center space-x-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
            <RefreshCw className="h-4 w-4" />
            <span className="text-sm">Refresh</span>
          </button>

          <button className="flex items-center space-x-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
            <Download className="h-4 w-4" />
            <span className="text-sm">Export</span>
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Jobs Discovered
              </h3>
              <Eye className="h-5 w-5 text-blue-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              {performanceData.summary.totalDiscovered}
            </p>
            <p className="text-xs text-green-600 mt-1">+12 this week</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Applications Sent
              </h3>
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              {performanceData.summary.totalApplied}
            </p>
            <p className="text-xs text-green-600 mt-1">+5 this week</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Response Rate
              </h3>
              <TrendingUp className="h-5 w-5 text-purple-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              12.8%
            </p>
            <p className="text-xs text-green-600 mt-1">+2.1% vs last month</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg. Match Score
              </h3>
              <Target className="h-5 w-5 text-orange-600" />
            </div>
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              87.5%
            </p>
            <p className="text-xs text-gray-500 mt-1">AI relevance score</p>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'jobs', label: 'Discovered Jobs', icon: Briefcase },
            { id: 'analytics', label: 'Detailed Analytics', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Performance Chart Placeholder */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Performance Trend
              </h3>
              <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                  <p>Performance chart would be rendered here</p>
                  <p className="text-sm">Integration with charting library needed</p>
                </div>
              </div>
            </div>

            {/* Platform Performance */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Platform Performance
              </h3>
              <div className="space-y-4">
                {['LinkedIn', 'Indeed', 'Glassdoor'].map((platform) => (
                  <div key={platform} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getPlatformIcon(platform)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{platform}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {Math.floor(Math.random() * 20) + 5} jobs discovered
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {(Math.random() * 30 + 70).toFixed(1)}%
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">match rate</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'jobs' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Discovered Jobs
                </h3>
                <div className="flex items-center space-x-3">
                  <select className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="all">All Status</option>
                    <option value="discovered">Discovered</option>
                    <option value="applied">Applied</option>
                    <option value="rejected">Rejected</option>
                  </select>
                  <button className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <Filter className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {discoveredJobs.map((job) => (
                <div key={job.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {job.title}
                        </h4>
                        <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(job.status)}`}>
                          {job.status}
                        </span>
                        <span className="text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">
                          {getPlatformIcon(job.sourcePlatform)} {job.sourcePlatform}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span className="flex items-center space-x-1">
                          <Briefcase className="h-4 w-4" />
                          <span>{job.company}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{job.location}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Target className="h-4 w-4" />
                          <span>{Math.round(job.relevanceScore * 100)}% match</span>
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                        {job.description}
                      </p>

                      <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>Discovered {formatRelativeTime(job.discoveredAt)}</span>
                        </span>
                        {job.appliedAt && (
                          <span className="flex items-center space-x-1">
                            <CheckCircle className="h-3 w-3" />
                            <span>Applied {formatRelativeTime(job.appliedAt)}</span>
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {getStatusIcon(job.status)}
                      <a
                        href={job.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-500 hover:text-primary-600 transition-colors"
                        title="View Job"
                      >
                        <Eye className="h-4 w-4" />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Detailed Analytics
              </h3>
              <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>Detailed analytics dashboard would be rendered here</p>
                  <p className="text-sm">Advanced metrics and insights</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}