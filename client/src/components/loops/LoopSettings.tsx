import React, { useState } from 'react';
import {
  Settings, Save, X, AlertTriangle, Clock, Target,
  Bell, Mail, Zap, Shield, Database, RefreshCw
} from 'lucide-react';

interface LoopSettingsProps {
  loopId: string;
  currentSettings: any;
  onSave: (settings: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function LoopSettings({
  loopId,
  currentSettings,
  onSave,
  onCancel,
  isLoading = false
}: LoopSettingsProps) {
  const [settings, setSettings] = useState({
    // Automation Settings
    maxApplicationsPerDay: currentSettings?.maxApplicationsPerDay || 5,
    delayBetweenApplications: currentSettings?.delayBetweenApplications || 300, // seconds
    autoApply: currentSettings?.autoApply || false,
    workingHours: {
      enabled: currentSettings?.workingHours?.enabled || false,
      start: currentSettings?.workingHours?.start || '09:00',
      end: currentSettings?.workingHours?.end || '17:00',
      timezone: currentSettings?.workingHours?.timezone || 'UTC'
    },
    
    // Job Discovery Settings
    discoveryFrequency: currentSettings?.discoveryFrequency || 120, // minutes
    minimumMatchScore: currentSettings?.minimumMatchScore || 0.7,
    duplicateDetection: currentSettings?.duplicateDetection || true,
    
    // Notification Settings
    notifications: {
      email: currentSettings?.notifications?.email || true,
      browser: currentSettings?.notifications?.browser || false,
      jobsDiscovered: currentSettings?.notifications?.jobsDiscovered || true,
      applicationsSent: currentSettings?.notifications?.applicationsSent || true,
      dailySummary: currentSettings?.notifications?.dailySummary || true,
      weeklyReport: currentSettings?.notifications?.weeklyReport || false
    },
    
    // Safety Settings
    safetyLimits: {
      maxFailedAttempts: currentSettings?.safetyLimits?.maxFailedAttempts || 3,
      pauseOnErrors: currentSettings?.safetyLimits?.pauseOnErrors || true,
      humanVerification: currentSettings?.safetyLimits?.humanVerification || false
    },
    
    // Data Retention
    dataRetention: {
      keepDiscoveredJobs: currentSettings?.dataRetention?.keepDiscoveredJobs || 90, // days
      keepAnalytics: currentSettings?.dataRetention?.keepAnalytics || 365, // days
      autoCleanup: currentSettings?.dataRetention?.autoCleanup || true
    }
  });

  const [activeSection, setActiveSection] = useState<string>('automation');
  const [hasChanges, setHasChanges] = useState(false);

  const updateSetting = (path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      setHasChanges(true);
      return newSettings;
    });
  };

  const handleSave = () => {
    onSave(settings);
  };

  const sections = [
    {
      id: 'automation',
      label: 'Automation',
      icon: Zap,
      description: 'Configure loop automation behavior'
    },
    {
      id: 'discovery',
      label: 'Job Discovery',
      icon: Target,
      description: 'Customize job search and matching'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Manage alerts and reports'
    },
    {
      id: 'safety',
      label: 'Safety & Limits',
      icon: Shield,
      description: 'Configure safety measures'
    },
    {
      id: 'data',
      label: 'Data Management',
      icon: Database,
      description: 'Data retention and cleanup'
    }
  ];

  const renderAutomationSection = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Maximum Applications Per Day
        </label>
        <input
          type="number"
          min="1"
          max="50"
          value={settings.maxApplicationsPerDay}
          onChange={(e) => updateSetting('maxApplicationsPerDay', parseInt(e.target.value) || 1)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Recommended: 5-10 applications per day to avoid being flagged
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Delay Between Applications (seconds)
        </label>
        <input
          type="number"
          min="60"
          max="3600"
          value={settings.delayBetweenApplications}
          onChange={(e) => updateSetting('delayBetweenApplications', parseInt(e.target.value) || 300)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Wait time between each application to appear more human-like
        </p>
      </div>

      <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
        <div className="flex items-center space-x-3">
          <Zap className="h-5 w-5 text-yellow-600" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Auto-Apply to Matching Jobs
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Automatically apply to jobs that meet your criteria
            </p>
          </div>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.autoApply}
            onChange={(e) => updateSetting('autoApply', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
        </label>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900 dark:text-white">Working Hours</h4>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.workingHours.enabled}
              onChange={(e) => updateSetting('workingHours.enabled', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
          </label>
        </div>

        {settings.workingHours.enabled && (
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start Time
              </label>
              <input
                type="time"
                value={settings.workingHours.start}
                onChange={(e) => updateSetting('workingHours.start', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                End Time
              </label>
              <input
                type="time"
                value={settings.workingHours.end}
                onChange={(e) => updateSetting('workingHours.end', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderDiscoverySection = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Discovery Frequency (minutes)
        </label>
        <select
          value={settings.discoveryFrequency}
          onChange={(e) => updateSetting('discoveryFrequency', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value={60}>Every hour</option>
          <option value={120}>Every 2 hours</option>
          <option value={240}>Every 4 hours</option>
          <option value={480}>Every 8 hours</option>
          <option value={1440}>Daily</option>
        </select>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          How often to search for new jobs
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Minimum Match Score
        </label>
        <div className="space-y-2">
          <input
            type="range"
            min="0.5"
            max="1.0"
            step="0.05"
            value={settings.minimumMatchScore}
            onChange={(e) => updateSetting('minimumMatchScore', parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>50%</span>
            <span className="font-medium">{Math.round(settings.minimumMatchScore * 100)}%</span>
            <span>100%</span>
          </div>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Only consider jobs with at least this match score
        </p>
      </div>

      <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-center space-x-3">
          <RefreshCw className="h-5 w-5 text-blue-600" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Duplicate Detection
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Prevent applying to the same job multiple times
            </p>
          </div>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.duplicateDetection}
            onChange={(e) => updateSetting('duplicateDetection', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderNotificationsSection = () => (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium text-gray-900 dark:text-white mb-4">Notification Methods</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Email Notifications</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.email}
                onChange={(e) => updateSetting('notifications.email', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Browser Notifications</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.browser}
                onChange={(e) => updateSetting('notifications.browser', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-medium text-gray-900 dark:text-white mb-4">Event Notifications</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Jobs Discovered</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.jobsDiscovered}
                onChange={(e) => updateSetting('notifications.jobsDiscovered', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Applications Sent</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.applicationsSent}
                onChange={(e) => updateSetting('notifications.applicationsSent', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Daily Summary</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.dailySummary}
                onChange={(e) => updateSetting('notifications.dailySummary', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Weekly Report</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications.weeklyReport}
                onChange={(e) => updateSetting('notifications.weeklyReport', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSafetySection = () => (
    <div className="space-y-6">
      <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
        <div className="flex items-center space-x-2 mb-2">
          <AlertTriangle className="h-5 w-5 text-red-600" />
          <h4 className="font-medium text-red-900 dark:text-red-200">Safety Measures</h4>
        </div>
        <p className="text-sm text-red-700 dark:text-red-300">
          These settings help protect your account from being flagged by job platforms.
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Max Failed Attempts
        </label>
        <input
          type="number"
          min="1"
          max="10"
          value={settings.safetyLimits.maxFailedAttempts}
          onChange={(e) => updateSetting('safetyLimits.maxFailedAttempts', parseInt(e.target.value) || 3)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Pause loop after this many consecutive failures
        </p>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">Pause on Errors</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Automatically pause when errors are detected
          </p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.safetyLimits.pauseOnErrors}
            onChange={(e) => updateSetting('safetyLimits.pauseOnErrors', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
        </label>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">Human Verification</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Require manual verification for sensitive actions
          </p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.safetyLimits.humanVerification}
            onChange={(e) => updateSetting('safetyLimits.humanVerification', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderDataSection = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Keep Discovered Jobs (days)
        </label>
        <select
          value={settings.dataRetention.keepDiscoveredJobs}
          onChange={(e) => updateSetting('dataRetention.keepDiscoveredJobs', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value={30}>30 days</option>
          <option value={60}>60 days</option>
          <option value={90}>90 days</option>
          <option value={180}>6 months</option>
          <option value={365}>1 year</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Keep Analytics Data (days)
        </label>
        <select
          value={settings.dataRetention.keepAnalytics}
          onChange={(e) => updateSetting('dataRetention.keepAnalytics', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value={90}>90 days</option>
          <option value={180}>6 months</option>
          <option value={365}>1 year</option>
          <option value={730}>2 years</option>
          <option value={-1}>Forever</option>
        </select>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">Auto Cleanup</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Automatically delete old data based on retention settings
          </p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.dataRetention.autoCleanup}
            onChange={(e) => updateSetting('dataRetention.autoCleanup', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'automation':
        return renderAutomationSection();
      case 'discovery':
        return renderDiscoverySection();
      case 'notifications':
        return renderNotificationsSection();
      case 'safety':
        return renderSafetySection();
      case 'data':
        return renderDataSection();
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-gray-50 dark:bg-gray-900 rounded-l-lg border-r border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Loop Settings
              </h2>
            </div>
          </div>
          
          <nav className="p-4 space-y-1">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeSection === section.id
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                <section.icon className="h-4 w-4" />
                <div>
                  <span className="block text-sm font-medium">{section.label}</span>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">
                    {section.description}
                  </span>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {sections.find(s => s.id === activeSection)?.label}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {sections.find(s => s.id === activeSection)?.description}
            </p>
          </div>

          <div className="space-y-6">
            {renderSectionContent()}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div>
              {hasChanges && (
                <p className="text-sm text-amber-600 dark:text-amber-400">
                  You have unsaved changes
                </p>
              )}
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={onCancel}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              >
                Cancel
              </button>
              
              <button
                onClick={handleSave}
                disabled={isLoading || !hasChanges}
                className="flex items-center space-x-2 px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Save className="h-4 w-4" />
                <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}