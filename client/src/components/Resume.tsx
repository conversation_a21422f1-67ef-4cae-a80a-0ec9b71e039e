import { useEffect, useState } from 'react';

interface Experience {
  company: string;
  role: string;
  start: string;
  end: string;
}

interface Resume {
  name: string;
  title: string;
  summary: string;
  skills: string[];
  experience: Experience[];
}

export default function Resume() {
  const [resume, setResume] = useState<Resume | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedResume, setEditedResume] = useState<Resume | null>(null);

  useEffect(() => {
    fetch('/api/resume')
      .then(res => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      })
      .then(data => setResume(data))
      .catch(error => {
        console.error('Failed to fetch resume:', error);
        setResume(null);
      });
  }, []);

  if (!resume) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600">Loading...</span>
      </div>
    );
  }

  const startEdit = () => {
    setEditedResume(JSON.parse(JSON.stringify(resume)));
    setIsEditing(true);
  };

  const cancelEdit = () => {
    setIsEditing(false);
  };

  const saveEdit = () => {
    if (!editedResume) return;
    fetch('/api/resume', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(editedResume)
    })
      .then(res => {
        if (!res.ok) throw new Error('Failed to save');
        return res.json();
      })
      .then(() => {
        setResume(editedResume);
        setIsEditing(false);
      })
      .catch(err => console.error(err));
  };

  const updateExp = (index: number, field: keyof Experience, value: string) => {
    if (!editedResume) return;
    const newExp = [...editedResume.experience];
    newExp[index] = { ...newExp[index], [field]: value };
    setEditedResume({ ...editedResume, experience: newExp });
  };

  const addExperience = () => {
    if (!editedResume) return;
    setEditedResume({
      ...editedResume,
      experience: [...editedResume.experience, { company: '', role: '', start: '', end: '' }]
    });
  };

  const removeExperience = (index: number) => {
    if (!editedResume) return;
    const newExp = editedResume.experience.filter((_, i) => i !== index);
    setEditedResume({ ...editedResume, experience: newExp });
  };

  if (isEditing && editedResume) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Edit Resume</h3>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Name
            </label>
            <input
              value={editedResume.name}
              onChange={e => setEditedResume({ ...editedResume, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title
            </label>
            <input
              value={editedResume.title}
              onChange={e => setEditedResume({ ...editedResume, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Summary
            </label>
            <textarea
              value={editedResume.summary}
              onChange={e => setEditedResume({ ...editedResume, summary: e.target.value })}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Skills (comma separated)
            </label>
            <input
              value={editedResume.skills.join(', ')}
              onChange={e => setEditedResume({ ...editedResume, skills: e.target.value.split(',').map(s => s.trim()).filter(Boolean) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Experience
            </label>
            <div className="space-y-4">
              {editedResume.experience.map((exp, idx) => (
                <div key={idx} className="border border-gray-200 rounded-md p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      placeholder="Company"
                      value={exp.company}
                      onChange={e => updateExp(idx, 'company', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                    <input
                      placeholder="Role"
                      value={exp.role}
                      onChange={e => updateExp(idx, 'role', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                    <input
                      placeholder="Start Date"
                      value={exp.start}
                      onChange={e => updateExp(idx, 'start', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                    <input
                      placeholder="End Date"
                      value={exp.end}
                      onChange={e => updateExp(idx, 'end', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <button
                    onClick={() => removeExperience(idx)}
                    className="mt-2 text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    Remove Experience
                  </button>
                </div>
              ))}
              <button
                onClick={addExperience}
                className="w-full py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Add Experience
              </button>
            </div>
          </div>

          <div className="flex space-x-4">
            <button
              onClick={saveEdit}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium"
            >
              Save
            </button>
            <button
              onClick={cancelEdit}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{resume.name}</h1>
        <h2 className="text-xl text-gray-600">{resume.title}</h2>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Summary</h3>
        <p className="text-gray-700">{resume.summary}</p>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Skills</h3>
        <div className="flex flex-wrap gap-2">
          {resume.skills.map(skill => (
            <span key={skill} className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
              {skill}
            </span>
          ))}
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Experience</h3>
        <div className="space-y-4">
          {resume.experience.map((exp, idx) => (
            <div key={idx} className="border-l-4 border-primary-500 pl-4">
              <h4 className="font-medium text-gray-900">{exp.role}</h4>
              <p className="text-gray-600">{exp.company}</p>
              <p className="text-sm text-gray-500">{exp.start} - {exp.end}</p>
            </div>
          ))}
        </div>
      </div>

      <button
        onClick={startEdit}
        className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium"
      >
        Edit Resume
      </button>
    </div>
  );
}
