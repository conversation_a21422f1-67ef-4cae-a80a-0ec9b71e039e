import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

export default function CultureFitAnalyzer() {
  const [cultureFit, setCultureFit] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resumeData, setResumeData] = useState<any>(null);
  const [companyData, setCompanyData] = useState({
    name: '',
    industry: '',
    size: '',
    description: ''
  });

  const { token } = useSelector((state: RootState) => state.auth);

  const analyzeCultureFit = async () => {
    if (!token || !resumeData || !companyData.name) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/api/automation/analyze-culture-fit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ resumeData, companyData }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze company culture fit');
      }

      const data = await response.json();
      setCultureFit(data.cultureFit);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Mock resume data for demo - in real app, this would come from user's profile
  const loadSampleResume = () => {
    setResumeData({
      name: "John Doe",
      title: "Senior Software Engineer",
      summary: "Experienced software engineer with expertise in full-stack development and team leadership.",
      skills: ["JavaScript", "React", "Node.js", "Python", "Azure", "Team Leadership"],
      experience: [
        {
          role: "Senior Software Engineer",
          company: "Tech Corp",
          start: "2020",
          end: "Present",
          description: "Led development team of 5 engineers, built scalable web applications"
        }
      ]
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">🤝 Company Culture Fit Analysis</h3>

        {/* Resume Data Input */}
        <div className="mb-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">👤 Resume Information</h4>
          {!resumeData ? (
            <div className="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
              <p className="text-gray-600 mb-4">Load your resume data to analyze culture fit</p>
              <button
                onClick={loadSampleResume}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Load Sample Resume
              </button>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="font-medium text-gray-900">{resumeData.name}</h5>
                  <p className="text-sm text-gray-600">{resumeData.title}</p>
                </div>
                <button
                  onClick={() => setResumeData(null)}
                  className="text-red-600 hover:text-red-800"
                >
                  Clear
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Company Data Input */}
        <div className="mb-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">🏢 Company Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                value={companyData.name}
                onChange={(e) => setCompanyData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Google, Microsoft, Startup Inc"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Industry
              </label>
              <input
                type="text"
                value={companyData.industry}
                onChange={(e) => setCompanyData(prev => ({ ...prev, industry: e.target.value }))}
                placeholder="e.g., Technology, Finance, Healthcare"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Size
              </label>
              <select
                value={companyData.size}
                onChange={(e) => setCompanyData(prev => ({ ...prev, size: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select size</option>
                <option value="startup">Startup (1-50)</option>
                <option value="small">Small (51-200)</option>
                <option value="medium">Medium (201-1000)</option>
                <option value="large">Large (1000+)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Description
              </label>
              <textarea
                value={companyData.description}
                onChange={(e) => setCompanyData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of company culture, values, etc."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <button
          onClick={analyzeCultureFit}
          disabled={isAnalyzing || !resumeData || !companyData.name}
          className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isAnalyzing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Analyzing Culture Fit...
            </>
          ) : (
            'Analyze Company Culture Fit'
          )}
        </button>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mt-4">
            {error}
          </div>
        )}

        {/* Culture Fit Results */}
        {cultureFit && (
          <div className="mt-6 space-y-6">
            <h4 className="text-lg font-semibold text-gray-900">🎯 Culture Fit Analysis Results</h4>

            {/* Overall Fit Score */}
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h5 className="text-lg font-medium text-gray-900">Overall Culture Fit Score</h5>
                <span className={`text-3xl font-bold ${getScoreColor(cultureFit.fitScore.overallScore)}`}>
                  {cultureFit.fitScore.overallScore.toFixed(0)}/100
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div 
                  className={`h-4 rounded-full transition-all duration-500 ${getScoreBgColor(cultureFit.fitScore.overallScore)}`}
                  style={{ width: `${cultureFit.fitScore.overallScore}%` }}
                ></div>
              </div>
            </div>

            {/* Detailed Breakdown */}
            <div>
              <h5 className="text-md font-semibold text-gray-900 mb-4">📊 Detailed Breakdown</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(cultureFit.fitScore.breakdown).map(([key, score]: [string, any]) => (
                  <div key={key} className="bg-white border border-gray-200 rounded-lg p-4">
                    <h6 className="text-sm font-medium text-gray-900 mb-2">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </h6>
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-3">
                        <div 
                          className={`h-3 rounded-full ${getScoreBgColor(score * 100)}`}
                          style={{ width: `${score * 100}%` }}
                        ></div>
                      </div>
                      <span className={`text-sm font-medium ${getScoreColor(score * 100)}`}>
                        {(score * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Matching Areas and Concerns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {cultureFit.matchingAreas?.length > 0 && (
                <div>
                  <h5 className="text-md font-semibold text-green-700 mb-3">✅ Matching Areas</h5>
                  <div className="space-y-2">
                    {cultureFit.matchingAreas.map((area: string, index: number) => (
                      <div key={index} className="bg-green-50 border border-green-200 rounded p-3">
                        <p className="text-sm text-green-800">{area}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {cultureFit.potentialConcerns?.length > 0 && (
                <div>
                  <h5 className="text-md font-semibold text-orange-700 mb-3">⚠️ Potential Concerns</h5>
                  <div className="space-y-2">
                    {cultureFit.potentialConcerns.map((concern: string, index: number) => (
                      <div key={index} className="bg-orange-50 border border-orange-200 rounded p-3">
                        <p className="text-sm text-orange-800">{concern}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Recommendations */}
            {cultureFit.recommendations?.length > 0 && (
              <div>
                <h5 className="text-md font-semibold text-blue-700 mb-3">💡 Recommendations</h5>
                <div className="space-y-3">
                  {cultureFit.recommendations.map((rec: any, index: number) => (
                    <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800">{rec}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Items */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h5 className="text-md font-semibold text-gray-900 mb-4">🚀 Next Steps</h5>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-sm text-gray-700">Research the company's recent news and initiatives</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-sm text-gray-700">Connect with current employees on LinkedIn</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-sm text-gray-700">Prepare questions about company culture for interviews</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-sm text-gray-700">Tailor your application to highlight cultural alignment</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}