interface TimingOptimizerProps {
  insights: any;
}

export default function TimingOptimizer({ insights }: TimingOptimizerProps) {
  if (!insights) {
    return (
      <div className="text-center text-gray-500 py-8">
        <div className="text-4xl mb-2">⏰</div>
        <p>No timing data available</p>
        <p className="text-sm text-gray-400">Apply to more jobs to see timing insights</p>
      </div>
    );
  }

  const getDayName = (dayNumber: number) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayNumber] || 'Unknown';
  };

  const getTimeFormatted = (hour: number) => {
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:00 ${ampm}`;
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">⏰ Application Timing Optimization</h3>
        
        {/* Current Performance */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
            <h4 className="text-md font-medium text-gray-900 mb-2">Current Success Rate</h4>
            <div className="text-3xl font-bold text-blue-600">
              {insights.currentSuccessRate?.toFixed(1) || 0}%
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Based on your application history
            </p>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
            <h4 className="text-md font-medium text-gray-900 mb-2">Optimization Potential</h4>
            <div className="text-3xl font-bold text-green-600">
              {insights.optimalTiming?.length > 0 ? `+${(insights.optimalTiming[0].successRate - insights.currentSuccessRate).toFixed(1)}%` : '0%'}
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Potential improvement with optimal timing
            </p>
          </div>
        </div>

        {/* Optimal Timing Recommendations */}
        {insights.optimalTiming?.length > 0 && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">🎯 Optimal Timing Recommendations</h4>
            <div className="space-y-4">
              {insights.optimalTiming.map((timing: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">
                        {timing.type === 'optimal_day' ? '📅' : '🕐'}
                      </span>
                      <h5 className="font-medium text-gray-900">
                        {timing.type === 'optimal_day' ? 'Best Day' : 'Best Time'}
                      </h5>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      timing.confidence === 'high' ? 'bg-green-100 text-green-800' :
                      timing.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {timing.confidence} confidence
                    </span>
                  </div>
                  
                  <div className="mb-2">
                    <span className="text-lg font-semibold text-blue-600">
                      {timing.type === 'optimal_day' ? getDayName(timing.day) : getTimeFormatted(timing.hour)}
                    </span>
                    <span className="text-sm text-gray-500 ml-2">
                      ({timing.successRate.toFixed(1)}% success rate)
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600">
                    {timing.type === 'optimal_day' 
                      ? `Applications submitted on ${getDayName(timing.day)}s have the highest success rate`
                      : `Applications submitted at ${getTimeFormatted(timing.hour)} show the best results`
                    }
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Insights */}
        {insights.insights?.length > 0 && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-4">💡 Timing Insights</h4>
            <div className="space-y-3">
              {insights.insights.map((insight: any, index: number) => (
                <div key={index} className={`rounded-lg p-4 border-l-4 ${
                  insight.impact === 'high' ? 'bg-red-50 border-red-400' :
                  insight.impact === 'medium' ? 'bg-yellow-50 border-yellow-400' :
                  'bg-blue-50 border-blue-400'
                }`}>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm font-medium text-gray-900">
                      {insight.type.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                      insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {insight.impact} impact
                    </span>
                  </div>
                  <p className="text-sm text-gray-700">{insight.message}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Implementation Tips */}
        <div className="mt-6 bg-gray-50 rounded-lg p-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">🚀 Implementation Tips</h4>
          <div className="space-y-2">
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-1">•</span>
              <span className="text-sm text-gray-700">Set up calendar reminders for optimal application times</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-1">•</span>
              <span className="text-sm text-gray-700">Prepare applications in advance to submit at optimal times</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-1">•</span>
              <span className="text-sm text-gray-700">Track your results to validate timing effectiveness</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-1">•</span>
              <span className="text-sm text-gray-700">Consider time zones when applying to remote positions</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}