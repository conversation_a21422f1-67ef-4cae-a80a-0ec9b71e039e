import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import TimingOptimizer from './TimingOptimizer';
import GeographicAnalyzer from './GeographicAnalyzer';
import CultureFitAnalyzer from './CultureFitAnalyzer';
import AntiDetectionPanel from './AntiDetectionPanel';

export default function AutomationDashboard() {
  const [insights, setInsights] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const { token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchApplicationInsights();
  }, [token]);

  const fetchApplicationInsights = async () => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/automation/application-insights', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch application insights');
      }

      const data = await response.json();
      setInsights(data.insights);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'timing', label: 'Timing Optimization', icon: '⏰' },
    { id: 'geographic', label: 'Geographic Analysis', icon: '🌍' },
    { id: 'culture', label: 'Culture Fit', icon: '🤝' },
    { id: 'anti-detection', label: 'Anti-Detection', icon: '🛡️' }
  ];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600 text-center">
          <h3 className="text-lg font-medium mb-2">Error Loading Automation Dashboard</h3>
          <p>{error}</p>
          <button 
            onClick={fetchApplicationInsights}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">🤖 Automation Dashboard</h2>
        <p className="text-gray-600">
          Optimize your job application strategy with AI-powered automation and insights.
        </p>
      </div>

      {/* Overview Cards */}
      {insights && activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">📈</div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Success Rate</h3>
                <p className="text-2xl font-bold text-green-600">
                  {insights.timing?.currentSuccessRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🎯</div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Health Score</h3>
                <p className="text-2xl font-bold text-blue-600">
                  {insights.summary?.overallHealthScore?.toFixed(0) || 0}/100
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🌍</div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Locations</h3>
                <p className="text-2xl font-bold text-purple-600">
                  {insights.geographic?.currentLocationPerformance?.locationCount || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🛡️</div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Risk Level</h3>
                <p className={`text-2xl font-bold ${
                  insights.antiDetection?.riskLevel === 'low' ? 'text-green-600' :
                  insights.antiDetection?.riskLevel === 'medium' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {insights.antiDetection?.riskLevel || 'Unknown'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && insights && (
            <div className="space-y-6">
              {/* Key Insights */}
              {insights.summary?.keyInsights?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">💡 Key Insights</h3>
                  <div className="space-y-3">
                    {insights.summary.keyInsights.map((insight: string, index: number) => (
                      <div key={index} className="bg-blue-50 border-l-4 border-blue-400 p-4">
                        <p className="text-blue-800">{insight}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Priorities */}
              {insights.summary?.actionPriorities?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">🎯 Action Priorities</h3>
                  <div className="space-y-2">
                    {insights.summary.actionPriorities.map((priority: string, index: number) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-gray-700">{priority}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Risk Factors */}
              {insights.summary?.riskFactors?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">⚠️ Risk Factors</h3>
                  <div className="space-y-3">
                    {insights.summary.riskFactors.map((risk: string, index: number) => (
                      <div key={index} className="bg-red-50 border-l-4 border-red-400 p-4">
                        <p className="text-red-800">{risk}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'timing' && (
            <TimingOptimizer insights={insights?.timing} />
          )}

          {activeTab === 'geographic' && (
            <GeographicAnalyzer insights={insights?.geographic} />
          )}

          {activeTab === 'culture' && (
            <CultureFitAnalyzer />
          )}

          {activeTab === 'anti-detection' && (
            <AntiDetectionPanel insights={insights?.antiDetection} />
          )}
        </div>
      </div>
    </div>
  );
}