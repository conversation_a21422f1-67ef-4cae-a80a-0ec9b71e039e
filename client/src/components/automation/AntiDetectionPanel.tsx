interface AntiDetectionPanelProps {
  insights?: any;
}

export default function AntiDetectionPanel({ insights }: AntiDetectionPanelProps) {
  if (!insights) {
    return (
      <div className="text-center text-gray-500 py-8">
        <div className="text-4xl mb-2">🛡️</div>
        <p>No anti-detection data available</p>
        <p className="text-sm text-gray-400">Apply to more jobs to see detection risk analysis</p>
      </div>
    );
  }

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low':
        return 'text-green-600 bg-green-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'high':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskIcon = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low':
        return '🟢';
      case 'medium':
        return '🟡';
      case 'high':
        return '🔴';
      default:
        return '⚪';
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">🛡️ Anti-Detection Analysis</h3>

        {/* Risk Level Overview */}
        <div className="mb-6">
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Current Detection Risk Level</h4>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{getRiskIcon(insights.riskLevel)}</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskLevelColor(insights.riskLevel)}`}>
                  {insights.riskLevel.toUpperCase()} RISK
                </span>
              </div>
            </div>
            <p className="text-gray-600">
              {insights.riskLevel === 'low' && 
                'Your application patterns appear natural and are unlikely to trigger automated detection systems.'}
              {insights.riskLevel === 'medium' && 
                'Some application patterns may need adjustment to reduce detection risk.'}
              {insights.riskLevel === 'high' && 
                'Your application patterns show signs that may trigger detection systems. Immediate action recommended.'}
            </p>
          </div>
        </div>

        {/* Application Pattern Analysis */}
        {insights.applicationPattern && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">📊 Application Pattern Analysis</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {insights.applicationPattern.totalApplications}
                  </div>
                  <div className="text-sm text-gray-600">Total Applications</div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {insights.applicationPattern.averageTimingInterval.toFixed(0)}s
                  </div>
                  <div className="text-sm text-gray-600">Avg. Interval</div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {insights.applicationPattern.dailyFrequency}
                  </div>
                  <div className="text-sm text-gray-600">Daily Frequency</div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {(insights.applicationPattern.consistencyScore * 100).toFixed(0)}%
                  </div>
                  <div className="text-sm text-gray-600">Consistency Score</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Behavior Recommendations */}
        {insights.behaviorRecommendations?.length > 0 && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">🎯 Behavior Optimization Recommendations</h4>
            <div className="space-y-4">
              {insights.behaviorRecommendations.map((rec: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">
                      {rec.type === 'timing_variation' && '⏱️'}
                      {rec.type === 'realistic_delays' && '⏸️'}
                      {rec.type === 'browsing_simulation' && '🌐'}
                      {!['timing_variation', 'realistic_delays', 'browsing_simulation'].includes(rec.type) && '💡'}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900 mb-1">
                        {rec.type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </h5>
                      <p className="text-sm text-gray-700 mb-2">{rec.suggestion}</p>
                      <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                        <strong>Implementation:</strong> {rec.implementation}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Rate Limiting Strategy */}
        {insights.rateLimitingStrategy && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">⚡ Rate Limiting Strategy</h4>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-blue-600">📊</span>
                    <span className="font-medium text-gray-900">Maximum Applications per Day</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {insights.rateLimitingStrategy.maxPerDay}
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-blue-600">⏰</span>
                    <span className="font-medium text-gray-900">Minimum Interval (seconds)</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {insights.rateLimitingStrategy.minInterval}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Best Practices */}
        <div className="mb-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">✅ Anti-Detection Best Practices</h4>
          <div className="bg-green-50 rounded-lg p-6">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="text-green-500 text-lg mt-0.5">✓</span>
                <div>
                  <h5 className="font-medium text-green-800">Vary Your Application Timing</h5>
                  <p className="text-sm text-green-700">
                    Avoid applying at exactly the same times every day. Add random variations of ±30-50%.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-green-500 text-lg mt-0.5">✓</span>
                <div>
                  <h5 className="font-medium text-green-800">Simulate Natural Browsing</h5>
                  <p className="text-sm text-green-700">
                    Visit job boards naturally, view multiple listings, and spend time reading before applying.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-green-500 text-lg mt-0.5">✓</span>
                <div>
                  <h5 className="font-medium text-green-800">Use Multiple Devices/Networks</h5>
                  <p className="text-sm text-green-700">
                    Rotate between different devices and network connections when possible.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-green-500 text-lg mt-0.5">✓</span>
                <div>
                  <h5 className="font-medium text-green-800">Maintain Realistic Application Rates</h5>
                  <p className="text-sm text-green-700">
                    Keep daily application volumes within human-achievable ranges (typically 5-15 per day).
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-green-500 text-lg mt-0.5">✓</span>
                <div>
                  <h5 className="font-medium text-green-800">Personalize Applications</h5>
                  <p className="text-sm text-green-700">
                    Ensure each application has unique elements - cover letters, slight resume variations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Warning Indicators */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4">🚨 Warning Indicators</h4>
          <div className="bg-red-50 rounded-lg p-6">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="text-red-500 text-lg mt-0.5">⚠️</span>
                <div>
                  <h5 className="font-medium text-red-800">Too Consistent Timing</h5>
                  <p className="text-sm text-red-700">
                    Applying at exactly the same time every day or with identical intervals.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-red-500 text-lg mt-0.5">⚠️</span>
                <div>
                  <h5 className="font-medium text-red-800">Superhuman Speed</h5>
                  <p className="text-sm text-red-700">
                    Completing applications faster than humanly possible (less than 2-3 minutes per application).
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-red-500 text-lg mt-0.5">⚠️</span>
                <div>
                  <h5 className="font-medium text-red-800">Identical Content</h5>
                  <p className="text-sm text-red-700">
                    Using exactly the same resume, cover letter, or responses across all applications.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <span className="text-red-500 text-lg mt-0.5">⚠️</span>
                <div>
                  <h5 className="font-medium text-red-800">Excessive Volume</h5>
                  <p className="text-sm text-red-700">
                    Applying to more than 20-30 positions per day consistently.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}