import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface GeographicAnalyzerProps {
  insights?: any;
}

export default function GeographicAnalyzer({ insights }: GeographicAnalyzerProps) {
  const [targetLocations, setTargetLocations] = useState<string[]>([]);
  const [newLocation, setNewLocation] = useState('');
  const [analysis, setAnalysis] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { token } = useSelector((state: RootState) => state.auth);

  const addLocation = () => {
    if (newLocation.trim() && !targetLocations.includes(newLocation.trim())) {
      setTargetLocations([...targetLocations, newLocation.trim()]);
      setNewLocation('');
    }
  };

  const removeLocation = (location: string) => {
    setTargetLocations(targetLocations.filter(loc => loc !== location));
  };

  const analyzeGeographicOpportunities = async () => {
    if (!token) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/api/automation/analyze-geographic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ targetLocations }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze geographic opportunities');
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">🌍 Geographic Opportunity Analysis</h3>

        {/* Current Location Performance */}
        {(insights?.currentLocationPerformance?.locations?.length > 0 || 
          analysis?.currentLocationPerformance?.locations?.length > 0) && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">📍 Current Location Performance</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 bg-white rounded-lg shadow">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Success Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performance
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(analysis?.currentLocationPerformance?.locations || 
                    insights?.currentLocationPerformance?.locations || []).map((location: any) => (
                    <tr key={location.location} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {location.location}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {location.total_applications}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {location.success_rate?.toFixed(1)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          location.success_rate >= 20 ? 'bg-green-100 text-green-800' :
                          location.success_rate >= 10 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {location.success_rate >= 20 ? 'Excellent' :
                           location.success_rate >= 10 ? 'Good' : 'Poor'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Target Location Analysis */}
        <div className="mb-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">🎯 Target Location Analysis</h4>
          
          {/* Add Target Locations */}
          <div className="mb-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={newLocation}
                onChange={(e) => setNewLocation(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addLocation()}
                placeholder="Enter location (e.g., San Francisco, New York, Remote)"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={addLocation}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Add
              </button>
            </div>
          </div>

          {/* Target Locations List */}
          {targetLocations.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {targetLocations.map((location, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {location}
                    <button
                      onClick={() => removeLocation(location)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          <button
            onClick={analyzeGeographicOpportunities}
            disabled={isAnalyzing}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Analyzing Geographic Opportunities...
              </>
            ) : (
              'Analyze Geographic Opportunities'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Analysis Results */}
        {analysis?.targetLocationInsights?.length > 0 && (
          <div className="mb-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">🔍 Market Analysis Results</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analysis.targetLocationInsights.map((market: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
                  <h5 className="font-semibold text-gray-900 mb-2">{market.location}</h5>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Market Health:</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${market.jobMarketHealth * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {(market.jobMarketHealth * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Competition:</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-red-500 h-2 rounded-full"
                            style={{ width: `${market.competitionLevel * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {(market.competitionLevel * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Salary:</span>
                      <span className="text-sm font-medium">
                        ${market.averageSalary?.toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Growth:</span>
                      <span className={`text-sm font-medium ${
                        market.growthTrends === 'positive' ? 'text-green-600' :
                        market.growthTrends === 'negative' ? 'text-red-600' :
                        'text-gray-600'
                      }`}>
                        {market.growthTrends}
                      </span>
                    </div>
                  </div>
                  
                  {market.topIndustries?.length > 0 && (
                    <div className="mt-3">
                      <span className="text-sm text-gray-600">Top Industries:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {market.topIndustries.slice(0, 3).map((industry: string, i: number) => (
                          <span key={i} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {industry}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recommendations */}
        {(analysis?.recommendations?.length > 0 || insights?.recommendations?.length > 0) && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-4">💡 Geographic Recommendations</h4>
            <div className="space-y-3">
              {(analysis?.recommendations || insights?.recommendations || []).map((rec: any, index: number) => (
                <div key={index} className={`rounded-lg p-4 border-l-4 ${
                  rec.priority === 'high' ? 'bg-red-50 border-red-400' :
                  rec.priority === 'medium' ? 'bg-yellow-50 border-yellow-400' :
                  'bg-blue-50 border-blue-400'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900">
                      {rec.type === 'expand_location' ? '📈 Expand Focus' : 
                       rec.type === 'new_target_location' ? '🎯 New Target' : 
                       '💡 Recommendation'}
                    </h5>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                      rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {rec.priority} priority
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">
                    <strong>{rec.location}:</strong> {rec.reason}
                  </p>
                  {rec.marketData && (
                    <div className="text-xs text-gray-600">
                      Market Health: {(rec.marketData.jobMarketHealth * 100).toFixed(0)}% • 
                      Competition: {(rec.marketData.competitionLevel * 100).toFixed(0)}%
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}