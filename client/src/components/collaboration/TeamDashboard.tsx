import React, { useState, useEffect } from 'react';
import { PlusIcon, UserPlusIcon, CogIcon, UserGroupIcon } from '@heroicons/react/24/outline';

interface Organization {
  id: number;
  name: string;
  description: string;
  role: string;
  memberCount?: number;
  sharedResumeCount?: number;
}

interface TeamMember {
  id: number;
  name: string;
  email: string;
  role: string;
  joinedAt: string;
  status: string;
}

export const TeamDashboard: React.FC = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Load organizations
  useEffect(() => {
    fetchOrganizations();
  }, []);

  // Load team members when organization changes
  useEffect(() => {
    if (selectedOrg) {
      fetchOrganizationDetails(selectedOrg.id);
    }
  }, [selectedOrg]);

  const fetchOrganizations = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/teams/organizations', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOrganizations(data.organizations);
        if (data.organizations.length > 0 && !selectedOrg) {
          setSelectedOrg(data.organizations[0]);
        }
      }
    } catch (error) {
      console.error('Failed to fetch organizations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganizationDetails = async (orgId: number) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/teams/organizations/${orgId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTeamMembers(data.members);
        
        // Update organization stats
        setOrganizations(prev => prev.map(org => 
          org.id === orgId 
            ? { ...org, memberCount: data.stats.memberCount, sharedResumeCount: data.stats.sharedResumeCount }
            : org
        ));
      }
    } catch (error) {
      console.error('Failed to fetch organization details:', error);
    }
  };

  const createOrganization = async (name: string, description: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/teams/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name, description })
      });

      if (response.ok) {
        const data = await response.json();
        setOrganizations(prev => [...prev, data.organization]);
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('Failed to create organization:', error);
    }
  };

  const inviteTeamMember = async (email: string, role: string) => {
    if (!selectedOrg) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/teams/organizations/${selectedOrg.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ email, role })
      });

      if (response.ok) {
        const data = await response.json();
        setTeamMembers(prev => [...prev, data.member]);
        setShowInviteModal(false);
      }
    } catch (error) {
      console.error('Failed to invite team member:', error);
    }
  };

  const updateMemberRole = async (memberId: number, newRole: string) => {
    if (!selectedOrg) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/teams/organizations/${selectedOrg.id}/members/${memberId}/role`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ role: newRole })
      });

      if (response.ok) {
        setTeamMembers(prev => prev.map(member => 
          member.id === memberId ? { ...member, role: newRole } : member
        ));
      }
    } catch (error) {
      console.error('Failed to update member role:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Team Workspaces</h1>
          <p className="text-gray-600">Manage your teams and collaborate on resumes</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>New Workspace</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar - Organizations */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Your Workspaces</h2>
            <div className="space-y-2">
              {organizations.map((org) => (
                <button
                  key={org.id}
                  onClick={() => setSelectedOrg(org)}
                  className={`w-full text-left p-3 rounded-md transition-colors ${
                    selectedOrg?.id === org.id
                      ? 'bg-blue-50 border-blue-200 border'
                      : 'hover:bg-gray-50 border border-transparent'
                  }`}
                >
                  <div className="font-medium text-gray-900">{org.name}</div>
                  <div className="text-sm text-gray-500 capitalize">{org.role}</div>
                  {org.memberCount && (
                    <div className="text-xs text-gray-400 mt-1">
                      {org.memberCount} members
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {selectedOrg ? (
            <div className="space-y-6">
              {/* Organization Header */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">{selectedOrg.name}</h2>
                    <p className="text-gray-600 mt-1">{selectedOrg.description}</p>
                    <div className="flex items-center space-x-4 mt-4">
                      <div className="flex items-center space-x-1">
                        <UserGroupIcon className="h-5 w-5 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {selectedOrg.memberCount || 0} members
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CogIcon className="h-5 w-5 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {selectedOrg.sharedResumeCount || 0} shared resumes
                        </span>
                      </div>
                    </div>
                  </div>
                  {selectedOrg.role === 'admin' && (
                    <button
                      onClick={() => setShowInviteModal(true)}
                      className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
                    >
                      <UserPlusIcon className="h-5 w-5" />
                      <span>Invite Member</span>
                    </button>
                  )}
                </div>
              </div>

              {/* Team Members */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Team Members</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {teamMembers.map((member) => (
                    <div key={member.id} className="px-6 py-4 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-700">
                            {member.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{member.name}</div>
                          <div className="text-sm text-gray-500">{member.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          member.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                          member.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                          member.role === 'reviewer' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {member.role}
                        </span>
                        {selectedOrg.role === 'admin' && member.role !== 'admin' && (
                          <select
                            value={member.role}
                            onChange={(e) => updateMemberRole(member.id, e.target.value)}
                            className="text-sm border border-gray-300 rounded px-2 py-1"
                          >
                            <option value="viewer">Viewer</option>
                            <option value="reviewer">Reviewer</option>
                            <option value="editor">Editor</option>
                          </select>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No workspace selected</h3>
              <p className="text-gray-600">Select a workspace from the sidebar to manage your team</p>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CreateOrganizationModal
          onClose={() => setShowCreateModal(false)}
          onCreate={createOrganization}
        />
      )}

      {showInviteModal && (
        <InviteMemberModal
          onClose={() => setShowInviteModal(false)}
          onInvite={inviteTeamMember}
        />
      )}
    </div>
  );
};

// Create Organization Modal
interface CreateOrganizationModalProps {
  onClose: () => void;
  onCreate: (name: string, description: string) => void;
}

const CreateOrganizationModal: React.FC<CreateOrganizationModalProps> = ({ onClose, onCreate }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onCreate(name.trim(), description.trim());
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Workspace</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Workspace Name
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter workspace name"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="Describe your workspace"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Create Workspace
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Invite Member Modal
interface InviteMemberModalProps {
  onClose: () => void;
  onInvite: (email: string, role: string) => void;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({ onClose, onInvite }) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('viewer');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      onInvite(email.trim(), role);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Invite Team Member</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Role
            </label>
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="viewer">Viewer - Can view shared resumes</option>
              <option value="reviewer">Reviewer - Can comment and review</option>
              <option value="editor">Editor - Can edit resumes</option>
            </select>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Send Invitation
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};