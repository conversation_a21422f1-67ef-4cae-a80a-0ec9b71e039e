import React, { useState, useEffect } from 'react';
import { ChatBubbleLeftIcon, PlusIcon, ArrowUturnLeftIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';

interface Comment {
  id: number;
  content: string;
  user_name: string;
  user_email: string;
  created_at: string;
  updated_at: string;
  section_id?: string;
  comment_type: 'comment' | 'reply' | 'suggestion';
  replies: Comment[];
}

interface CommentSystemProps {
  resumeId: string;
  sectionId?: string;
  className?: string;
}

export const CommentSystem: React.FC<CommentSystemProps> = ({
  resumeId,
  sectionId,
  className = ''
}) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');
  const [editingComment, setEditingComment] = useState<number | null>(null);
  const [editText, setEditText] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (resumeId) {
      fetchComments();
    }
  }, [resumeId]);

  const fetchComments = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/resumes/${resumeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setComments(data.comments || []);
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
    }
  };

  const addComment = async () => {
    if (!newComment.trim()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/resumes/${resumeId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: newComment.trim(),
          sectionId,
          commentType: 'comment'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setComments(prev => [...prev, { ...data.comment, replies: [] }]);
        setNewComment('');
      }
    } catch (error) {
      console.error('Failed to add comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const addReply = async (parentCommentId: number) => {
    if (!replyText.trim()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/${parentCommentId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: replyText.trim()
        })
      });

      if (response.ok) {
        const data = await response.json();
        setComments(prev => prev.map(comment => 
          comment.id === parentCommentId
            ? { ...comment, replies: [...comment.replies, data.reply] }
            : comment
        ));
        setReplyText('');
        setReplyingTo(null);
      }
    } catch (error) {
      console.error('Failed to add reply:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateComment = async (commentId: number) => {
    if (!editText.trim()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: editText.trim()
        })
      });

      if (response.ok) {
        setComments(prev => prev.map(comment => {
          if (comment.id === commentId) {
            return { ...comment, content: editText.trim() };
          }
          return {
            ...comment,
            replies: comment.replies.map(reply => 
              reply.id === commentId 
                ? { ...reply, content: editText.trim() }
                : reply
            )
          };
        }));
        setEditText('');
        setEditingComment(null);
      }
    } catch (error) {
      console.error('Failed to update comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteComment = async (commentId: number) => {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setComments(prev => prev.map(comment => {
          if (comment.id === commentId) {
            return null;
          }
          return {
            ...comment,
            replies: comment.replies.filter(reply => reply.id !== commentId)
          };
        }).filter(Boolean) as Comment[]);
      }
    } catch (error) {
      console.error('Failed to delete comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const startEditing = (comment: Comment) => {
    setEditingComment(comment.id);
    setEditText(comment.content);
  };

  const cancelEditing = () => {
    setEditingComment(null);
    setEditText('');
  };

  const startReplying = (commentId: number) => {
    setReplyingTo(commentId);
    setReplyText('');
  };

  const cancelReplying = () => {
    setReplyingTo(null);
    setReplyText('');
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <ChatBubbleLeftIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900">
            Comments {sectionId && `- ${sectionId}`}
          </h3>
          <span className="text-sm text-gray-500">({comments.length})</span>
        </div>
      </div>

      <div className="p-4">
        {/* Add new comment */}
        <div className="mb-6">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add a comment..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={3}
          />
          <div className="flex justify-end mt-2">
            <button
              onClick={addComment}
              disabled={!newComment.trim() || loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Add Comment</span>
            </button>
          </div>
        </div>

        {/* Comments list */}
        <div className="space-y-4">
          {comments.map((comment) => (
            <div key={comment.id} className="border border-gray-200 rounded-lg p-4">
              {/* Comment header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700">
                    {getUserInitials(comment.user_name)}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{comment.user_name}</div>
                    <div className="text-sm text-gray-500">{formatDate(comment.created_at)}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => startReplying(comment.id)}
                    className="text-gray-400 hover:text-gray-600"
                    title="Reply"
                  >
                    <ArrowUturnLeftIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => startEditing(comment)}
                    className="text-gray-400 hover:text-gray-600"
                    title="Edit"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => deleteComment(comment.id)}
                    className="text-gray-400 hover:text-red-600"
                    title="Delete"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Comment content */}
              {editingComment === comment.id ? (
                <div className="mb-3">
                  <textarea
                    value={editText}
                    onChange={(e) => setEditText(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows={3}
                  />
                  <div className="flex justify-end space-x-2 mt-2">
                    <button
                      onClick={cancelEditing}
                      className="px-3 py-1 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => updateComment(comment.id)}
                      disabled={!editText.trim() || loading}
                      className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      Save
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-800 mb-3 whitespace-pre-wrap">{comment.content}</div>
              )}

              {/* Reply form */}
              {replyingTo === comment.id && (
                <div className="mb-3 pl-8">
                  <textarea
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                    placeholder="Write a reply..."
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows={2}
                  />
                  <div className="flex justify-end space-x-2 mt-2">
                    <button
                      onClick={cancelReplying}
                      className="px-3 py-1 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => addReply(comment.id)}
                      disabled={!replyText.trim() || loading}
                      className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                    >
                      Reply
                    </button>
                  </div>
                </div>
              )}

              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="pl-8 border-l-2 border-gray-200 space-y-3">
                  {comment.replies.map((reply) => (
                    <div key={reply.id} className="border border-gray-100 rounded-lg p-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700">
                            {getUserInitials(reply.user_name)}
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 text-sm">{reply.user_name}</span>
                            <span className="text-xs text-gray-500 ml-2">{formatDate(reply.created_at)}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => startEditing(reply)}
                            className="text-gray-400 hover:text-gray-600"
                            title="Edit"
                          >
                            <PencilIcon className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => deleteComment(reply.id)}
                            className="text-gray-400 hover:text-red-600"
                            title="Delete"
                          >
                            <TrashIcon className="h-3 w-3" />
                          </button>
                        </div>
                      </div>

                      {editingComment === reply.id ? (
                        <div>
                          <textarea
                            value={editText}
                            onChange={(e) => setEditText(e.target.value)}
                            className="w-full border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
                            rows={2}
                          />
                          <div className="flex justify-end space-x-2 mt-2">
                            <button
                              onClick={cancelEditing}
                              className="px-2 py-1 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 text-xs"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => updateComment(reply.id)}
                              disabled={!editText.trim() || loading}
                              className="px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-xs"
                            >
                              Save
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="text-gray-700 text-sm whitespace-pre-wrap">{reply.content}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {comments.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <ChatBubbleLeftIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No comments yet. Be the first to add one!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};