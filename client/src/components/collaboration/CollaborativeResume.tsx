import React, { useEffect, useState } from 'react';
import { CollaborativeEditor } from '../collaboration/CollaborativeEditor';
import { CommentSystem } from '../collaboration/CommentSystem';
import { ShareIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline';

interface Experience {
  company: string;
  role: string;
  start: string;
  end: string;
}

interface Resume {
  id: string;
  name: string;
  title: string;
  summary: string;
  skills: string[];
  experience: Experience[];
}

interface CollaborativeResumeProps {
  resumeId: string;
  initialData?: Resume;
  isCollaborationEnabled?: boolean;
}

export const CollaborativeResume: React.FC<CollaborativeResumeProps> = ({
  resumeId,
  initialData,
  isCollaborationEnabled = true
}) => {
  const [resume, setResume] = useState<Resume | null>(initialData || null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedResume, setEditedResume] = useState<Resume | null>(null);
  const [showComments, setShowComments] = useState(false);
  const [activeSection] = useState<string | null>(null);
  const [, setIsSharing] = useState(false);

  useEffect(() => {
    if (!initialData && resumeId) {
      fetchResume();
    }
  }, [resumeId, initialData]);

  const fetchResume = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/resumes/${resumeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setResume(data);
      }
    } catch (error) {
      console.error('Failed to fetch resume:', error);
    }
  };

  const startEdit = () => {
    setEditedResume(JSON.parse(JSON.stringify(resume)));
    setIsEditing(true);
  };

  const cancelEdit = () => {
    setIsEditing(false);
    setEditedResume(null);
  };

  const saveEdit = async () => {
    if (!editedResume) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/resumes/${resumeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(editedResume)
      });

      if (response.ok) {
        setResume(editedResume);
        setIsEditing(false);
        setEditedResume(null);
      }
    } catch (error) {
      console.error('Failed to save resume:', error);
    }
  };

  const updateSection = (sectionName: string, value: any) => {
    if (!editedResume) return;
    setEditedResume({
      ...editedResume,
      [sectionName]: value
    });
  };

  const addExperience = () => {
    if (!editedResume) return;
    setEditedResume({
      ...editedResume,
      experience: [...editedResume.experience, { company: '', role: '', start: '', end: '' }]
    });
  };

  const removeExperience = (index: number) => {
    if (!editedResume) return;
    const newExp = editedResume.experience.filter((_, i) => i !== index);
    setEditedResume({ ...editedResume, experience: newExp });
  };

  const updateExp = (index: number, field: keyof Experience, value: string) => {
    if (!editedResume) return;
    const newExp = [...editedResume.experience];
    newExp[index] = { ...newExp[index], [field]: value };
    setEditedResume({ ...editedResume, experience: newExp });
  };

  const shareResume = () => {
    setIsSharing(true);
    // Implementation for sharing modal would go here
    console.log('Share resume functionality');
  };

  if (!resume) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading...</span>
      </div>
    );
  }

  const currentResume = editedResume || resume;

  if (isEditing && editedResume) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Edit Resume</h2>
          <div className="flex items-center space-x-3">
            {isCollaborationEnabled && (
              <>
                <button
                  onClick={() => setShowComments(!showComments)}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <ChatBubbleLeftIcon className="h-5 w-5" />
                  <span>Comments</span>
                </button>
                <button
                  onClick={shareResume}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <ShareIcon className="h-5 w-5" />
                  <span>Share</span>
                </button>
              </>
            )}
            <button
              onClick={cancelEdit}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={saveEdit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Save Changes
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main editing area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Name Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  {isCollaborationEnabled ? (
                    <CollaborativeEditor
                      resumeId={resumeId}
                      content={editedResume.name}
                      onChange={(value: string) => updateSection('name', value)}
                      placeholder="Full name"
                      className="text-lg font-semibold"
                    />
                  ) : (
                    <input
                      value={editedResume.name}
                      onChange={e => updateSection('name', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Full name"
                    />
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                  {isCollaborationEnabled ? (
                    <CollaborativeEditor
                      resumeId={resumeId}
                      content={editedResume.title}
                      onChange={(value: string) => updateSection('title', value)}
                      placeholder="Professional title"
                    />
                  ) : (
                    <input
                      value={editedResume.title}
                      onChange={e => updateSection('title', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Professional title"
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Summary Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
              {isCollaborationEnabled ? (
                <CollaborativeEditor
                  resumeId={resumeId}
                  content={editedResume.summary}
                  onChange={(value: string) => updateSection('summary', value)}
                  placeholder="Professional summary"
                  className="min-h-32"
                />
              ) : (
                <textarea
                  value={editedResume.summary}
                  onChange={e => updateSection('summary', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={4}
                  placeholder="Professional summary"
                />
              )}
            </div>

            {/* Skills Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Skills</h3>
              {isCollaborationEnabled ? (
                <CollaborativeEditor
                  resumeId={resumeId}
                  content={editedResume.skills.join(', ')}
                  onChange={(value: string) => updateSection('skills', value.split(', ').filter((s: string) => s.trim()))}
                  placeholder="Skills (comma-separated)"
                />
              ) : (
                <input
                  value={editedResume.skills.join(', ')}
                  onChange={e => updateSection('skills', e.target.value.split(', ').filter(s => s.trim()))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Skills (comma-separated)"
                />
              )}
            </div>

            {/* Experience Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Experience</h3>
                <button
                  onClick={addExperience}
                  className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 text-sm"
                >
                  Add Experience
                </button>
              </div>
              <div className="space-y-4">
                {editedResume.experience.map((exp, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-medium text-gray-900">Experience {index + 1}</h4>
                      <button
                        onClick={() => removeExperience(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <input
                          value={exp.company}
                          onChange={e => updateExp(index, 'company', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Company name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <input
                          value={exp.role}
                          onChange={e => updateExp(index, 'role', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Job title"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input
                          value={exp.start}
                          onChange={e => updateExp(index, 'start', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Start date"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input
                          value={exp.end}
                          onChange={e => updateExp(index, 'end', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="End date or 'Present'"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Comments sidebar */}
          {isCollaborationEnabled && showComments && (
            <div className="lg:col-span-1">
              <CommentSystem
                resumeId={resumeId}
                sectionId={activeSection || undefined}
                className="sticky top-4"
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  // View mode
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Resume</h2>
        <div className="flex items-center space-x-3">
          {isCollaborationEnabled && (
            <>
              <button
                onClick={() => setShowComments(!showComments)}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <ChatBubbleLeftIcon className="h-5 w-5" />
                <span>Comments</span>
              </button>
              <button
                onClick={shareResume}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <ShareIcon className="h-5 w-5" />
                <span>Share</span>
              </button>
            </>
          )}
          <button
            onClick={startEdit}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Edit Resume
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900">{currentResume.name}</h1>
              <p className="text-xl text-gray-600 mt-2">{currentResume.title}</p>
            </div>

            <div className="space-y-8">
              <section>
                <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                  Summary
                </h2>
                <p className="text-gray-700">{currentResume.summary}</p>
              </section>

              <section>
                <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                  Skills
                </h2>
                <div className="flex flex-wrap gap-2">
                  {currentResume.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </section>

              <section>
                <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                  Experience
                </h2>
                <div className="space-y-6">
                  {currentResume.experience.map((exp, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                      <h3 className="text-lg font-semibold text-gray-900">{exp.role}</h3>
                      <p className="text-blue-600 font-medium">{exp.company}</p>
                      <p className="text-gray-500 text-sm">{exp.start} - {exp.end}</p>
                    </div>
                  ))}
                </div>
              </section>
            </div>
          </div>
        </div>

        {isCollaborationEnabled && showComments && (
          <div className="lg:col-span-1">
            <CommentSystem
              resumeId={resumeId}
              className="sticky top-4"
            />
          </div>
        )}
      </div>
    </div>
  );
};