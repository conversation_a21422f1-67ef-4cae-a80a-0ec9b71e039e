import React, { useState, useRef, useEffect } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';

interface CollaborativeEditorProps {
  resumeId: string;
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

interface UserCursor {
  userId: string;
  sessionId: string;
  name: string;
  cursor: { line: number; column: number };
  selection: { start: number; end: number } | null;
  color: string;
}

const CURSOR_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
];

export const CollaborativeEditor: React.FC<CollaborativeEditorProps> = ({
  resumeId,
  content,
  onChange,
  placeholder,
  className = ''
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [localContent, setLocalContent] = useState(content);
  const [cursors, setCursors] = useState<UserCursor[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const lastOperationRef = useRef<string>('');
  const userColorMap = useRef<Map<string, string>>(new Map());

  // Get user color
  const getUserColor = (userId: string) => {
    if (!userColorMap.current.has(userId)) {
      const colorIndex = userColorMap.current.size % CURSOR_COLORS.length;
      userColorMap.current.set(userId, CURSOR_COLORS[colorIndex]);
    }
    return userColorMap.current.get(userId)!;
  };

  // Apply remote operation
  const applyOperation = (operation: any) => {
    if (operation.operationId === lastOperationRef.current) {
      return; // Skip our own operation
    }

    switch (operation.type) {
      case 'insert':
        setLocalContent(prev => {
          const position = Math.min(operation.position, prev.length);
          return prev.slice(0, position) + operation.newContent + prev.slice(position);
        });
        break;

      case 'delete':
        setLocalContent(prev => {
          const start = Math.min(operation.position, prev.length);
          const end = Math.min(start + operation.length, prev.length);
          return prev.slice(0, start) + prev.slice(end);
        });
        break;

      case 'replace':
        setLocalContent(prev => {
          const start = Math.min(operation.position, prev.length);
          const end = Math.min(start + operation.oldContent.length, prev.length);
          return prev.slice(0, start) + operation.newContent + prev.slice(end);
        });
        break;
    }
  };

  // Handle cursor updates
  const handleCursorUpdate = (data: any) => {
    setCursors(prev => {
      const existing = prev.find(c => c.sessionId === data.sessionId);
      const newCursor: UserCursor = {
        userId: data.userId,
        sessionId: data.sessionId,
        name: data.name || 'Unknown User',
        cursor: data.cursor,
        selection: data.selection,
        color: getUserColor(data.userId)
      };

      if (existing) {
        return prev.map(c => c.sessionId === data.sessionId ? newCursor : c);
      } else {
        return [...prev, newCursor];
      }
    });
  };

  // Collaboration hook
  const {
    isConnected,
    activeUsers,
    sendOperation,
    updateCursor
  } = useCollaboration(resumeId, {
    onOperation: applyOperation,
    onCursorUpdate: handleCursorUpdate,
    onUserJoined: (data) => {
      console.log('User joined:', data);
    },
    onUserLeft: (data) => {
      setCursors(prev => prev.filter(c => c.sessionId !== data.sessionId));
    },
    onError: (error) => {
      console.error('Collaboration error:', error);
    }
  });

  // Update connection status
  useEffect(() => {
    setConnectionStatus(isConnected ? 'connected' : 'connecting');
  }, [isConnected]);

  // Sync local changes
  useEffect(() => {
    if (localContent !== content) {
      onChange(localContent);
    }
  }, [localContent, onChange]);

  // Handle text input
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    const oldContent = localContent;
    
    if (newContent === oldContent) return;

    // Simple diff algorithm to detect changes
    let operation;
    const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    lastOperationRef.current = operationId;

    if (newContent.length > oldContent.length) {
      // Insertion
      const position = findInsertPosition(oldContent, newContent);
      const insertedText = newContent.slice(position, position + (newContent.length - oldContent.length));
      
      operation = {
        type: 'insert',
        position,
        newContent: insertedText,
        operationId,
        timestamp: Date.now()
      };
    } else if (newContent.length < oldContent.length) {
      // Deletion
      const position = findDeletePosition(oldContent, newContent);
      const deletedLength = oldContent.length - newContent.length;
      
      operation = {
        type: 'delete',
        position,
        length: deletedLength,
        operationId,
        timestamp: Date.now()
      };
    } else {
      // Replacement
      const { start, end } = findReplaceRange(oldContent, newContent);
      
      operation = {
        type: 'replace',
        position: start,
        oldContent: oldContent.slice(start, end),
        newContent: newContent.slice(start, start + (end - start)),
        operationId,
        timestamp: Date.now()
      };
    }

    setLocalContent(newContent);
    sendOperation(operation);
  };

  // Handle cursor position changes
  const handleSelectionChange = () => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    // Convert position to line/column
    const lines = textarea.value.substring(0, start).split('\n');
    const cursor = {
      line: lines.length - 1,
      column: lines[lines.length - 1].length
    };

    const selection = start !== end ? { start, end } : null;
    
    updateCursor(cursor, selection);
  };

  // Simple diff helpers
  const findInsertPosition = (oldText: string, newText: string): number => {
    let i = 0;
    while (i < oldText.length && i < newText.length && oldText[i] === newText[i]) {
      i++;
    }
    return i;
  };

  const findDeletePosition = (oldText: string, newText: string): number => {
    let i = 0;
    while (i < oldText.length && i < newText.length && oldText[i] === newText[i]) {
      i++;
    }
    return i;
  };

  const findReplaceRange = (oldText: string, newText: string): { start: number; end: number } => {
    let start = 0;
    while (start < oldText.length && start < newText.length && oldText[start] === newText[start]) {
      start++;
    }

    let oldEnd = oldText.length;
    let newEnd = newText.length;
    while (oldEnd > start && newEnd > start && oldText[oldEnd - 1] === newText[newEnd - 1]) {
      oldEnd--;
      newEnd--;
    }

    return { start, end: oldEnd };
  };

  return (
    <div className={`relative ${className}`}>
      {/* Connection indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            connectionStatus === 'connected' ? 'bg-green-500' : 
            connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 
            'bg-red-500'
          }`} />
          <span className="text-xs text-gray-600">
            {connectionStatus === 'connected' ? 'Connected' : 
             connectionStatus === 'connecting' ? 'Connecting...' : 
             'Disconnected'}
          </span>
        </div>

        {/* Active users */}
        {activeUsers.length > 0 && (
          <div className="flex items-center space-x-1">
            <span className="text-xs text-gray-600">Editing:</span>
            {activeUsers.slice(0, 3).map((user) => (
              <div
                key={user.sessionId}
                className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium"
                style={{ backgroundColor: getUserColor(user.userId) }}
                title={user.name}
              >
                {user.name.charAt(0).toUpperCase()}
              </div>
            ))}
            {activeUsers.length > 3 && (
              <div className="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-medium text-white">
                +{activeUsers.length - 3}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Editor */}
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={localContent}
          onChange={handleInput}
          onSelect={handleSelectionChange}
          onBlur={handleSelectionChange}
          placeholder={placeholder}
          className={`w-full min-h-32 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${className}`}
          style={{ fontFamily: 'monospace' }}
        />

        {/* Cursor overlays */}
        {cursors.map((cursor) => (
          <div
            key={cursor.sessionId}
            className="absolute pointer-events-none"
            style={{
              // This is a simplified cursor position calculation
              // In a real implementation, you'd need more sophisticated positioning
              top: `${cursor.cursor.line * 1.2 + 0.5}em`,
              left: `${cursor.cursor.column * 0.6}em`,
              transform: 'translateX(3px) translateY(3px)'
            }}
          >
            <div
              className="w-0.5 h-5 animate-pulse"
              style={{ backgroundColor: cursor.color }}
            />
            <div
              className="absolute -top-6 left-0 px-1 py-0.5 text-xs text-white rounded whitespace-nowrap"
              style={{ backgroundColor: cursor.color }}
            >
              {cursor.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};