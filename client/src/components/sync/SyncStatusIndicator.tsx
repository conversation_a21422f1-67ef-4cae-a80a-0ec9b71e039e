import React from 'react';
import { useSyncStatus } from '../../hooks/usePWA';

interface SyncStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({ 
  className = '', 
  showDetails = false 
}) => {
  const syncStatus = useSyncStatus();

  if (!syncStatus.isActive && syncStatus.completedItems === 0 && syncStatus.errors.length === 0) {
    return null; // Don't show anything if no sync activity
  }

  const getStatusIcon = () => {
    if (syncStatus.isActive) {
      return (
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
      );
    }
    
    if (syncStatus.errors.length > 0) {
      return (
        <div className="h-4 w-4 rounded-full bg-red-500 flex items-center justify-center">
          <span className="text-white text-xs">!</span>
        </div>
      );
    }
    
    return (
      <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center">
        <span className="text-white text-xs">✓</span>
      </div>
    );
  };

  const getStatusText = () => {
    if (syncStatus.isActive) {
      return `Syncing ${syncStatus.currentItem ? `(${syncStatus.completedItems + 1}/${syncStatus.totalItems})` : '...'}`;
    }
    
    if (syncStatus.errors.length > 0) {
      return `Sync completed with ${syncStatus.errors.length} error(s)`;
    }
    
    return `Synced ${syncStatus.completedItems} item(s)`;
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {getStatusIcon()}
      <span className="text-sm text-gray-600">{getStatusText()}</span>
      
      {syncStatus.isActive && (
        <div className="w-24 bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${syncStatus.progress}%` }}
          ></div>
        </div>
      )}
      
      {showDetails && syncStatus.errors.length > 0 && (
        <details className="mt-2">
          <summary className="cursor-pointer text-red-600 text-sm">
            View errors ({syncStatus.errors.length})
          </summary>
          <ul className="mt-1 text-xs text-red-500 space-y-1">
            {syncStatus.errors.map((error, index) => (
              <li key={index}>
                <span className="font-mono">{error.key}</span>: {error.error}
              </li>
            ))}
          </ul>
        </details>
      )}
    </div>
  );
};

export default SyncStatusIndicator;