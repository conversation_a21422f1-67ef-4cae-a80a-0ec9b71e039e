import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface RecruiterContact {
  id: number;
  email: string;
  name?: string;
  company?: string;
  title?: string;
  verification_status: string;
  response_status: string;
  last_contacted?: string;
  created_at: string;
}

interface ContactStatistics {
  total_contacts: number;
  verified_contacts: number;
  responded_contacts: number;
  contacted_contacts: number;
  response_rate: string;
  verification_rate: string;
}

const RecruiterContactsManager: React.FC = () => {
  const [contacts, setContacts] = useState<RecruiterContact[]>([]);
  const [statistics, setStatistics] = useState<ContactStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [discoveryData, setDiscoveryData] = useState({
    company_name: '',
    company_domain: ''
  });

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/email/contacts', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch contacts');
      }

      const data = await response.json();
      setContacts(data.contacts || []);
      setStatistics(data.statistics || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch contacts');
    } finally {
      setLoading(false);
    }
  };

  const discoverRecruiters = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/email/discover-recruiters', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(discoveryData)
      });

      if (!response.ok) {
        throw new Error('Failed to discover recruiters');
      }

      const data = await response.json();
      alert(`Discovered ${data.discovered} potential recruiters!`);
      
      // Refresh contacts
      fetchContacts();
      
      // Clear form
      setDiscoveryData({ company_name: '', company_domain: '' });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to discover recruiters');
    } finally {
      setLoading(false);
    }
  };

  const updateContact = async (contactId: number, updateData: Partial<RecruiterContact>) => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/email/contacts/${contactId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error('Failed to update contact');
      }

      // Refresh contacts
      fetchContacts();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update contact');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'unverified': return 'bg-yellow-100 text-yellow-800';
      case 'invalid': return 'bg-red-100 text-red-800';
      case 'responded': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && contacts.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Recruiter Contacts</h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Statistics */}
      {statistics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="text-center py-4">
              <p className="text-2xl font-bold text-blue-600">{statistics.total_contacts}</p>
              <p className="text-sm text-gray-500">Total Contacts</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="text-center py-4">
              <p className="text-2xl font-bold text-green-600">{statistics.verification_rate}%</p>
              <p className="text-sm text-gray-500">Verified</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="text-center py-4">
              <p className="text-2xl font-bold text-purple-600">{statistics.contacted_contacts}</p>
              <p className="text-sm text-gray-500">Contacted</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="text-center py-4">
              <p className="text-2xl font-bold text-orange-600">{statistics.response_rate}%</p>
              <p className="text-sm text-gray-500">Response Rate</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Discovery Form */}
      <Card>
        <CardHeader>
          <CardTitle>Discover New Recruiters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <input
              type="text"
              placeholder="Company Name (e.g., TechCorp Inc)"
              value={discoveryData.company_name}
              onChange={(e) => setDiscoveryData({ ...discoveryData, company_name: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="text"
              placeholder="Company Domain (e.g., techcorp.com)"
              value={discoveryData.company_domain}
              onChange={(e) => setDiscoveryData({ ...discoveryData, company_domain: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={discoverRecruiters}
              disabled={!discoveryData.company_name || !discoveryData.company_domain || loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Discovering...' : 'Discover Recruiters'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contacts List */}
      <div className="grid gap-4">
        {contacts.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No recruiter contacts yet</p>
              <p className="text-sm text-gray-400">
                Use the discovery tool above to find recruiters for target companies
              </p>
            </CardContent>
          </Card>
        ) : (
          contacts.map((contact) => (
            <Card key={contact.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="py-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="font-medium text-gray-900">
                        {contact.name || contact.email}
                      </h3>
                      <Badge className={getStatusColor(contact.verification_status)}>
                        {contact.verification_status}
                      </Badge>
                      {contact.response_status !== 'none' && (
                        <Badge className={getStatusColor(contact.response_status)}>
                          {contact.response_status}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{contact.email}</p>
                    {contact.company && (
                      <p className="text-sm text-gray-500">
                        {contact.title} at {contact.company}
                      </p>
                    )}
                    {contact.last_contacted && (
                      <p className="text-xs text-gray-400 mt-1">
                        Last contacted: {new Date(contact.last_contacted).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateContact(contact.id, { 
                        verification_status: contact.verification_status === 'verified' ? 'unverified' : 'verified' 
                      })}
                    >
                      {contact.verification_status === 'verified' ? 'Unverify' : 'Verify'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default RecruiterContactsManager;