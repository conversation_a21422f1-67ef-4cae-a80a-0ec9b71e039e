import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';

interface EmailTemplateEditorProps {
  template?: any;
  onSave?: (template: any) => void;
  onCancel?: () => void;
}

const EmailTemplateEditor: React.FC<EmailTemplateEditorProps> = ({
  template,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    type: template?.type || 'introduction',
    subject: template?.subject || '',
    content: template?.content || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave?.(formData);
  };

  const templateTypes = [
    { value: 'introduction', label: 'Introduction' },
    { value: 'follow_up', label: 'Follow-up' },
    { value: 'networking', label: 'Networking' },
    { value: 'thank_you', label: 'Thank You' },
    { value: 'cold_outreach', label: 'Cold Outreach' }
  ];

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {template ? 'Edit Template' : 'Create Email Template'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Template Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Template Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {templateTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject Line
            </label>
            <input
              type="text"
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Exploring Opportunities at {{company}}"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Content
            </label>
            <div className="mb-2">
              <p className="text-xs text-gray-500">
                Available variables: &#123;&#123;name&#125;&#125;, &#123;&#123;company&#125;&#125;, &#123;&#123;role&#125;&#125;, &#123;&#123;senderName&#125;&#125;, &#123;&#123;email&#125;&#125;, &#123;&#123;skills&#125;&#125;, &#123;&#123;experience&#125;&#125;
              </p>
            </div>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={12}
              placeholder="Write your email template here..."
              required
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              className="bg-blue-600 hover:bg-blue-700"
            >
              Save Template
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default EmailTemplateEditor;