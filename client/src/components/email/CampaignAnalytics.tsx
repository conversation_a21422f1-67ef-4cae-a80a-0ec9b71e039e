import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/Card';

interface AnalyticsData {
  total_sent: number;
  total_opened: number;
  total_clicked: number;
  total_replied: number;
  open_rate: string;
  click_rate: string;
  reply_rate: string;
}

interface CampaignAnalyticsProps {
  campaignId?: number;
}

const CampaignAnalytics: React.FC<CampaignAnalyticsProps> = ({ campaignId }) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, [campaignId]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const url = campaignId 
        ? `/api/email/analytics?campaign_id=${campaignId}`
        : '/api/email/analytics';
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data.analytics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold text-gray-900">
        {campaignId ? 'Campaign Analytics' : 'Overall Email Analytics'}
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Total Sent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-blue-600">
              {analytics?.total_sent || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Open Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-green-600">
              {analytics?.open_rate || 0}%
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {analytics?.total_opened || 0} opened
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Click Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-purple-600">
              {analytics?.click_rate || 0}%
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {analytics?.total_clicked || 0} clicked
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Reply Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-orange-600">
              {analytics?.reply_rate || 0}%
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {analytics?.total_replied || 0} replied
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Performance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Email Delivery</span>
              <span className="font-medium">
                {analytics?.total_sent || 0} emails sent
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Engagement</span>
              <span className="font-medium">
                {((parseFloat(analytics?.open_rate || '0') + parseFloat(analytics?.click_rate || '0')) / 2).toFixed(1)}% average engagement
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Response Rate</span>
              <span className="font-medium">
                {analytics?.reply_rate || 0}% replied
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignAnalytics;