import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { login, register, clearError } from '../../store/authSlice';
import { useTheme } from '../../contexts/ThemeContext';
import { Button, Input } from '../ui';
import type { AppDispatch, RootState } from '../../store';

export default function AuthForm() {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
  });

  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const { theme, toggleTheme } = useTheme();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLogin) {
      dispatch(login({ email: formData.email, password: formData.password }));
    } else {
      dispatch(register({ 
        email: formData.email, 
        password: formData.password, 
        name: formData.name 
      }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    dispatch(clearError());
    setFormData({ email: '', password: '', name: '' });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 opacity-50"></div>
      
      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className="absolute top-6 right-6 p-3 rounded-xl bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 focus-enhanced"
        aria-label="Toggle theme"
      >
        <span className="text-xl">{theme === 'light' ? '🌙' : '☀️'}</span>
      </button>

      <div className="relative max-w-md w-full">
        {/* Enhanced Card Container */}
        <div className="card-enhanced p-8 animate-fade-in">
          {/* Header Section */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-6">
              <div className="w-12 h-12 gradient-primary rounded-2xl flex items-center justify-center shadow-lg micro-float">
                <span className="text-white font-bold text-lg">CV</span>
              </div>
            </div>
            <h2 className="text-fluid-xl font-bold text-gray-900 dark:text-white mb-2">
              {isLogin ? 'Welcome back' : 'Create your account'}
            </h2>
            <p className="text-fluid-base text-gray-600 dark:text-gray-400">
              {isLogin ? 'Sign in to continue your career journey' : 'Start building your perfect resume'}
            </p>
          </div>

          {/* Toggle Section */}
          <div className="flex rounded-xl bg-gray-100 dark:bg-gray-800 p-1 mb-8">
            <button
              type="button"
              onClick={() => isLogin || toggleMode()}
              className={`flex-1 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 ${
                isLogin
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              Sign In
            </button>
            <button
              type="button"
              onClick={() => !isLogin || toggleMode()}
              className={`flex-1 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 ${
                !isLogin
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              Sign Up
            </button>
          </div>
          {/* Form Section */}
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-xl bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 p-4 animate-slide-in">
                <div className="flex items-center">
                  <span className="text-error-400 mr-2">⚠️</span>
                  <p className="text-sm text-error-700 dark:text-error-400">{error}</p>
                </div>
              </div>
            )}
            
            <div className="space-y-5">
              {!isLogin && (
                <div className="animate-slide-in">
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required={!isLogin}
                    value={formData.name}
                    onChange={handleInputChange}
                    label="Full Name"
                    placeholder="Enter your full name"
                    inputSize="lg"
                    leftIcon="👤"
                  />
                </div>
              )}
              
              <div>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  label="Email Address"
                  placeholder="Enter your email"
                  inputSize="lg"
                  leftIcon="✉️"
                />
              </div>
              
              <div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete={isLogin ? 'current-password' : 'new-password'}
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  label="Password"
                  placeholder="Enter your password"
                  inputSize="lg"
                  leftIcon="🔒"
                />
              </div>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="xl"
              isLoading={isLoading}
              loadingText="Loading..."
              disabled={isLoading}
              className="w-full focus-enhanced"
            >
              {isLogin ? 'Sign In' : 'Create Account'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}