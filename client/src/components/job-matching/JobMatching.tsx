import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface JobMatchingProps {
  onMatchFound?: (match: any) => void;
}

export default function JobMatching({ onMatchFound }: JobMatchingProps) {
  const { token } = useSelector((state: RootState) => state.auth);
  const [activeTab, setActiveTab] = useState<'match' | 'recommendations' | 'culture' | 'market' | 'history'>('match');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [history, setHistory] = useState<any[]>([]);

  // Job matching state
  const [jobDescription, setJobDescription] = useState('');
  const [companyData, setCompanyData] = useState({
    name: '',
    size: '',
    industry: '',
    culture: ''
  });

  // Recommendations state
  const [preferences, setPreferences] = useState({
    location: '',
    industry: '',
    experience_level: '',
    skills: ''
  });

  // Culture fit state
  const [cultureCompanyData, setCultureCompanyData] = useState({
    name: '',
    values: '',
    workStyle: '',
    size: '',
    industry: ''
  });

  // Market analysis state
  const [marketParams, setMarketParams] = useState({
    industry: '',
    location: '',
    role: ''
  });

  const handleJobMatch = async () => {
    if (!jobDescription) return;
    
    setLoading(true);
    try {
      // Mock resume data - in real app, this would come from user's profile
      const resumeData = {
        name: 'User',
        title: 'Current Role',
        experience: [],
        skills: [],
        education: []
      };

      const response = await fetch('/api/job-matching/match', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          resumeData,
          jobDescription: {
            title: 'Job Title',
            description: jobDescription,
            requirements: jobDescription
          },
          companyData
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
        if (onMatchFound) {
          onMatchFound(data.data);
        }
      }
    } catch (error) {
      console.error('Job matching failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGetRecommendations = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(preferences).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/api/job-matching/recommendations?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Recommendations failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCultureFitAnalysis = async () => {
    if (!cultureCompanyData.name) return;
    
    setLoading(true);
    try {
      const resumeData = {
        name: 'User',
        title: 'Current Role',
        experience: [],
        skills: []
      };

      const response = await fetch('/api/job-matching/culture-fit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          resumeData,
          companyData: cultureCompanyData
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Culture fit analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarketAnalysis = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(marketParams).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/api/job-matching/market-analysis?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Market analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchHistory = async () => {
    try {
      const response = await fetch('/api/job-matching/history', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setHistory(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch history:', error);
    }
  };

  const tabs = [
    { id: 'match', label: 'Job Match Analysis', icon: '🎯' },
    { id: 'recommendations', label: 'Job Recommendations', icon: '💡' },
    { id: 'culture', label: 'Culture Fit', icon: '🏢' },
    { id: 'market', label: 'Market Analysis', icon: '📊' },
    { id: 'history', label: 'Match History', icon: '📋' }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Advanced Job Matching Engine</h2>
          <p className="text-gray-600 mt-1">AI-powered job matching with culture fit and market analysis</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id as any);
                  setResults(null);
                  if (tab.id === 'history') {
                    fetchHistory();
                  }
                }}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Job Match Analysis Tab */}
          {activeTab === 'match' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Analyze Job Match Score</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Job Description
                </label>
                <textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 h-32"
                  placeholder="Paste the job description here..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={companyData.name}
                    onChange={(e) => setCompanyData({...companyData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Google"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Size
                  </label>
                  <select
                    value={companyData.size}
                    onChange={(e) => setCompanyData({...companyData, size: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Select size</option>
                    <option value="startup">Startup (1-50)</option>
                    <option value="small">Small (51-200)</option>
                    <option value="medium">Medium (201-1000)</option>
                    <option value="large">Large (1000+)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Industry
                  </label>
                  <input
                    type="text"
                    value={companyData.industry}
                    onChange={(e) => setCompanyData({...companyData, industry: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Technology"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Culture
                  </label>
                  <input
                    type="text"
                    value={companyData.culture}
                    onChange={(e) => setCompanyData({...companyData, culture: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Fast-paced, innovative"
                  />
                </div>
              </div>

              <button
                onClick={handleJobMatch}
                disabled={loading || !jobDescription}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Analyzing...' : 'Analyze Job Match'}
              </button>
            </div>
          )}

          {/* Job Recommendations Tab */}
          {activeTab === 'recommendations' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Get Personalized Job Recommendations</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Location
                  </label>
                  <input
                    type="text"
                    value={preferences.location}
                    onChange={(e) => setPreferences({...preferences, location: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., San Francisco, Remote"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Industry
                  </label>
                  <input
                    type="text"
                    value={preferences.industry}
                    onChange={(e) => setPreferences({...preferences, industry: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Technology, Finance"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Experience Level
                  </label>
                  <select
                    value={preferences.experience_level}
                    onChange={(e) => setPreferences({...preferences, experience_level: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Select level</option>
                    <option value="entry">Entry Level</option>
                    <option value="mid">Mid Level</option>
                    <option value="senior">Senior Level</option>
                    <option value="executive">Executive</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Skills
                  </label>
                  <input
                    type="text"
                    value={preferences.skills}
                    onChange={(e) => setPreferences({...preferences, skills: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Python, React, Machine Learning"
                  />
                </div>
              </div>

              <button
                onClick={handleGetRecommendations}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Generating...' : 'Get Recommendations'}
              </button>
            </div>
          )}

          {/* Culture Fit Tab */}
          {activeTab === 'culture' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Company Culture Fit Analysis</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={cultureCompanyData.name}
                    onChange={(e) => setCultureCompanyData({...cultureCompanyData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Netflix"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Values
                  </label>
                  <input
                    type="text"
                    value={cultureCompanyData.values}
                    onChange={(e) => setCultureCompanyData({...cultureCompanyData, values: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Innovation, Transparency, Freedom"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Work Style
                  </label>
                  <input
                    type="text"
                    value={cultureCompanyData.workStyle}
                    onChange={(e) => setCultureCompanyData({...cultureCompanyData, workStyle: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Remote-first, Collaborative, Fast-paced"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Industry
                  </label>
                  <input
                    type="text"
                    value={cultureCompanyData.industry}
                    onChange={(e) => setCultureCompanyData({...cultureCompanyData, industry: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Entertainment Technology"
                  />
                </div>
              </div>

              <button
                onClick={handleCultureFitAnalysis}
                disabled={loading || !cultureCompanyData.name}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Analyzing...' : 'Analyze Culture Fit'}
              </button>
            </div>
          )}

          {/* Market Analysis Tab */}
          {activeTab === 'market' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Job Market Analysis</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Industry
                  </label>
                  <input
                    type="text"
                    value={marketParams.industry}
                    onChange={(e) => setMarketParams({...marketParams, industry: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Technology"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    value={marketParams.location}
                    onChange={(e) => setMarketParams({...marketParams, location: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., San Francisco Bay Area"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role
                  </label>
                  <input
                    type="text"
                    value={marketParams.role}
                    onChange={(e) => setMarketParams({...marketParams, role: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Software Engineer"
                  />
                </div>
              </div>

              <button
                onClick={handleMarketAnalysis}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Analyzing...' : 'Generate Market Analysis'}
              </button>
            </div>
          )}

          {/* Match History Tab */}
          {activeTab === 'history' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Your Match History</h3>
              
              {history.length === 0 ? (
                <p className="text-gray-600">No match history found. Start by analyzing some job matches!</p>
              ) : (
                <div className="space-y-4">
                  {history.map((match, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{match.job_title} at {match.company}</h4>
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {match.overall_score}% match
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Analyzed: {new Date(match.analyzed_at).toLocaleDateString()}
                      </p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div>Skills: {match.skill_match_score}%</div>
                        <div>Experience: {match.experience_match_score}%</div>
                        <div>Culture: {match.culture_fit_score}%</div>
                        <div>Growth: {match.growth_potential_score}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Results Display */}
          {results && (
            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-semibold mb-4">Analysis Results</h4>
              <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96 bg-white p-4 rounded border">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}