import React, { useState, useMemo } from 'react';
import { AnimatePresence } from 'framer-motion';
import Input from './ui/Input';
import Button from './ui/Button';

// Project interface
interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  category: string;
  imageUrl?: string;
  demoUrl?: string;
  githubUrl?: string;
  featured: boolean;
  completedDate: string;
}

// Sample projects data - in a real app, this would come from an API or CMS
const sampleProjects: Project[] = [
  {
    id: '1',
    title: 'CVleap Resume Builder',
    description: 'A comprehensive resume building platform with AI-powered optimization, real-time collaboration, and ATS-friendly templates. Built with React, Node.js, and advanced AI integrations.',
    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'AI/ML', 'Tailwind CSS'],
    category: 'Web Application',
    imageUrl: '/api/placeholder/600/400',
    demoUrl: 'https://cvleap.com',
    githubUrl: 'https://github.com/moss101/cvleap',
    featured: true,
    completedDate: '2024-01-15',
  },
  {
    id: '2',
    title: 'Job Application Automation System',
    description: 'Intelligent job application automation with anti-detection mechanisms, timing optimization, and multi-platform support. Increases application efficiency by 300%.',
    technologies: ['Python', 'Selenium', 'Machine Learning', 'PostgreSQL', 'REST API'],
    category: 'Automation',
    imageUrl: '/api/placeholder/600/400',
    demoUrl: 'https://demo.jobautomation.com',
    featured: true,
    completedDate: '2023-11-20',
  },
  {
    id: '3',
    title: 'AI-Powered Document Intelligence',
    description: 'Advanced document parsing and analysis system using computer vision and NLP. Extracts structured data from resumes, cover letters, and portfolios.',
    technologies: ['Python', 'TensorFlow', 'OpenCV', 'spaCy', 'FastAPI', 'Docker'],
    category: 'AI/ML',
    imageUrl: '/api/placeholder/600/400',
    githubUrl: 'https://github.com/example/document-ai',
    featured: true,
    completedDate: '2023-09-12',
  },
  {
    id: '4',
    title: 'Real-time Collaboration Platform',
    description: 'WebSocket-based real-time collaboration system for document editing with conflict resolution, user presence indicators, and seamless synchronization.',
    technologies: ['Socket.io', 'Redis', 'React', 'Node.js', 'MongoDB'],
    category: 'Web Application',
    imageUrl: '/api/placeholder/600/400',
    demoUrl: 'https://collaborate.example.com',
    featured: false,
    completedDate: '2023-07-08',
  },
  {
    id: '5',
    title: 'Career Analytics Dashboard',
    description: 'Comprehensive analytics platform tracking job market trends, salary insights, and career progression patterns with beautiful data visualizations.',
    technologies: ['D3.js', 'React', 'Python', 'Pandas', 'PostgreSQL', 'Recharts'],
    category: 'Data Visualization',
    imageUrl: '/api/placeholder/600/400',
    demoUrl: 'https://analytics.career.com',
    featured: false,
    completedDate: '2023-05-22',
  },
  {
    id: '6',
    title: 'Mobile Resume Builder',
    description: 'Progressive Web App for creating and editing resumes on mobile devices with offline capabilities and cloud synchronization.',
    technologies: ['React Native', 'PWA', 'TypeScript', 'Redux', 'Service Workers'],
    category: 'Mobile',
    imageUrl: '/api/placeholder/600/400',
    demoUrl: 'https://mobile.resume.app',
    featured: false,
    completedDate: '2023-03-15',
  },
];

// Available categories and technologies for filtering
const categories = ['All', 'Web Application', 'AI/ML', 'Automation', 'Data Visualization', 'Mobile'];
const allTechnologies = Array.from(
  new Set(sampleProjects.flatMap(project => project.technologies))
).sort();

interface ProjectSectionProps {
  className?: string;
  showAddProject?: boolean;
}

const ProjectSection: React.FC<ProjectSectionProps> = ({ 
  className = '', 
  showAddProject = false 
}) => {
  const [projects] = useState<Project[]>(sampleProjects);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedTechnologies, setSelectedTechnologies] = useState<string[]>([]);
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Filter and search projects
  const filteredProjects = useMemo(() => {
    return projects.filter(project => {
      // Search query filter
      const matchesSearch = searchQuery === '' || 
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.technologies.some(tech => 
          tech.toLowerCase().includes(searchQuery.toLowerCase())
        );

      // Category filter
      const matchesCategory = selectedCategory === 'All' || 
        project.category === selectedCategory;

      // Technology filter
      const matchesTechnologies = selectedTechnologies.length === 0 ||
        selectedTechnologies.every(tech => project.technologies.includes(tech));

      // Featured filter
      const matchesFeatured = !showFeaturedOnly || project.featured;

      return matchesSearch && matchesCategory && matchesTechnologies && matchesFeatured;
    });
  }, [projects, searchQuery, selectedCategory, selectedTechnologies, showFeaturedOnly]);

  const toggleTechnology = (tech: string) => {
    setSelectedTechnologies(prev => 
      prev.includes(tech) 
        ? prev.filter(t => t !== tech)
        : [...prev, tech]
    );
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('All');
    setSelectedTechnologies([]);
    setShowFeaturedOnly(false);
  };

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="text-center mb-12">
        <h2 
          className="text-4xl font-bold text-gray-900 dark:text-white mb-4"
        >
          Featured Projects
        </h2>
        <p 
          className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto"
        >
          Explore our portfolio of innovative solutions, from AI-powered applications to 
          comprehensive web platforms that drive real business value.
        </p>
      </div>

      {/* Filters */}
      <div 
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
      >
        <div className="space-y-6">
          {/* Search */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Search projects by title, description, or technology..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon="🔍"
                inputSize="lg"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={showFeaturedOnly ? 'primary' : 'outline'}
                onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
              >
                ⭐ Featured Only
              </Button>
              <Button
                variant="ghost"
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Category
            </h4>
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    selectedCategory === category
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Technology Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Technologies ({selectedTechnologies.length} selected)
            </h4>
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {allTechnologies.map(tech => (
                <button
                  key={tech}
                  onClick={() => toggleTechnology(tech)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                    selectedTechnologies.includes(tech)
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {tech}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div 
        className="text-center mb-6"
      >
        <p className="text-gray-600 dark:text-gray-400">
          Showing <span className="font-semibold">{filteredProjects.length}</span> of{' '}
          <span className="font-semibold">{projects.length}</span> projects
        </p>
      </div>

      {/* Projects Grid */}
      <AnimatePresence mode="wait">
        {filteredProjects.length === 0 ? (
          <div
            key="no-results"
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No projects found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your filters or search terms
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Clear All Filters
            </Button>
          </div>
        ) : (
          <div 
            key="results"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredProjects.map((project) => (
              <div
                key={project.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
              >
                {/* Project Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-400 to-purple-600 overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
                  <div className="absolute top-4 left-4">
                    {project.featured && (
                      <span className="bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                        ⭐ Featured
                      </span>
                    )}
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <span className="text-xs bg-black bg-opacity-50 px-2 py-1 rounded">
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {project.technologies.slice(0, 4).map(tech => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 4 && (
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
                        +{project.technologies.length - 4} more
                      </span>
                    )}
                  </div>

                  {/* Project Links */}
                  <div className="flex gap-2">
                    {project.demoUrl && (
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => window.open(project.demoUrl, '_blank')}
                        leftIcon="🚀"
                      >
                        Demo
                      </Button>
                    )}
                    {project.githubUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(project.githubUrl, '_blank')}
                        leftIcon="🐙"
                      >
                        Code
                      </Button>
                    )}
                  </div>

                  {/* Completion Date */}
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Completed: {new Date(project.completedDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </AnimatePresence>

      {/* Add Project Section (if enabled) */}
      {showAddProject && (
        <div 
          className="mt-12 text-center"
        >
          <div className="bg-gradient-to-r from-primary-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              Have a Project Idea?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              We're always looking for interesting challenges and innovative projects. 
              Let's discuss how we can bring your vision to life.
            </p>
            <Button
              variant="primary"
              size="lg"
              leftIcon="💡"
            >
              Discuss Your Project
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectSection;