import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { GripVertical, Grid, Zap, Accessibility } from 'lucide-react';

// Enhanced drag handle with better visual feedback and accessibility
export const EnhancedDragHandle = forwardRef<
  HTMLDivElement,
  {
    isDragging?: boolean;
    className?: string;
    section?: { title: string; id: string };
    onKeyboardMove?: (direction: 'up' | 'down') => void;
  }
>(({ isDragging = false, className = '', section, onKeyboardMove }, ref) => {
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      onKeyboardMove?.('up');
    } else if (e.key === 'ArrowDown' && (e.ctrl<PERSON><PERSON> || e.metaKey)) {
      e.preventDefault();
      onKeyboardMove?.('down');
    }
  }, [onKeyboardMove]);

  return (
    <motion.div
      ref={ref}
      className={`
        p-3 hover:bg-gray-100 rounded-lg cursor-grab active:cursor-grabbing 
        transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      animate={{
        rotateZ: isDragging ? [0, 2, -2, 0] : 0,
        scale: isDragging ? 1.1 : 1,
        backgroundColor: isDragging ? 'rgba(59, 130, 246, 0.1)' : 'transparent'
      }}
      transition={{ 
        duration: isDragging ? 0.3 : 0.2,
        repeat: isDragging ? Infinity : 0,
        repeatType: 'reverse'
      }}
      role="button"
      tabIndex={0}
      aria-label={`Drag handle for ${section?.title || 'section'}. Use Ctrl+Arrow keys to reorder`}
      onKeyDown={handleKeyDown}
    >
      <div className="flex flex-col items-center space-y-1">
        <div className="flex space-x-1">
          {[1, 2].map(i => (
            <div
              key={i}
              className={`w-1 h-1 rounded-full transition-colors ${
                isDragging ? 'bg-blue-500' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
        <div className="flex space-x-1">
          {[1, 2].map(i => (
            <div
              key={i}
              className={`w-1 h-1 rounded-full transition-colors ${
                isDragging ? 'bg-blue-500' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
        <div className="flex space-x-1">
          {[1, 2].map(i => (
            <div
              key={i}
              className={`w-1 h-1 rounded-full transition-colors ${
                isDragging ? 'bg-blue-500' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
});

EnhancedDragHandle.displayName = 'EnhancedDragHandle';

// Enhanced drop zone indicator with snap-to-grid visual feedback
export const EnhancedDropZoneIndicator: React.FC<{
  isActive: boolean;
  position: 'above' | 'below' | 'between';
  snapToGrid?: boolean;
  intensity?: 'low' | 'medium' | 'high';
}> = ({ isActive, position, snapToGrid = false, intensity = 'medium' }) => {
  if (!isActive) return null;

  const getIntensityClasses = () => {
    switch (intensity) {
      case 'low':
        return 'border-blue-300 bg-blue-50/50';
      case 'high':
        return 'border-blue-500 bg-blue-100 shadow-lg';
      default:
        return 'border-blue-400 bg-blue-50';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, height: 0, scale: 0.8 }}
      animate={{
        opacity: 1,
        height: 'auto',
        scale: snapToGrid ? [1, 1.05, 1] : 1
      }}
      exit={{ opacity: 0, height: 0, scale: 0.8 }}
      transition={{
        duration: 0.3,
        ease: 'easeOut',
        scale: {
          repeat: snapToGrid ? Infinity : 0,
          repeatType: 'reverse',
          duration: 1
        }
      }}
      className={`
        flex items-center justify-center py-3 px-4 mx-2 rounded-lg border-2 border-dashed
        ${position === 'above' ? 'mb-2' : position === 'below' ? 'mt-2' : 'my-2'}
        ${getIntensityClasses()}
        ${snapToGrid ? 'ring-2 ring-green-200' : ''}
      `}
    >
      <div className="flex items-center space-x-2 text-blue-600">
        {snapToGrid && <Grid size={16} className="text-green-600" />}
        <div className="flex space-x-1">
          {[1, 2, 3].map(i => (
            <motion.div
              key={i}
              className="w-1 h-1 bg-blue-500 rounded-full"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
        <span className="text-xs font-medium">
          {snapToGrid ? 'Snap to grid' : 'Drop here'}
        </span>
      </div>
    </motion.div>
  );
};

// Enhanced section card with improved visual states
export const EnhancedSectionCard: React.FC<{
  children: React.ReactNode;
  isDragging?: boolean;
  isVisible?: boolean;
  isSelected?: boolean;
  isPreview?: boolean;
  performance?: 'smooth' | 'fast';
  className?: string;
  sectionType?: string;
}> = ({
  children,
  isDragging = false,
  isVisible = true,
  isSelected = false,
  isPreview = false,
  performance = 'smooth',
  className = '',
  sectionType = 'default'
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Performance optimization based on settings
  const shouldAnimate = performance === 'smooth';
  
  const getCardStyles = () => {
    let styles = 'rounded-xl border-2 transition-all duration-200 bg-white';
    
    if (isDragging) {
      styles += ' border-blue-400 shadow-xl z-20 scale-105';
    } else if (isSelected) {
      styles += ' border-blue-300 shadow-lg ring-2 ring-blue-100';
    } else if (isHovered) {
      styles += ' border-gray-300 shadow-md';
    } else {
      styles += ' border-gray-200';
    }
    
    if (!isVisible) {
      styles += ' opacity-60 bg-gray-50';
    }
    
    return styles;
  };

  const cardVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95,
      transition: { duration: 0.2 }
    },
    hover: shouldAnimate ? {
      y: -2,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    } : {},
    tap: shouldAnimate ? { scale: 0.98 } : {},
    drag: {
      scale: 1.05,
      rotate: [0, 1, -1],
      boxShadow: '0 20px 40px -10px rgba(0, 0, 0, 0.2)',
      zIndex: 20
    }
  };

  return (
    <motion.div
      className={`${getCardStyles()} ${className}`}
      variants={shouldAnimate ? cardVariants : undefined}
      initial={shouldAnimate ? 'initial' : undefined}
      animate={shouldAnimate ? 'animate' : undefined}
      exit={shouldAnimate ? 'exit' : undefined}
      whileHover={shouldAnimate ? 'hover' : undefined}
      whileTap={shouldAnimate ? 'tap' : undefined}
      whileDrag={shouldAnimate ? 'drag' : undefined}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      data-section-type={sectionType}
      role="article"
      aria-label={`${sectionType} section`}
    >
      {children}
    </motion.div>
  );
};

// Enhanced mobile touch feedback
export const TouchFeedback: React.FC<{
  isActive: boolean;
  children: React.ReactNode;
  onTouchStart?: (e: React.TouchEvent) => void;
  onTouchMove?: (e: React.TouchEvent) => void;
  onTouchEnd?: (e: React.TouchEvent) => void;
}> = ({ isActive, children, onTouchStart, onTouchMove, onTouchEnd }) => {
  const [touchPosition, setTouchPosition] = useState({ x: 0, y: 0 });
  const [showRipple, setShowRipple] = useState(false);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const rect = e.currentTarget.getBoundingClientRect();
    setTouchPosition({
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top
    });
    setShowRipple(true);
    onTouchStart?.(e);
  }, [onTouchStart]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    setShowRipple(false);
    onTouchEnd?.(e);
  }, [onTouchEnd]);

  return (
    <div
      className={`relative overflow-hidden touch-manipulation select-none ${
        isActive ? 'active-touch' : ''
      }`}
      onTouchStart={handleTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {children}
      
      {showRipple && (
        <motion.div
          className="absolute rounded-full bg-blue-400/20 pointer-events-none"
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6 }}
          style={{
            left: touchPosition.x - 20,
            top: touchPosition.y - 20,
            width: 40,
            height: 40
          }}
        />
      )}
    </div>
  );
};

// Enhanced snap-to-grid system
export const SnapToGrid = {
  // Visual grid overlay
  GridOverlay: ({ isVisible, spacing = 20 }: { isVisible: boolean; spacing?: number }) => {
    if (!isVisible) return null;

    return (
      <div 
        className="absolute inset-0 pointer-events-none opacity-20 z-10"
        style={{
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: `${spacing}px ${spacing}px`
        }}
      />
    );
  },

  // Calculate snap position
  calculateSnapPosition: (
    mouseY: number,
    containerRef: React.RefObject<HTMLElement>,
    gridSize: number = 20
  ) => {
    if (!containerRef.current) return mouseY;

    const containerRect = containerRef.current.getBoundingClientRect();
    const relativeY = mouseY - containerRect.top;
    const snappedY = Math.round(relativeY / gridSize) * gridSize;
    
    return snappedY + containerRect.top;
  },

  // Grid snapping utilities
  snapValue: (value: number, gridSize: number = 20) => {
    return Math.round(value / gridSize) * gridSize;
  }
};

// Enhanced accessibility features
export const AccessibilityEnhancements = {
  // Screen reader announcements
  ScreenReaderAnnouncer: ({ message }: { message: string }) => {
    const [announcement, setAnnouncement] = useState('');

    useEffect(() => {
      if (message) {
        setAnnouncement(message);
        const timer = setTimeout(() => setAnnouncement(''), 1000);
        return () => clearTimeout(timer);
      }
    }, [message]);

    return (
      <div
        className="sr-only"
        aria-live="assertive"
        aria-atomic="true"
      >
        {announcement}
      </div>
    );
  },

  // Keyboard navigation hints
  KeyboardHints: ({ isVisible }: { isVisible: boolean }) => {
    if (!isVisible) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-4 right-4 bg-gray-900 text-white text-xs p-3 rounded-lg shadow-lg z-50"
      >
        <div className="space-y-1">
          <div><kbd className="bg-gray-700 px-1 rounded">Ctrl</kbd> + <kbd className="bg-gray-700 px-1 rounded">↑</kbd> Move up</div>
          <div><kbd className="bg-gray-700 px-1 rounded">Ctrl</kbd> + <kbd className="bg-gray-700 px-1 rounded">↓</kbd> Move down</div>
          <div><kbd className="bg-gray-700 px-1 rounded">Tab</kbd> Navigate sections</div>
        </div>
      </motion.div>
    );
  },

  // Focus management
  useFocusManagement: () => {
    const focusRef = useRef<HTMLElement | null>(null);

    const setFocus = useCallback((element: HTMLElement | null) => {
      focusRef.current = element;
      if (element) {
        element.focus();
      }
    }, []);

    const restoreFocus = useCallback(() => {
      if (focusRef.current) {
        focusRef.current.focus();
      }
    }, []);

    return { setFocus, restoreFocus };
  }
};

// Performance monitoring and optimization
export const PerformanceEnhancements = {
  // Lazy loading for heavy sections
  LazySection: ({ 
    children, 
    threshold = 0.1 
  }: { 
    children: React.ReactNode; 
    threshold?: number; 
  }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [hasLoaded, setHasLoaded] = useState(false);
    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            setHasLoaded(true);
            observer.disconnect();
          }
        },
        { threshold }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => observer.disconnect();
    }, [threshold]);

    return (
      <div ref={ref} className="min-h-[100px]">
        {(isVisible || hasLoaded) ? children : (
          <div className="flex items-center justify-center h-24 bg-gray-50 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>
    );
  },

  // Performance metrics
  usePerformanceMetrics: () => {
    const [metrics, setMetrics] = useState({
      dragStartTime: 0,
      dragEndTime: 0,
      renderCount: 0
    });

    const trackDragStart = useCallback(() => {
      setMetrics(prev => ({
        ...prev,
        dragStartTime: performance.now()
      }));
    }, []);

    const trackDragEnd = useCallback(() => {
      setMetrics(prev => ({
        ...prev,
        dragEndTime: performance.now()
      }));
    }, []);

    const trackRender = useCallback(() => {
      setMetrics(prev => ({
        ...prev,
        renderCount: prev.renderCount + 1
      }));
    }, []);

    return {
      metrics,
      trackDragStart,
      trackDragEnd,
      trackRender
    };
  }
};

// Collision detection system
export const CollisionDetection = {
  // Detect overlapping elements
  detectCollision: (
    draggingRect: DOMRect,
    targetRect: DOMRect,
    threshold: number = 0.5
  ): boolean => {
    const overlapX = Math.max(0, Math.min(draggingRect.right, targetRect.right) - Math.max(draggingRect.left, targetRect.left));
    const overlapY = Math.max(0, Math.min(draggingRect.bottom, targetRect.bottom) - Math.max(draggingRect.top, targetRect.top));
    
    const draggingArea = draggingRect.width * draggingRect.height;
    const overlapArea = overlapX * overlapY;
    
    return (overlapArea / draggingArea) > threshold;
  },

  // Auto-spacing calculation
  calculateAutoSpacing: (
    elements: DOMRect[],
    containerHeight: number,
    minSpacing: number = 16
  ): number[] => {
    const totalElementHeight = elements.reduce((sum, rect) => sum + rect.height, 0);
    const availableSpace = containerHeight - totalElementHeight;
    const spacingCount = elements.length + 1;
    const optimalSpacing = Math.max(minSpacing, availableSpace / spacingCount);
    
    return Array(spacingCount).fill(optimalSpacing);
  }
};

// Export enhanced drag and drop context
export const EnhancedDragDropProvider: React.FC<{
  children: React.ReactNode;
  options?: {
    snapToGrid?: boolean;
    showGrid?: boolean;
    gridSize?: number;
    autoSpacing?: boolean;
    performance?: 'smooth' | 'fast';
    accessibility?: boolean;
  };
}> = ({ 
  children, 
  options = {
    snapToGrid: true,
    showGrid: false,
    gridSize: 20,
    autoSpacing: true,
    performance: 'smooth',
    accessibility: true
  }
}) => {
  const [isGridVisible, setIsGridVisible] = useState(options.showGrid || false);
  const [showKeyboardHints, setShowKeyboardHints] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Toggle grid visibility
  const toggleGrid = useCallback(() => {
    setIsGridVisible(prev => !prev);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'g' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        toggleGrid();
      }
      if (e.key === 'h' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        setShowKeyboardHints(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleGrid]);

  return (
    <div ref={containerRef} className="relative">
      {options.snapToGrid && (
        <SnapToGrid.GridOverlay 
          isVisible={isGridVisible} 
          spacing={options.gridSize} 
        />
      )}
      
      {children}
      
      {options.accessibility && (
        <AccessibilityEnhancements.KeyboardHints 
          isVisible={showKeyboardHints} 
        />
      )}
    </div>
  );
};