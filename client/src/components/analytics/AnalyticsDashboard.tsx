import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import MetricsCard from './MetricsCard';
import ChartContainer from './ChartContainer';
import SuccessPatterns from './SuccessPatterns';
import RecommendationPanel from './RecommendationPanel';

interface AnalyticsDashboardProps {
  timeRange?: string;
}

export default function AnalyticsDashboard({ timeRange = '30 days' }: AnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  const { token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchAnalytics();
    fetchRecommendations();
  }, [selectedTimeRange, token]);

  const fetchAnalytics = async () => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics/dashboard?timeRange=${selectedTimeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data.analytics);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchRecommendations = async () => {
    if (!token) return;

    try {
      const response = await fetch('/api/analytics/recommendations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations);
      }
    } catch (err) {
      console.error('Failed to fetch recommendations:', err);
    }
  };

  const timeRangeOptions = [
    '7 days', '30 days', '90 days', '6 months', '1 year', 'all'
  ];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600 text-center">
          <h3 className="text-lg font-medium mb-2">Error Loading Analytics</h3>
          <p>{error}</p>
          <button 
            onClick={fetchAnalytics}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-gray-500 text-center">
          <h3 className="text-lg font-medium mb-2">No Analytics Data</h3>
          <p>Start applying to jobs to see your analytics.</p>
        </div>
      </div>
    );
  }

  const { applicationStats, resumePerformance, emailMetrics, successPatterns } = analytics;

  return (
    <div className="space-y-6">
      {/* Header with Time Range Selector */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Time Range:</label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {timeRangeOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricsCard
            title="Total Applications"
            value={applicationStats.total_applications}
            trend={applicationStats.total_applications > 0 ? 'positive' : 'neutral'}
            icon="📊"
          />
          <MetricsCard
            title="Success Rate"
            value={`${applicationStats.success_rate?.toFixed(1)}%`}
            trend={applicationStats.success_rate > 15 ? 'positive' : applicationStats.success_rate > 5 ? 'neutral' : 'negative'}
            icon="🎯"
          />
          <MetricsCard
            title="Interviews"
            value={applicationStats.interviews}
            trend={applicationStats.interviews > 0 ? 'positive' : 'neutral'}
            icon="🤝"
          />
          <MetricsCard
            title="Offers"
            value={applicationStats.offers}
            trend={applicationStats.offers > 0 ? 'positive' : 'neutral'}
            icon="🎉"
          />
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer
          title="Application Status Distribution"
          data={[
            { name: 'Pending', value: applicationStats.pending, color: '#fbbf24' },
            { name: 'Interviews', value: applicationStats.interviews, color: '#3b82f6' },
            { name: 'Offers', value: applicationStats.offers, color: '#10b981' },
            { name: 'Rejected', value: applicationStats.rejections, color: '#ef4444' }
          ]}
          type="pie"
        />

        <ChartContainer
          title="Resume Performance"
          data={resumePerformance.map((resume: any) => ({
            name: resume.title,
            responseRate: resume.response_rate || 0,
            applications: resume.applications || 0
          }))}
          type="bar"
        />
      </div>

      {/* Success Patterns and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SuccessPatterns patterns={successPatterns} />
        <RecommendationPanel recommendations={recommendations} />
      </div>

      {/* Detailed Email Metrics */}
      {emailMetrics.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Email Campaign Performance</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Campaign Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Open Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reply Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {emailMetrics.map((metric: any, index: number) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {metric.campaign_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {metric.total_sent}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {metric.open_rate?.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {metric.reply_rate?.toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}