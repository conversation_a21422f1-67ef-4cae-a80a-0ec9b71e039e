import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface ChartContainerProps {
  title: string;
  data: any[];
  type: 'pie' | 'bar' | 'line';
  height?: number;
}

export default function ChartContainer({ title, data, type, height = 300 }: ChartContainerProps) {
  const renderChart = () => {
    switch (type) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color || `#${Math.floor(Math.random()*16777215).toString(16)}`} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );
      
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="responseRate" fill="#3b82f6" name="Response Rate %" />
              <Bar dataKey="applications" fill="#10b981" name="Applications" />
            </BarChart>
          </ResponsiveContainer>
        );
      
      default:
        return <div className="text-gray-500">Chart type not supported</div>;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      {data.length > 0 ? (
        renderChart()
      ) : (
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <div className="text-4xl mb-2">📊</div>
            <p>No data available</p>
          </div>
        </div>
      )}
    </div>
  );
}