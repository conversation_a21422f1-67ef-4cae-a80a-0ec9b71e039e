interface MetricsCardProps {
  title: string;
  value: string | number;
  trend: 'positive' | 'negative' | 'neutral';
  icon?: string;
  subtitle?: string;
}

export default function MetricsCard({ title, value, trend, icon, subtitle }: MetricsCardProps) {
  const getTrendColor = () => {
    switch (trend) {
      case 'positive':
        return 'text-green-600 bg-green-100';
      case 'negative':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'positive':
        return '↗️';
      case 'negative':
        return '↘️';
      default:
        return '→';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            {icon && <span className="text-2xl">{icon}</span>}
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              {title}
            </h3>
          </div>
          
          <div className="mt-2">
            <div className="text-3xl font-bold text-gray-900">
              {value}
            </div>
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTrendColor()}`}>
          <span className="mr-1">{getTrendIcon()}</span>
          {trend}
        </div>
      </div>
    </div>
  );
}