import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface AnalyticsData {
  applicationStats: {
    totalApplications: number;
    successRate: number;
    averageResponseTime: number;
    pendingApplications: number;
    interviewsScheduled: number;
  };
  jobMarketInsights: {
    topInDemandSkills: Array<{skill: string; demand: number; growth: number}>;
    competitionLevel: string;
    marketOutlook: string;
  };
  resumePerformance: {
    averageATSScore: number;
    keywordOptimization: number;
    performanceByResume: Array<{
      resumeId: number;
      title: string;
      applications: number;
      successRate: number;
    }>;
  };
  weeklyTrends: {
    weeklyData: Array<{
      week: string;
      applications: number;
      responses: number;
      interviews: number;
    }>;
    totalTrend: string;
  };
}

export default function AdvancedAnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState('overview');
  
  const { token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchAnalytics();
  }, [token]);

  const fetchAnalytics = async () => {
    if (!token) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/analytics/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data.analytics);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error loading analytics: {error}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">No analytics data available</p>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'market', name: 'Market Insights', icon: '📈' },
    { id: 'resume', name: 'Resume Performance', icon: '📄' },
    { id: 'trends', name: 'Trends', icon: '📉' }
  ];

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id)}
              className={`${
                selectedTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {selectedTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Application Overview</h3>
            
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-900">
                  {analytics.applicationStats.totalApplications}
                </div>
                <div className="text-sm text-blue-700">Total Applications</div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-900">
                  {analytics.applicationStats.successRate.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">Success Rate</div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-900">
                  {analytics.applicationStats.interviewsScheduled}
                </div>
                <div className="text-sm text-purple-700">Interviews Scheduled</div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-orange-900">
                  {analytics.applicationStats.averageResponseTime}d
                </div>
                <div className="text-sm text-orange-700">Avg Response Time</div>
              </div>
            </div>

            {/* Quick Insights */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Quick Insights</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• You have {analytics.applicationStats.pendingApplications} applications pending response</li>
                <li>• Your success rate is {analytics.applicationStats.successRate > 15 ? 'above' : 'below'} average (15%)</li>
                <li>• Response time is {analytics.applicationStats.averageResponseTime > 7 ? 'slower' : 'faster'} than typical</li>
              </ul>
            </div>
          </div>
        )}

        {selectedTab === 'market' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Job Market Insights</h3>
            
            {/* Market Outlook */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Market Outlook</h4>
                <div className="text-2xl font-bold text-blue-800 capitalize">
                  {analytics.jobMarketInsights.marketOutlook}
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  Overall job market conditions
                </p>
              </div>
              
              <div className="bg-yellow-50 rounded-lg p-4">
                <h4 className="font-medium text-yellow-900 mb-2">Competition Level</h4>
                <div className="text-2xl font-bold text-yellow-800 capitalize">
                  {analytics.jobMarketInsights.competitionLevel}
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  For your target roles
                </p>
              </div>
            </div>

            {/* Top Skills in Demand */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Top Skills in Demand</h4>
              <div className="space-y-2">
                {analytics.jobMarketInsights.topInDemandSkills.slice(0, 5).map((skill, index) => (
                  <div key={skill.skill} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm font-medium text-gray-900">#{index + 1}</div>
                      <div className="text-sm text-gray-900">{skill.skill}</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-green-600">+{skill.growth.toFixed(1)}%</div>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{width: `${Math.min(100, skill.demand * 10)}%`}}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'resume' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Resume Performance</h3>
            
            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-green-50 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">ATS Compatibility</h4>
                <div className="text-3xl font-bold text-green-800">
                  {analytics.resumePerformance.averageATSScore.toFixed(0)}%
                </div>
                <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{width: `${analytics.resumePerformance.averageATSScore}%`}}
                  ></div>
                </div>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Keyword Optimization</h4>
                <div className="text-3xl font-bold text-blue-800">
                  {analytics.resumePerformance.keywordOptimization.toFixed(0)}%
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{width: `${analytics.resumePerformance.keywordOptimization}%`}}
                  ></div>
                </div>
              </div>
            </div>

            {/* Resume Performance by Version */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Performance by Resume</h4>
              <div className="space-y-3">
                {analytics.resumePerformance.performanceByResume.map((resume) => (
                  <div key={resume.resumeId} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="font-medium text-gray-900">{resume.title}</h5>
                      <span className="text-sm text-gray-500">{resume.applications} applications</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-sm text-gray-600">
                        Success Rate: <span className="font-medium text-green-600">{resume.successRate.toFixed(1)}%</span>
                      </div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{width: `${Math.min(100, resume.successRate)}%`}}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'trends' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Application Trends</h3>
            
            {/* Trend Direction */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Overall Trend</h4>
              <div className={`text-2xl font-bold ${
                analytics.weeklyTrends.totalTrend === 'increasing' ? 'text-green-600' : 
                analytics.weeklyTrends.totalTrend === 'decreasing' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {analytics.weeklyTrends.totalTrend === 'increasing' ? '📈 Increasing' :
                 analytics.weeklyTrends.totalTrend === 'decreasing' ? '📉 Decreasing' : '📊 Stable'}
              </div>
              <p className="text-sm text-gray-600 mt-1">Application activity over the last 12 weeks</p>
            </div>

            {/* Weekly Data */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Weekly Breakdown</h4>
              <div className="space-y-2">
                {analytics.weeklyTrends.weeklyData.slice(-6).map((week) => (
                  <div key={week.week} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-900">{week.week}</div>
                    <div className="flex space-x-4 text-sm">
                      <span className="text-blue-600">{week.applications} apps</span>
                      <span className="text-green-600">{week.responses} responses</span>
                      <span className="text-purple-600">{week.interviews} interviews</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}