interface SuccessPatternsProps {
  patterns: any[];
}

export default function SuccessPatterns({ patterns }: SuccessPatternsProps) {
  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'timing':
        return '⏰';
      case 'company_size':
        return '🏢';
      case 'skills':
        return '🛠️';
      case 'location':
        return '📍';
      default:
        return '📊';
    }
  };

  const getPatternTypeLabel = (type: string) => {
    switch (type) {
      case 'timing':
        return 'Application Timing';
      case 'company_size':
        return 'Company Size';
      case 'skills':
        return 'Skills';
      case 'location':
        return 'Location';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Success Patterns</h3>
      
      {patterns.length > 0 ? (
        <div className="space-y-4">
          {patterns.map((pattern, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">{getPatternIcon(pattern.pattern_type)}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      {getPatternTypeLabel(pattern.pattern_type)}
                    </h4>
                    <span className="text-xs text-gray-500">
                      {pattern.frequency} occurrences
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {pattern.description || 'No description available'}
                  </p>
                  {pattern.success_rate && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(pattern.success_rate, 100)}%` }}
                          ></div>
                        </div>
                        <span className="text-xs font-medium text-green-600">
                          {pattern.success_rate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-8">
          <div className="text-4xl mb-2">🔍</div>
          <p className="text-sm">No patterns identified yet</p>
          <p className="text-xs text-gray-400 mt-1">
            Apply to more jobs to discover success patterns
          </p>
        </div>
      )}
    </div>
  );
}