interface RecommendationPanelProps {
  recommendations: any[];
}

export default function RecommendationPanel({ recommendations }: RecommendationPanelProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      case 'low':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return '🔴';
      case 'medium':
        return '🟡';
      case 'low':
        return '🟢';
      default:
        return '⚪';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'resume_optimization':
        return '📝';
      case 'targeting_optimization':
        return '🎯';
      case 'communication_optimization':
        return '💬';
      case 'skill_development':
        return '📚';
      case 'networking':
        return '🤝';
      default:
        return '💡';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
      
      {recommendations.length > 0 ? (
        <div className="space-y-4">
          {recommendations.map((recommendation, index) => (
            <div 
              key={index} 
              className={`border rounded-lg p-4 ${getPriorityColor(recommendation.priority)}`}
            >
              <div className="flex items-start space-x-3">
                <div className="flex items-center space-x-1">
                  <span className="text-lg">{getTypeIcon(recommendation.type)}</span>
                  <span className="text-sm">{getPriorityIcon(recommendation.priority)}</span>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-semibold text-gray-900">
                      {recommendation.title}
                    </h4>
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      recommendation.priority === 'high' 
                        ? 'bg-red-100 text-red-800'
                        : recommendation.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {recommendation.priority} priority
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mt-1">
                    {recommendation.description}
                  </p>
                  
                  {recommendation.actionItems && recommendation.actionItems.length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-xs font-medium text-gray-700 mb-2">Action Items:</h5>
                      <ul className="space-y-1">
                        {recommendation.actionItems.map((item: string, itemIndex: number) => (
                          <li key={itemIndex} className="flex items-start space-x-2">
                            <span className="text-xs mt-1">•</span>
                            <span className="text-xs text-gray-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {recommendation.estimatedImpact && (
                    <div className="mt-3 text-xs text-gray-500">
                      Estimated Impact: {recommendation.estimatedImpact}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-8">
          <div className="text-4xl mb-2">✅</div>
          <p className="text-sm">No recommendations at this time</p>
          <p className="text-xs text-gray-400 mt-1">
            Your job search strategy is performing well!
          </p>
        </div>
      )}
    </div>
  );
}