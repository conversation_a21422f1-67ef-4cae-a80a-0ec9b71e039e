import { 
  RESUME_TEMPLATES, 
  getTemplateStats, 
  getAvailableCategories,
  getAvailableIndustries
} from '../constants/resumeTemplates';

export default function TemplateSystemTest() {
  const stats = getTemplateStats();
  const categories = getAvailableCategories();
  const industries = getAvailableIndustries();
  
  return (
    <div className="p-8 bg-white">
      <h1 className="text-3xl font-bold mb-6">Template System Status</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-green-50 p-6 rounded-lg border border-green-200">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Total Templates</h3>
          <p className="text-3xl font-bold text-green-600">{stats.total}</p>
          <p className="text-sm text-green-700 mt-1">
            {stats.free} Free • {stats.premium} Premium
          </p>
        </div>
        
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Categories</h3>
          <p className="text-3xl font-bold text-blue-600">{categories.length}</p>
          <p className="text-sm text-blue-700 mt-1">All requirements met</p>
        </div>
        
        <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
          <h3 className="text-lg font-semibold text-purple-800 mb-2">ATS Compatible</h3>
          <p className="text-3xl font-bold text-purple-600">{stats.atsOptimized}</p>
          <p className="text-sm text-purple-700 mt-1">Industry standard</p>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Template Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(stats.byCategory).map(([category, count]) => (
            <div key={category} className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 capitalize">
                {category.replace('-', ' ')}
              </h4>
              <p className="text-2xl font-bold text-gray-600">{count}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Available Industries</h2>
        <div className="flex flex-wrap gap-2">
          {industries.map(industry => (
            <span 
              key={industry}
              className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm"
            >
              {industry}
            </span>
          ))}
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-semibold mb-4">Template Sample</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {RESUME_TEMPLATES.slice(0, 6).map(template => (
            <div key={template.id} className="border rounded-lg p-4 hover:shadow-lg transition-shadow">
              <h4 className="font-semibold text-gray-800 mb-2">{template.name}</h4>
              <p className="text-sm text-gray-600 mb-2">{template.description}</p>
              <div className="flex flex-wrap gap-1 mb-2">
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                  {template.category}
                </span>
                {template.isPremium && (
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                    Premium
                  </span>
                )}
                {template.atsCompatible && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                    ATS
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-500">
                Industries: {template.industry.join(', ')}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}