import React from 'react';
import type { ResumeTemplate, TemplateSectionConfig } from '../../../constants/resumeTemplates';

interface ExperienceLevelTemplateProps {
  template: ResumeTemplate;
  data?: any;
  colorScheme?: string;
  className?: string;
}

interface TemplateData {
  personalInfo?: {
    name?: string;
    title?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    github?: string;
    website?: string;
  };
  summary?: string;
  experience?: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate: string;
    description: string;
    achievements?: string[];
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    graduationDate: string;
    gpa?: string;
    honors?: string;
  }>;
  skills?: string[];
  projects?: Array<{
    name: string;
    description: string;
    technologies?: string[];
    link?: string;
  }>;
  internships?: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate: string;
    description: string;
  }>;
  achievements?: string[];
}

const getDefaultDataByLevel = (level: string): TemplateData => {
  switch (level) {
    case 'entry-level':
      return {
        personalInfo: {
          name: '<PERSON>',
          title: 'Recent Computer Science Graduate',
          email: '<EMAIL>',
          phone: '(*************',
          location: 'Seattle, WA',
          linkedin: 'linkedin.com/in/emilyjohnson',
          github: 'github.com/emilyjohnson',
          website: 'emilyjohnson.dev'
        },
        summary: 'Motivated Computer Science graduate with strong programming fundamentals and passion for software development. Experienced in full-stack web development through academic projects and internships.',
        experience: [
          {
            company: 'Tech Startup Inc.',
            title: 'Software Development Intern',
            startDate: 'Summer 2023',
            endDate: 'Summer 2023',
            description: 'Developed web applications using React and Node.js',
            achievements: [
              'Built responsive web components used by 1,000+ users',
              'Collaborated with 5-person development team using Agile methodologies',
              'Improved application performance by 20% through code optimization'
            ]
          }
        ],
        education: [
          {
            institution: 'University of Washington',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            graduationDate: '2024',
            gpa: '3.7',
            honors: 'Magna Cum Laude'
          }
        ],
        skills: [
          'JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'Git', 'HTML/CSS', 'Java'
        ],
        projects: [
          {
            name: 'E-commerce Web App',
            description: 'Full-stack e-commerce application with user authentication and payment processing',
            technologies: ['React', 'Node.js', 'MongoDB', 'Stripe API']
          },
          {
            name: 'Task Management Mobile App',
            description: 'React Native mobile app for personal task and project management',
            technologies: ['React Native', 'Firebase', 'TypeScript']
          }
        ]
      };

    case 'mid-career':
      return {
        personalInfo: {
          name: 'David Chen',
          title: 'Senior Marketing Manager',
          email: '<EMAIL>',
          phone: '(*************',
          location: 'Austin, TX',
          linkedin: 'linkedin.com/in/davidchen',
          website: 'davidchen.marketing'
        },
        summary: 'Results-driven marketing professional with 8+ years of experience leading cross-functional teams and driving revenue growth. Proven track record of developing successful marketing strategies for B2B and B2C markets.',
        experience: [
          {
            company: 'GrowthTech Solutions',
            title: 'Senior Marketing Manager',
            startDate: '2021',
            endDate: 'Present',
            description: 'Lead marketing strategy and execution for SaaS platform serving enterprise clients',
            achievements: [
              'Increased lead generation by 150% through integrated digital marketing campaigns',
              'Managed marketing budget of $2M+ with 25% year-over-year growth in ROI',
              'Led team of 6 marketing professionals across content, digital, and events',
              'Launched successful product rebrand resulting in 40% increase in brand recognition'
            ]
          },
          {
            company: 'InnovateCorp',
            title: 'Marketing Manager',
            startDate: '2019',
            endDate: '2021',
            description: 'Developed and executed marketing strategies for emerging technology products',
            achievements: [
              'Drove 200% increase in website traffic through SEO and content marketing',
              'Launched 3 successful product launches generating $5M+ in new revenue',
              'Built marketing automation workflows improving lead nurturing by 60%'
            ]
          }
        ],
        education: [
          {
            institution: 'University of Texas at Austin',
            degree: 'Master of Business Administration',
            field: 'Marketing',
            graduationDate: '2018'
          },
          {
            institution: 'Rice University',
            degree: 'Bachelor of Arts',
            field: 'Communications',
            graduationDate: '2016'
          }
        ],
        skills: [
          'Digital Marketing', 'Marketing Strategy', 'Team Leadership', 'Budget Management',
          'SEO/SEM', 'Marketing Automation', 'Analytics', 'Brand Management'
        ]
      };

    case 'senior-executive':
      return {
        personalInfo: {
          name: 'Robert Harrison',
          title: 'Chief Technology Officer',
          email: '<EMAIL>',
          phone: '(*************',
          location: 'New York, NY',
          linkedin: 'linkedin.com/in/robertharrison'
        },
        summary: 'Visionary technology executive with 15+ years of experience leading digital transformation initiatives for Fortune 500 companies. Proven track record of scaling technology teams, driving innovation, and delivering strategic business outcomes.',
        experience: [
          {
            company: 'Global Enterprise Corp',
            title: 'Chief Technology Officer',
            startDate: '2020',
            endDate: 'Present',
            description: 'Lead technology strategy and operations for $2B global enterprise with 10,000+ employees',
            achievements: [
              'Spearheaded digital transformation initiative saving $50M annually in operational costs',
              'Built and led technology organization of 200+ engineers and data scientists',
              'Implemented cloud-first architecture reducing infrastructure costs by 40%',
              'Established innovation lab resulting in 5 patents and 3 new product lines'
            ]
          },
          {
            company: 'TechLeader Inc.',
            title: 'Vice President of Engineering',
            startDate: '2017',
            endDate: '2020',
            description: 'Directed product engineering for enterprise software platform serving 1M+ users',
            achievements: [
              'Scaled engineering team from 30 to 120 professionals across 4 global offices',
              'Led platform modernization increasing system reliability to 99.9% uptime',
              'Implemented DevOps practices reducing deployment time from weeks to hours'
            ]
          }
        ],
        education: [
          {
            institution: 'Stanford University',
            degree: 'Master of Science',
            field: 'Computer Science',
            graduationDate: '2008'
          },
          {
            institution: 'MIT',
            degree: 'Bachelor of Science',
            field: 'Electrical Engineering',
            graduationDate: '2006'
          }
        ],
        skills: [
          'Technology Strategy', 'Digital Transformation', 'Team Leadership', 'Cloud Architecture',
          'Innovation Management', 'P&L Management', 'Board Relations', 'Mergers & Acquisitions'
        ]
      };

    default:
      return getDefaultDataByLevel('mid-career');
  }
};

export default function ExperienceLevelTemplate({
  template,
  data,
  colorScheme,
  className = ''
}: ExperienceLevelTemplateProps) {
  const level = template.experienceLevel || 'mid-career';
  const templateData = { ...getDefaultDataByLevel(level), ...data };
  const styles = template.styles;
  
  // Apply color scheme if provided
  const appliedColorScheme = colorScheme && template.colorSchemes 
    ? template.colorSchemes.find(cs => cs.name === colorScheme)
    : null;
  
  const colors = appliedColorScheme ? {
    primary: appliedColorScheme.primaryColor,
    secondary: appliedColorScheme.secondaryColor,
    accent: appliedColorScheme.accentColor
  } : {
    primary: styles.primaryColor,
    secondary: styles.secondaryColor,
    accent: styles.accentColor
  };

  const renderHeader = () => {
    if (level === 'senior-executive') {
      return (
        <div className="template-header executive-header text-center py-8 mb-8 border-t-4 border-b-4" 
             style={{ borderColor: colors.primary }}>
          <h1 className="text-4xl font-bold mb-3" style={{ color: colors.primary }}>
            {templateData.personalInfo?.name}
          </h1>
          <h2 className="text-2xl mb-6" style={{ color: colors.secondary }}>
            {templateData.personalInfo?.title}
          </h2>
          
          <div className="flex justify-center space-x-12 text-sm">
            {templateData.personalInfo?.email && (
              <div className="text-center">
                <div className="font-semibold" style={{ color: colors.accent }}>Email</div>
                <div style={{ color: colors.secondary }}>{templateData.personalInfo.email}</div>
              </div>
            )}
            {templateData.personalInfo?.phone && (
              <div className="text-center">
                <div className="font-semibold" style={{ color: colors.accent }}>Phone</div>
                <div style={{ color: colors.secondary }}>{templateData.personalInfo.phone}</div>
              </div>
            )}
            {templateData.personalInfo?.location && (
              <div className="text-center">
                <div className="font-semibold" style={{ color: colors.accent }}>Location</div>
                <div style={{ color: colors.secondary }}>{templateData.personalInfo.location}</div>
              </div>
            )}
          </div>
        </div>
      );
    }

    if (level === 'entry-level') {
      return (
        <div className="template-header entry-header text-center pb-6 mb-8 border-b-2" 
             style={{ borderBottomColor: colors.primary }}>
          <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>
            {templateData.personalInfo?.name}
          </h1>
          <h2 className="text-lg mb-4" style={{ color: colors.secondary }}>
            {templateData.personalInfo?.title}
          </h2>
          
          <div className="flex justify-center flex-wrap gap-4 text-sm">
            {templateData.personalInfo?.email && (
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.email}</span>
            )}
            {templateData.personalInfo?.phone && (
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.phone}</span>
            )}
            {templateData.personalInfo?.location && (
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.location}</span>
            )}
            {templateData.personalInfo?.linkedin && (
              <span style={{ color: colors.accent }}>{templateData.personalInfo.linkedin}</span>
            )}
            {templateData.personalInfo?.github && (
              <span style={{ color: colors.accent }}>{templateData.personalInfo.github}</span>
            )}
          </div>
        </div>
      );
    }

    // Mid-career layout
    return (
      <div className="template-header mid-career-header grid grid-cols-2 gap-8 pb-6 mb-8 border-b-2" 
           style={{ borderBottomColor: colors.primary }}>
        <div>
          <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>
            {templateData.personalInfo?.name}
          </h1>
          <h2 className="text-xl" style={{ color: colors.secondary }}>
            {templateData.personalInfo?.title}
          </h2>
        </div>
        
        <div className="text-right text-sm space-y-1">
          {templateData.personalInfo?.email && (
            <div style={{ color: colors.secondary }}>{templateData.personalInfo.email}</div>
          )}
          {templateData.personalInfo?.phone && (
            <div style={{ color: colors.secondary }}>{templateData.personalInfo.phone}</div>
          )}
          {templateData.personalInfo?.location && (
            <div style={{ color: colors.secondary }}>{templateData.personalInfo.location}</div>
          )}
          {templateData.personalInfo?.linkedin && (
            <div style={{ color: colors.accent }}>{templateData.personalInfo.linkedin}</div>
          )}
        </div>
      </div>
    );
  };

  const renderSection = (sectionConfig: TemplateSectionConfig) => {
    const sectionStyle = {
      marginBottom: styles.spacing === 'compact' ? '1.5rem' : styles.spacing === 'spacious' ? '2.5rem' : '2rem'
    };

    switch (sectionConfig.type) {
      case 'summary':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className={`font-bold mb-3 ${level === 'senior-executive' ? 'text-2xl text-center' : 'text-lg border-b pb-1'}`}
              style={{ 
                color: colors.primary, 
                borderBottomColor: level !== 'senior-executive' ? colors.primary : 'transparent'
              }}
            >
              {sectionConfig.title}
            </h3>
            <p 
              className={`text-sm leading-relaxed ${level === 'senior-executive' ? 'text-center text-base' : ''}`}
              style={{ color: colors.secondary }}
            >
              {templateData.summary}
            </p>
          </div>
        );

      case 'experience':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className={`font-bold mb-4 ${level === 'senior-executive' ? 'text-2xl text-center' : 'text-lg border-b pb-1'}`}
              style={{ 
                color: colors.primary,
                borderBottomColor: level !== 'senior-executive' ? colors.primary : 'transparent'
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-5">
              {templateData.experience?.map((exp, index) => (
                <div key={index} className="experience-item">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-bold text-base" style={{ color: colors.primary }}>
                        {exp.title}
                      </h4>
                      <p className="font-semibold" style={{ color: colors.secondary }}>
                        {exp.company}
                      </p>
                    </div>
                    <span 
                      className="text-sm font-bold px-3 py-1 rounded"
                      style={{ 
                        backgroundColor: colors.accent,
                        color: 'white'
                      }}
                    >
                      {exp.startDate} - {exp.endDate}
                    </span>
                  </div>
                  <p className="text-sm mb-3 text-gray-700 italic">{exp.description}</p>
                  {exp.achievements && (
                    <ul className="text-sm space-y-1">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start">
                          <span style={{ color: colors.accent }} className="mr-2 mt-1">•</span>
                          <span className="text-gray-700">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className={`font-bold mb-4 ${level === 'senior-executive' ? 'text-xl' : 'text-lg border-b pb-1'}`}
              style={{ 
                color: colors.primary,
                borderBottomColor: level !== 'senior-executive' ? colors.primary : 'transparent'
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-3">
              {templateData.education?.map((edu, index) => (
                <div key={index} className="education-item">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-bold" style={{ color: colors.primary }}>
                        {edu.degree} in {edu.field}
                      </h4>
                      <p className="font-semibold" style={{ color: colors.secondary }}>
                        {edu.institution}
                      </p>
                      {(edu.gpa || edu.honors) && (
                        <div className="text-sm text-gray-600 mt-1">
                          {edu.gpa && <span>GPA: {edu.gpa}</span>}
                          {edu.honors && edu.gpa && <span> • </span>}
                          {edu.honors && <span>{edu.honors}</span>}
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-semibold" style={{ color: colors.accent }}>
                      {edu.graduationDate}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className={`font-bold mb-4 ${level === 'senior-executive' ? 'text-xl' : 'text-lg border-b pb-1'}`}
              style={{ 
                color: colors.primary,
                borderBottomColor: level !== 'senior-executive' ? colors.primary : 'transparent'
              }}
            >
              {level === 'senior-executive' ? 'Core Competencies' : sectionConfig.title}
            </h3>
            <div className={`${level === 'entry-level' ? 'flex flex-wrap gap-2' : 'grid grid-cols-2 gap-2'}`}>
              {templateData.skills?.map((skill, index) => (
                <span
                  key={index}
                  className={`${
                    level === 'entry-level' 
                      ? 'px-3 py-1 text-sm font-medium rounded-full border'
                      : 'px-3 py-2 text-sm font-medium'
                  }`}
                  style={{ 
                    backgroundColor: level === 'entry-level' ? `${colors.accent}20` : `${colors.primary}10`,
                    color: level === 'entry-level' ? colors.accent : colors.primary,
                    borderColor: level === 'entry-level' ? colors.accent : 'transparent'
                  }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        );

      case 'projects':
        if (level !== 'entry-level') return null;
        
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 border-b pb-1"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-3">
              {templateData.projects?.map((project, index) => (
                <div key={index} className="project-item">
                  <h4 className="font-bold mb-1" style={{ color: colors.primary }}>
                    {project.name}
                  </h4>
                  <p className="text-sm text-gray-700 mb-2">{project.description}</p>
                  {project.technologies && (
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 text-xs font-medium rounded"
                          style={{ 
                            backgroundColor: `${colors.secondary}20`,
                            color: colors.secondary
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const isLayoutWithSidebar = styles.layout === 'two-column' && level !== 'senior-executive';

  return (
    <div 
      className={`template-preview template-${level} ${className}`}
      style={{
        fontFamily: styles.fontFamily,
        padding: level === 'senior-executive' 
          ? (styles.spacing === 'spacious' ? '3rem' : '2.5rem')
          : (styles.spacing === 'compact' ? '2rem' : '2.5rem')
      }}
    >
      {renderHeader()}
      
      {isLayoutWithSidebar ? (
        <div className="grid grid-cols-3 gap-8">
          {/* Main content */}
          <div className="col-span-2">
            {template.sections
              .filter(section => ['summary', 'experience'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
          
          {/* Sidebar */}
          <div className="col-span-1">
            {template.sections
              .filter(section => ['education', 'skills', 'projects'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
        </div>
      ) : (
        // Single column layout
        <div>
          {template.sections
            .filter(section => section.type !== 'header')
            .sort((a, b) => a.order - b.order)
            .map(renderSection)}
        </div>
      )}
    </div>
  );
}