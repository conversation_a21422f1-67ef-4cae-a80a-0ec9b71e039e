import React, { useState } from 'react';
import { getTemplatePlaceholderSvg } from '../../utils/templatePreviewUtils';

interface TemplatePreviewImageProps {
  src: string;
  alt: string;
  templateName: string;
  category: string;
  className?: string;
}

const TemplatePreviewImage: React.FC<TemplatePreviewImageProps> = ({
  src,
  alt,
  templateName,
  category,
  className = ''
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const placeholderSvg = getTemplatePlaceholderSvg(templateName, category);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading...</div>
        </div>
      )}
      
      {imageError ? (
        <img 
          src={placeholderSvg}
          alt={alt}
          className="w-full h-full object-cover"
        />
      ) : (
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      )}
    </div>
  );
};

export default TemplatePreviewImage;