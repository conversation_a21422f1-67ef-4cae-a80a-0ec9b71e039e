import React from 'react';
import type { ResumeTemplate, TemplateSectionConfig } from '../../../constants/resumeTemplates';

interface CreativeTemplateProps {
  template: ResumeTemplate;
  data?: any;
  colorScheme?: string;
  className?: string;
}

interface TemplateData {
  personalInfo?: {
    name?: string;
    title?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    website?: string;
    portfolio?: string;
  };
  summary?: string;
  experience?: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate: string;
    description: string;
    achievements?: string[];
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    graduationDate: string;
  }>;
  skills?: string[];
  projects?: Array<{
    name: string;
    description: string;
    technologies?: string[];
    link?: string;
  }>;
}

const DEFAULT_DATA: TemplateData = {
  personalInfo: {
    name: '<PERSON>',
    title: 'Creative Director & UX Designer',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'San Francisco, CA',
    linkedin: 'linkedin.com/in/sarah<PERSON>hnson',
    website: 'sarahjohnson.design',
    portfolio: 'behance.net/sarah<PERSON><PERSON>son'
  },
  summary: 'Award-winning creative director with 6+ years of experience in digital design and user experience. Passionate about creating intuitive, beautiful designs that solve real-world problems. Led design teams for major brands and startups.',
  experience: [
    {
      company: 'Design Studio Pro',
      title: 'Senior Creative Director',
      startDate: '2022',
      endDate: 'Present',
      description: 'Lead creative vision and design strategy for diverse client portfolio',
      achievements: [
        'Directed design projects resulting in 40% increase in client engagement',
        'Managed creative team of 12 designers and developers',
        'Won 3 industry awards for innovative digital campaigns'
      ]
    },
    {
      company: 'Tech Innovations Inc.',
      title: 'UX/UI Designer',
      startDate: '2020',
      endDate: '2022',
      description: 'Designed user interfaces for mobile and web applications',
      achievements: [
        'Redesigned mobile app interface, improving user retention by 60%',
        'Created design system used across 5+ product lines',
        'Conducted user research with 200+ participants monthly'
      ]
    }
  ],
  education: [
    {
      institution: 'Rhode Island School of Design',
      degree: 'Master of Fine Arts',
      field: 'Digital Media',
      graduationDate: '2020'
    },
    {
      institution: 'California Institute of Arts',
      degree: 'Bachelor of Fine Arts',
      field: 'Graphic Design',
      graduationDate: '2018'
    }
  ],
  skills: [
    'Adobe Creative Suite', 'Figma', 'Sketch', 'Prototyping', 'User Research',
    'Information Architecture', 'Visual Design', 'Brand Development'
  ],
  projects: [
    {
      name: 'EcoTrack Mobile App',
      description: 'Designed sustainable living tracking app with 100K+ downloads',
      technologies: ['Figma', 'Adobe XD', 'Principle']
    },
    {
      name: 'Brand Identity for StartupCo',
      description: 'Complete brand identity including logo, guidelines, and digital assets',
      technologies: ['Adobe Illustrator', 'InDesign', 'After Effects']
    }
  ]
};

export default function CreativeTemplate({
  template,
  data,
  colorScheme,
  className = ''
}: CreativeTemplateProps) {
  const templateData = { ...DEFAULT_DATA, ...data };
  const styles = template.styles;
  
  // Apply color scheme if provided
  const appliedColorScheme = colorScheme && template.colorSchemes 
    ? template.colorSchemes.find(cs => cs.name === colorScheme)
    : null;
  
  const colors = appliedColorScheme ? {
    primary: appliedColorScheme.primaryColor,
    secondary: appliedColorScheme.secondaryColor,
    accent: appliedColorScheme.accentColor
  } : {
    primary: styles.primaryColor,
    secondary: styles.secondaryColor,
    accent: styles.accentColor
  };

  const renderHeader = () => (
    <div 
      className="template-header creative-header rounded-t-lg p-8 mb-8 text-white"
      style={{ 
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%)` 
      }}
    >
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-2">
          {templateData.personalInfo?.name}
        </h1>
        <h2 className="text-xl mb-6 opacity-90">
          {templateData.personalInfo?.title}
        </h2>
        
        <div className="flex justify-center space-x-8 text-sm">
          {templateData.personalInfo?.email && (
            <div className="flex flex-col items-center">
              <span className="opacity-75">Email</span>
              <span className="font-medium">{templateData.personalInfo.email}</span>
            </div>
          )}
          {templateData.personalInfo?.phone && (
            <div className="flex flex-col items-center">
              <span className="opacity-75">Phone</span>
              <span className="font-medium">{templateData.personalInfo.phone}</span>
            </div>
          )}
          {templateData.personalInfo?.website && (
            <div className="flex flex-col items-center">
              <span className="opacity-75">Portfolio</span>
              <span className="font-medium">{templateData.personalInfo.website}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderSection = (sectionConfig: TemplateSectionConfig) => {
    const sectionStyle = {
      marginBottom: styles.spacing === 'compact' ? '1.5rem' : styles.spacing === 'spacious' ? '3rem' : '2rem'
    };

    switch (sectionConfig.type) {
      case 'summary':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-2xl font-bold mb-4 border-l-4 pl-4"
              style={{ 
                color: colors.primary,
                borderLeftColor: colors.primary
              }}
            >
              {sectionConfig.title}
            </h3>
            <p 
              className="text-base leading-relaxed pl-4"
              style={{ color: colors.secondary }}
            >
              {templateData.summary}
            </p>
          </div>
        );

      case 'experience':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-2xl font-bold mb-6 border-l-4 pl-4"
              style={{ 
                color: colors.primary,
                borderLeftColor: colors.primary
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-6 pl-4">
              {templateData.experience?.map((exp, index) => (
                <div 
                  key={index} 
                  className="experience-item p-6 rounded-lg border-l-4"
                  style={{ 
                    backgroundColor: `${colors.accent}10`,
                    borderLeftColor: colors.accent
                  }}
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-bold text-lg" style={{ color: colors.primary }}>
                        {exp.title}
                      </h4>
                      <p className="font-semibold text-base" style={{ color: colors.accent }}>
                        {exp.company}
                      </p>
                    </div>
                    <span 
                      className="text-sm font-bold px-3 py-1 rounded-full"
                      style={{ 
                        backgroundColor: colors.primary,
                        color: 'white'
                      }}
                    >
                      {exp.startDate} - {exp.endDate}
                    </span>
                  </div>
                  <p className="text-sm mb-3 text-gray-700">{exp.description}</p>
                  {exp.achievements && (
                    <ul className="text-sm space-y-2">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start">
                          <span 
                            className="w-2 h-2 rounded-full mr-3 mt-2 flex-shrink-0"
                            style={{ backgroundColor: colors.accent }}
                          ></span>
                          <span className="text-gray-700">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-2xl font-bold mb-6 border-l-4 pl-4"
              style={{ 
                color: colors.primary,
                borderLeftColor: colors.primary
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-4 pl-4">
              {templateData.education?.map((edu, index) => (
                <div 
                  key={index} 
                  className="education-item p-4 rounded-lg"
                  style={{ backgroundColor: `${colors.primary}10` }}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-bold text-base" style={{ color: colors.primary }}>
                        {edu.degree} in {edu.field}
                      </h4>
                      <p className="font-semibold" style={{ color: colors.secondary }}>
                        {edu.institution}
                      </p>
                    </div>
                    <span 
                      className="text-sm font-bold px-2 py-1 rounded"
                      style={{ 
                        backgroundColor: colors.accent,
                        color: 'white'
                      }}
                    >
                      {edu.graduationDate}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-2xl font-bold mb-6 border-l-4 pl-4"
              style={{ 
                color: colors.primary,
                borderLeftColor: colors.primary
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="grid grid-cols-2 gap-3 pl-4">
              {templateData.skills?.map((skill, index) => (
                <div
                  key={index}
                  className="skill-item px-4 py-3 rounded-lg text-center font-semibold shadow-sm"
                  style={{ 
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%)`,
                    color: 'white'
                  }}
                >
                  {skill}
                </div>
              ))}
            </div>
          </div>
        );

      case 'projects':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-2xl font-bold mb-6 border-l-4 pl-4"
              style={{ 
                color: colors.primary,
                borderLeftColor: colors.primary
              }}
            >
              {sectionConfig.title}
            </h3>
            <div className="grid grid-cols-1 gap-4 pl-4">
              {templateData.projects?.map((project, index) => (
                <div 
                  key={index} 
                  className="project-item p-6 rounded-lg shadow-sm border"
                  style={{ 
                    backgroundColor: 'white',
                    borderColor: colors.accent
                  }}
                >
                  <h4 className="font-bold text-lg mb-2" style={{ color: colors.primary }}>
                    {project.name}
                  </h4>
                  <p className="text-sm text-gray-700 mb-3">{project.description}</p>
                  {project.technologies && (
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1 text-xs font-medium rounded-full"
                          style={{ 
                            backgroundColor: `${colors.secondary}20`,
                            color: colors.secondary
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const isLayoutWithSidebar = styles.layout === 'sidebar' || styles.layout === 'two-column';

  return (
    <div 
      className={`template-preview template-creative ${className}`}
      style={{
        fontFamily: styles.fontFamily,
        padding: styles.spacing === 'compact' ? '1.5rem' : styles.spacing === 'spacious' ? '2.5rem' : '2rem'
      }}
    >
      {renderHeader()}
      
      {isLayoutWithSidebar ? (
        <div className="grid grid-cols-3 gap-8">
          {/* Main content */}
          <div className="col-span-2">
            {template.sections
              .filter(section => ['summary', 'experience'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
          
          {/* Sidebar */}
          <div className="col-span-1">
            {template.sections
              .filter(section => ['education', 'skills', 'projects'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
        </div>
      ) : (
        // Single column layout
        <div>
          {template.sections
            .filter(section => section.type !== 'header')
            .sort((a, b) => a.order - b.order)
            .map(renderSection)}
        </div>
      )}
    </div>
  );
}