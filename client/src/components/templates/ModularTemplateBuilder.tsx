import React, { useState, useCallback } from 'react';
import { DragDrop<PERSON>ontext, Droppable, Draggable } from 'react-beautiful-dnd';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  Cog6ToothIcon,
  Bars3Icon,
  DocumentIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  UserIcon,
  StarIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import type { ResumeTemplate, TemplateSectionConfig } from '../../constants/resumeTemplates';

interface ModularTemplateBuilderProps {
  template: ResumeTemplate;
  onTemplateUpdate: (updatedTemplate: ResumeTemplate) => void;
  onPreview: () => void;
  onSave: () => void;
}

interface DragDropItem {
  id: string;
  type: 'section' | 'component';
  data: TemplateSectionConfig | ComponentConfig;
}

interface ComponentConfig {
  id: string;
  type: 'text' | 'list' | 'grid' | 'divider' | 'spacer' | 'image';
  label: string;
  icon: React.ElementType;
  defaultProps: Record<string, any>;
}

const SECTION_TYPES: Record<string, { icon: React.ElementType; color: string; label: string }> = {
  header: { icon: UserIcon, color: 'blue', label: 'Header' },
  summary: { icon: DocumentIcon, color: 'green', label: 'Summary' },
  experience: { icon: BriefcaseIcon, color: 'purple', label: 'Experience' },
  education: { icon: AcademicCapIcon, color: 'indigo', label: 'Education' },
  skills: { icon: WrenchScrewdriverIcon, color: 'orange', label: 'Skills' },
  projects: { icon: StarIcon, color: 'yellow', label: 'Projects' },
  custom: { icon: ClipboardDocumentListIcon, color: 'gray', label: 'Custom' }
};

const AVAILABLE_COMPONENTS: ComponentConfig[] = [
  {
    id: 'text-block',
    type: 'text',
    label: 'Text Block',
    icon: DocumentIcon,
    defaultProps: { content: 'Your text content here...', fontSize: 'medium', alignment: 'left' }
  },
  {
    id: 'bullet-list',
    type: 'list',
    label: 'Bullet List',
    icon: ClipboardDocumentListIcon,
    defaultProps: { items: ['Item 1', 'Item 2', 'Item 3'], bulletStyle: 'dots' }
  },
  {
    id: 'skill-grid',
    type: 'grid',
    label: 'Skill Grid',
    icon: ChartBarIcon,
    defaultProps: { columns: 3, items: ['Skill 1', 'Skill 2', 'Skill 3'] }
  },
  {
    id: 'divider',
    type: 'divider',
    label: 'Divider',
    icon: Bars3Icon,
    defaultProps: { style: 'line', thickness: 1, color: '#e5e7eb' }
  },
  {
    id: 'spacer',
    type: 'spacer',
    label: 'Spacer',
    icon: Bars3Icon,
    defaultProps: { height: 20 }
  }
];

export default function ModularTemplateBuilder({
  template,
  onTemplateUpdate,
  onPreview,
  onSave
}: ModularTemplateBuilderProps) {
  const [sections, setSections] = useState<TemplateSectionConfig[]>(template.sections);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [showComponentPanel, setShowComponentPanel] = useState(true);
  const [customSectionName, setCustomSectionName] = useState('');
  const [showAddSectionDialog, setShowAddSectionDialog] = useState(false);

  const handleDragEnd = useCallback((result: any) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destIndex = result.destination.index;

    if (result.type === 'section') {
      const newSections = Array.from(sections);
      const [reorderedSection] = newSections.splice(sourceIndex, 1);
      newSections.splice(destIndex, 0, reorderedSection);

      // Update order values
      const updatedSections = newSections.map((section, index) => ({
        ...section,
        order: index + 1
      }));

      setSections(updatedSections);
      onTemplateUpdate({
        ...template,
        sections: updatedSections
      });
    }
  }, [sections, template, onTemplateUpdate]);

  const addSection = (type: string, customName?: string) => {
    const newSection: TemplateSectionConfig = {
      id: `${type}-${Date.now()}`,
      type: type as any,
      title: customName || SECTION_TYPES[type]?.label || 'Custom Section',
      required: false,
      order: sections.length + 1,
      layout: 'list',
      styling: {
        showIcons: true,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    };

    const updatedSections = [...sections, newSection];
    setSections(updatedSections);
    onTemplateUpdate({
      ...template,
      sections: updatedSections
    });
    setShowAddSectionDialog(false);
    setCustomSectionName('');
  };

  const removeSection = (sectionId: string) => {
    const updatedSections = sections
      .filter(section => section.id !== sectionId)
      .map((section, index) => ({ ...section, order: index + 1 }));
    
    setSections(updatedSections);
    onTemplateUpdate({
      ...template,
      sections: updatedSections
    });
    
    if (selectedSection === sectionId) {
      setSelectedSection(null);
    }
  };

  const updateSection = (sectionId: string, updates: Partial<TemplateSectionConfig>) => {
    const updatedSections = sections.map(section =>
      section.id === sectionId ? { ...section, ...updates } : section
    );
    
    setSections(updatedSections);
    onTemplateUpdate({
      ...template,
      sections: updatedSections
    });
  };

  const duplicateSection = (sectionId: string) => {
    const sectionToDuplicate = sections.find(s => s.id === sectionId);
    if (!sectionToDuplicate) return;

    const duplicatedSection: TemplateSectionConfig = {
      ...sectionToDuplicate,
      id: `${sectionToDuplicate.type}-${Date.now()}`,
      title: `${sectionToDuplicate.title} (Copy)`,
      order: sections.length + 1
    };

    const updatedSections = [...sections, duplicatedSection];
    setSections(updatedSections);
    onTemplateUpdate({
      ...template,
      sections: updatedSections
    });
  };

  return (
    <div className="h-full flex bg-gray-50">
      {/* Component Panel */}
      <AnimatePresence>
        {showComponentPanel && (
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: 300 }}
            exit={{ width: 0 }}
            className="bg-white border-r border-gray-200 overflow-hidden"
          >
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900">Components</h3>
              <p className="text-sm text-gray-600 mt-1">Drag to add sections</p>
            </div>

            <div className="p-4 space-y-4">
              {/* Section Types */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Section Types</h4>
                <div className="space-y-2">
                  {Object.entries(SECTION_TYPES).map(([type, config]) => {
                    const IconComponent = config.icon;
                    return (
                      <div
                        key={type}
                        className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                        onClick={() => type === 'custom' ? setShowAddSectionDialog(true) : addSection(type)}
                      >
                        <div className={`p-2 rounded-lg bg-${config.color}-100`}>
                          <IconComponent className={`h-4 w-4 text-${config.color}-600`} />
                        </div>
                        <span className="text-sm font-medium text-gray-900">{config.label}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Available Components */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Components</h4>
                <div className="space-y-2">
                  {AVAILABLE_COMPONENTS.map((component) => {
                    const IconComponent = component.icon;
                    return (
                      <div
                        key={component.id}
                        className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                        draggable
                        onDragStart={(e) => {
                          e.dataTransfer.setData('text/plain', JSON.stringify(component));
                        }}
                      >
                        <div className="p-2 rounded-lg bg-blue-100">
                          <IconComponent className="h-4 w-4 text-blue-600" />
                        </div>
                        <span className="text-sm font-medium text-gray-900">{component.label}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Builder Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowComponentPanel(!showComponentPanel)}
                className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
              >
                <Bars3Icon className="h-5 w-5" />
              </button>
              <h2 className="text-lg font-semibold text-gray-900">Template Builder</h2>
              <span className="text-sm text-gray-500">• {template.name}</span>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={() => setPreviewMode(!previewMode)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  previewMode
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                {previewMode ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                <span>{previewMode ? 'Edit' : 'Preview'}</span>
              </button>
              <button
                onClick={onPreview}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Full Preview
              </button>
              <button
                onClick={onSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Template
              </button>
            </div>
          </div>
        </div>

        {/* Builder Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="template-sections" type="section">
                {(provided, snapshot) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className={`space-y-4 transition-colors ${
                      snapshot.isDraggingOver ? 'bg-blue-50' : ''
                    }`}
                  >
                    {sections.map((section, index) => (
                      <Draggable
                        key={section.id}
                        draggableId={section.id}
                        index={index}
                        isDragDisabled={previewMode}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`bg-white rounded-lg border transition-all ${
                              snapshot.isDragging
                                ? 'shadow-lg border-blue-300'
                                : selectedSection === section.id
                                ? 'border-blue-400 shadow-md'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <SectionBuilder
                              section={section}
                              isSelected={selectedSection === section.id}
                              isPreviewMode={previewMode}
                              dragHandleProps={provided.dragHandleProps}
                              onSelect={() => setSelectedSection(section.id)}
                              onUpdate={(updates) => updateSection(section.id, updates)}
                              onRemove={() => removeSection(section.id)}
                              onDuplicate={() => duplicateSection(section.id)}
                            />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}

                    {/* Add Section Button */}
                    {!previewMode && (
                      <div className="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
                        <button
                          onClick={() => setShowAddSectionDialog(true)}
                          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
                        >
                          <PlusIcon className="h-5 w-5" />
                          <span>Add Section</span>
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        </div>
      </div>

      {/* Add Section Dialog */}
      {showAddSectionDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Add Custom Section</h3>
            <input
              type="text"
              placeholder="Section name..."
              value={customSectionName}
              onChange={(e) => setCustomSectionName(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 mb-4 focus:ring-2 focus:ring-blue-500"
            />
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowAddSectionDialog(false);
                  setCustomSectionName('');
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => addSection('custom', customSectionName)}
                disabled={!customSectionName.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Add Section
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface SectionBuilderProps {
  section: TemplateSectionConfig;
  isSelected: boolean;
  isPreviewMode: boolean;
  dragHandleProps: any;
  onSelect: () => void;
  onUpdate: (updates: Partial<TemplateSectionConfig>) => void;
  onRemove: () => void;
  onDuplicate: () => void;
}

function SectionBuilder({
  section,
  isSelected,
  isPreviewMode,
  dragHandleProps,
  onSelect,
  onUpdate,
  onRemove,
  onDuplicate
}: SectionBuilderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(section.title);

  const sectionConfig = SECTION_TYPES[section.type] || SECTION_TYPES.custom;
  const IconComponent = sectionConfig.icon;

  const handleTitleSave = () => {
    onUpdate({ title: editedTitle });
    setIsEditing(false);
  };

  const handleTitleCancel = () => {
    setEditedTitle(section.title);
    setIsEditing(false);
  };

  return (
    <div className="p-4" onClick={onSelect}>
      {/* Section Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {!isPreviewMode && (
            <div {...dragHandleProps} className="cursor-grab hover:cursor-grabbing">
              <Bars3Icon className="h-5 w-5 text-gray-400" />
            </div>
          )}
          <div className={`p-2 rounded-lg bg-${sectionConfig.color}-100`}>
            <IconComponent className={`h-4 w-4 text-${sectionConfig.color}-600`} />
          </div>
          
          {isEditing ? (
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={editedTitle}
                onChange={(e) => setEditedTitle(e.target.value)}
                className="border border-gray-300 rounded px-2 py-1 text-sm"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleTitleSave();
                  if (e.key === 'Escape') handleTitleCancel();
                }}
                autoFocus
              />
              <button onClick={handleTitleSave} className="text-green-600 hover:text-green-800">
                <CheckIcon className="h-4 w-4" />
              </button>
              <button onClick={handleTitleCancel} className="text-red-600 hover:text-red-800">
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <h4 
              className="font-medium text-gray-900 cursor-pointer hover:text-blue-600"
              onClick={() => !isPreviewMode && setIsEditing(true)}
            >
              {section.title}
            </h4>
          )}
        </div>

        {!isPreviewMode && (
          <div className="flex items-center space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDuplicate();
              }}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Duplicate section"
            >
              <DocumentIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Toggle section visibility or settings
              }}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Section settings"
            >
              <Cog6ToothIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className="p-1 text-gray-400 hover:text-red-600 rounded"
              title="Remove section"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Section Content */}
      <div className="bg-gray-50 rounded-lg p-4 min-h-24">
        <div className="text-sm text-gray-600">
          <div className="mb-2">
            <strong>Layout:</strong> {section.layout}
          </div>
          <div className="mb-2">
            <strong>Order:</strong> {section.order}
          </div>
          {section.required && (
            <div className="text-red-600 text-xs">Required section</div>
          )}
        </div>

        {/* Section-specific content preview */}
        <div className="mt-4 text-gray-700">
          {section.type === 'header' && (
            <div>
              <div className="font-semibold">John Doe</div>
              <div className="text-sm"><EMAIL> • (555) 123-4567</div>
            </div>
          )}
          {section.type === 'summary' && (
            <div className="text-sm">
              Professional summary with key qualifications and career highlights...
            </div>
          )}
          {section.type === 'experience' && (
            <div className="space-y-2">
              <div className="text-sm">
                <div className="font-medium">Senior Developer</div>
                <div className="text-gray-600">Company Name • 2020-Present</div>
              </div>
            </div>
          )}
          {section.type === 'skills' && (
            <div className="flex flex-wrap gap-2">
              {['JavaScript', 'React', 'Node.js', 'Python'].map((skill) => (
                <span key={skill} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                  {skill}
                </span>
              ))}
            </div>
          )}
          {section.type === 'custom' && (
            <div className="text-sm text-gray-500 italic">
              Custom section content will appear here...
            </div>
          )}
        </div>
      </div>

      {/* Section Configuration Panel */}
      {isSelected && !isPreviewMode && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h5 className="font-medium text-blue-900 mb-3">Section Configuration</h5>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Layout</label>
              <select
                value={section.layout}
                onChange={(e) => onUpdate({ layout: e.target.value as any })}
                className="w-full border border-gray-300 rounded px-3 py-1 text-sm"
              >
                <option value="list">List</option>
                <option value="grid">Grid</option>
                <option value="timeline">Timeline</option>
                <option value="cards">Cards</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Bullet Style</label>
              <select
                value={section.styling.bulletStyle}
                onChange={(e) => onUpdate({ 
                  styling: { ...section.styling, bulletStyle: e.target.value as any }
                })}
                className="w-full border border-gray-300 rounded px-3 py-1 text-sm"
              >
                <option value="dots">Dots</option>
                <option value="lines">Lines</option>
                <option value="arrows">Arrows</option>
                <option value="none">None</option>
              </select>
            </div>
          </div>
          <div className="mt-3 flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={section.styling.showIcons}
                onChange={(e) => onUpdate({ 
                  styling: { ...section.styling, showIcons: e.target.checked }
                })}
                className="mr-2"
              />
              <span className="text-sm">Show Icons</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={section.styling.showDividers}
                onChange={(e) => onUpdate({ 
                  styling: { ...section.styling, showDividers: e.target.checked }
                })}
                className="mr-2"
              />
              <span className="text-sm">Show Dividers</span>
            </label>
          </div>
        </div>
      )}
    </div>
  );
}

// Add missing imports for CheckIcon and XMarkIcon
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';