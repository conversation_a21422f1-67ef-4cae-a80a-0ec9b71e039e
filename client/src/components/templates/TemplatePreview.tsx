import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassPlusIcon as ZoomInIcon,
  MagnifyingGlassMinusIcon as ZoomOutIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  EyeIcon,
  Cog6ToothIcon,
  DocumentArrowDownIcon,
  PrinterIcon,
  ShareIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import type { ResumeTemplate } from '../../constants/resumeTemplates';
import { generatePreviewData } from '../../utils/templateUtils';

interface TemplatePreviewProps {
  template: ResumeTemplate;
  onEdit?: () => void;
  onExport?: (format: 'pdf' | 'word' | 'google-docs') => void;
  onShare?: () => void;
  isEditable?: boolean;
  data?: any; // Resume data to populate the template
}

interface PreviewSettings {
  zoom: number;
  showGrid: boolean;
  showMargins: boolean;
  showRulers: boolean;
  backgroundColor: string;
}

export default function TemplatePreview({
  template,
  onEdit,
  onExport,
  onShare,
  isEditable = false,
  data
}: TemplatePreviewProps) {
  const [settings, setSettings] = useState<PreviewSettings>({
    zoom: 1,
    showGrid: false,
    showMargins: true,
    showRulers: false,
    backgroundColor: '#ffffff'
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  
  const previewRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Use provided data or generate preview data
  const previewData = data || generatePreviewData();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen]);

  const handleZoomIn = () => {
    setSettings(prev => ({ ...prev, zoom: Math.min(prev.zoom + 0.1, 2) }));
  };

  const handleZoomOut = () => {
    setSettings(prev => ({ ...prev, zoom: Math.max(prev.zoom - 0.1, 0.5) }));
  };

  const handleFitToWidth = () => {
    if (containerRef.current && previewRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      const previewWidth = 794; // A4 width in pixels at 96 DPI
      const newZoom = (containerWidth - 40) / previewWidth; // 40px for padding
      setSettings(prev => ({ ...prev, zoom: Math.max(0.5, Math.min(2, newZoom)) }));
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleElementClick = (elementId: string) => {
    if (isEditMode) {
      setSelectedElement(elementId);
    }
  };

  const renderSectionContent = (section: any) => {
    switch (section.type) {
      case 'header':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`header-${section.id}`)}
          >
            <div 
              className="text-3xl font-bold mb-2"
              style={{ color: template.styles.primaryColor }}
            >
              {previewData.personalInfo.fullName}
            </div>
            <div className="text-lg text-gray-600 mb-3">
              {previewData.personalInfo.title}
            </div>
            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
              <span>{previewData.personalInfo.email}</span>
              <span>{previewData.personalInfo.phone}</span>
              <span>{previewData.personalInfo.location}</span>
              {previewData.personalInfo.website && (
                <span>{previewData.personalInfo.website}</span>
              )}
            </div>
          </div>
        );

      case 'summary':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`summary-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-3"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <p className="text-gray-700 leading-relaxed">
              {previewData.summary}
            </p>
          </div>
        );

      case 'experience':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`experience-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <div className="space-y-4">
              {previewData.experience.map((exp: any, index: number) => (
                <div key={index} className="border-l-2 border-gray-200 pl-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold text-gray-900">{exp.position}</h4>
                      <p className="text-gray-600">{exp.company}</p>
                    </div>
                    <span className="text-sm text-gray-500">{exp.duration}</span>
                  </div>
                  <ul className="text-gray-700 text-sm space-y-1">
                    {exp.achievements.map((achievement: string, achIndex: number) => (
                      <li key={achIndex} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`education-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <div className="space-y-3">
              {previewData.education.map((edu: any, index: number) => (
                <div key={index}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold text-gray-900">{edu.degree}</h4>
                      <p className="text-gray-600">{edu.school}</p>
                    </div>
                    <span className="text-sm text-gray-500">{edu.year}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`skills-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(previewData.skills).map(([category, skills]: [string, any]) => (
                <div key={category}>
                  <h4 className="font-medium text-gray-900 mb-2 capitalize">{category}</h4>
                  <div className="flex flex-wrap gap-2">
                    {skills.map((skill: string, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded"
                        style={{ backgroundColor: template.styles.accentColor + '20' }}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'projects':
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`projects-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <div className="space-y-4">
              {previewData.projects?.map((project: any, index: number) => (
                <div key={index}>
                  <h4 className="font-semibold text-gray-900">{project.name}</h4>
                  <p className="text-gray-600 text-sm mb-2">{project.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech: string, techIndex: number) => (
                      <span
                        key={techIndex}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              )) || <p className="text-gray-500 italic">No projects to display</p>}
            </div>
          </div>
        );

      default:
        return (
          <div 
            className={`mb-6 ${isEditMode ? 'hover:ring-2 hover:ring-blue-300 rounded p-2' : ''}`}
            onClick={() => handleElementClick(`custom-${section.id}`)}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: template.styles.primaryColor }}
            >
              {section.title}
            </h3>
            <p className="text-gray-500 italic">Custom section content</p>
          </div>
        );
    }
  };

  const previewContent = (
    <div className="bg-white">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-4">
          <h2 className="font-semibold text-gray-900">{template.name}</h2>
          <span className="text-sm text-gray-500">• Preview</span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Zoom Controls */}
          <div className="flex items-center space-x-1 border border-gray-300 rounded-lg">
            <button
              onClick={handleZoomOut}
              className="p-2 hover:bg-gray-100 rounded-l-lg"
              disabled={settings.zoom <= 0.5}
            >
              <ZoomOutIcon className="h-4 w-4" />
            </button>
            <span className="px-3 py-2 text-sm font-medium min-w-16 text-center">
              {Math.round(settings.zoom * 100)}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-2 hover:bg-gray-100"
              disabled={settings.zoom >= 2}
            >
              <ZoomInIcon className="h-4 w-4" />
            </button>
            <button
              onClick={handleFitToWidth}
              className="p-2 hover:bg-gray-100 border-l border-gray-300"
            >
              <ArrowsPointingOutIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Edit Mode Toggle */}
          {isEditable && (
            <button
              onClick={() => setIsEditMode(!isEditMode)}
              className={`p-2 rounded-lg ${
                isEditMode 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <PencilIcon className="h-4 w-4" />
            </button>
          )}

          {/* Settings */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
          >
            <Cog6ToothIcon className="h-4 w-4" />
          </button>

          {/* Export Options */}
          {onExport && (
            <div className="relative">
              <select
                onChange={(e) => {
                  const format = e.target.value as 'pdf' | 'word' | 'google-docs';
                  if (format) onExport(format);
                  e.target.value = '';
                }}
                className="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 pr-8 text-sm"
              >
                <option value="">Export</option>
                <option value="pdf">PDF</option>
                <option value="word">Word</option>
                <option value="google-docs">Google Docs</option>
              </select>
              <DocumentArrowDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => window.print()}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              title="Print"
            >
              <PrinterIcon className="h-4 w-4" />
            </button>
            
            {onShare && (
              <button
                onClick={onShare}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                title="Share"
              >
                <ShareIcon className="h-4 w-4" />
              </button>
            )}

            {!isFullscreen && (
              <button
                onClick={toggleFullscreen}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                title="Fullscreen"
              >
                <ArrowsPointingOutIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            className="border-b bg-gray-50 overflow-hidden"
          >
            <div className="p-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.showGrid}
                  onChange={(e) => setSettings(prev => ({ ...prev, showGrid: e.target.checked }))}
                  className="mr-2"
                />
                Show Grid
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.showMargins}
                  onChange={(e) => setSettings(prev => ({ ...prev, showMargins: e.target.checked }))}
                  className="mr-2"
                />
                Show Margins
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.showRulers}
                  onChange={(e) => setSettings(prev => ({ ...prev, showRulers: e.target.checked }))}
                  className="mr-2"
                />
                Show Rulers
              </label>
              <div className="flex items-center space-x-2">
                <span className="text-sm">Background:</span>
                <input
                  type="color"
                  value={settings.backgroundColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, backgroundColor: e.target.value }))}
                  className="w-8 h-8 rounded border"
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Preview Area */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-auto p-8"
        style={{ backgroundColor: settings.backgroundColor }}
      >
        <div className="flex justify-center">
          <div
            ref={previewRef}
            className={`bg-white shadow-lg transition-transform ${
              settings.showMargins ? 'border border-gray-300' : ''
            } ${settings.showGrid ? 'bg-grid' : ''}`}
            style={{
              width: '794px', // A4 width
              minHeight: '1123px', // A4 height
              transform: `scale(${settings.zoom})`,
              transformOrigin: 'top center',
              fontFamily: template.styles.fontFamily
            }}
          >
            {/* Page Content */}
            <div className="p-12">
              {template.sections
                .sort((a, b) => a.order - b.order)
                .map((section) => (
                  <div key={section.id}>
                    {renderSectionContent(section)}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Mode Overlay */}
      {isEditMode && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <EyeIcon className="h-4 w-4" />
            <span className="text-sm">Click elements to edit • ESC to exit</span>
          </div>
        </div>
      )}
    </div>
  );

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-white flex flex-col">
        <div className="flex-1">
          {previewContent}
        </div>
        <div className="border-t p-4 bg-gray-50">
          <div className="flex justify-center">
            <button
              onClick={toggleFullscreen}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <ArrowsPointingInIcon className="h-4 w-4" />
              <span>Exit Fullscreen</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <div className="h-full flex flex-col">{previewContent}</div>;
}