import React from 'react';
import type { ResumeTemplate, TemplateSectionConfig } from '../../../constants/resumeTemplates';

interface IndustryTemplateProps {
  template: ResumeTemplate;
  data?: any;
  colorScheme?: string;
  className?: string;
}

interface TemplateData {
  personalInfo?: {
    name?: string;
    title?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    certifications?: string[];
    licenses?: string[];
  };
  summary?: string;
  experience?: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate: string;
    description: string;
    achievements?: string[];
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    graduationDate: string;
    gpa?: string;
  }>;
  skills?: string[];
  certifications?: string[];
  projects?: Array<{
    name: string;
    description: string;
    technologies?: string[];
  }>;
}

const getDefaultDataByIndustry = (industry: string): TemplateData => {
  const baseData = {
    personalInfo: {
      name: 'Dr. <PERSON>',
      title: 'Senior Healthcare Administrator',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Boston, MA',
      linkedin: 'linkedin.com/in/michaelrodriguez',
      certifications: ['FACHE', 'CHCAE'],
      licenses: ['Healthcare Administrator License - MA']
    },
    summary: 'Dedicated healthcare professional with 10+ years of experience in clinical operations and healthcare administration. Proven track record of improving patient outcomes while reducing operational costs.',
    experience: [
      {
        company: 'Boston Medical Center',
        title: 'Senior Healthcare Administrator',
        startDate: '2020',
        endDate: 'Present',
        description: 'Oversee clinical operations for 500-bed medical facility',
        achievements: [
          'Reduced patient wait times by 35% through process optimization',
          'Managed annual budget of $15M with 10% cost savings',
          'Led implementation of new Electronic Health Record system'
        ]
      }
    ],
    education: [
      {
        institution: 'Harvard School of Public Health',
        degree: 'Master of Health Administration',
        field: 'Healthcare Management',
        graduationDate: '2018'
      }
    ],
    skills: ['Healthcare Administration', 'Clinical Operations', 'Budget Management', 'EHR Systems'],
    certifications: ['FACHE - Fellow of the American College of Healthcare Executives', 'CHCAE - Certified Healthcare Administrative Executive']
  };

  // Customize based on industry
  switch (industry) {
    case 'Education':
      return {
        ...baseData,
        personalInfo: {
          ...baseData.personalInfo,
          name: 'Dr. Sarah Williams',
          title: 'Principal & Educational Leader',
          email: '<EMAIL>',
          certifications: ['Ed.D', 'State Teaching License'],
          licenses: ['Administrative License - State of MA']
        },
        summary: 'Passionate educational leader with 12+ years of experience in K-12 administration. Committed to fostering innovative learning environments and improving student outcomes.',
        skills: ['Educational Leadership', 'Curriculum Development', 'Student Assessment', 'Faculty Development']
      };
    
    case 'Engineering':
      return {
        ...baseData,
        personalInfo: {
          ...baseData.personalInfo,
          name: 'James Chen',
          title: 'Senior Software Engineer',
          email: '<EMAIL>',
          certifications: ['AWS Certified', 'Kubernetes Certified'],
          licenses: ['Professional Engineer License - CA']
        },
        summary: 'Innovative software engineer with 8+ years of experience building scalable systems. Expert in cloud architecture and DevOps practices.',
        skills: ['Python', 'JavaScript', 'AWS', 'Kubernetes', 'DevOps', 'System Architecture']
      };
    
    case 'Legal':
      return {
        ...baseData,
        personalInfo: {
          ...baseData.personalInfo,
          name: 'Elizabeth Thompson',
          title: 'Senior Corporate Attorney',
          email: '<EMAIL>',
          certifications: ['J.D.', 'Bar Admission'],
          licenses: ['Bar License - NY', 'Bar License - CT']
        },
        summary: 'Experienced corporate attorney with 15+ years specializing in mergers & acquisitions and corporate governance. Proven track record in complex transaction management.',
        skills: ['Corporate Law', 'M&A Transactions', 'Contract Negotiation', 'Regulatory Compliance']
      };
    
    default:
      return baseData;
  }
};

export default function IndustryTemplate({
  template,
  data,
  colorScheme,
  className = ''
}: IndustryTemplateProps) {
  const industry = template.industry[0] || 'Healthcare';
  const templateData = { ...getDefaultDataByIndustry(industry), ...data };
  const styles = template.styles;
  
  // Apply color scheme if provided
  const appliedColorScheme = colorScheme && template.colorSchemes 
    ? template.colorSchemes.find(cs => cs.name === colorScheme)
    : null;
  
  const colors = appliedColorScheme ? {
    primary: appliedColorScheme.primaryColor,
    secondary: appliedColorScheme.secondaryColor,
    accent: appliedColorScheme.accentColor
  } : {
    primary: styles.primaryColor,
    secondary: styles.secondaryColor,
    accent: styles.accentColor
  };

  const renderHeader = () => (
    <div className="template-header industry-header mb-8 pb-6 border-b-4" style={{ borderBottomColor: colors.primary }}>
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>
          {templateData.personalInfo?.name}
        </h1>
        <h2 className="text-xl mb-4" style={{ color: colors.secondary }}>
          {templateData.personalInfo?.title}
        </h2>
        
        <div className="grid grid-cols-2 gap-4 text-sm max-w-2xl mx-auto">
          <div className="text-right">
            {templateData.personalInfo?.email && (
              <div className="mb-1" style={{ color: colors.secondary }}>
                {templateData.personalInfo.email}
              </div>
            )}
            {templateData.personalInfo?.phone && (
              <div className="mb-1" style={{ color: colors.secondary }}>
                {templateData.personalInfo.phone}
              </div>
            )}
          </div>
          <div className="text-left">
            {templateData.personalInfo?.location && (
              <div className="mb-1" style={{ color: colors.secondary }}>
                {templateData.personalInfo.location}
              </div>
            )}
            {templateData.personalInfo?.linkedin && (
              <div className="mb-1" style={{ color: colors.accent }}>
                {templateData.personalInfo.linkedin}
              </div>
            )}
          </div>
        </div>
        
        {/* Certifications/Licenses */}
        {(templateData.personalInfo?.certifications || templateData.personalInfo?.licenses) && (
          <div className="mt-4 flex justify-center flex-wrap gap-2">
            {templateData.personalInfo?.certifications?.map((cert, index) => (
              <span
                key={index}
                className="px-3 py-1 text-xs font-semibold rounded-full border"
                style={{ 
                  backgroundColor: `${colors.accent}20`,
                  color: colors.accent,
                  borderColor: colors.accent
                }}
              >
                {cert}
              </span>
            ))}
            {templateData.personalInfo?.licenses?.map((license, index) => (
              <span
                key={index}
                className="px-3 py-1 text-xs font-semibold rounded-full border"
                style={{ 
                  backgroundColor: `${colors.primary}20`,
                  color: colors.primary,
                  borderColor: colors.primary
                }}
              >
                {license}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderSection = (sectionConfig: TemplateSectionConfig) => {
    const sectionStyle = {
      marginBottom: styles.spacing === 'compact' ? '1.5rem' : styles.spacing === 'spacious' ? '2.5rem' : '2rem'
    };

    switch (sectionConfig.type) {
      case 'summary':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-3 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <p className="text-sm leading-relaxed" style={{ color: colors.secondary }}>
              {templateData.summary}
            </p>
          </div>
        );

      case 'experience':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-4">
              {templateData.experience?.map((exp, index) => (
                <div key={index} className="experience-item">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-bold text-base" style={{ color: colors.primary }}>
                        {exp.title}
                      </h4>
                      <p className="font-semibold text-base" style={{ color: colors.secondary }}>
                        {exp.company}
                      </p>
                    </div>
                    <span 
                      className="text-sm font-bold px-2 py-1 rounded"
                      style={{ 
                        backgroundColor: colors.accent,
                        color: 'white'
                      }}
                    >
                      {exp.startDate} - {exp.endDate}
                    </span>
                  </div>
                  <p className="text-sm mb-2 text-gray-700 italic">{exp.description}</p>
                  {exp.achievements && (
                    <ul className="text-sm space-y-1">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start">
                          <span style={{ color: colors.accent }} className="mr-2 mt-1 font-bold">▪</span>
                          <span className="text-gray-700">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-3">
              {templateData.education?.map((edu, index) => (
                <div key={index} className="education-item">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-bold" style={{ color: colors.primary }}>
                        {edu.degree} in {edu.field}
                      </h4>
                      <p className="font-semibold" style={{ color: colors.secondary }}>
                        {edu.institution}
                      </p>
                    </div>
                    <span className="text-sm font-semibold" style={{ color: colors.accent }}>
                      {edu.graduationDate}
                    </span>
                  </div>
                  {edu.gpa && (
                    <p className="text-sm text-gray-600">GPA: {edu.gpa}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              Professional {sectionConfig.title}
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {templateData.skills?.map((skill, index) => (
                <div
                  key={index}
                  className="skill-item px-3 py-2 text-sm font-medium border-l-4"
                  style={{ 
                    backgroundColor: `${colors.accent}10`,
                    borderLeftColor: colors.accent,
                    color: colors.primary
                  }}
                >
                  {skill}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div 
      className={`template-preview template-industry ${industry.toLowerCase()} ${className}`}
      style={{
        fontFamily: styles.fontFamily,
        padding: styles.spacing === 'compact' ? '2rem' : styles.spacing === 'spacious' ? '3rem' : '2.5rem'
      }}
    >
      {renderHeader()}
      
      <div className={styles.layout === 'two-column' ? 'grid grid-cols-3 gap-8' : ''}>
        <div className={styles.layout === 'two-column' ? 'col-span-2' : ''}>
          {template.sections
            .filter(section => ['summary', 'experience'].includes(section.type))
            .sort((a, b) => a.order - b.order)
            .map(renderSection)}
        </div>
        
        {styles.layout === 'two-column' && (
          <div className="col-span-1">
            {template.sections
              .filter(section => ['education', 'skills'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
        )}
        
        {styles.layout === 'single-column' && (
          <>
            {template.sections
              .filter(section => !['summary', 'experience'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </>
        )}
      </div>

      {/* Industry-specific certifications section */}
      {templateData.certifications && templateData.certifications.length > 0 && (
        <div className="template-section mt-8">
          <h3 
            className="text-lg font-bold mb-4 pb-1 border-b"
            style={{ color: colors.primary, borderBottomColor: colors.primary }}
          >
            Professional Certifications
          </h3>
          <div className="grid grid-cols-1 gap-2">
            {templateData.certifications.map((cert, index) => (
              <div
                key={index}
                className="certification-item px-4 py-3 border-l-4 font-medium"
                style={{ 
                  backgroundColor: `${colors.primary}10`,
                  borderLeftColor: colors.primary,
                  color: colors.secondary
                }}
              >
                {cert}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}