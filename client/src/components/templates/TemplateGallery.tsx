import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FunnelIcon, 
  MagnifyingGlassIcon, 
  StarIcon,
  EyeIcon,
  SparklesIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import type { RootState } from '../../store';
import type { ResumeTemplate } from '../../constants/resumeTemplates';
import { templateAPI } from '../../services/templateAPI';
import { getTemplatesByCategory } from '../../constants/resumeTemplates';

interface TemplateGalleryProps {
  onTemplateSelect: (template: ResumeTemplate) => void;
  onTemplatePreview: (template: ResumeTemplate) => void;
  selectedTemplateId?: string;
}

interface FilterState {
  category: string;
  industry: string;
  experienceLevel: string;
  isPremium: boolean | null;
  atsOptimized: boolean | null;
  searchQuery: string;
  sortBy: 'popularity' | 'rating' | 'newest' | 'alphabetical';
}

const CATEGORIES = [
  { value: 'all', label: 'All Templates' },
  { value: 'professional', label: 'Professional' },
  { value: 'creative', label: 'Creative' },
  { value: 'executive', label: 'Executive' },
  { value: 'industry-specific', label: 'Industry Specific' },
  { value: 'entry-level', label: 'Entry Level' },
  { value: 'mid-career', label: 'Mid Career' },
  { value: 'senior-executive', label: 'Senior Executive' },
  { value: 'ats-optimized', label: 'ATS Optimized' },
  { value: 'modern', label: 'Modern' },
  { value: 'classic', label: 'Classic' },
  { value: 'minimalist', label: 'Minimalist' },
  { value: 'technical', label: 'Technical' }
];

const INDUSTRIES = [
  { value: 'all', label: 'All Industries' },
  { value: 'Technology', label: 'Technology' },
  { value: 'Healthcare', label: 'Healthcare' },
  { value: 'Finance', label: 'Finance' },
  { value: 'Education', label: 'Education' },
  { value: 'Engineering', label: 'Engineering' },
  { value: 'Sales', label: 'Sales' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Design', label: 'Design' },
  { value: 'Legal', label: 'Legal' },
  { value: 'Consulting', label: 'Consulting' },
  { value: 'Media', label: 'Media' }
];

const EXPERIENCE_LEVELS = [
  { value: 'all', label: 'All Levels' },
  { value: 'entry-level', label: 'Entry Level' },
  { value: 'mid-career', label: 'Mid Career' },
  { value: 'senior-executive', label: 'Senior Executive' }
];

export default function TemplateGallery({ 
  onTemplateSelect, 
  onTemplatePreview, 
  selectedTemplateId 
}: TemplateGalleryProps) {
  const { token } = useSelector((state: RootState) => state.auth);
  const [templates, setTemplates] = useState<ResumeTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<FilterState>({
    category: 'all',
    industry: 'all',
    experienceLevel: 'all',
    isPremium: null,
    atsOptimized: null,
    searchQuery: '',
    sortBy: 'popularity'
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the static template generator for now
      const allTemplates = getTemplatesByCategory('all');
      setTemplates(allTemplates);
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError('Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const filteredAndSortedTemplates = useMemo(() => {
    let result = [...templates];

    // Apply filters
    if (filters.category !== 'all') {
      result = result.filter(template => template.category === filters.category);
    }

    if (filters.industry !== 'all') {
      result = result.filter(template => 
        template.industry.some(ind => ind.toLowerCase().includes(filters.industry.toLowerCase()))
      );
    }

    if (filters.experienceLevel !== 'all') {
      result = result.filter(template => template.experienceLevel === filters.experienceLevel);
    }

    if (filters.isPremium !== null) {
      result = result.filter(template => template.isPremium === filters.isPremium);
    }

    if (filters.atsOptimized !== null) {
      result = result.filter(template => template.atsCompatible === filters.atsOptimized);
    }

    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      result = result.filter(template => 
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.features.some(feature => feature.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      switch (filters.sortBy) {
        case 'alphabetical':
          return a.name.localeCompare(b.name);
        case 'newest':
          return b.id.localeCompare(a.id); // Assuming newer templates have higher IDs
        case 'rating':
          return 0; // Would implement with actual ratings
        case 'popularity':
        default:
          return 0; // Would implement with actual usage data
      }
    });

    return result;
  }, [templates, filters]);

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      industry: 'all',
      experienceLevel: 'all',
      isPremium: null,
      atsOptimized: null,
      searchQuery: '',
      sortBy: 'popularity'
    });
  };

  const handleTemplateSelect = async (template: ResumeTemplate) => {
    onTemplateSelect(template);
    
    // Track template usage
    if (token) {
      try {
        await templateAPI.trackTemplateUsage(template.id, {
          usage_type: 'select',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Failed to track template usage:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600">Loading templates...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-12">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={fetchTemplates}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Template Gallery</h1>
          <p className="text-gray-600 mt-2">
            Choose from {templates.length}+ professional resume templates
          </p>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          <FunnelIcon className="h-5 w-5" />
          <span>Filters</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="relative mb-6">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search templates by name, industry, or features..."
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={filters.searchQuery}
          onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
        />
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-50 rounded-lg p-6 mb-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  {CATEGORIES.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                <select
                  value={filters.industry}
                  onChange={(e) => handleFilterChange('industry', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  {INDUSTRIES.map(industry => (
                    <option key={industry.value} value={industry.value}>
                      {industry.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Experience Level</label>
                <select
                  value={filters.experienceLevel}
                  onChange={(e) => handleFilterChange('experienceLevel', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  {EXPERIENCE_LEVELS.map(level => (
                    <option key={level.value} value={level.value}>
                      {level.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value as FilterState['sortBy'])}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="popularity">Popularity</option>
                  <option value="rating">Rating</option>
                  <option value="newest">Newest</option>
                  <option value="alphabetical">Alphabetical</option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-4 mt-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.isPremium === true}
                  onChange={(e) => handleFilterChange('isPremium', e.target.checked ? true : null)}
                  className="mr-2"
                />
                Premium Only
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.atsOptimized === true}
                  onChange={(e) => handleFilterChange('atsOptimized', e.target.checked ? true : null)}
                  className="mr-2"
                />
                ATS Optimized
              </label>
              <button
                onClick={clearFilters}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Clear All Filters
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Count */}
      <div className="flex items-center justify-between mb-6">
        <p className="text-gray-600">
          {filteredAndSortedTemplates.length} template{filteredAndSortedTemplates.length !== 1 ? 's' : ''} found
        </p>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredAndSortedTemplates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplateId === template.id}
            onSelect={() => handleTemplateSelect(template)}
            onPreview={() => onTemplatePreview(template)}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredAndSortedTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <span className="text-6xl">🔍</span>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your filters or search terms to find the perfect template.
          </p>
          <button
            onClick={clearFilters}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );
}

interface TemplateCardProps {
  template: ResumeTemplate;
  isSelected: boolean;
  onSelect: () => void;
  onPreview: () => void;
}

function TemplateCard({ template, isSelected, onSelect, onPreview }: TemplateCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ y: -4 }}
      className={`relative bg-white rounded-lg shadow-md border-2 transition-all duration-200 cursor-pointer ${
        isSelected ? 'border-blue-500 shadow-lg' : 'border-transparent hover:border-gray-300'
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onSelect}
    >
      {/* Premium Badge */}
      {template.isPremium && (
        <div className="absolute top-2 right-2 z-10">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <SparklesIcon className="h-3 w-3 mr-1" />
            Premium
          </span>
        </div>
      )}

      {/* Template Preview */}
      <div className="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
        {/* Placeholder for template preview */}
        <div 
          className="h-full w-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center"
          style={{ 
            backgroundColor: template.styles.primaryColor + '20',
            borderColor: template.styles.primaryColor
          }}
        >
          <div className="text-center">
            <div className="text-2xl font-bold mb-2" style={{ color: template.styles.primaryColor }}>
              {template.name}
            </div>
            <div className="text-sm text-gray-600">
              {template.styles.layout} • {template.styles.fontFamily.split(',')[0]}
            </div>
          </div>
        </div>

        {/* Hover Overlay */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
            >
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onPreview();
                }}
                className="flex items-center space-x-2 px-4 py-2 bg-white rounded-lg hover:bg-gray-100 transition-colors"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Preview</span>
              </button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute top-2 left-2">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <CheckIcon className="h-4 w-4 text-white" />
            </div>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 truncate">{template.name}</h3>
          {template.atsCompatible && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
              ATS
            </span>
          )}
        </div>
        
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{template.description}</p>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <StarIcon key={i} className="h-4 w-4 text-gray-300" />
            ))}
          </div>
          <span className="text-xs text-gray-500 capitalize">{template.category}</span>
        </div>

        <div className="mt-3 flex flex-wrap gap-1">
          {template.features.slice(0, 2).map((feature, index) => (
            <span
              key={index}
              className="inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
            >
              {feature}
            </span>
          ))}
          {template.features.length > 2 && (
            <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
              +{template.features.length - 2} more
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );
}