import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  StarIcon,
  SparklesIcon,
  CheckIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

import type { ResumeTemplate } from '../../constants/resumeTemplates';
import { 
  RESUME_TEMPLATES, 
  getTemplatesByCategory, 
  getTemplatesByIndustry,
  getTemplatesByExperienceLevel,
  getAvailableCategories,
  getAvailableIndustries,
  getTemplateStats
} from '../../constants/resumeTemplates';
import TemplatePreview from './TemplatePreview';
import TemplateSelector from './TemplateSelector';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface TemplateLibraryProps {
  onTemplateSelect: (template: ResumeTemplate) => void;
  selectedTemplateId?: string;
  showPreview?: boolean;
  isModal?: boolean;
  onClose?: () => void;
}

interface FilterState {
  category: string;
  industry: string;
  experienceLevel: string;
  isPremium: boolean | null;
  atsOptimized: boolean | null;
  searchQuery: string;
  colorScheme: string;
  layout: string;
  fontFamily: string;
}

type ViewMode = 'grid' | 'list';
type SortOption = 'name' | 'category' | 'popularity' | 'newest';

const INITIAL_FILTERS: FilterState = {
  category: 'all',
  industry: 'all', 
  experienceLevel: 'all',
  isPremium: null,
  atsOptimized: null,
  searchQuery: '',
  colorScheme: 'all',
  layout: 'all',
  fontFamily: 'all'
};

const CATEGORIES = [
  { value: 'all', label: 'All Templates', count: 0 },
  { value: 'professional', label: 'Professional', count: 0 },
  { value: 'creative', label: 'Creative', count: 0 },
  { value: 'industry-specific', label: 'Industry Specific', count: 0 },
  { value: 'entry-level', label: 'Entry Level', count: 0 },
  { value: 'mid-career', label: 'Mid Career', count: 0 },
  { value: 'senior-executive', label: 'Senior Executive', count: 0 },
  { value: 'ats-optimized', label: 'ATS Optimized', count: 0 },
  { value: 'executive', label: 'Executive', count: 0 },
  { value: 'technical', label: 'Technical', count: 0 }
];

const INDUSTRIES = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Engineering',
  'Sales', 'Marketing', 'Legal', 'Consulting', 'Design', 'Retail',
  'Customer Service'
];

const EXPERIENCE_LEVELS = [
  { value: 'entry-level', label: 'Entry Level' },
  { value: 'mid-career', label: 'Mid Career' },
  { value: 'senior-executive', label: 'Senior Executive' }
];

export default function TemplateLibrary({
  onTemplateSelect,
  selectedTemplateId,
  showPreview = true,
  isModal = false,
  onClose
}: TemplateLibraryProps) {
  const [filters, setFilters] = useState<FilterState>(INITIAL_FILTERS);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [showFilters, setShowFilters] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<ResumeTemplate | null>(null);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Get template statistics
  const stats = useMemo(() => getTemplateStats(), []);
  
  // Update category counts
  const categoriesWithCounts = useMemo(() => {
    const statsMap = stats.byCategory;
    return CATEGORIES.map(cat => ({
      ...cat,
      count: cat.value === 'all' ? stats.total : (statsMap[cat.value] || 0)
    }));
  }, [stats]);

  // Filter and sort templates
  const filteredTemplates = useMemo(() => {
    let templates = RESUME_TEMPLATES;

    // Apply filters
    if (filters.category !== 'all') {
      templates = getTemplatesByCategory(filters.category);
    }

    if (filters.industry !== 'all') {
      templates = templates.filter(t => t.industry.includes(filters.industry));
    }

    if (filters.experienceLevel !== 'all') {
      templates = templates.filter(t => t.experienceLevel === filters.experienceLevel);
    }

    if (filters.isPremium !== null) {
      templates = templates.filter(t => t.isPremium === filters.isPremium);
    }

    if (filters.atsOptimized !== null) {
      templates = templates.filter(t => t.atsCompatible === filters.atsOptimized);
    }

    if (filters.layout !== 'all') {
      templates = templates.filter(t => t.styles.layout === filters.layout);
    }

    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase();
      templates = templates.filter(t => 
        t.name.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.category.toLowerCase().includes(query) ||
        t.features.some(f => f.toLowerCase().includes(query)) ||
        t.industry.some(i => i.toLowerCase().includes(query))
      );
    }

    // Sort templates
    switch (sortBy) {
      case 'name':
        templates.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'category':
        templates.sort((a, b) => a.category.localeCompare(b.category));
        break;
      case 'popularity':
        // Sort by premium last, then by name
        templates.sort((a, b) => {
          if (a.isPremium !== b.isPremium) {
            return a.isPremium ? 1 : -1;
          }
          return a.name.localeCompare(b.name);
        });
        break;
      case 'newest':
        // For demo purposes, sort by ID (newer templates have higher IDs)
        templates.sort((a, b) => b.id.localeCompare(a.id));
        break;
    }

    return templates;
  }, [filters, sortBy]);

  const handleFilterChange = useCallback((key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(INITIAL_FILTERS);
  }, []);

  const toggleFavorite = useCallback((templateId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(templateId)) {
        newFavorites.delete(templateId);
      } else {
        newFavorites.add(templateId);
      }
      return newFavorites;
    });
  }, []);

  const hasActiveFilters = useMemo(() => {
    return filters.category !== 'all' ||
           filters.industry !== 'all' ||
           filters.experienceLevel !== 'all' ||
           filters.isPremium !== null ||
           filters.atsOptimized !== null ||
           filters.layout !== 'all' ||
           filters.searchQuery.trim() !== '';
  }, [filters]);

  return (
    <div className={`flex flex-col h-full ${isModal ? 'max-h-[90vh]' : ''}`}>
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Template Library</h1>
            <p className="text-sm text-gray-600 mt-1">
              Choose from {stats.total} professionally designed resume templates
            </p>
          </div>
          
          {isModal && onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-5 h-5" />
            </Button>
          )}
        </div>

        {/* Search and Controls */}
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates by name, category, or industry..."
              value={filters.searchQuery}
              onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <Button
            variant={showFilters ? 'primary' : 'outline'}
            size="md"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <FunnelIcon className="w-4 h-4" />
            Filters
            {hasActiveFilters && (
              <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                !
              </span>
            )}
          </Button>

          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <Squares2X2Icon className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <ListBulletIcon className="w-4 h-4" />
            </button>
          </div>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="name">Sort by Name</option>
            <option value="category">Sort by Category</option>
            <option value="popularity">Sort by Popularity</option>
            <option value="newest">Sort by Newest</option>
          </select>
        </div>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="flex-shrink-0 bg-gray-50 border-b border-gray-200 overflow-hidden"
          >
            <div className="p-6 space-y-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <div className="flex flex-wrap gap-2">
                  {categoriesWithCounts.map((category) => (
                    <button
                      key={category.value}
                      onClick={() => handleFilterChange('category', category.value)}
                      className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                        filters.category === category.value
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                      }`}
                    >
                      {category.label} ({category.count})
                    </button>
                  ))}
                </div>
              </div>

              {/* Other Filters Row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* Industry Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                  <select
                    value={filters.industry}
                    onChange={(e) => handleFilterChange('industry', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Industries</option>
                    {INDUSTRIES.map(industry => (
                      <option key={industry} value={industry}>{industry}</option>
                    ))}
                  </select>
                </div>

                {/* Experience Level Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Experience</label>
                  <select
                    value={filters.experienceLevel}
                    onChange={(e) => handleFilterChange('experienceLevel', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Levels</option>
                    {EXPERIENCE_LEVELS.map(level => (
                      <option key={level.value} value={level.value}>{level.label}</option>
                    ))}
                  </select>
                </div>

                {/* Premium Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <select
                    value={filters.isPremium === null ? 'all' : filters.isPremium ? 'premium' : 'free'}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleFilterChange('isPremium', value === 'all' ? null : value === 'premium');
                    }}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Templates</option>
                    <option value="free">Free Templates</option>
                    <option value="premium">Premium Templates</option>
                  </select>
                </div>

                {/* ATS Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ATS Optimized</label>
                  <select
                    value={filters.atsOptimized === null ? 'all' : filters.atsOptimized ? 'yes' : 'no'}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleFilterChange('atsOptimized', value === 'all' ? null : value === 'yes');
                    }}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Templates</option>
                    <option value="yes">ATS Optimized</option>
                    <option value="no">Not ATS Optimized</option>
                  </select>
                </div>
              </div>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <div className="flex justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    Clear all filters
                  </Button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Summary */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            Showing {filteredTemplates.length} of {stats.total} templates
            {hasActiveFilters && (
              <span className="ml-2 text-blue-600 font-medium">
                • Filtered results
              </span>
            )}
          </span>
        </div>
      </div>

      {/* Templates Grid/List */}
      <div className="flex-1 overflow-auto p-6">
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your filters or search terms to find more templates.
            </p>
            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters}>
                Clear all filters
              </Button>
            )}
          </div>
        ) : (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredTemplates.map((template) => (
              <TemplateSelector
                key={template.id}
                template={template}
                isSelected={selectedTemplateId === template.id}
                isFavorite={favorites.has(template.id)}
                viewMode={viewMode}
                onSelect={() => onTemplateSelect(template)} 
                onPreview={() => setPreviewTemplate(template)}
                onToggleFavorite={() => toggleFavorite(template.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Preview Modal */}
      <AnimatePresence>
        {previewTemplate && showPreview && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            >
              <TemplatePreview
                template={previewTemplate}
                onEdit={() => {
                  onTemplateSelect(previewTemplate);
                  setPreviewTemplate(null);
                }}
                onExport={() => {
                  // Handle export
                  console.log('Export template:', previewTemplate.id);
                }}
                onShare={() => {
                  // Handle share
                  console.log('Share template:', previewTemplate.id);
                }}
                isEditable={true}
              />
              <div className="p-4 border-t border-gray-200 flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setPreviewTemplate(null)}
                >
                  Close
                </Button>
                <Button
                  variant="primary"
                  onClick={() => {
                    onTemplateSelect(previewTemplate);
                    setPreviewTemplate(null);
                  }}
                >
                  Use Template
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}