import React from 'react';
import { motion } from 'framer-motion';
import {
  StarIcon,
  EyeIcon,
  SparklesIcon,
  CheckIcon,
  SwatchIcon,
  DocumentTextIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

import type { ResumeTemplate } from '../../constants/resumeTemplates';
import Button from '../ui/Button';

interface TemplateSelectorProps {
  template: ResumeTemplate;
  isSelected: boolean;
  isFavorite: boolean;
  viewMode: 'grid' | 'list';
  onSelect: () => void;
  onPreview: () => void;
  onToggleFavorite: () => void;
  showActions?: boolean;
}

const LAYOUT_ICONS = {
  'single-column': '📄',
  'two-column': '📊', 
  'three-column': '📋',
  'sidebar': '🗂️'
};

const CATEGORY_COLORS = {
  'professional': 'bg-blue-100 text-blue-800',
  'creative': 'bg-purple-100 text-purple-800',
  'industry-specific': 'bg-green-100 text-green-800',
  'entry-level': 'bg-yellow-100 text-yellow-800',
  'mid-career': 'bg-orange-100 text-orange-800',
  'senior-executive': 'bg-red-100 text-red-800',
  'ats-optimized': 'bg-gray-100 text-gray-800',
  'executive': 'bg-indigo-100 text-indigo-800',
  'technical': 'bg-teal-100 text-teal-800',
  'modern': 'bg-pink-100 text-pink-800',
  'classic': 'bg-amber-100 text-amber-800',
  'minimalist': 'bg-slate-100 text-slate-800'
};

export default function TemplateSelector({
  template,
  isSelected,
  isFavorite,
  viewMode,
  onSelect,
  onPreview,
  onToggleFavorite,
  showActions = true
}: TemplateSelectorProps) {
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onSelect();
    }
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white rounded-lg border-2 transition-all duration-200 hover:shadow-lg cursor-pointer ${
          isSelected ? 'border-blue-500 shadow-md' : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={onSelect}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-label={`Select ${template.name} template`}
      >
        <div className="p-6 flex items-center gap-6">
          {/* Template Preview Thumbnail */}
          <div className="w-24 h-32 bg-gray-100 rounded-lg flex-shrink-0 overflow-hidden border border-gray-200">
            {template.previewImage ? (
              <img
                src={template.previewImage}
                alt={`${template.name} template preview`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to layout icon if image fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-2xl">
                {LAYOUT_ICONS[template.styles.layout] || '📄'}
              </div>
            )}
            
            {isSelected && (
              <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-blue-600" />
              </div>
            )}
          </div>

          {/* Template Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {template.name}
                  {template.isPremium && (
                    <SparklesIcon className="inline w-4 h-4 text-yellow-500 ml-2" />
                  )}
                </h3>
                <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                  {template.description}
                </p>
              </div>
              
              {showActions && (
                <div className="flex items-center gap-2 flex-shrink-0">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleFavorite();
                    }}
                    className="p-2 rounded-full hover:bg-gray-100 text-gray-400 hover:text-yellow-500 transition-colors"
                  >
                    {isFavorite ? (
                      <StarIconSolid className="w-5 h-5 text-yellow-500" />
                    ) : (
                      <StarIcon className="w-5 h-5" />
                    )}
                  </button>
                  <Button
                    variant="ghost"
                    size="sm" 
                    onClick={(e) => {
                      e.stopPropagation();
                      onPreview();
                    }}
                  >
                    <EyeIcon className="w-4 h-4 mr-1" />
                    Preview
                  </Button>
                </div>
              )}
            </div>

            {/* Tags and Features */}
            <div className="flex flex-wrap items-center gap-2 mb-3">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                CATEGORY_COLORS[template.category as keyof typeof CATEGORY_COLORS] || 'bg-gray-100 text-gray-800'
              }`}>
                {template.category.replace('-', ' ')}
              </span>
              
              {template.atsCompatible && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <CpuChipIcon className="w-3 h-3 mr-1" />
                  ATS
                </span>
              )}
              
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {template.styles.layout.replace('-', ' ')}
              </span>
              
              {template.isPremium ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <SparklesIcon className="w-3 h-3 mr-1" />
                  Premium
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Free
                </span>
              )}

              {template.colorSchemes && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  <SwatchIcon className="w-3 h-3 mr-1" />
                  {template.colorSchemes.length} colors
                </span>
              )}
            </div>

            {/* Industries and Features */}
            <div className="text-xs text-gray-500">
              <span className="font-medium">Industries:</span> {template.industry.slice(0, 3).join(', ')}
              {template.industry.length > 3 && ` +${template.industry.length - 3} more`}
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Grid view
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`bg-white rounded-lg border-2 transition-all duration-200 hover:shadow-lg cursor-pointer group relative ${
        isSelected ? 'border-blue-500 shadow-md' : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={onSelect}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Select ${template.name} template`}
    >
      {/* Template Preview */}
      <div className="relative aspect-[3/4] bg-gray-100 rounded-t-lg overflow-hidden">
        {template.previewImage ? (
          <img
            src={template.previewImage}
            alt={`${template.name} template preview`}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to layout icon if image fails to load  
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-4xl">
            {LAYOUT_ICONS[template.styles.layout] || '📄'}
          </div>
        )}
        
        {/* Favorite Button */}
        {showActions && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite();
            }}
            className="absolute top-3 right-3 p-2 rounded-full bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-400 hover:text-yellow-500 transition-all opacity-0 group-hover:opacity-100"
          >
            {isFavorite ? (
              <StarIconSolid className="w-5 h-5 text-yellow-500" />
            ) : (
              <StarIcon className="w-5 h-5" />
            )}
          </button>
        )}

        {/* Premium Badge */}
        {template.isPremium && (
          <div className="absolute top-3 left-3 bg-yellow-500 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center">
            <SparklesIcon className="w-3 h-3 mr-1" />
            Premium
          </div>
        )}

        {/* ATS Badge */}
        {template.atsCompatible && (
          <div className="absolute bottom-3 left-3 bg-green-500 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center">
            <CpuChipIcon className="w-3 h-3 mr-1" />
            ATS
          </div>
        )}

        {/* Selected Overlay */}
        {isSelected && (
          <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
            <div className="bg-blue-500 text-white rounded-full p-2">
              <CheckIcon className="w-6 h-6" />
            </div>
          </div>
        )}

        {/* Preview Button */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              variant="primary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onPreview();
              }}
              className="bg-white text-gray-900 hover:bg-gray-100"
            >
              <EyeIcon className="w-4 h-4 mr-1" />
              Preview
            </Button>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 truncate text-sm">
            {template.name}
          </h3>
        </div>
        
        <p className="text-xs text-gray-600 mb-3 line-clamp-2">
          {template.description}
        </p>

        {/* Category Tag */}
        <div className="flex items-center justify-between">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            CATEGORY_COLORS[template.category as keyof typeof CATEGORY_COLORS] || 'bg-gray-100 text-gray-800'
          }`}>
            {template.category.replace('-', ' ')}
          </span>
          
          {template.colorSchemes && (
            <span className="text-xs text-gray-500 flex items-center">
              <SwatchIcon className="w-3 h-3 mr-1" />
              {template.colorSchemes.length}
            </span>
          )}
        </div>

        {/* Layout Info */}
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>{template.styles.layout.replace('-', ' ')}</span>
          <span>{template.styles.fontFamily.split(',')[0]}</span>
        </div>
      </div>
    </motion.div>
  );
}