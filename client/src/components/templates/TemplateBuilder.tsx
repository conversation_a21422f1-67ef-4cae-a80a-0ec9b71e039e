import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import type { Template, TemplateUsageData } from '../../types/phase1';
import { templateAPI } from '../../services/templateAPI';

interface TemplateBuilderProps {
  onTemplateSelect?: (template: Template) => void;
  onTemplateCustomize?: (template: Template, customizations: Record<string, any>) => void;
}

export default function TemplateBuilder({ onTemplateSelect, onTemplateCustomize }: TemplateBuilderProps) {
  const { token } = useSelector((state: RootState) => state.auth);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [showPremiumOnly, setShowPremiumOnly] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, [selectedCategory, selectedIndustry, showPremiumOnly]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: { category?: string; industry?: string } = {};
      if (selectedCategory !== 'all') filters.category = selectedCategory;
      if (selectedIndustry !== 'all') filters.industry = selectedIndustry;

      const result = await templateAPI.getTemplates(filters);
      
      if (result.success) {
        let templatesData = result.data;
        
        // Filter by premium if needed
        if (showPremiumOnly) {
          templatesData = templatesData.filter(template => template.is_premium);
        }

        // Ensure templates have required fields with defaults
        const normalizedTemplates = templatesData.map(template => ({
          ...template,
          industry: Array.isArray(template.industry) ? template.industry : [template.industry || ''],
          analytics: template.analytics || {
            usageCount: template.usage_count || 0,
            successRate: 0,
            averageAtsScore: template.ats_score || 0,
            userRating: template.rating || 0
          }
        }));

        setTemplates(normalizedTemplates);
      } else {
        setError(result.error || 'Failed to fetch templates');
      }
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError('Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateUse = async (template: Template) => {
    setSelectedTemplate(template);
    onTemplateSelect?.(template);

    // Track template usage
    if (token) {
      try {
        const usageData: TemplateUsageData = {
          selectedSections: template.structure?.sections?.map((s: any) => s.id) || [],
          completionTime: Date.now(),
        };
        
        await templateAPI.trackTemplateUsage(template.id, usageData);
      } catch (err) {
        console.error('Error tracking template usage:', err);
      }
    }
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    onTemplateSelect?.(template);
  };

  const categories = ['all', 'professional', 'creative', 'executive', 'ats-optimized', 'industry-specific', 'entry-level', 'mid-career', 'senior-executive', 'technical'];
  const industries = ['all', 'Technology', 'Finance', 'Healthcare', 'Design', 'Marketing', 'Education', 'Engineering', 'Legal', 'Sales', 'Consulting', 'Government', 'Academia'];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
        {error}
        <button
          onClick={fetchTemplates}
          className="ml-4 text-red-700 underline hover:text-red-900"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Smart Resume Templates</h1>
        <p className="text-gray-600">Choose from our collection of ATS-optimized, industry-specific templates</p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {industries.map(industry => (
                <option key={industry} value={industry}>
                  {industry === 'all' ? 'All Industries' : industry}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showPremiumOnly}
                onChange={(e) => setShowPremiumOnly(e.target.checked)}
                className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Premium only</span>
            </label>
          </div>

          <div className="flex items-end">
            <button
              onClick={fetchTemplates}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplate?.id === template.id}
            onSelect={() => handleTemplateSelect(template)}
            onUse={() => handleTemplateUse(template)}
          />
        ))}
      </div>

      {templates.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No templates found matching your criteria.</p>
          <button
            onClick={() => {
              setSelectedCategory('all');
              setSelectedIndustry('all');
              setShowPremiumOnly(false);
            }}
            className="mt-4 text-blue-600 hover:text-blue-800"
          >
            Clear filters
          </button>
        </div>
      )}

      {/* Template Preview & Customization */}
      {selectedTemplate && (
        <TemplatePreview
          template={selectedTemplate}
          onCustomize={onTemplateCustomize}
        />
      )}
    </div>
  );
}

interface TemplateCardProps {
  template: Template;
  isSelected: boolean;
  onSelect: () => void;
  onUse: () => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ 
  template, 
  isSelected, 
  onSelect, 
  onUse 
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all cursor-pointer ${
        isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
      }`}
      onClick={onSelect}
    >
      <div className="relative">
        <div className="bg-gray-100 h-48 flex items-center justify-center">
          {template.preview_url ? (
            <img
              src={template.preview_url}
              alt={template.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-gray-500">Template Preview</span>
          )}
        </div>
        {template.is_premium && (
          <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Premium
          </div>
        )}
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
          ATS Score: {template.ats_score}%
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{template.name}</h3>
        <p className="text-gray-600 mb-4 text-sm">{template.description}</p>
        
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm bg-gray-100 text-gray-800 px-2 py-1 rounded capitalize">
            {template.category}
          </span>
          <div className="flex items-center">
            <span className="text-yellow-500">★</span>
            <span className="text-sm text-gray-600 ml-1">
              {template.analytics?.userRating || template.rating || 0}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <span className="text-xs text-gray-500">
            Used {template.analytics?.usageCount || template.usage_count || 0} times
          </span>
          {template.industry.length > 0 && (
            <span className="text-xs text-blue-600">
              {template.industry.slice(0, 2).join(', ')}
              {template.industry.length > 2 && ' +more'}
            </span>
          )}
        </div>

        <div className="flex space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUse();
            }}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            Use Template
          </button>
          <button 
            onClick={(e) => e.stopPropagation()}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm"
          >
            Preview
          </button>
        </div>
      </div>
    </div>
  );
};

interface TemplatePreviewProps {
  template: Template;
  onCustomize?: (template: Template, customizations: Record<string, any>) => void;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({ template, onCustomize }) => {
  const [customizations, setCustomizations] = useState<Record<string, any>>({});

  const handleCustomization = (key: string, value: any) => {
    const newCustomizations = { ...customizations, [key]: value };
    setCustomizations(newCustomizations);
    onCustomize?.(template, newCustomizations);
  };

  if (!template.structure || !template.structure.sections) {
    return (
      <div className="mt-8 border-t pt-6">
        <h3 className="text-xl font-semibold mb-4">Template Preview</h3>
        <div className="bg-gray-50 p-6 rounded-lg">
          <p className="text-gray-600">Preview not available for this template.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 border-t pt-6">
      <h3 className="text-xl font-semibold mb-4">Template Preview & Customization</h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Preview Panel */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-3">Preview</h4>
          <div className="bg-white border rounded p-4 min-h-96">
            <div className="space-y-4">
              <div className="text-lg font-bold">{template.name}</div>
              <div className="text-sm text-gray-600">
                Layout: {template.structure.layout?.type || 'single-column'}
              </div>
              <div className="text-sm text-gray-600">
                Font: {template.structure.styling?.fontFamily || 'Arial'}
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium">Sections:</h5>
                {template.structure.sections.map((section: any) => (
                  <div key={section.id} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded"></div>
                    <span className="text-sm">{section.name}</span>
                    {section.required && (
                      <span className="text-xs text-red-500">*</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Customization Panel */}
        <div className="space-y-4">
          <h4 className="font-medium">Customize Template</h4>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Font Family
            </label>
            <select
              value={customizations.fontFamily || template.structure.styling?.fontFamily || 'Arial'}
              onChange={(e) => handleCustomization('fontFamily', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Arial">Arial</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Georgia">Georgia</option>
              <option value="Helvetica">Helvetica</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Font Size
            </label>
            <select
              value={customizations.fontSize || template.structure.styling?.fontSize || 12}
              onChange={(e) => handleCustomization('fontSize', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="10">10pt</option>
              <option value="11">11pt</option>
              <option value="12">12pt</option>
              <option value="14">14pt</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Color Scheme
            </label>
            <select
              value={customizations.colorScheme || template.structure.styling?.colorScheme || 'professional'}
              onChange={(e) => handleCustomization('colorScheme', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="professional">Professional</option>
              <option value="creative">Creative</option>
              <option value="classic">Classic</option>
              <option value="modern">Modern</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};