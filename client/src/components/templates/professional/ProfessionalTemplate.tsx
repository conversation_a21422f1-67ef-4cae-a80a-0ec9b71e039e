import React from 'react';
import type { ResumeTemplate, TemplateSectionConfig } from '../../../constants/resumeTemplates';

interface ProfessionalTemplateProps {
  template: ResumeTemplate;
  data?: any;
  colorScheme?: string;
  className?: string;
}

interface TemplateData {
  personalInfo?: {
    name?: string;
    title?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    website?: string;
  };
  summary?: string;
  experience?: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate: string;
    description: string;
    achievements?: string[];
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    graduationDate: string;
    gpa?: string;
  }>;
  skills?: string[];
  projects?: Array<{
    name: string;
    description: string;
    technologies?: string[];
    link?: string;
  }>;
}

const DEFAULT_DATA: TemplateData = {
  personalInfo: {
    name: '<PERSON>',
    title: 'Senior Marketing Manager',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'New York, NY',
    linkedin: 'linkedin.com/in/johnsmith',
    website: 'johnsmith.com'
  },
  summary: 'Results-driven marketing professional with 8+ years of experience developing and executing comprehensive marketing strategies. Proven track record of increasing brand awareness by 150% and driving revenue growth of $2M+ annually.',
  experience: [
    {
      company: 'TechCorp Solutions',
      title: 'Senior Marketing Manager',
      startDate: '2021',
      endDate: 'Present',
      description: 'Lead comprehensive marketing initiatives for B2B technology solutions',
      achievements: [
        'Increased lead generation by 200% through strategic digital marketing campaigns',
        'Managed marketing budget of $500K+ with 15% cost reduction',
        'Led cross-functional team of 8 marketing professionals'
      ]
    },
    {
      company: 'Innovation Labs',
      title: 'Marketing Specialist',
      startDate: '2019',
      endDate: '2021',
      description: 'Developed and implemented marketing strategies for startup environment',
      achievements: [
        'Created content marketing strategy resulting in 300% website traffic increase',
        'Managed social media presence across 5 platforms with 50K+ followers',
        'Coordinated product launch campaigns for 3 major releases'
      ]
    }
  ],
  education: [
    {
      institution: 'University of California, Berkeley',
      degree: 'Master of Business Administration',
      field: 'Marketing',
      graduationDate: '2019'
    },
    {
      institution: 'New York University',
      degree: 'Bachelor of Arts',
      field: 'Communications',
      graduationDate: '2017'
    }
  ],
  skills: [
    'Digital Marketing', 'Content Strategy', 'SEO/SEM', 'Google Analytics',
    'Marketing Automation', 'Brand Management', 'Lead Generation', 'Team Leadership'
  ],
  projects: [
    {
      name: 'Brand Redesign Initiative',
      description: 'Led complete rebrand including logo, website, and marketing materials',
      technologies: ['Adobe Creative Suite', 'WordPress', 'Google Analytics']
    },
    {
      name: 'Marketing Automation Platform',
      description: 'Implemented HubSpot for lead nurturing and customer journey mapping',
      technologies: ['HubSpot', 'Salesforce', 'Zapier']
    }
  ]
};

export default function ProfessionalTemplate({
  template,
  data,
  colorScheme,
  className = ''
}: ProfessionalTemplateProps) {
  const templateData = { ...DEFAULT_DATA, ...data };
  const styles = template.styles;
  
  // Apply color scheme if provided
  const appliedColorScheme = colorScheme && template.colorSchemes 
    ? template.colorSchemes.find(cs => cs.name === colorScheme)
    : null;
  
  const colors = appliedColorScheme ? {
    primary: appliedColorScheme.primaryColor,
    secondary: appliedColorScheme.secondaryColor,
    accent: appliedColorScheme.accentColor
  } : {
    primary: styles.primaryColor,
    secondary: styles.secondaryColor,
    accent: styles.accentColor
  };

  const renderHeader = () => (
    <div className="template-header mb-8" style={{ borderBottomColor: colors.primary }}>
      <div className={`${styles.layout === 'two-column' ? 'flex justify-between items-start' : 'text-center'}`}>
        <div>
          <h1 
            className="text-3xl font-bold mb-2"
            style={{ color: colors.primary }}
          >
            {templateData.personalInfo?.name}
          </h1>
          <h2 
            className="text-xl mb-4"
            style={{ color: colors.secondary }}
          >
            {templateData.personalInfo?.title}
          </h2>
        </div>
        
        <div className={`${styles.layout === 'two-column' ? 'text-right' : 'text-center'} text-sm`}>
          {templateData.personalInfo?.email && (
            <div className="mb-1">
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.email}</span>
            </div>
          )}
          {templateData.personalInfo?.phone && (
            <div className="mb-1">
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.phone}</span>
            </div>
          )}
          {templateData.personalInfo?.location && (
            <div className="mb-1">
              <span style={{ color: colors.secondary }}>{templateData.personalInfo.location}</span>
            </div>
          )}
          {templateData.personalInfo?.linkedin && (
            <div className="mb-1">
              <span style={{ color: colors.accent }}>{templateData.personalInfo.linkedin}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderSection = (sectionConfig: TemplateSectionConfig) => {
    const sectionStyle = {
      marginBottom: styles.spacing === 'compact' ? '1rem' : styles.spacing === 'spacious' ? '2rem' : '1.5rem'
    };

    switch (sectionConfig.type) {
      case 'summary':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-3 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <p className="text-sm leading-relaxed" style={{ color: colors.secondary }}>
              {templateData.summary}
            </p>
          </div>
        );

      case 'experience':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-4">
              {templateData.experience?.map((exp, index) => (
                <div key={index} className="experience-item">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold text-base" style={{ color: colors.primary }}>
                        {exp.title}
                      </h4>
                      <p className="font-medium" style={{ color: colors.secondary }}>
                        {exp.company}
                      </p>
                    </div>
                    <span className="text-sm font-medium" style={{ color: colors.accent }}>
                      {exp.startDate} - {exp.endDate}
                    </span>
                  </div>
                  <p className="text-sm mb-2 text-gray-700">{exp.description}</p>
                  {exp.achievements && (
                    <ul className="text-sm space-y-1">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start">
                          <span style={{ color: colors.accent }} className="mr-2 mt-1">•</span>
                          <span className="text-gray-700">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-3">
              {templateData.education?.map((edu, index) => (
                <div key={index} className="education-item">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold" style={{ color: colors.primary }}>
                        {edu.degree} in {edu.field}
                      </h4>
                      <p className="font-medium" style={{ color: colors.secondary }}>
                        {edu.institution}
                      </p>
                    </div>
                    <span className="text-sm font-medium" style={{ color: colors.accent }}>
                      {edu.graduationDate}
                    </span>
                  </div>
                  {edu.gpa && (
                    <p className="text-sm text-gray-600">GPA: {edu.gpa}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="flex flex-wrap gap-2">
              {templateData.skills?.map((skill, index) => (
                <span
                  key={index}
                  className="px-3 py-1 text-sm font-medium rounded-full"
                  style={{ 
                    backgroundColor: `${colors.accent}20`,
                    color: colors.accent,
                    border: `1px solid ${colors.accent}30`
                  }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        );

      case 'projects':
        return (
          <div key={sectionConfig.id} className="template-section" style={sectionStyle}>
            <h3 
              className="text-lg font-bold mb-4 pb-1 border-b"
              style={{ color: colors.primary, borderBottomColor: colors.primary }}
            >
              {sectionConfig.title}
            </h3>
            <div className="space-y-3">
              {templateData.projects?.map((project, index) => (
                <div key={index} className="project-item">
                  <h4 className="font-semibold mb-1" style={{ color: colors.primary }}>
                    {project.name}
                  </h4>
                  <p className="text-sm text-gray-700 mb-2">{project.description}</p>
                  {project.technologies && (
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 text-xs rounded"
                          style={{ 
                            backgroundColor: `${colors.secondary}20`,
                            color: colors.secondary
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div 
      className={`template-preview template-professional font-${styles.fontFamily.split(',')[0].toLowerCase().replace(' ', '-')} ${className}`}
      style={{
        fontFamily: styles.fontFamily,
        padding: styles.spacing === 'compact' ? '2rem' : styles.spacing === 'spacious' ? '3rem' : '2.5rem'
      }}
    >
      {renderHeader()}
      
      <div className={`${styles.layout === 'two-column' ? 'grid grid-cols-3 gap-8' : ''}`}>
        <div className={styles.layout === 'two-column' ? 'col-span-2' : ''}>
          {template.sections
            .filter(section => ['summary', 'experience'].includes(section.type))
            .sort((a, b) => a.order - b.order)
            .map(renderSection)}
        </div>
        
        {styles.layout === 'two-column' && (
          <div className="col-span-1">
            {template.sections
              .filter(section => ['education', 'skills', 'projects'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
        )}
        
        {styles.layout === 'single-column' && (
          <>
            {template.sections
              .filter(section => !['summary', 'experience'].includes(section.type))
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </>
        )}
      </div>
    </div>
  );
}