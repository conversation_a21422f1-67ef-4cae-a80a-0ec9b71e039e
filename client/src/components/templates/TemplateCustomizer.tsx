import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SwatchIcon,
  AdjustmentsHorizontalIcon,
  PaintBrushIcon,
  EyeIcon,
  ArrowPathIcon,
  CheckIcon,
  XMarkIcon,
  DocumentTextIcon,
  Squares2X2Icon,
  RectangleGroupIcon
} from '@heroicons/react/24/outline';
import type { ResumeTemplate, ColorScheme } from '../../constants/resumeTemplates';
import { COLOR_SCHEMES } from '../../constants/templateGenerator';
import { applyColorScheme } from '../../utils/templateUtils';

interface TemplateCustomizerProps {
  template: ResumeTemplate;
  onTemplateUpdate: (updatedTemplate: ResumeTemplate) => void;
  onPreview?: () => void;
  onSave?: () => void;
  onReset?: () => void;
}

interface CustomizationState {
  colors: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
  };
  fonts: {
    fontFamily: string;
    headingFont?: string;
    bodyFont?: string;
  };
  layout: {
    layout: 'single-column' | 'two-column' | 'three-column' | 'sidebar';
    spacing: 'compact' | 'normal' | 'spacious';
  };
  sections: {
    [sectionId: string]: {
      visible: boolean;
      order: number;
      styling: any;
    };
  };
}

const FONT_OPTIONS = [
  { name: 'Inter', family: 'Inter, sans-serif', category: 'Modern' },
  { name: 'Arial', family: 'Arial, sans-serif', category: 'Classic' },
  { name: 'Times New Roman', family: 'Times New Roman, serif', category: 'Traditional' },
  { name: 'Georgia', family: 'Georgia, serif', category: 'Elegant' },
  { name: 'Helvetica', family: 'Helvetica, sans-serif', category: 'Clean' },
  { name: 'Calibri', family: 'Calibri, sans-serif', category: 'Modern' },
  { name: 'Montserrat', family: 'Montserrat, sans-serif', category: 'Creative' },
  { name: 'Open Sans', family: 'Open Sans, sans-serif', category: 'Friendly' },
  { name: 'Roboto', family: 'Roboto, sans-serif', category: 'Technical' },
  { name: 'Playfair Display', family: 'Playfair Display, serif', category: 'Sophisticated' },
  { name: 'Lato', family: 'Lato, sans-serif', category: 'Approachable' },
  { name: 'Source Sans Pro', family: 'Source Sans Pro, sans-serif', category: 'Professional' }
];

const LAYOUT_OPTIONS = [
  { value: 'single-column', label: 'Single Column', icon: DocumentTextIcon },
  { value: 'two-column', label: 'Two Column', icon: Squares2X2Icon },
  { value: 'three-column', label: 'Three Column', icon: RectangleGroupIcon },
  { value: 'sidebar', label: 'Sidebar', icon: RectangleGroupIcon }
];

const SPACING_OPTIONS = [
  { value: 'compact', label: 'Compact' },
  { value: 'normal', label: 'Normal' },
  { value: 'spacious', label: 'Spacious' }
];

export default function TemplateCustomizer({
  template,
  onTemplateUpdate,
  onPreview,
  onSave,
  onReset
}: TemplateCustomizerProps) {
  const [activeTab, setActiveTab] = useState<'colors' | 'fonts' | 'layout' | 'sections'>('colors');
  const [customization, setCustomization] = useState<CustomizationState>({
    colors: {
      primaryColor: template.styles.primaryColor,
      secondaryColor: template.styles.secondaryColor,
      accentColor: template.styles.accentColor
    },
    fonts: {
      fontFamily: template.styles.fontFamily
    },
    layout: {
      layout: template.styles.layout,
      spacing: template.styles.spacing
    },
    sections: template.sections.reduce((acc, section) => ({
      ...acc,
      [section.id]: {
        visible: true,
        order: section.order,
        styling: section.styling
      }
    }), {})
  });
  
  const [isCustomColor, setIsCustomColor] = useState(false);
  const [selectedColorScheme, setSelectedColorScheme] = useState<string | null>(null);

  // Get available color schemes for the template category
  const availableColorSchemes = COLOR_SCHEMES[template.category] || COLOR_SCHEMES.professional;

  useEffect(() => {
    // Apply customizations to template
    const updatedTemplate: ResumeTemplate = {
      ...template,
      styles: {
        ...template.styles,
        ...customization.colors,
        fontFamily: customization.fonts.fontFamily,
        layout: customization.layout.layout,
        spacing: customization.layout.spacing
      },
      sections: template.sections.map(section => ({
        ...section,
        ...customization.sections[section.id],
        order: customization.sections[section.id]?.order || section.order
      })).sort((a, b) => a.order - b.order)
    };

    onTemplateUpdate(updatedTemplate);
  }, [customization, template, onTemplateUpdate]);

  const handleColorSchemeSelect = (colorScheme: ColorScheme) => {
    setCustomization(prev => ({
      ...prev,
      colors: {
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor
      }
    }));
    setSelectedColorScheme(colorScheme.name);
    setIsCustomColor(false);
  };

  const handleCustomColorChange = (colorType: keyof CustomizationState['colors'], color: string) => {
    setCustomization(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: color
      }
    }));
    setIsCustomColor(true);
    setSelectedColorScheme(null);
  };

  const handleFontChange = (fontFamily: string) => {
    setCustomization(prev => ({
      ...prev,
      fonts: { ...prev.fonts, fontFamily }
    }));
  };

  const handleLayoutChange = (layout: CustomizationState['layout']['layout']) => {
    setCustomization(prev => ({
      ...prev,
      layout: { ...prev.layout, layout }
    }));
  };

  const handleSpacingChange = (spacing: CustomizationState['layout']['spacing']) => {
    setCustomization(prev => ({
      ...prev,
      layout: { ...prev.layout, spacing }
    }));
  };

  const handleSectionVisibilityToggle = (sectionId: string) => {
    setCustomization(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        [sectionId]: {
          ...prev.sections[sectionId],
          visible: !prev.sections[sectionId]?.visible
        }
      }
    }));
  };

  const handleSectionOrderChange = (sectionId: string, newOrder: number) => {
    setCustomization(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        [sectionId]: {
          ...prev.sections[sectionId],
          order: newOrder
        }
      }
    }));
  };

  const resetToDefault = () => {
    setCustomization({
      colors: {
        primaryColor: template.styles.primaryColor,
        secondaryColor: template.styles.secondaryColor,
        accentColor: template.styles.accentColor
      },
      fonts: {
        fontFamily: template.styles.fontFamily
      },
      layout: {
        layout: template.styles.layout,
        spacing: template.styles.spacing
      },
      sections: template.sections.reduce((acc, section) => ({
        ...acc,
        [section.id]: {
          visible: true,
          order: section.order,
          styling: section.styling
        }
      }), {})
    });
    setSelectedColorScheme(null);
    setIsCustomColor(false);
    onReset?.();
  };

  const tabs = [
    { id: 'colors', label: 'Colors', icon: SwatchIcon },
    { id: 'fonts', label: 'Fonts', icon: DocumentTextIcon },
    { id: 'layout', label: 'Layout', icon: RectangleGroupIcon },
    { id: 'sections', label: 'Sections', icon: AdjustmentsHorizontalIcon }
  ];

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Customize Template</h3>
        <div className="flex items-center space-x-2">
          {onPreview && (
            <button
              onClick={onPreview}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
            >
              <EyeIcon className="h-4 w-4" />
              <span className="text-sm">Preview</span>
            </button>
          )}
          <button
            onClick={resetToDefault}
            className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
          >
            <ArrowPathIcon className="h-4 w-4" />
            <span className="text-sm">Reset</span>
          </button>
          {onSave && (
            <button
              onClick={onSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <CheckIcon className="h-4 w-4" />
              <span className="text-sm">Save</span>
            </button>
          )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <IconComponent className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto p-4">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'colors' && (
              <div className="space-y-6">
                {/* Predefined Color Schemes */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Color Schemes</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {availableColorSchemes.map((scheme) => (
                      <motion.button
                        key={scheme.name}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleColorSchemeSelect(scheme)}
                        className={`p-3 border rounded-lg text-left transition-all ${
                          selectedColorScheme === scheme.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3 mb-2">
                          <div className="flex space-x-1">
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: scheme.primaryColor }}
                            />
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: scheme.secondaryColor }}
                            />
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: scheme.accentColor }}
                            />
                          </div>
                          {selectedColorScheme === scheme.name && (
                            <CheckIcon className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                        <div className="text-sm font-medium text-gray-900">{scheme.name}</div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Custom Colors */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Custom Colors</h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          Primary Color
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            value={customization.colors.primaryColor}
                            onChange={(e) => handleCustomColorChange('primaryColor', e.target.value)}
                            className="w-8 h-8 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={customization.colors.primaryColor}
                            onChange={(e) => handleCustomColorChange('primaryColor', e.target.value)}
                            className="flex-1 text-xs border border-gray-300 rounded px-2 py-1"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          Secondary Color
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            value={customization.colors.secondaryColor}
                            onChange={(e) => handleCustomColorChange('secondaryColor', e.target.value)}
                            className="w-8 h-8 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={customization.colors.secondaryColor}
                            onChange={(e) => handleCustomColorChange('secondaryColor', e.target.value)}
                            className="flex-1 text-xs border border-gray-300 rounded px-2 py-1"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          Accent Color
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            value={customization.colors.accentColor}
                            onChange={(e) => handleCustomColorChange('accentColor', e.target.value)}
                            className="w-8 h-8 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={customization.colors.accentColor}
                            onChange={(e) => handleCustomColorChange('accentColor', e.target.value)}
                            className="flex-1 text-xs border border-gray-300 rounded px-2 py-1"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'fonts' && (
              <div className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Font Family</h4>
                  <div className="space-y-2">
                    {FONT_OPTIONS.map((font) => (
                      <button
                        key={font.family}
                        onClick={() => handleFontChange(font.family)}
                        className={`w-full p-3 border rounded-lg text-left transition-all ${
                          customization.fonts.fontFamily === font.family
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        style={{ fontFamily: font.family }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{font.name}</div>
                            <div className="text-sm text-gray-500">{font.category}</div>
                          </div>
                          {customization.fonts.fontFamily === font.family && (
                            <CheckIcon className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                        <div className="mt-2 text-sm text-gray-700">
                          The quick brown fox jumps over the lazy dog
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'layout' && (
              <div className="space-y-6">
                {/* Layout Structure */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Layout Structure</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {LAYOUT_OPTIONS.map((layout) => {
                      const IconComponent = layout.icon;
                      return (
                        <button
                          key={layout.value}
                          onClick={() => handleLayoutChange(layout.value as any)}
                          className={`p-4 border rounded-lg transition-all ${
                            customization.layout.layout === layout.value
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <IconComponent className="h-8 w-8 mx-auto mb-2 text-gray-600" />
                          <div className="text-sm font-medium text-gray-900">{layout.label}</div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Spacing */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Spacing</h4>
                  <div className="grid grid-cols-3 gap-3">
                    {SPACING_OPTIONS.map((spacing) => (
                      <button
                        key={spacing.value}
                        onClick={() => handleSpacingChange(spacing.value as any)}
                        className={`p-3 border rounded-lg text-center transition-all ${
                          customization.layout.spacing === spacing.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="text-sm font-medium text-gray-900">{spacing.label}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'sections' && (
              <div className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Section Management</h4>
                  <div className="space-y-2">
                    {template.sections.map((section) => (
                      <div
                        key={section.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={customization.sections[section.id]?.visible !== false}
                            onChange={() => handleSectionVisibilityToggle(section.id)}
                            className="rounded"
                          />
                          <div>
                            <div className="font-medium text-gray-900">{section.title}</div>
                            <div className="text-sm text-gray-500 capitalize">{section.type}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="number"
                            min="1"
                            max={template.sections.length}
                            value={customization.sections[section.id]?.order || section.order}
                            onChange={(e) => handleSectionOrderChange(section.id, parseInt(e.target.value))}
                            className="w-16 text-center border border-gray-300 rounded px-2 py-1 text-sm"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}