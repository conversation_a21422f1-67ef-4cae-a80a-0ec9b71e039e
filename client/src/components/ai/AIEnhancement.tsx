import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface AIEnhancementProps {
  resumeData?: any;
  onEnhancement?: (enhancement: any) => void;
}

export default function AIEnhancement({ resumeData, onEnhancement }: AIEnhancementProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [enhancement, setEnhancement] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [targetJob, setTargetJob] = useState('');
  
  const { token } = useSelector((state: RootState) => state.auth);

  const handleEnhanceResume = async () => {
    if (!resumeData || !token) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/enhance-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ resumeData, targetJob }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance resume');
      }

      const data = await response.json();
      setEnhancement(data.enhancement);
      onEnhancement?.(data.enhancement);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const [atsAnalysis, setAtsAnalysis] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleAnalyzeATS = async () => {
    if (!resumeData || !token) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/analyze-ats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ resumeData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze ATS compatibility');
      }

      const data = await response.json();
      setAtsAnalysis(data.analysis);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const [skillSuggestions, setSkillSuggestions] = useState<any>(null);
  const [isSuggestingSkills, setIsSuggestingSkills] = useState(false);

  const handleSuggestSkills = async () => {
    if (!resumeData || !token) return;

    setIsSuggestingSkills(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/suggest-skills', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ 
          currentSkills: resumeData.skills || [],
          jobTitle: resumeData.title || 'Software Engineer'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to suggest skills');
      }

      const data = await response.json();
      setSkillSuggestions(data.suggestions);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsSuggestingSkills(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">AI Enhancement Tools</h3>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="space-y-6">
        {/* Resume Enhancement */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">Enhance Resume Content</h4>
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Job (Optional)
            </label>
            <input
              type="text"
              value={targetJob}
              onChange={(e) => setTargetJob(e.target.value)}
              placeholder="e.g., Senior Software Engineer"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <button
            onClick={handleEnhanceResume}
            disabled={isLoading || !resumeData}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Enhancing...' : 'Enhance with AI'}
          </button>
          
          {enhancement && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h5 className="font-medium text-green-900 mb-2">Enhanced Content:</h5>
              <div className="text-sm text-green-800 space-y-2">
                <div>
                  <strong>Enhanced Summary:</strong> {enhancement.enhancedSummary}
                </div>
                {enhancement.suggestedSkills?.length > 0 && (
                  <div>
                    <strong>Suggested Skills:</strong> {enhancement.suggestedSkills.join(', ')}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* ATS Analysis */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">ATS Compatibility Analysis</h4>
          <button
            onClick={handleAnalyzeATS}
            disabled={isAnalyzing || !resumeData}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze ATS Compatibility'}
          </button>
          
          {atsAnalysis && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h5 className="font-medium text-blue-900 mb-2">ATS Score: {atsAnalysis.overallScore}/100</h5>
              <div className="text-sm text-blue-800 space-y-2">
                {atsAnalysis.recommendations?.length > 0 && (
                  <div>
                    <strong>Recommendations:</strong>
                    <ul className="list-disc list-inside mt-1">
                      {atsAnalysis.recommendations.map((rec: string, idx: number) => (
                        <li key={idx}>{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Skill Suggestions */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">Skill Suggestions</h4>
          <button
            onClick={handleSuggestSkills}
            disabled={isSuggestingSkills || !resumeData}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSuggestingSkills ? 'Getting Suggestions...' : 'Get Skill Suggestions'}
          </button>
          
          {skillSuggestions && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h5 className="font-medium text-green-900 mb-2">Skill Suggestions:</h5>
              <div className="text-sm text-green-800 space-y-2">
                {skillSuggestions.technicalSkills?.length > 0 && (
                  <div>
                    <strong>Technical Skills:</strong> {skillSuggestions.technicalSkills.join(', ')}
                  </div>
                )}
                {skillSuggestions.softSkills?.length > 0 && (
                  <div>
                    <strong>Soft Skills:</strong> {skillSuggestions.softSkills.join(', ')}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}