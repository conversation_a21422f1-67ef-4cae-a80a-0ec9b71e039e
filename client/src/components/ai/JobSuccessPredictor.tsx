import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface JobSuccessPredictor {
  resumeData?: any;
}

export default function JobSuccessPredictor({ resumeData }: JobSuccessPredictor) {
  const [prediction, setPrediction] = useState<any>(null);
  const [isPredicting, setIsPredicting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [companyData, setCompanyData] = useState({
    name: '',
    industry: '',
    size: ''
  });
  
  const { token } = useSelector((state: RootState) => state.auth);

  const handlePredictSuccess = async () => {
    if (!resumeData || !token || !jobDescription) return;

    setIsPredicting(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/predict-job-success', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ 
          resumeData, 
          jobDescription,
          companyData
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to predict job success');
      }

      const data = await response.json();
      setPrediction(data.prediction);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsPredicting(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">🎯 Job Success Predictor</h3>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {/* Job Description Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Job Description *
          </label>
          <textarea
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the job description here..."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        {/* Company Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={companyData.name}
              onChange={(e) => setCompanyData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Google, Microsoft"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Industry
            </label>
            <input
              type="text"
              value={companyData.industry}
              onChange={(e) => setCompanyData(prev => ({ ...prev, industry: e.target.value }))}
              placeholder="e.g., Technology, Finance"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company Size
            </label>
            <select
              value={companyData.size}
              onChange={(e) => setCompanyData(prev => ({ ...prev, size: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Select size</option>
              <option value="startup">Startup (1-50)</option>
              <option value="small">Small (51-200)</option>
              <option value="medium">Medium (201-1000)</option>
              <option value="large">Large (1000+)</option>
            </select>
          </div>
        </div>

        <button
          onClick={handlePredictSuccess}
          disabled={isPredicting || !resumeData || !jobDescription}
          className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isPredicting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Analyzing Success Probability...
            </>
          ) : (
            'Predict Job Success'
          )}
        </button>
      </div>

      {/* Prediction Results */}
      {prediction && (
        <div className="mt-6 border-t pt-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Success Prediction Analysis</h4>

          {/* Overall Success Probability */}
          <div className="mb-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-lg font-medium text-gray-900">Success Probability</span>
              <span className={`text-3xl font-bold ${getScoreColor(prediction.successProbability)}`}>
                {prediction.successProbability}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div 
                className={`h-4 rounded-full transition-all duration-500 ${getScoreBgColor(prediction.successProbability)}`}
                style={{ width: `${prediction.successProbability}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              Confidence Level: {prediction.confidenceLevel}%
            </div>
          </div>

          {/* Detailed Scores */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className={`text-2xl font-bold ${getScoreColor(prediction.skillMatch)}`}>
                {prediction.skillMatch}%
              </div>
              <div className="text-sm text-gray-600">Skill Match</div>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className={`text-2xl font-bold ${getScoreColor(prediction.experienceRelevance)}`}>
                {prediction.experienceRelevance}%
              </div>
              <div className="text-sm text-gray-600">Experience Relevance</div>
            </div>
            
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className={`text-2xl font-bold ${getScoreColor(prediction.culturalFit)}`}>
                {prediction.culturalFit}%
              </div>
              <div className="text-sm text-gray-600">Cultural Fit</div>
            </div>
            
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className={`text-2xl font-bold ${getScoreColor(prediction.competitiveness)}`}>
                {prediction.competitiveness}%
              </div>
              <div className="text-sm text-gray-600">Competitiveness</div>
            </div>
          </div>

          {/* Market Outlook */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h5 className="text-md font-semibold text-gray-900 mb-2">Market Outlook</h5>
            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
              prediction.marketOutlook === 'favorable' ? 'bg-green-100 text-green-800' :
              prediction.marketOutlook === 'competitive' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {prediction.marketOutlook.charAt(0).toUpperCase() + prediction.marketOutlook.slice(1)}
            </span>
          </div>

          {/* Strengths and Improvement Areas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {prediction.strengthAreas && prediction.strengthAreas.length > 0 && (
              <div>
                <h5 className="text-md font-semibold text-green-700 mb-3">✅ Strength Areas</h5>
                <ul className="space-y-2">
                  {prediction.strengthAreas.map((area: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 bg-green-50 p-2 rounded flex items-start space-x-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>{area}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {prediction.improvementAreas && prediction.improvementAreas.length > 0 && (
              <div>
                <h5 className="text-md font-semibold text-orange-700 mb-3">🎯 Improvement Areas</h5>
                <ul className="space-y-2">
                  {prediction.improvementAreas.map((area: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 bg-orange-50 p-2 rounded flex items-start space-x-2">
                      <span className="text-orange-500 mt-1">•</span>
                      <span>{area}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Recommendations */}
          {prediction.recommendations && prediction.recommendations.length > 0 && (
            <div>
              <h5 className="text-md font-semibold text-blue-700 mb-3">💡 Recommendations</h5>
              <div className="space-y-3">
                {prediction.recommendations.map((rec: any, index: number) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                        rec.impact === 'high' ? 'bg-red-100 text-red-800' :
                        rec.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {rec.impact} impact
                      </span>
                      <span className="text-xs text-gray-500">{rec.timeToImplement}</span>
                    </div>
                    <p className="text-sm text-gray-700">{rec.suggestion}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}