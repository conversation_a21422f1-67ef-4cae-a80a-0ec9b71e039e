import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface InterviewSimulatorProps {
  resumeData?: any;
}

export default function InterviewSimulator({ resumeData }: InterviewSimulatorProps) {
  const [simulation, setSimulation] = useState<any>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [industry, setIndustry] = useState('');
  const [difficulty, setDifficulty] = useState('medium');
  
  const { token } = useSelector((state: RootState) => state.auth);

  const handleSimulateInterview = async () => {
    if (!resumeData || !token || !jobDescription) return;

    setIsSimulating(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/simulate-interview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ 
          resumeData, 
          jobDescription,
          industry,
          difficulty
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to simulate interview');
      }

      const data = await response.json();
      setSimulation(data.simulation);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsSimulating(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">🎤 Interview Simulator</h3>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {/* Job Description Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Job Description *
          </label>
          <textarea
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the job description here..."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        {/* Industry Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Industry (Optional)
          </label>
          <input
            type="text"
            value={industry}
            onChange={(e) => setIndustry(e.target.value)}
            placeholder="e.g., Technology, Finance, Healthcare"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        {/* Difficulty Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Interview Difficulty
          </label>
          <select
            value={difficulty}
            onChange={(e) => setDifficulty(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="easy">Easy - Entry Level</option>
            <option value="medium">Medium - Mid Level</option>
            <option value="hard">Hard - Senior Level</option>
          </select>
        </div>

        <button
          onClick={handleSimulateInterview}
          disabled={isSimulating || !resumeData || !jobDescription}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isSimulating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Simulating Interview...
            </>
          ) : (
            'Start Interview Simulation'
          )}
        </button>
      </div>

      {/* Simulation Results */}
      {simulation && (
        <div className="mt-6 border-t pt-6">
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-lg font-semibold text-gray-900">Interview Simulation Results</h4>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Overall Score:</span>
                <span className={`text-lg font-bold ${
                  simulation.overallScore >= 80 ? 'text-green-600' :
                  simulation.overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {simulation.overallScore}/100
                </span>
              </div>
            </div>
            
            {/* Score Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  simulation.overallScore >= 80 ? 'bg-green-500' :
                  simulation.overallScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${simulation.overallScore}%` }}
              ></div>
            </div>
          </div>

          {/* Questions and Answers */}
          <div className="space-y-6">
            <h5 className="text-md font-semibold text-gray-900">Interview Questions & Guidance</h5>
            {simulation.questions?.map((question: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-2 mb-3">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                    {question.type}
                  </span>
                  <span className="text-sm font-semibold text-gray-900">
                    Question {index + 1}
                  </span>
                </div>
                
                <p className="text-gray-800 mb-3 font-medium">{question.question}</p>
                
                <div className="space-y-3">
                  <div>
                    <h6 className="text-sm font-medium text-gray-700 mb-1">Sample Strong Answer:</h6>
                    <p className="text-sm text-gray-600 bg-green-50 p-3 rounded border-l-4 border-green-400">
                      {question.sampleAnswer}
                    </p>
                  </div>
                  
                  {question.improvementTips && question.improvementTips.length > 0 && (
                    <div>
                      <h6 className="text-sm font-medium text-gray-700 mb-1">Improvement Tips:</h6>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {question.improvementTips.map((tip: string, tipIndex: number) => (
                          <li key={tipIndex} className="flex items-start space-x-2">
                            <span className="text-blue-500 mt-1">•</span>
                            <span>{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Strengths and Areas for Improvement */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            {simulation.strengths && simulation.strengths.length > 0 && (
              <div>
                <h5 className="text-md font-semibold text-green-700 mb-3">✅ Strengths</h5>
                <ul className="space-y-2">
                  {simulation.strengths.map((strength: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 bg-green-50 p-2 rounded">
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {simulation.areasForImprovement && simulation.areasForImprovement.length > 0 && (
              <div>
                <h5 className="text-md font-semibold text-orange-700 mb-3">🎯 Areas for Improvement</h5>
                <ul className="space-y-2">
                  {simulation.areasForImprovement.map((area: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 bg-orange-50 p-2 rounded">
                      {area}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Preparation Tips */}
          {simulation.preparationTips && simulation.preparationTips.length > 0 && (
            <div className="mt-6">
              <h5 className="text-md font-semibold text-blue-700 mb-3">💡 Preparation Tips</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {simulation.preparationTips.map((tip: string, index: number) => (
                  <div key={index} className="text-sm text-gray-700 bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                    {tip}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}