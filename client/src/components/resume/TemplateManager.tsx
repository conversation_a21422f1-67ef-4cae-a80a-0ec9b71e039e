import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import {
  SaveIcon,
  FolderIcon,
  ShareIcon,
  DownloadIcon,
  UploadIcon,
  TrashIcon,
  CopyIcon,
  XIcon,
  StarIcon,
  ClockIcon,
  UsersIcon,
  FilterIcon
} from 'lucide-react';
import type { RootState } from '../../store';
import type { ModularTemplate } from '../../types/resumeModules';
import { setCurrentTemplate } from '../../store/modularResumeSlice';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface TemplateManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SavedTemplate extends ModularTemplate {
  isShared: boolean;
  isStarred: boolean;
  downloads: number;
  lastModified: string;
  thumbnail?: string;
}

// Mock data for saved templates
const MOCK_SAVED_TEMPLATES: SavedTemplate[] = [
  {
    id: '1',
    name: 'Software Engineer Resume',
    description: 'Clean and professional layout optimized for tech roles',
    sections: [],
    globalStyling: {
      fontFamily: 'system-ui, sans-serif',
      fontSize: 12,
      colorScheme: 'professional',
      layout: 'single-column',
      spacing: 'normal'
    },
    metadata: {
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z',
      version: '1.2.0'
    },
    isShared: true,
    isStarred: true,
    downloads: 1247,
    lastModified: '2 days ago'
  },
  {
    id: '2',
    name: 'Creative Designer Layout',
    description: 'Bold and colorful design for creative professionals',
    sections: [],
    globalStyling: {
      fontFamily: 'Inter, sans-serif',
      fontSize: 14,
      colorScheme: 'creative',
      layout: 'two-column',
      spacing: 'relaxed'
    },
    metadata: {
      createdAt: '2024-01-10T08:00:00Z',
      updatedAt: '2024-01-18T16:15:00Z',
      version: '1.1.0'
    },
    isShared: false,
    isStarred: false,
    downloads: 0,
    lastModified: '1 week ago'
  },
  {
    id: '3',
    name: 'Executive Summary',
    description: 'Sophisticated template for senior-level positions',
    sections: [],
    globalStyling: {
      fontFamily: 'Times New Roman, serif',
      fontSize: 11,
      colorScheme: 'minimal',
      layout: 'single-column',
      spacing: 'compact'
    },
    metadata: {
      createdAt: '2024-01-05T12:00:00Z',
      updatedAt: '2024-01-15T09:45:00Z',
      version: '1.0.0'
    },
    isShared: true,
    isStarred: false,
    downloads: 892,
    lastModified: '1 month ago'
  }
];

const TemplateManager: React.FC<TemplateManagerProps> = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const { currentTemplate } = useSelector((state: RootState) => state.modularResume);
  
  const [activeTab, setActiveTab] = useState<'my-templates' | 'shared' | 'create'>('my-templates');
  const [savedTemplates, setSavedTemplates] = useState<SavedTemplate[]>(MOCK_SAVED_TEMPLATES);
  const [filter, setFilter] = useState<'all' | 'starred' | 'shared'>('all');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveTemplate, setSaveTemplate] = useState({
    name: '',
    description: '',
    isPublic: false
  });

  const filteredTemplates = savedTemplates.filter(template => {
    switch (filter) {
      case 'starred': return template.isStarred;
      case 'shared': return template.isShared;
      default: return true;
    }
  });

  const handleSaveCurrentTemplate = useCallback(async () => {
    if (!currentTemplate || !saveTemplate.name.trim()) return;

    const newTemplate: SavedTemplate = {
      ...currentTemplate,
      name: saveTemplate.name,
      description: saveTemplate.description,
      isShared: saveTemplate.isPublic,
      isStarred: false,
      downloads: 0,
      lastModified: 'Just now',
      metadata: {
        ...currentTemplate.metadata,
        updatedAt: new Date().toISOString()
      }
    };

    setSavedTemplates(prev => [newTemplate, ...prev]);
    setShowSaveDialog(false);
    setSaveTemplate({ name: '', description: '', isPublic: false });
    
    // In a real app, you'd save to backend here
    console.log('Template saved:', newTemplate);
  }, [currentTemplate, saveTemplate]);

  const handleLoadTemplate = useCallback((template: SavedTemplate) => {
    dispatch(setCurrentTemplate(template));
    onClose();
  }, [dispatch, onClose]);

  const handleDuplicateTemplate = useCallback((template: SavedTemplate) => {
    const duplicated: SavedTemplate = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (Copy)`,
      isShared: false,
      isStarred: false,
      downloads: 0,
      lastModified: 'Just now',
      metadata: {
        ...template.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    setSavedTemplates(prev => [duplicated, ...prev]);
  }, []);

  const handleToggleStar = useCallback((templateId: string) => {
    setSavedTemplates(prev => 
      prev.map(template => 
        template.id === templateId 
          ? { ...template, isStarred: !template.isStarred }
          : template
      )
    );
  }, []);

  const handleDeleteTemplate = useCallback((templateId: string) => {
    setSavedTemplates(prev => prev.filter(template => template.id !== templateId));
  }, []);

  const handleExportTemplate = useCallback((template: SavedTemplate) => {
    const dataStr = JSON.stringify(template, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${template.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }, []);

  const handleImportTemplate = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const template = JSON.parse(e.target?.result as string) as SavedTemplate;
        template.id = Date.now().toString();
        template.name = `${template.name} (Imported)`;
        template.lastModified = 'Just now';
        
        setSavedTemplates(prev => [template, ...prev]);
      } catch (error) {
        console.error('Failed to import template:', error);
        alert('Failed to import template. Please check the file format.');
      }
    };
    reader.readAsText(file);
    
    // Reset the input
    event.target.value = '';
  }, []);

  const tabs = [
    { id: 'my-templates', label: 'My Templates', icon: FolderIcon, count: savedTemplates.length },
    { id: 'shared', label: 'Community', icon: ShareIcon, count: savedTemplates.filter(t => t.isShared).length },
    { id: 'create', label: 'Create New', icon: SaveIcon, count: 0 }
  ];

  const filters = [
    { id: 'all', label: 'All Templates', count: savedTemplates.length },
    { id: 'starred', label: 'Starred', count: savedTemplates.filter(t => t.isStarred).length },
    { id: 'shared', label: 'Shared', count: savedTemplates.filter(t => t.isShared).length }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.95, y: 20 }}
            className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Template Manager</h2>
                <p className="text-gray-600 mt-1">Save, organize, and share your resume templates</p>
              </div>
              <Button variant="outline" size="sm" onClick={onClose}>
                <XIcon className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex h-[calc(90vh-120px)]">
              {/* Sidebar */}
              <div className="w-64 bg-gray-50 border-r">
                <div className="p-4">
                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id as any)}
                          className={`
                            w-full flex items-center justify-between px-3 py-2 rounded-md text-left transition-colors
                            ${activeTab === tab.id 
                              ? 'bg-blue-100 text-blue-700' 
                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                            }
                          `}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className="h-4 w-4" />
                            <span className="font-medium">{tab.label}</span>
                          </div>
                          {tab.count > 0 && (
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              activeTab === tab.id 
                                ? 'bg-blue-200 text-blue-800' 
                                : 'bg-gray-200 text-gray-600'
                            }`}>
                              {tab.count}
                            </span>
                          )}
                        </button>
                      );
                    })}
                  </nav>

                  {/* Filters */}
                  {activeTab === 'my-templates' && (
                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                        <FilterIcon className="h-4 w-4 mr-2" />
                        Filter
                      </h4>
                      <div className="space-y-1">
                        {filters.map((filterOption) => (
                          <button
                            key={filterOption.id}
                            onClick={() => setFilter(filterOption.id as any)}
                            className={`
                              w-full flex items-center justify-between px-3 py-2 rounded text-sm transition-colors
                              ${filter === filterOption.id 
                                ? 'bg-gray-200 text-gray-900' 
                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                              }
                            `}
                          >
                            <span>{filterOption.label}</span>
                            <span className="text-xs text-gray-500">{filterOption.count}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="mt-6 space-y-2">
                    <Button
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => setShowSaveDialog(true)}
                      disabled={!currentTemplate}
                    >
                      <SaveIcon className="h-4 w-4 mr-2" />
                      Save Current
                    </Button>
                    
                    <label className="block">
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImportTemplate}
                        className="hidden"
                      />
                      <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                        <span>
                          <UploadIcon className="h-4 w-4 mr-2" />
                          Import Template
                        </span>
                      </Button>
                    </label>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto">
                {/* My Templates Tab */}
                {activeTab === 'my-templates' && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredTemplates.map((template) => (
                        <motion.div
                          key={template.id}
                          layout
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                        >
                          <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer group">
                            {/* Template Preview */}
                            <div className="aspect-[3/4] bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                              <span className="text-gray-400 text-4xl">📄</span>
                            </div>

                            {/* Template Info */}
                            <div className="space-y-2">
                              <div className="flex items-start justify-between">
                                <h3 className="font-medium text-gray-900 line-clamp-2">{template.name}</h3>
                                <button
                                  onClick={() => handleToggleStar(template.id)}
                                  className="flex-shrink-0 ml-2 p-1 hover:bg-gray-100 rounded"
                                >
                                  <StarIcon 
                                    className={`h-4 w-4 ${
                                      template.isStarred 
                                        ? 'text-yellow-500 fill-current' 
                                        : 'text-gray-400'
                                    }`} 
                                  />
                                </button>
                              </div>
                              
                              <p className="text-sm text-gray-600 line-clamp-2">{template.description}</p>
                              
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <ClockIcon className="h-3 w-3" />
                                  <span>{template.lastModified}</span>
                                </div>
                                {template.isShared && (
                                  <div className="flex items-center space-x-1">
                                    <UsersIcon className="h-3 w-3" />
                                    <span>{template.downloads}</span>
                                  </div>
                                )}
                              </div>

                              {/* Actions */}
                              <div className="flex items-center space-x-2 pt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  onClick={() => handleLoadTemplate(template)}
                                  className="flex-1"
                                >
                                  Load
                                </Button>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDuplicateTemplate(template)}
                                >
                                  <CopyIcon className="h-3 w-3" />
                                </Button>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleExportTemplate(template)}
                                >
                                  <DownloadIcon className="h-3 w-3" />
                                </Button>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteTemplate(template.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <TrashIcon className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </Card>
                        </motion.div>
                      ))}
                    </div>

                    {filteredTemplates.length === 0 && (
                      <div className="text-center py-12">
                        <div className="text-gray-400 mb-4">
                          <FolderIcon className="h-16 w-16 mx-auto" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          No templates found
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {filter === 'all' 
                            ? 'Start by saving your current resume template'
                            : `No ${filter} templates to display`
                          }
                        </p>
                        <Button onClick={() => setShowSaveDialog(true)} disabled={!currentTemplate}>
                          Save Current Template
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* Create Tab */}
                {activeTab === 'create' && (
                  <div className="p-6">
                    <div className="max-w-2xl mx-auto">
                      <h3 className="text-lg font-medium text-gray-900 mb-6">
                        Create New Template
                      </h3>
                      <p className="text-gray-600 mb-8">
                        Start with a blank template or choose from our pre-designed layouts.
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                          <div className="text-center">
                            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                              <span className="text-2xl">📄</span>
                            </div>
                            <h4 className="font-medium text-gray-900 mb-2">Blank Template</h4>
                            <p className="text-sm text-gray-600">Start from scratch with a clean slate</p>
                          </div>
                        </Card>
                        
                        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                          <div className="text-center">
                            <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                              <span className="text-2xl">🎯</span>
                            </div>
                            <h4 className="font-medium text-gray-900 mb-2">Quick Start</h4>
                            <p className="text-sm text-gray-600">Use our guided template builder</p>
                          </div>
                        </Card>
                      </div>
                    </div>
                  </div>
                )}

                {/* Shared Tab */}
                {activeTab === 'shared' && (
                  <div className="p-6">
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <ShareIcon className="h-16 w-16 mx-auto" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Community Templates
                      </h3>
                      <p className="text-gray-600">
                        Discover and share templates with the community (coming soon)
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Save Dialog */}
            <AnimatePresence>
              {showSaveDialog && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
                >
                  <motion.div
                    initial={{ scale: 0.95 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0.95 }}
                    className="bg-white rounded-lg p-6 max-w-md w-full"
                  >
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Save Template</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Template Name
                        </label>
                        <input
                          type="text"
                          value={saveTemplate.name}
                          onChange={(e) => setSaveTemplate(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full border border-gray-300 rounded-md px-3 py-2"
                          placeholder="Enter template name"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description (optional)
                        </label>
                        <textarea
                          value={saveTemplate.description}
                          onChange={(e) => setSaveTemplate(prev => ({ ...prev, description: e.target.value }))}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 h-20"
                          placeholder="Describe this template"
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="isPublic"
                          checked={saveTemplate.isPublic}
                          onChange={(e) => setSaveTemplate(prev => ({ ...prev, isPublic: e.target.checked }))}
                          className="rounded"
                        />
                        <label htmlFor="isPublic" className="text-sm text-gray-700">
                          Make this template public (share with community)
                        </label>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3 mt-6">
                      <Button
                        onClick={handleSaveCurrentTemplate}
                        disabled={!saveTemplate.name.trim()}
                        className="flex-1"
                      >
                        Save Template
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowSaveDialog(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TemplateManager;