import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PaletteIcon, 
  TypeIcon, 
  LayoutIcon, 
  SpacingIcon,
  SaveIcon,
  XIcon,
  ResetIcon
} from 'lucide-react';
import type { ModularSection } from '../../types/resumeModules';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface SectionCustomizerProps {
  section: ModularSection;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (sectionId: string, updates: Partial<ModularSection>) => void;
}

// Color palette options
const COLOR_SCHEMES = {
  default: { bg: '#ffffff', text: '#000000', accent: '#3b82f6' },
  professional: { bg: '#f8fafc', text: '#1e293b', accent: '#0f172a' },
  modern: { bg: '#ffffff', text: '#374151', accent: '#6366f1' },
  creative: { bg: '#fef3c7', text: '#92400e', accent: '#f59e0b' },
  minimal: { bg: '#ffffff', text: '#6b7280', accent: '#9ca3af' },
  bold: { bg: '#1e293b', text: '#ffffff', accent: '#3b82f6' }
};

// Font options
const FONT_OPTIONS = [
  { name: 'Professional', value: 'system-ui, sans-serif' },
  { name: 'Modern', value: 'Inter, sans-serif' },
  { name: 'Classic', value: 'Times New Roman, serif' },
  { name: 'Clean', value: 'Helvetica, Arial, sans-serif' },
  { name: 'Friendly', value: 'ui-rounded, sans-serif' }
];

// Layout options
const LAYOUT_OPTIONS = [
  { name: 'Single Column', value: 'single', icon: '▢' },
  { name: 'Two Column', value: 'two-column', icon: '▦' },
  { name: 'Side Panel', value: 'side-panel', icon: '◧' },
  { name: 'Grid', value: 'grid', icon: '⊞' }
];

// Spacing options
const SPACING_OPTIONS = [
  { name: 'Compact', value: 'compact', size: '0.5rem' },
  { name: 'Normal', value: 'normal', size: '1rem' },
  { name: 'Relaxed', value: 'relaxed', size: '1.5rem' },
  { name: 'Spacious', value: 'spacious', size: '2rem' }
];

const SectionCustomizer: React.FC<SectionCustomizerProps> = ({
  section,
  isOpen,
  onClose,
  onUpdate
}) => {
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'layout' | 'spacing'>('colors');
  const [localStyling, setLocalStyling] = useState(section.styling || {});

  const handleStyleUpdate = (updates: any) => {
    const newStyling = { ...localStyling, ...updates };
    setLocalStyling(newStyling);
  };

  const handleSave = () => {
    onUpdate(section.id, { styling: localStyling });
    onClose();
  };

  const handleReset = () => {
    setLocalStyling({});
  };

  const tabs = [
    { id: 'colors', label: 'Colors', icon: PaletteIcon },
    { id: 'typography', label: 'Typography', icon: TypeIcon },
    { id: 'layout', label: 'Layout', icon: LayoutIcon },
    { id: 'spacing', label: 'Spacing', icon: SpacingIcon }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.95, y: 20 }}
            className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Customize Section: {section.title}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  Personalize the appearance and layout of this section
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={handleReset}>
                  <ResetIcon className="h-4 w-4 mr-1" />
                  Reset
                </Button>
                <Button variant="outline" size="sm" onClick={onClose}>
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex">
              {/* Sidebar */}
              <div className="w-64 bg-gray-50 p-4 border-r">
                <nav className="space-y-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id as any)}
                        className={`
                          w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors
                          ${activeTab === tab.id 
                            ? 'bg-blue-100 text-blue-700' 
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                          }
                        `}
                      >
                        <Icon className="h-4 w-4" />
                        <span className="font-medium">{tab.label}</span>
                      </button>
                    );
                  })}
                </nav>

                {/* Preview */}
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Live Preview</h4>
                  <Card 
                    className="p-3 text-sm"
                    style={{
                      backgroundColor: localStyling.backgroundColor || COLOR_SCHEMES.default.bg,
                      color: localStyling.textColor || COLOR_SCHEMES.default.text,
                      fontFamily: localStyling.fontFamily || 'system-ui',
                      fontSize: localStyling.fontSize || '14px',
                      fontWeight: localStyling.fontWeight || 'normal',
                      padding: localStyling.spacing || '12px'
                    }}
                  >
                    <div className="font-semibold mb-2">{section.title}</div>
                    <div className="text-xs opacity-75">
                      Sample content with your styling applied
                    </div>
                  </Card>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                {/* Colors Tab */}
                {activeTab === 'colors' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Color Scheme</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {Object.entries(COLOR_SCHEMES).map(([name, colors]) => (
                          <motion.button
                            key={name}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleStyleUpdate({
                              backgroundColor: colors.bg,
                              textColor: colors.text,
                              accentColor: colors.accent
                            })}
                            className="p-4 border rounded-lg hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <div className="flex space-x-2 mb-3">
                              <div 
                                className="w-6 h-6 rounded" 
                                style={{ backgroundColor: colors.bg, border: '1px solid #e5e7eb' }}
                              />
                              <div 
                                className="w-6 h-6 rounded" 
                                style={{ backgroundColor: colors.text }}
                              />
                              <div 
                                className="w-6 h-6 rounded" 
                                style={{ backgroundColor: colors.accent }}
                              />
                            </div>
                            <div className="text-sm font-medium capitalize">{name}</div>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    {/* Custom Colors */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Custom Colors</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Background Color
                          </label>
                          <input
                            type="color"
                            value={localStyling.backgroundColor || '#ffffff'}
                            onChange={(e) => handleStyleUpdate({ backgroundColor: e.target.value })}
                            className="w-full h-10 border rounded-md cursor-pointer"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Text Color
                          </label>
                          <input
                            type="color"
                            value={localStyling.textColor || '#000000'}
                            onChange={(e) => handleStyleUpdate({ textColor: e.target.value })}
                            className="w-full h-10 border rounded-md cursor-pointer"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Accent Color
                          </label>
                          <input
                            type="color"
                            value={localStyling.accentColor || '#3b82f6'}
                            onChange={(e) => handleStyleUpdate({ accentColor: e.target.value })}
                            className="w-full h-10 border rounded-md cursor-pointer"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Typography Tab */}
                {activeTab === 'typography' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Typography</h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Font Family
                          </label>
                          <select
                            value={localStyling.fontFamily || 'system-ui'}
                            onChange={(e) => handleStyleUpdate({ fontFamily: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            {FONT_OPTIONS.map((font) => (
                              <option key={font.value} value={font.value}>
                                {font.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Font Size
                          </label>
                          <select
                            value={localStyling.fontSize || '14px'}
                            onChange={(e) => handleStyleUpdate({ fontSize: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="12px">Small (12px)</option>
                            <option value="14px">Normal (14px)</option>
                            <option value="16px">Large (16px)</option>
                            <option value="18px">Extra Large (18px)</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Font Weight
                          </label>
                          <select
                            value={localStyling.fontWeight || 'normal'}
                            onChange={(e) => handleStyleUpdate({ fontWeight: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="300">Light</option>
                            <option value="normal">Normal</option>
                            <option value="500">Medium</option>
                            <option value="600">Semi-bold</option>
                            <option value="bold">Bold</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Line Height
                          </label>
                          <select
                            value={localStyling.lineHeight || '1.5'}
                            onChange={(e) => handleStyleUpdate({ lineHeight: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="1.2">Tight (1.2)</option>
                            <option value="1.5">Normal (1.5)</option>
                            <option value="1.75">Relaxed (1.75)</option>
                            <option value="2">Loose (2.0)</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Layout Tab */}
                {activeTab === 'layout' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Layout Options</h3>
                      <div className="grid grid-cols-2 gap-4">
                        {LAYOUT_OPTIONS.map((layout) => (
                          <motion.button
                            key={layout.value}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleStyleUpdate({ layout: layout.value })}
                            className={`
                              p-4 border rounded-lg text-center transition-colors
                              ${localStyling.layout === layout.value 
                                ? 'border-blue-500 bg-blue-50' 
                                : 'border-gray-300 hover:border-gray-400'
                              }
                            `}
                          >
                            <div className="text-2xl mb-2">{layout.icon}</div>
                            <div className="font-medium">{layout.name}</div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Spacing Tab */}
                {activeTab === 'spacing' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Spacing & Padding</h3>
                      <div className="grid grid-cols-2 gap-4">
                        {SPACING_OPTIONS.map((spacing) => (
                          <motion.button
                            key={spacing.value}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleStyleUpdate({ spacing: spacing.size })}
                            className={`
                              p-4 border rounded-lg text-center transition-colors
                              ${localStyling.spacing === spacing.size 
                                ? 'border-blue-500 bg-blue-50' 
                                : 'border-gray-300 hover:border-gray-400'
                              }
                            `}
                          >
                            <div className="font-medium mb-1">{spacing.name}</div>
                            <div className="text-sm text-gray-600">{spacing.size}</div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleSave} className="flex items-center space-x-2">
                <SaveIcon className="h-4 w-4" />
                <span>Save Changes</span>
              </Button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SectionCustomizer;