import React from 'react';
import type { ModularTemplate, ModularSection, HeaderContent, SummaryContent, ExperienceContent, SkillsContent, EducationContent, ProjectsContent, AdditionalContent } from '../../types/resumeModules';

interface ResumePreviewProps {
  template: ModularTemplate;
  className?: string;
}

const ResumePreview: React.FC<ResumePreviewProps> = ({ template, className = '' }) => {
  const renderSection = (section: ModularSection) => {
    if (!section.isVisible) return null;

    switch (section.type) {
      case 'header':
        return <HeaderPreview key={section.id} content={section.content as HeaderContent} />;
      case 'summary':
        return <SummaryPreview key={section.id} content={section.content as SummaryContent} />;
      case 'experience':
        return <ExperiencePreview key={section.id} content={section.content as ExperienceContent} />;
      case 'education':
        return <EducationPreview key={section.id} content={section.content as EducationContent} />;
      case 'skills':
        return <SkillsPreview key={section.id} content={section.content as SkillsContent} />;
      case 'projects':
        return <ProjectsPreview key={section.id} content={section.content as ProjectsContent} />;
      case 'awards':
        return <AwardsPreview key={section.id} content={section.content as AdditionalContent} />;
      case 'languages':
        return <LanguagesPreview key={section.id} content={section.content as AdditionalContent} />;
      case 'publications':
        return <PublicationsPreview key={section.id} content={section.content as AdditionalContent} />;
      case 'volunteer':
        return <VolunteerPreview key={section.id} content={section.content as AdditionalContent} />;
      case 'certifications':
        return <CertificationsPreview key={section.id} content={section.content as AdditionalContent} />;
      default:
        return (
          <div key={section.id} className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h3>
            <p className="text-gray-600">Section content will be displayed here</p>
          </div>
        );
    }
  };

  const getFontFamily = () => {
    switch (template.globalStyling.fontFamily) {
      case 'Times New Roman': return 'font-serif';
      case 'Georgia': return 'font-serif';
      case 'Helvetica': return 'font-sans';
      default: return 'font-sans';
    }
  };

  const getFontSize = () => {
    const size = template.globalStyling.fontSize;
    if (size <= 10) return 'text-xs';
    if (size <= 12) return 'text-sm';
    if (size <= 14) return 'text-base';
    return 'text-lg';
  };

  const getSpacing = () => {
    switch (template.globalStyling.spacing) {
      case 'compact': return 'space-y-4';
      case 'relaxed': return 'space-y-8';
      default: return 'space-y-6';
    }
  };

  const getLayoutClass = () => {
    switch (template.globalStyling.layout) {
      case 'two-column': return 'grid grid-cols-1 md:grid-cols-3 gap-6';
      case 'hybrid': return 'space-y-6';
      default: return 'space-y-6';
    }
  };

  return (
    <div className={`bg-white p-8 shadow-lg rounded-lg ${getFontFamily()} ${getFontSize()} ${className}`}>
      <div className={getLayoutClass()}>
        <div className={template.globalStyling.layout === 'two-column' ? 'md:col-span-2' : ''}>
          <div className={getSpacing()}>
            {template.sections
              .filter(section => section.isVisible)
              .sort((a, b) => a.order - b.order)
              .map(renderSection)}
          </div>
        </div>
        
        {template.globalStyling.layout === 'two-column' && (
          <div className="md:col-span-1">
            <div className={getSpacing()}>
              {/* Side sections would go here */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const HeaderPreview: React.FC<{ content: HeaderContent }> = ({ content }) => {
  if (!content.name && !content.title) return null;

  return (
    <div className="mb-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {content.name || 'Your Name'}
        </h1>
        <p className="text-xl text-gray-600 mb-4">
          {content.title || 'Your Professional Title'}
        </p>
        
        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
          {content.contact.email && (
            <span className="flex items-center">
              📧 {content.contact.email}
            </span>
          )}
          {content.contact.phone && (
            <span className="flex items-center">
              📞 {content.contact.phone}
            </span>
          )}
          {content.contact.location && (
            <span className="flex items-center">
              📍 {content.contact.location}
            </span>
          )}
          {content.contact.website && (
            <span className="flex items-center">
              🌐 {content.contact.website}
            </span>
          )}
          {content.contact.linkedin && (
            <span className="flex items-center">
              💼 LinkedIn
            </span>
          )}
          {content.contact.github && (
            <span className="flex items-center">
              💻 GitHub
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

const SummaryPreview: React.FC<{ content: SummaryContent }> = ({ content }) => {
  if (!content.summary) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">Professional Summary</h3>
      <p className="text-gray-700 leading-relaxed">{content.summary}</p>
      {content.objective && (
        <div className="mt-4">
          <h4 className="font-medium text-gray-900 mb-2">Career Objective</h4>
          <p className="text-gray-700 leading-relaxed">{content.objective}</p>
        </div>
      )}
    </div>
  );
};

const ExperiencePreview: React.FC<{ content: ExperienceContent }> = ({ content }) => {
  if (!content.experiences || content.experiences.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Work Experience</h3>
      <div className="space-y-6">
        {content.experiences.map((experience) => (
          <div key={experience.id} className="border-l-3 border-blue-500 pl-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-semibold text-gray-900">{experience.position}</h4>
                <p className="text-blue-600 font-medium">{experience.company}</p>
                <p className="text-sm text-gray-600">{experience.location}</p>
              </div>
              <div className="text-sm text-gray-500">
                {experience.startDate} - {experience.current ? 'Present' : experience.endDate}
              </div>
            </div>

            {experience.description && (
              <p className="text-gray-700 mb-3">{experience.description}</p>
            )}

            {experience.achievements.filter(achievement => achievement.trim()).length > 0 && (
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                {experience.achievements
                  .filter(achievement => achievement.trim())
                  .map((achievement, index) => (
                    <li key={index}>{achievement}</li>
                  ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const SkillsPreview: React.FC<{ content: SkillsContent }> = ({ content }) => {
  if (!content.skills || content.skills.length === 0) return null;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-red-100 text-red-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-blue-100 text-blue-800';
      case 'expert': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Skills</h3>
      
      {content.categories?.technical?.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Technical Skills</h4>
          <div className="flex flex-wrap gap-2">
            {content.categories.technical.map((skill) => (
              <div key={skill.id} className="flex items-center space-x-1">
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {skill.name}
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                  {skill.level}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {content.categories?.soft?.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Soft Skills</h4>
          <div className="flex flex-wrap gap-2">
            {content.categories.soft.map((skill) => (
              <div key={skill.id} className="flex items-center space-x-1">
                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                  {skill.name}
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                  {skill.level}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {content.categories?.languages?.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Languages</h4>
          <div className="flex flex-wrap gap-2">
            {content.categories.languages.map((skill) => (
              <div key={skill.id} className="flex items-center space-x-1">
                <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                  {skill.name}
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                  {skill.level}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {(!content.categories || Object.values(content.categories).every(cat => cat.length === 0)) && (
        <div className="flex flex-wrap gap-2">
          {content.skills.map((skill) => (
            <div key={skill.id} className="flex items-center space-x-1">
              <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                {skill.name}
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                {skill.level}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const EducationPreview: React.FC<{ content: EducationContent }> = ({ content }) => {
  if (!content.educations || content.educations.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Education</h3>
      <div className="space-y-4">
        {content.educations.map((education) => (
          <div key={education.id} className="border-l-3 border-green-500 pl-4">
            <div className="flex justify-between items-start mb-1">
              <div>
                <h4 className="font-semibold text-gray-900">{education.degree} in {education.field}</h4>
                <p className="text-green-600 font-medium">{education.institution}</p>
                {education.honors && (
                  <p className="text-sm text-gray-600 italic">{education.honors}</p>
                )}
                {education.gpa && (
                  <p className="text-sm text-gray-600">GPA: {education.gpa}</p>
                )}
              </div>
              <div className="text-sm text-gray-500">
                {education.startDate} - {education.endDate}
              </div>
            </div>
            {education.relevantCourses && education.relevantCourses.filter(course => course.trim()).length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium text-gray-700 mb-1">Relevant Courses:</p>
                <div className="flex flex-wrap gap-1">
                  {education.relevantCourses
                    .filter(course => course.trim())
                    .map((course, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                        {course}
                      </span>
                    ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const ProjectsPreview: React.FC<{ content: ProjectsContent }> = ({ content }) => {
  if (!content.projects || content.projects.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Projects</h3>
      <div className="space-y-4">
        {content.projects.map((project) => (
          <div key={project.id} className="border-l-3 border-purple-500 pl-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-semibold text-gray-900">{project.name}</h4>
                <div className="flex items-center space-x-3 mt-1">
                  {project.link && (
                    <a href={project.link} target="_blank" rel="noopener noreferrer" 
                       className="text-purple-600 hover:text-purple-800 text-sm">
                      Live Demo
                    </a>
                  )}
                  {project.repository && (
                    <a href={project.repository} target="_blank" rel="noopener noreferrer" 
                       className="text-purple-600 hover:text-purple-800 text-sm">
                      Repository
                    </a>
                  )}
                </div>
              </div>
              <div className="text-sm text-gray-500">
                {project.startDate} - {project.endDate || 'Present'}
              </div>
            </div>
            <p className="text-gray-700 mb-2">{project.description}</p>
            {project.technologies.filter(tech => tech.trim()).length > 0 && (
              <div className="mb-2">
                <div className="flex flex-wrap gap-1">
                  {project.technologies
                    .filter(tech => tech.trim())
                    .map((tech, index) => (
                      <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">
                        {tech}
                      </span>
                    ))}
                </div>
              </div>
            )}
            {project.achievements.filter(achievement => achievement.trim()).length > 0 && (
              <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                {project.achievements
                  .filter(achievement => achievement.trim())
                  .map((achievement, index) => (
                    <li key={index}>{achievement}</li>
                  ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const AwardsPreview: React.FC<{ content: AdditionalContent }> = ({ content }) => {
  if (!content.awards || content.awards.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Awards & Honors</h3>
      <div className="space-y-3">
        {content.awards.map((award) => (
          <div key={award.id} className="border-l-3 border-yellow-500 pl-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-900">{award.title}</h4>
                <p className="text-yellow-600 font-medium">{award.issuer}</p>
                {award.description && (
                  <p className="text-gray-700 text-sm mt-1">{award.description}</p>
                )}
              </div>
              <span className="text-sm text-gray-500">{award.date}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const LanguagesPreview: React.FC<{ content: AdditionalContent }> = ({ content }) => {
  if (!content.languages || content.languages.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Languages</h3>
      <div className="flex flex-wrap gap-3">
        {content.languages.map((language) => (
          <div key={language.id} className="flex items-center space-x-2">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              {language.name}
            </span>
            <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
              {language.proficiency}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

const PublicationsPreview: React.FC<{ content: AdditionalContent }> = ({ content }) => {
  if (!content.publications || content.publications.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Publications</h3>
      <div className="space-y-3">
        {content.publications.map((publication) => (
          <div key={publication.id} className="border-l-3 border-indigo-500 pl-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-900">{publication.title}</h4>
                <p className="text-indigo-600 font-medium">{publication.publisher}</p>
                {publication.description && (
                  <p className="text-gray-700 text-sm mt-1">{publication.description}</p>
                )}
                {publication.url && (
                  <a href={publication.url} target="_blank" rel="noopener noreferrer" 
                     className="text-indigo-600 hover:text-indigo-800 text-sm mt-1 inline-block">
                    View Publication
                  </a>
                )}
              </div>
              <span className="text-sm text-gray-500">{publication.date}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const VolunteerPreview: React.FC<{ content: AdditionalContent }> = ({ content }) => {
  if (!content.volunteerWork || content.volunteerWork.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Volunteer Work</h3>
      <div className="space-y-4">
        {content.volunteerWork.map((volunteer) => (
          <div key={volunteer.id} className="border-l-3 border-pink-500 pl-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-semibold text-gray-900">{volunteer.role}</h4>
                <p className="text-pink-600 font-medium">{volunteer.organization}</p>
              </div>
              <div className="text-sm text-gray-500">
                {volunteer.startDate} - {volunteer.current ? 'Present' : volunteer.endDate}
              </div>
            </div>
            <p className="text-gray-700 mb-2">{volunteer.description}</p>
            {volunteer.achievements && volunteer.achievements.filter(achievement => achievement.trim()).length > 0 && (
              <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                {volunteer.achievements
                  .filter(achievement => achievement.trim())
                  .map((achievement, index) => (
                    <li key={index}>{achievement}</li>
                  ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const CertificationsPreview: React.FC<{ content: AdditionalContent }> = ({ content }) => {
  if (!content.certifications || content.certifications.length === 0) return null;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Certifications</h3>
      <div className="space-y-3">
        {content.certifications.map((certification) => (
          <div key={certification.id} className="border-l-3 border-green-500 pl-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-900">{certification.name}</h4>
                <p className="text-green-600 font-medium">{certification.issuer}</p>
                {certification.url && (
                  <a href={certification.url} target="_blank" rel="noopener noreferrer" 
                     className="text-green-600 hover:text-green-800 text-sm mt-1 inline-block">
                    View Certificate
                  </a>
                )}
              </div>
              <div className="text-sm text-gray-500 text-right">
                <div>{certification.date}</div>
                {certification.expiryDate && (
                  <div className="text-xs">Expires: {certification.expiryDate}</div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResumePreview;