import { useState, useEffect } from 'react';
import { Save, Eye, EyeOff, Download, Settings } from 'lucide-react';
import { Breadcrumb, StatusIndicator } from '../ui';
import EnhancedExportPanel from './EnhancedExportPanel';
import type { ResumeExportData } from '../../services/resumeExportService';
import useAutoSave, { AutoSaveIndicator } from '../../hooks/useAutoSave';

interface ResumeSection {
  id: string;
  type: 'header' | 'summary' | 'experience' | 'education' | 'skills' | 'projects' | 'additional';
  title: string;
  content: any;
  isVisible: boolean;
  order: number;
}

interface SplitScreenResumeBuilderProps {
  initialSections?: ResumeSection[];
  onSave?: (sections: ResumeSection[]) => void;
  autoSave?: boolean;
  resumeId?: number;
  className?: string;
}

export default function SplitScreenResumeBuilder({
  initialSections = [],
  onSave,
  autoSave = true,
  resumeId,
  className = ''
}: SplitScreenResumeBuilderProps) {
  const [sections, setSections] = useState<ResumeSection[]>(initialSections);
  const [activeSection, setActiveSection] = useState<string>('header');
  const [isPreviewCollapsed, setIsPreviewCollapsed] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const [showExportPanel, setShowExportPanel] = useState(false);

  // Auto-save functionality
  const autoSaveHook = useAutoSave({
    enabled: autoSave && !!resumeId,
    resumeId,
    onSaveSuccess: () => setHasUnsavedChanges(false),
    onSaveError: (error) => console.error('Auto-save failed:', error)
  });

  // Mock default sections if none provided
  useEffect(() => {
    if (initialSections.length === 0) {
      const defaultSections: ResumeSection[] = [
        {
          id: 'header',
          type: 'header',
          title: 'Header',
          content: { name: '', title: '', email: '', phone: '', location: '', linkedin: '' },
          isVisible: true,
          order: 0
        },
        {
          id: 'summary',
          type: 'summary',
          title: 'Professional Summary',
          content: { text: '' },
          isVisible: true,
          order: 1
        },
        {
          id: 'experience',
          type: 'experience',
          title: 'Work Experience',
          content: { entries: [] },
          isVisible: true,
          order: 2
        },
        {
          id: 'education',
          type: 'education',
          title: 'Education',
          content: { entries: [] },
          isVisible: true,
          order: 3
        },
        {
          id: 'skills',
          type: 'skills',
          title: 'Skills',
          content: { categories: [] },
          isVisible: true,
          order: 4
        }
      ];
      setSections(defaultSections);
    }
  }, [initialSections]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      const timer = setTimeout(() => {
        handleSave();
      }, 2000); // Auto-save after 2 seconds of no changes

      return () => clearTimeout(timer);
    }
  }, [sections, hasUnsavedChanges, autoSave]);

  // Real-time validation
  useEffect(() => {
    validateSections();
  }, [sections]);

  const validateSections = () => {
    const errors: Record<string, string[]> = {};

    sections.forEach(section => {
      const sectionErrors: string[] = [];

      if (section.type === 'header') {
        if (!section.content.name?.trim()) {
          sectionErrors.push('Name is required');
        }
        if (!section.content.email?.trim()) {
          sectionErrors.push('Email is required');
        } else if (!/\S+@\S+\.\S+/.test(section.content.email)) {
          sectionErrors.push('Valid email is required');
        }
      }

      if (section.type === 'summary' && !section.content.text?.trim()) {
        sectionErrors.push('Professional summary is recommended');
      }

      if (sectionErrors.length > 0) {
        errors[section.id] = sectionErrors;
      }
    });

    setValidationErrors(errors);
  };

  const handleSectionUpdate = (sectionId: string, newContent: any) => {
    setSections(prev => {
      const updated = prev.map(section => 
        section.id === sectionId ? { ...section, content: newContent } : section
      );
      
      // Trigger auto-save
      autoSaveHook.saveData({ sections: updated });
      
      return updated;
    });
    setHasUnsavedChanges(true);
  };

  const handleSave = () => {
    onSave?.(sections);
    setHasUnsavedChanges(false);
  };

  // Convert sections to export data format
  const convertToExportData = (): ResumeExportData => {
    const headerSection = sections.find(s => s.type === 'header');
    const summarySection = sections.find(s => s.type === 'summary');
    const experienceSection = sections.find(s => s.type === 'experience');
    const educationSection = sections.find(s => s.type === 'education');
    const skillsSection = sections.find(s => s.type === 'skills');
    const projectsSection = sections.find(s => s.type === 'projects');

    return {
      personalInfo: {
        fullName: headerSection?.content?.name || '',
        email: headerSection?.content?.email || '',
        phone: headerSection?.content?.phone || '',
        location: headerSection?.content?.location || '',
        linkedin: headerSection?.content?.linkedin || '',
        website: headerSection?.content?.website || ''
      },
      summary: summarySection?.content?.text || '',
      experience: experienceSection?.content?.experiences || [],
      education: educationSection?.content?.education || [],
      skills: skillsSection?.content?.skills || {},
      projects: projectsSection?.content?.projects || []
    };
  };

  const toggleSectionVisibility = (sectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId ? { ...section, isVisible: !section.isVisible } : section
    ));
    setHasUnsavedChanges(true);
  };

  const renderSectionEditor = (section: ResumeSection) => {
    switch (section.type) {
      case 'header':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={section.content.name || ''}
                  onChange={(e) => handleSectionUpdate(section.id, { ...section.content, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="John Doe"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Professional Title
                </label>
                <input
                  type="text"
                  value={section.content.title || ''}
                  onChange={(e) => handleSectionUpdate(section.id, { ...section.content, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Senior Frontend Developer"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  value={section.content.email || ''}
                  onChange={(e) => handleSectionUpdate(section.id, { ...section.content, email: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={section.content.phone || ''}
                  onChange={(e) => handleSectionUpdate(section.id, { ...section.content, phone: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>
        );

      case 'summary':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Professional Summary
            </label>
            <textarea
              value={section.content.text || ''}
              onChange={(e) => handleSectionUpdate(section.id, { text: e.target.value })}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
              placeholder="Write a compelling summary of your professional background, key skills, and career objectives..."
            />
            <div className="mt-2 flex justify-between items-center text-sm text-gray-500">
              <span>Recommended: 3-4 sentences</span>
              <span>{section.content.text?.length || 0}/300 characters</span>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p>Section editor for {section.title} coming soon...</p>
          </div>
        );
    }
  };

  const renderPreview = () => {
    const visibleSections = sections.filter(s => s.isVisible).sort((a, b) => a.order - b.order);

    return (
      <div className="bg-white shadow-lg max-w-2xl mx-auto" style={{ aspectRatio: '8.5/11' }}>
        <div className="p-8 space-y-6">
          {visibleSections.map(section => {
            if (section.type === 'header') {
              return (
                <div key={section.id} className="text-center border-b border-gray-200 pb-4">
                  <h1 className="text-2xl font-bold text-gray-900">
                    {section.content.name || 'Your Name'}
                  </h1>
                  {section.content.title && (
                    <p className="text-lg text-gray-600 mt-1">{section.content.title}</p>
                  )}
                  <div className="flex justify-center items-center space-x-4 mt-2 text-sm text-gray-600">
                    {section.content.email && <span>{section.content.email}</span>}
                    {section.content.phone && <span>{section.content.phone}</span>}
                  </div>
                </div>
              );
            }

            if (section.type === 'summary') {
              return (
                <div key={section.id}>
                  <h2 className="text-lg font-semibold text-gray-900 mb-2">{section.title}</h2>
                  <p className="text-gray-700 leading-relaxed">
                    {section.content.text || 'Your professional summary will appear here...'}
                  </p>
                </div>
              );
            }

            return (
              <div key={section.id}>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">{section.title}</h2>
                <p className="text-gray-500 italic">Content for {section.title} section...</p>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const currentSection = sections.find(s => s.id === activeSection);

  return (
    <div className={`h-screen flex flex-col ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        {/* Breadcrumb Navigation */}
        <div className="mb-4">
          <Breadcrumb
            items={[
              { label: 'Dashboard', onClick: () => window.history.back() },
              { label: 'Resume Builder' },
            ]}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Resume Builder</h1>
            {hasUnsavedChanges && (
              <StatusIndicator
                status="warning"
                message="Unsaved changes"
                size="sm"
                showIcon={false}
              />
            )}
          </div>
          <div className="flex items-center space-x-3">
            <AutoSaveIndicator
              lastSaved={autoSaveHook.lastSaved}
              isSaving={autoSaveHook.isSaving}
              hasUnsavedChanges={autoSaveHook.hasUnsavedChanges}
            />
            
            <button
              onClick={() => setIsPreviewCollapsed(!isPreviewCollapsed)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title={isPreviewCollapsed ? 'Show preview' : 'Hide preview'}
            >
              {isPreviewCollapsed ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>Save</span>
            </button>
            <button 
              onClick={() => setShowExportPanel(!showExportPanel)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title="Export resume"
            >
              <Download className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Section Navigation */}
        <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Resume Sections</h3>
            <div className="space-y-1">
              {sections.map(section => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center justify-between p-3 text-left rounded-lg transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSectionVisibility(section.id);
                      }}
                      className={`text-xs ${section.isVisible ? 'text-green-600' : 'text-gray-400'}`}
                    >
                      {section.isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </button>
                    <span className="text-sm font-medium">{section.title}</span>
                  </div>
                  {validationErrors[section.id] && (
                    <div className="w-2 h-2 bg-red-500 rounded-full" title="Has validation errors" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Editor Panel */}
        <div className={`flex-1 ${isPreviewCollapsed ? 'w-full' : 'w-1/2'} transition-all duration-300`}>
          <div className="h-full overflow-y-auto">
            <div className="p-6">
              {currentSection && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {currentSection.title}
                    </h2>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
                        <Settings className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Validation Errors */}
                  {validationErrors[currentSection.id] && (
                    <StatusIndicator
                      status="error"
                      message={`${validationErrors[currentSection.id].length} validation error(s)`}
                      className="mb-4"
                    />
                  )}

                  {renderSectionEditor(currentSection)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Preview Panel */}
        {!isPreviewCollapsed && (
          <div className="w-1/2 bg-gray-100 dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Live Preview</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Zoom:</span>
                  <button className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 rounded">75%</button>
                </div>
              </div>
              <div className="transform scale-75 origin-top">
                <div id="resume-preview-export">
                  {renderPreview()}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Export Panel */}
      {showExportPanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Export Resume</h2>
                <button
                  onClick={() => setShowExportPanel(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Settings className="h-6 w-6 rotate-45" />
                </button>
              </div>
              <EnhancedExportPanel
                resumeElementId="resume-preview-export"
                resumeData={convertToExportData()}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}