import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { Monitor, Smartphone, Tablet, ZoomIn, ZoomOut, RefreshCw } from 'lucide-react';
import type { RootState } from '../../store';
import type { ModularTemplate, ModularSection } from '../../types/resumeModules';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface RealTimePreviewProps {
  isVisible: boolean;
  className?: string;
}

type PreviewDevice = 'desktop' | 'tablet' | 'mobile';
type ZoomLevel = 0.5 | 0.75 | 1 | 1.25 | 1.5;

// Optimized section renderer that only updates when content changes
const OptimizedSectionRenderer = React.memo<{
  section: ModularSection;
  globalStyling: ModularTemplate['globalStyling'];
  device: PreviewDevice;
}>(({ section, globalStyling, device }) => {
  const sectionStyle = useMemo(() => ({
    fontFamily: section.styling?.fontFamily || globalStyling.fontFamily,
    fontSize: device === 'mobile' ? '12px' : (section.styling?.fontSize || `${globalStyling.fontSize}px`),
    color: section.styling?.textColor || '#000000',
    backgroundColor: section.styling?.backgroundColor || '#ffffff',
    padding: section.styling?.spacing || (device === 'mobile' ? '8px' : '12px'),
    lineHeight: section.styling?.lineHeight || '1.5',
    fontWeight: section.styling?.fontWeight || 'normal'
  }), [section.styling, globalStyling, device]);

  const renderContent = useCallback(() => {
    switch (section.type) {
      case 'header':
        const headerContent = section.content as any;
        return (
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">{headerContent.name || 'Your Name'}</h1>
            <p className="text-lg text-gray-600 mb-3">{headerContent.title || 'Your Title'}</p>
            <div className="text-sm text-gray-500 space-y-1">
              {headerContent.contact?.email && <p>{headerContent.contact.email}</p>}
              {headerContent.contact?.phone && <p>{headerContent.contact.phone}</p>}
              {headerContent.contact?.location && <p>{headerContent.contact.location}</p>}
            </div>
          </div>
        );

      case 'summary':
        const summaryContent = section.content as any;
        return (
          <div>
            <p className="text-sm leading-relaxed">
              {summaryContent.summary || 'Professional summary will appear here...'}
            </p>
          </div>
        );

      case 'experience':
        const expContent = section.content as any;
        return (
          <div className="space-y-4">
            {expContent.experiences?.map((exp: any, index: number) => (
              <div key={index} className="border-l-2 border-blue-200 pl-4">
                <h3 className="font-semibold">{exp.position}</h3>
                <p className="text-gray-600 text-sm">{exp.company} • {exp.location}</p>
                <p className="text-xs text-gray-500 mb-2">
                  {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                </p>
                <ul className="text-sm space-y-1">
                  {exp.achievements?.map((achievement: string, idx: number) => (
                    <li key={idx} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{achievement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )) || <p className="text-gray-400 text-sm">No experience added yet</p>}
          </div>
        );

      case 'education':
        const eduContent = section.content as any;
        return (
          <div className="space-y-3">
            {eduContent.educations?.map((edu: any, index: number) => (
              <div key={index}>
                <h3 className="font-semibold">{edu.degree} in {edu.field}</h3>
                <p className="text-gray-600 text-sm">{edu.institution}</p>
                <p className="text-xs text-gray-500">{edu.startDate} - {edu.endDate}</p>
                {edu.gpa && <p className="text-xs text-gray-500">GPA: {edu.gpa}</p>}
              </div>
            )) || <p className="text-gray-400 text-sm">No education added yet</p>}
          </div>
        );

      case 'skills':
        const skillsContent = section.content as any;
        return (
          <div className="space-y-3">
            {['technical', 'soft', 'languages'].map(category => {
              const categorySkills = skillsContent.categories?.[category] || [];
              if (categorySkills.length === 0) return null;
              
              return (
                <div key={category}>
                  <h4 className="font-medium text-sm mb-2 capitalize">{category} Skills</h4>
                  <div className="flex flex-wrap gap-2">
                    {categorySkills.map((skill: any, index: number) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      >
                        {skill.name}
                      </span>
                    ))}
                  </div>
                </div>
              );
            })}
            {(skillsContent.skills?.length || 0) === 0 && (
              <p className="text-gray-400 text-sm">No skills added yet</p>
            )}
          </div>
        );

      case 'projects':
        const projectsContent = section.content as any;
        return (
          <div className="space-y-4">
            {projectsContent.projects?.map((project: any, index: number) => (
              <div key={index}>
                <h3 className="font-semibold">{project.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{project.description}</p>
                <div className="flex flex-wrap gap-1 mb-2">
                  {project.technologies?.map((tech: string, idx: number) => (
                    <span key={idx} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                      {tech}
                    </span>
                  ))}
                </div>
                <ul className="text-sm space-y-1">
                  {project.achievements?.map((achievement: string, idx: number) => (
                    <li key={idx} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{achievement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )) || <p className="text-gray-400 text-sm">No projects added yet</p>}
          </div>
        );

      default:
        return (
          <div className="text-gray-400 text-sm">
            {section.title} content will appear here...
          </div>
        );
    }
  }, [section]);

  return (
    <motion.div
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="mb-6"
      style={sectionStyle}
    >
      <h2 className="text-lg font-bold mb-3 pb-1 border-b border-gray-300">
        {section.title}
      </h2>
      {renderContent()}
    </motion.div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if section content, styling, or device changes
  return (
    JSON.stringify(prevProps.section) === JSON.stringify(nextProps.section) &&
    JSON.stringify(prevProps.globalStyling) === JSON.stringify(nextProps.globalStyling) &&
    prevProps.device === nextProps.device
  );
});

OptimizedSectionRenderer.displayName = 'OptimizedSectionRenderer';

const RealTimePreview: React.FC<RealTimePreviewProps> = ({ isVisible, className = '' }) => {
  const { currentTemplate, isPreviewMode } = useSelector((state: RootState) => state.modularResume);
  const [device, setDevice] = useState<PreviewDevice>('desktop');
  const [zoom, setZoom] = useState<ZoomLevel>(1);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());

  // Update timestamp when template changes
  useEffect(() => {
    setLastUpdateTime(Date.now());
  }, [currentTemplate]);

  // Debounced refresh function
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    setTimeout(() => {
      setIsRefreshing(false);
      setLastUpdateTime(Date.now());
    }, 500);
  }, []);

  // Get device dimensions
  const deviceDimensions = useMemo(() => {
    switch (device) {
      case 'mobile':
        return { width: 320, height: 568, maxWidth: '320px' };
      case 'tablet':
        return { width: 768, height: 1024, maxWidth: '768px' };
      default:
        return { width: 1200, height: 800, maxWidth: '100%' };
    }
  }, [device]);

  // Filter visible sections
  const visibleSections = useMemo(() => {
    return currentTemplate?.sections?.filter(section => section.isVisible) || [];
  }, [currentTemplate?.sections]);

  // Calculate container styles
  const containerStyles = useMemo(() => ({
    transform: `scale(${zoom})`,
    transformOrigin: 'top left',
    width: deviceDimensions.maxWidth,
    maxWidth: deviceDimensions.maxWidth,
    minHeight: '500px',
    backgroundColor: '#ffffff',
    boxShadow: device !== 'desktop' ? '0 0 20px rgba(0,0,0,0.1)' : 'none',
    borderRadius: device !== 'desktop' ? '8px' : '0',
    overflow: 'hidden'
  }), [zoom, deviceDimensions, device]);

  const devices = [
    { id: 'desktop', icon: Monitor, label: 'Desktop' },
    { id: 'tablet', icon: Tablet, label: 'Tablet' },
    { id: 'mobile', icon: Smartphone, label: 'Mobile' }
  ];

  const zoomLevels: ZoomLevel[] = [0.5, 0.75, 1, 1.25, 1.5];

  if (!isVisible || !currentTemplate) {
    return null;
  }

  return (
    <Card className={`${className} p-0 overflow-hidden`}>
      {/* Preview Controls */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-4">
          <h3 className="font-medium text-gray-900">Live Preview</h3>
          <div className="text-xs text-gray-500">
            Updated {new Date(lastUpdateTime).toLocaleTimeString()}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Device Selection */}
          <div className="flex items-center space-x-1 bg-white rounded-lg p-1">
            {devices.map((deviceOption) => {
              const Icon = deviceOption.icon;
              return (
                <button
                  key={deviceOption.id}
                  onClick={() => setDevice(deviceOption.id as PreviewDevice)}
                  className={`p-2 rounded transition-colors ${
                    device === deviceOption.id
                      ? 'bg-blue-100 text-blue-600'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                  title={deviceOption.label}
                >
                  <Icon className="h-4 w-4" />
                </button>
              );
            })}
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-1 bg-white rounded-lg p-1">
            <button
              onClick={() => {
                const currentIndex = zoomLevels.indexOf(zoom);
                if (currentIndex > 0) setZoom(zoomLevels[currentIndex - 1]);
              }}
              disabled={zoom <= zoomLevels[0]}
              className="p-2 rounded transition-colors text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <ZoomOut className="h-4 w-4" />
            </button>
            
            <div className="px-2 py-1 text-sm font-medium text-gray-600 min-w-[50px] text-center">
              {Math.round(zoom * 100)}%
            </div>
            
            <button
              onClick={() => {
                const currentIndex = zoomLevels.indexOf(zoom);
                if (currentIndex < zoomLevels.length - 1) setZoom(zoomLevels[currentIndex + 1]);
              }}
              disabled={zoom >= zoomLevels[zoomLevels.length - 1]}
              className="p-2 rounded transition-colors text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <ZoomIn className="h-4 w-4" />
            </button>
          </div>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center space-x-1"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="p-6 bg-gray-100 min-h-[600px] overflow-auto">
        <div className="flex justify-center">
          <motion.div
            layout
            style={containerStyles}
            className="bg-white shadow-lg"
          >
            <div className="p-6">
              <AnimatePresence mode="popLayout">
                {visibleSections.map((section) => (
                  <OptimizedSectionRenderer
                    key={section.id}
                    section={section}
                    globalStyling={currentTemplate.globalStyling}
                    device={device}
                  />
                ))}
              </AnimatePresence>

              {visibleSections.length === 0 && (
                <div className="text-center py-12 text-gray-400">
                  <div className="text-4xl mb-4">📄</div>
                  <p>No visible sections to preview</p>
                  <p className="text-sm mt-2">Add sections to see your resume come to life</p>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Preview Status */}
      <div className="px-4 py-2 border-t bg-gray-50 text-xs text-gray-500 flex justify-between items-center">
        <div>
          {visibleSections.length} section{visibleSections.length !== 1 ? 's' : ''} • 
          {device === 'desktop' ? ' Desktop view' : device === 'tablet' ? ' Tablet view' : ' Mobile view'}
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            isRefreshing ? 'bg-yellow-400' : 'bg-green-400'
          }`} />
          <span>
            {isRefreshing ? 'Updating...' : 'Live'}
          </span>
        </div>
      </div>
    </Card>
  );
};

export default RealTimePreview;