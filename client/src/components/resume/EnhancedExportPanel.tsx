import React, { useState } from 'react';
import { 
  DocumentArrowDownIcon, 
  PhotoIcon, 
  DocumentTextIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { Button } from '../ui';
import ResumeExportService, { ExportOptions, ResumeExportData } from '../../services/resumeExportService';

interface EnhancedExportPanelProps {
  resumeElementId: string;
  resumeData?: ResumeExportData;
  className?: string;
}

const EnhancedExportPanel: React.FC<EnhancedExportPanelProps> = ({
  resumeElementId,
  resumeData,
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    quality: 0.95,
    paperSize: 'a4',
    orientation: 'portrait'
  });

  const exportSupport = ResumeExportService.checkExportSupport();

  const handleExport = async (format: 'pdf' | 'png' | 'jpg' | 'word') => {
    if (isExporting) return;

    setIsExporting(true);
    
    try {
      const options: ExportOptions = {
        ...exportOptions,
        format,
        filename: `resume_${new Date().toISOString().split('T')[0]}.${format}`
      };

      if (format === 'word') {
        if (resumeData) {
          ResumeExportService.exportToWord(resumeData, options.filename);
        } else {
          throw new Error('Resume data required for Word export');
        }
      } else if (format === 'pdf') {
        await ResumeExportService.exportToPDF(resumeElementId, options);
      } else {
        await ResumeExportService.exportToImage(resumeElementId, options);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExporting(false);
    }
  };

  const ExportButton: React.FC<{
    format: 'pdf' | 'png' | 'jpg' | 'word';
    icon: React.ReactNode;
    label: string;
    description: string;
    available: boolean;
  }> = ({ format, icon, label, description, available }) => (
    <Button
      onClick={() => handleExport(format)}
      disabled={!available || isExporting}
      variant={available ? 'outline' : 'ghost'}
      className={`flex-1 h-auto p-4 flex flex-col items-center space-y-2 ${
        !available ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      <div className="text-2xl">{icon}</div>
      <div className="text-center">
        <div className="font-medium">{label}</div>
        <div className="text-xs text-gray-500">{description}</div>
      </div>
    </Button>
  );

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Export Resume</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          leftIcon={<Cog6ToothIcon className="h-4 w-4" />}
        >
          {showAdvanced ? 'Hide' : 'Advanced'}
        </Button>
      </div>

      {/* Advanced Options */}
      {showAdvanced && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Paper Size
              </label>
              <select
                value={exportOptions.paperSize}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  paperSize: e.target.value as 'a4' | 'letter' 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="a4">A4</option>
                <option value="letter">Letter</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Orientation
              </label>
              <select
                value={exportOptions.orientation}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  orientation: e.target.value as 'portrait' | 'landscape' 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Quality: {Math.round(exportOptions.quality! * 100)}%
            </label>
            <input
              type="range"
              min="0.5"
              max="1"
              step="0.05"
              value={exportOptions.quality}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                quality: parseFloat(e.target.value) 
              }))}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* Export Options */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <ExportButton
            format="pdf"
            icon={<DocumentArrowDownIcon className="h-6 w-6" />}
            label="PDF"
            description="Professional format"
            available={exportSupport.pdf}
          />
          
          <ExportButton
            format="word"
            icon={<DocumentTextIcon className="h-6 w-6" />}
            label="Text/Word"
            description="Editable format"
            available={exportSupport.word && !!resumeData}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <ExportButton
            format="png"
            icon={<PhotoIcon className="h-6 w-6" />}
            label="PNG"
            description="High quality image"
            available={exportSupport.image}
          />
          
          <ExportButton
            format="jpg"
            icon={<PhotoIcon className="h-6 w-6" />}
            label="JPG"
            description="Compressed image"
            available={exportSupport.image}
          />
        </div>
      </div>

      {isExporting && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            <span className="text-sm text-blue-700">
              Generating your resume export...
            </span>
          </div>
        </div>
      )}

      {/* Support Messages */}
      {(!exportSupport.pdf || !exportSupport.image) && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-700">
            Some export formats may not be available due to browser limitations.
            PDF and image exports work best in modern browsers.
          </p>
        </div>
      )}
    </div>
  );
};

export default EnhancedExportPanel;