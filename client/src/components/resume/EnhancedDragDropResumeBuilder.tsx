import { useState, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import type { DropResult, DroppableProvided, DroppableStateSnapshot, DraggableProvided, DraggableStateSnapshot } from 'react-beautiful-dnd';
import { Plus, Trash, Eye, Undo, Redo, X, Settings, Grid, Folder } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';

import type { RootState } from '../../store';
import {
  reorderSections,
  toggleSectionVisibility,
  removeSection,
  addSection,
  updateSection,
  setPreviewMode,
  setSelectedSection,
  undo,
  redo
} from '../../store/modularResumeSlice';

import type { ModularSection, ModularTemplate, HeaderContent, SummaryContent, ExperienceContent, SkillsContent, EducationContent, ProjectsContent, AdditionalContent } from '../../types/resumeModules';

import Card from '../ui/Card';
import Button from '../ui/Button';
import HeaderModule from './modules/HeaderModule';
import SummaryModule from './modules/SummaryModule';
import ExperienceModule from './modules/ExperienceModule';
import SkillsModule from './modules/SkillsModule';
import EducationModule from './modules/EducationModule';
import ProjectsModule from './modules/ProjectsModule';
import AdditionalModule from './modules/AdditionalModule';
import SectionCustomizer from './SectionCustomizer';
import TemplateManager from './TemplateManager';
import { 
  DragHandle, 
  DropZoneIndicator, 
  SectionCard, 
  TouchEnhancements,
  AccessibilityEnhancements,
  SnapToGrid,
  PerformanceEnhancements
} from './DragDropEnhancements';

interface EnhancedDragDropResumeBuilderProps {
  template?: ModularTemplate;
}

const SECTION_ICONS = {
  header: '👤',
  summary: '📝',
  experience: '💼',
  education: '🎓',
  skills: '⚡',
  projects: '🚀',
  awards: '🏆',
  languages: '🌍',
  publications: '📚',
  volunteer: '❤️',
  certifications: '📜',
  custom: '📄'
};

const SECTION_DESCRIPTIONS = {
  header: 'Personal information and contact details',
  summary: 'Professional summary or objective statement',
  experience: 'Work history and professional achievements',
  education: 'Academic background and qualifications',
  skills: 'Technical and soft skills with proficiency levels',
  projects: 'Key projects and accomplishments',
  awards: 'Awards, honors, and recognitions',
  languages: 'Language proficiencies',
  publications: 'Published works and articles',
  volunteer: 'Volunteer work and community service',
  certifications: 'Professional certifications and licenses',
  custom: 'Custom section with personalized content'
};

const AVAILABLE_SECTION_TYPES = [
  { type: 'summary', name: 'Professional Summary' },
  { type: 'experience', name: 'Work Experience' },
  { type: 'education', name: 'Education' },
  { type: 'skills', name: 'Skills' },
  { type: 'projects', name: 'Projects' },
  { type: 'awards', name: 'Awards' },
  { type: 'languages', name: 'Languages' },
  { type: 'publications', name: 'Publications' },
  { type: 'volunteer', name: 'Volunteer Work' },
  { type: 'certifications', name: 'Certifications' },
  { type: 'custom', name: 'Custom Section' }
];

export default function EnhancedDragDropResumeBuilder({}: EnhancedDragDropResumeBuilderProps) {
  const dispatch = useDispatch();
  const { currentTemplate, isPreviewMode, undoStack, redoStack } = useSelector(
    (state: RootState) => state.modularResume
  );

  const [showAddSection, setShowAddSection] = useState(false);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [customizingSectionId, setCustomizingSectionId] = useState<string | null>(null);
  const [showTemplateManager, setShowTemplateManager] = useState(false);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [draggedSectionId, setDraggedSectionId] = useState<string | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const sections = currentTemplate?.sections || [];
  
  // Performance optimization - debounced update
  const debouncedUpdate = PerformanceEnhancements.useDebouncedUpdate(
    (updates: any) => {
      // Handle any real-time updates here
      console.log('Debounced update:', updates);
    }
  );

  const handleDragStart = useCallback((start: any) => {
    setDraggedSectionId(start.draggableId);
    // Announce to screen readers
    const section = sections.find(s => s.id === start.draggableId);
    if (section) {
      // You could implement announcements here for accessibility
      console.log(AccessibilityEnhancements.announcements.sectionMoved(section.title, start.source.index + 1));
    }
  }, [sections]);

  const handleDragEnd = useCallback((result: DropResult) => {
    setDraggedSectionId(null);
    
    if (!result.destination) return;

    dispatch(reorderSections({
      sourceIndex: result.source.index,
      destinationIndex: result.destination.index
    }));
    
    // Announce completion for accessibility
    const section = sections.find(s => s.id === result.draggableId);
    if (section) {
      console.log(AccessibilityEnhancements.announcements.sectionMoved(
        section.title, 
        result.destination.index + 1
      ));
    }
  }, [dispatch, sections]);

  const handleSectionVisibilityToggle = useCallback((sectionId: string) => {
    dispatch(toggleSectionVisibility(sectionId));
  }, [dispatch]);

  const handleSectionRemove = useCallback((sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (section?.isRequired) {
      alert('This section is required and cannot be removed.');
      return;
    }
    dispatch(removeSection(sectionId));
  }, [dispatch, sections]);

  const handleSectionEdit = useCallback((sectionId: string) => {
    setEditingSectionId(sectionId);
    dispatch(setSelectedSection(sectionId));
  }, [dispatch]);

  const handleSectionCustomize = useCallback((sectionId: string) => {
    setCustomizingSectionId(sectionId);
  }, []);

  const handleCustomizerClose = useCallback(() => {
    setCustomizingSectionId(null);
  }, []);

  const handleSectionStyleUpdate = useCallback((sectionId: string, updates: Partial<ModularSection>) => {
    const section = sections.find(s => s.id === sectionId);
    if (section) {
      dispatch(updateSection({
        ...section,
        ...updates
      }));
    }
  }, [dispatch, sections]);

  const handleKeyboardNavigation = useCallback((e: React.KeyboardEvent, sectionIndex: number) => {
    const moveUp = () => {
      if (sectionIndex > 0) {
        dispatch(reorderSections({
          sourceIndex: sectionIndex,
          destinationIndex: sectionIndex - 1
        }));
      }
    };
    
    const moveDown = () => {
      if (sectionIndex < sections.length - 1) {
        dispatch(reorderSections({
          sourceIndex: sectionIndex,
          destinationIndex: sectionIndex + 1
        }));
      }
    };
    
    AccessibilityEnhancements.keyboardNavigation.onKeyDown(e, moveUp, moveDown);
  }, [dispatch, sections.length]);

  const handleSectionSave = useCallback((sectionId: string, content: any) => {
    const section = sections.find(s => s.id === sectionId);
    if (section) {
      dispatch(updateSection({
        ...section,
        content
      }));
    }
    setEditingSectionId(null);
  }, [dispatch, sections]);

  const handleSectionCancel = useCallback(() => {
    setEditingSectionId(null);
    dispatch(setSelectedSection(null));
  }, [dispatch]);

  const handleAddSection = useCallback((sectionType: ModularSection['type']) => {
    const newSection: ModularSection = {
      id: Date.now().toString(),
      type: sectionType,
      title: AVAILABLE_SECTION_TYPES.find(t => t.type === sectionType)?.name || 'Custom Section',
      content: getDefaultContent(sectionType),
      isRequired: false,
      isVisible: true,
      order: sections.length
    };

    dispatch(addSection(newSection));
    setShowAddSection(false);
    
    // Announce addition for accessibility
    console.log(AccessibilityEnhancements.announcements.sectionAdded(newSection.title));
  }, [dispatch, sections.length]);

  const togglePreview = useCallback(() => {
    dispatch(setPreviewMode(!isPreviewMode));
  }, [dispatch, isPreviewMode]);

  const handleUndo = useCallback(() => {
    dispatch(undo());
  }, [dispatch]);

  const handleRedo = useCallback(() => {
    dispatch(redo());
  }, [dispatch]);

  const renderSectionContent = (section: ModularSection) => {
    const isEditing = editingSectionId === section.id;
    
    switch (section.type) {
      case 'header':
        return (
          <HeaderModule
            content={section.content as HeaderContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'summary':
        return (
          <SummaryModule
            content={section.content as SummaryContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'experience':
        return (
          <ExperienceModule
            content={section.content as ExperienceContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'education':
        return (
          <EducationModule
            content={section.content as EducationContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'skills':
        return (
          <SkillsModule
            content={section.content as SkillsContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'projects':
        return (
          <ProjectsModule
            content={section.content as ProjectsContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
          />
        );
      case 'awards':
      case 'languages':
      case 'publications':
      case 'volunteer':
      case 'certifications':
        return (
          <AdditionalModule
            content={section.content as AdditionalContent}
            isEditing={isEditing}
            onSave={(content) => handleSectionSave(section.id, content)}
            onCancel={handleSectionCancel}
            onEdit={() => handleSectionEdit(section.id)}
            sectionType={section.type}
          />
        );
      default:
        return (
          <div className="p-4 border rounded-lg bg-white">
            <p className="text-gray-500">Content editor for {section.type} coming soon...</p>
          </div>
        );
    }
  };

  const getSectionPreview = (section: ModularSection) => {
    switch (section.type) {
      case 'header':
        const headerContent = section.content as HeaderContent;
        return `${headerContent.name || 'Your Name'} • ${headerContent.title || 'Your Title'}`;
      case 'summary':
        const summaryContent = section.content as SummaryContent;
        return summaryContent.summary?.substring(0, 100) + '...' || 'Professional summary...';
      case 'experience':
        const expContent = section.content as ExperienceContent;
        const expCount = expContent.experiences?.length || 0;
        return `${expCount} work experience${expCount !== 1 ? 's' : ''}`;
      case 'education':
        const eduContent = section.content as EducationContent;
        const eduCount = eduContent.educations?.length || 0;
        return `${eduCount} education entr${eduCount !== 1 ? 'ies' : 'y'}`;
      case 'skills':
        const skillsContent = section.content as SkillsContent;
        const skillCount = skillsContent.skills?.length || 0;
        return `${skillCount} skill${skillCount !== 1 ? 's' : ''}`;
      case 'projects':
        const projectsContent = section.content as ProjectsContent;
        const projectCount = projectsContent.projects?.length || 0;
        return `${projectCount} project${projectCount !== 1 ? 's' : ''}`;
      case 'awards':
        const awardsContent = section.content as AdditionalContent;
        const awardCount = awardsContent.awards?.length || 0;
        return `${awardCount} award${awardCount !== 1 ? 's' : ''}`;
      case 'languages':
        const langContent = section.content as AdditionalContent;
        const langCount = langContent.languages?.length || 0;
        return `${langCount} language${langCount !== 1 ? 's' : ''}`;
      case 'publications':
        const pubContent = section.content as AdditionalContent;
        const pubCount = pubContent.publications?.length || 0;
        return `${pubCount} publication${pubCount !== 1 ? 's' : ''}`;
      case 'volunteer':
        const volContent = section.content as AdditionalContent;
        const volCount = volContent.volunteerWork?.length || 0;
        return `${volCount} volunteer experience${volCount !== 1 ? 's' : ''}`;
      case 'certifications':
        const certContent = section.content as AdditionalContent;
        const certCount = certContent.certifications?.length || 0;
        return `${certCount} certification${certCount !== 1 ? 's' : ''}`;
      default:
        return 'Custom content...';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Resume Builder</h2>
          <p className="text-gray-600">Drag and drop sections to customize your resume layout</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleUndo}
            disabled={undoStack.length === 0}
            className="flex items-center space-x-1"
          >
            <Undo className="h-4 w-4" />
            <span>Undo</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRedo}
            disabled={redoStack.length === 0}
            className="flex items-center space-x-1"
          >
            <Redo className="h-4 w-4" />
            <span>Redo</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setSnapToGrid(!snapToGrid)}
            className={`flex items-center space-x-1 ${snapToGrid ? 'bg-green-50 border-green-200 text-green-700' : ''}`}
          >
            <Grid className="h-4 w-4" />
            <span>Grid</span>
          </Button>

          <Button
            variant="outline"
            onClick={togglePreview}
            className="flex items-center space-x-2"
          >
            <Eye className="h-4 w-4" />
            <span>{isPreviewMode ? 'Edit Mode' : 'Preview'}</span>
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowTemplateManager(true)}
            className="flex items-center space-x-2"
          >
            <Folder className="h-4 w-4" />
            <span>Templates</span>
          </Button>

          <Button
            onClick={() => setShowAddSection(true)}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Section</span>
          </Button>
        </div>
      </div>

      {/* Template Info */}
      {currentTemplate && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-blue-600 font-semibold">Template:</span>
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">{currentTemplate.name}</h3>
              <p className="text-sm text-blue-700">{currentTemplate.description}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Drag and Drop Interface */}
      <DragDropContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
        <div className="relative" ref={containerRef}>
          {/* Snap to Grid Overlay */}
          {snapToGrid && <SnapToGrid.GridOverlay isActive={draggedSectionId !== null} />}
          
          <Droppable droppableId="resume-sections">
            {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={`
                  space-y-4 p-4 rounded-lg border-2 border-dashed transition-all duration-300 relative
                  ${TouchEnhancements.mobileSpacing}
                  ${snapshot.isDraggingOver
                    ? 'border-blue-400 bg-blue-50 shadow-inner'
                    : 'border-gray-300 bg-gray-50'
                  }
                `}
              >
                {sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id}
                    index={index}
                    isDragDisabled={isPreviewMode || editingSectionId !== null}
                  >
                    {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
                      <SectionCard
                        isDragging={snapshot.isDragging}
                        isPreview={isPreviewMode}
                        isVisible={section.isVisible}
                        className={TouchEnhancements.mobileSpacing}
                      >
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          style={{
                            ...provided.draggableProps.style,
                            ...(section.styling && {
                              backgroundColor: section.styling.backgroundColor,
                              color: section.styling.textColor,
                              fontFamily: section.styling.fontFamily,
                              fontSize: section.styling.fontSize,
                              fontWeight: section.styling.fontWeight,
                              padding: section.styling.spacing
                            })
                          }}
                        >
                          {/* Drop Zone Indicators */}
                          {snapshot.isDraggingOver && index === 0 && (
                            <DropZoneIndicator 
                              isActive={true} 
                              position="above" 
                              snapToGrid={snapToGrid} 
                            />
                          )}
                          
                          {/* Section Header */}
                          {!isPreviewMode && editingSectionId !== section.id && (
                            <div className="flex items-center justify-between p-4 border-b">
                              <div className="flex items-center space-x-4 flex-1">
                                <DragHandle 
                                  {...provided.dragHandleProps}
                                  {...AccessibilityEnhancements.dragHandleAria}
                                  isDragging={snapshot.isDragging}
                                  className={TouchEnhancements.touchTarget}
                                  onKeyDown={(e) => handleKeyboardNavigation(e, index)}
                                />
                                
                                <div className="flex items-center space-x-3">
                                  <span className="text-2xl">{SECTION_ICONS[section.type]}</span>
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2">
                                      <h3 className="font-semibold text-gray-900">{section.title}</h3>
                                      {section.isRequired && (
                                        <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded-full">
                                          Required
                                        </span>
                                      )}
                                      {!section.isVisible && (
                                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                                          Hidden
                                        </span>
                                      )}
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                      {SECTION_DESCRIPTIONS[section.type]}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Section Controls */}
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSectionCustomize(section.id)}
                                  className={`flex items-center space-x-1 ${TouchEnhancements.touchTarget}`}
                                  {...TouchEnhancements.touchFeedback}
                                >
                                  <Settings className="h-4 w-4" />
                                  <span className="text-xs">Style</span>
                                </Button>

                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSectionVisibilityToggle(section.id)}
                                  className={`flex items-center space-x-1 ${TouchEnhancements.touchTarget}`}
                                  {...TouchEnhancements.touchFeedback}
                                >
                                  <Eye className={`h-4 w-4 ${!section.isVisible ? 'text-gray-400' : 'text-blue-600'}`} />
                                  <span className="text-xs">
                                    {section.isVisible ? 'Hide' : 'Show'}
                                  </span>
                                </Button>

                                {!section.isRequired && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleSectionRemove(section.id)}
                                    className={`text-red-600 hover:text-red-700 ${TouchEnhancements.touchTarget}`}
                                    {...TouchEnhancements.touchFeedback}
                                  >
                                    <Trash className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Section Content */}
                          {section.isVisible && (
                            <PerformanceEnhancements.MemoizedSection
                              section={section}
                              isEditing={editingSectionId === section.id}
                            >
                              <div className={editingSectionId === section.id ? '' : 'p-0'}>
                                {renderSectionContent(section)}
                              </div>
                            </PerformanceEnhancements.MemoizedSection>
                          )}

                          {/* Preview Mode Summary */}
                          {isPreviewMode && section.isVisible && editingSectionId !== section.id && (
                            <div className="p-4 border-t bg-gray-50">
                              <div className="text-sm text-gray-700">
                                {getSectionPreview(section)}
                              </div>
                            </div>
                          )}
                          
                          {/* Drop Zone Indicator Below */}
                          {snapshot.isDraggingOver && (
                            <DropZoneIndicator 
                              isActive={true} 
                              position="below" 
                              snapToGrid={snapToGrid} 
                            />
                          )}
                        </div>
                      </SectionCard>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}

                {/* Empty State */}
                {sections.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <span className="text-6xl">📄</span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No sections added yet
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Start building your resume by adding sections
                    </p>
                    <Button 
                      onClick={() => setShowAddSection(true)} 
                      className={`flex items-center space-x-2 mx-auto ${TouchEnhancements.touchTarget}`}
                      {...TouchEnhancements.touchFeedback}
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Your First Section</span>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Droppable>
        </div>
      </DragDropContext>

      {/* Add Section Modal */}
      <AnimatePresence>
        {showAddSection && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            >
              <div className="flex justify-between items-center p-6 border-b">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Add New Section</h3>
                  <p className="text-sm text-gray-600 mt-1">Choose a section type to add to your resume</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setShowAddSection(false)}
                  className={TouchEnhancements.touchTarget}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {AVAILABLE_SECTION_TYPES.map((sectionType) => (
                    <motion.button
                      key={sectionType.type}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleAddSection(sectionType.type as ModularSection['type'])}
                      className={`
                        p-4 border rounded-xl hover:border-blue-300 hover:bg-blue-50 
                        focus:outline-none focus:ring-2 focus:ring-blue-500 text-left
                        transition-all duration-200 ${TouchEnhancements.touchTarget}
                      `}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <span className="text-lg">{SECTION_ICONS[sectionType.type as keyof typeof SECTION_ICONS]}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 mb-1">{sectionType.name}</div>
                          <div className="text-sm text-gray-600 leading-relaxed">
                            {SECTION_DESCRIPTIONS[sectionType.type as keyof typeof SECTION_DESCRIPTIONS]}
                          </div>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Section Customizer */}
      {customizingSectionId && (
        <SectionCustomizer
          section={sections.find(s => s.id === customizingSectionId)!}
          isOpen={true}
          onClose={handleCustomizerClose}
          onUpdate={handleSectionStyleUpdate}
        />
      )}

      {/* Template Manager */}
      <TemplateManager
        isOpen={showTemplateManager}
        onClose={() => setShowTemplateManager(false)}
      />

      {/* Enhanced Tips */}
      {!isPreviewMode && sections.length > 0 && editingSectionId === null && (
        <Card className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <h4 className="font-medium text-yellow-800 mb-3 flex items-center">
            💡 Pro Tips for Building Your Resume
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-yellow-700">
            <div className="space-y-2">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">🎯</span>
                <span>Drag sections to reorder by importance</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">👁️</span>
                <span>Hide sections without deleting content</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-purple-500 mt-0.5">🎨</span>
                <span>Use the Style button to customize appearance</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-start space-x-2">
                <span className="text-indigo-500 mt-0.5">⊞</span>
                <span>Enable snap-to-grid for precise alignment</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-orange-500 mt-0.5">↩️</span>
                <span>Use undo/redo to experiment safely</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-500 mt-0.5">📱</span>
                <span>Touch and hold on mobile to drag sections</span>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}

// Helper function to create default content for new sections
function getDefaultContent(sectionType: ModularSection['type']): any {
  switch (sectionType) {
    case 'header':
      return {
        name: '',
        title: '',
        contact: {
          email: '',
          phone: '',
          location: '',
          website: '',
          linkedin: '',
          github: ''
        }
      } as HeaderContent;
    case 'summary':
      return {
        summary: '',
        objective: ''
      } as SummaryContent;
    case 'experience':
      return {
        experiences: []
      } as ExperienceContent;
    case 'education':
      return {
        educations: []
      } as EducationContent;
    case 'skills':
      return {
        skills: [],
        categories: {
          technical: [],
          soft: [],
          languages: [],
          other: []
        }
      } as SkillsContent;
    case 'projects':
      return {
        projects: []
      } as ProjectsContent;
    case 'awards':
      return {
        awards: []
      } as AdditionalContent;
    case 'languages':
      return {
        languages: []
      } as AdditionalContent;
    case 'publications':
      return {
        publications: []
      } as AdditionalContent;
    case 'volunteer':
      return {
        volunteerWork: []
      } as AdditionalContent;
    case 'certifications':
      return {
        certifications: []
      } as AdditionalContent;
    default:
      return {};
  }
}