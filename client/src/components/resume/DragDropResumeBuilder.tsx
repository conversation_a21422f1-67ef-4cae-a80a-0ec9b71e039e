import { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import type { DropResult, DroppableProvided, DroppableStateSnapshot, DraggableProvided, DraggableStateSnapshot } from 'react-beautiful-dnd';
import { PlusIcon, GripVerticalIcon, TrashIcon, EyeIcon } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';

interface DraggableSection {
  id: string;
  type: 'header' | 'summary' | 'experience' | 'education' | 'skills' | 'projects' | 'custom';
  title: string;
  content: any;
  isRequired: boolean;
  isVisible: boolean;
}

interface DragDropResumeBuilderProps {
  sections: DraggableSection[];
  onSectionsUpdate: (sections: DraggableSection[]) => void;
  onSectionEdit: (sectionId: string) => void;
  onSectionAdd: () => void;
  template: any;
}

const SECTION_ICONS = {
  header: '👤',
  summary: '📝',
  experience: '💼',
  education: '🎓',
  skills: '⚡',
  projects: '🚀',
  custom: '📄'
};

const SECTION_DESCRIPTIONS = {
  header: 'Personal information and contact details',
  summary: 'Professional summary or objective statement',
  experience: 'Work history and professional achievements',
  education: 'Academic background and qualifications',
  skills: 'Technical and soft skills',
  projects: 'Key projects and accomplishments',
  custom: 'Custom section with personalized content'
};

export default function DragDropResumeBuilder({
  sections,
  onSectionsUpdate,
  onSectionEdit,
  onSectionAdd,
  template
}: DragDropResumeBuilderProps) {
  const [previewMode, setPreviewMode] = useState(false);

  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;

    const newSections = Array.from(sections);
    const [reorderedSection] = newSections.splice(result.source.index, 1);
    newSections.splice(result.destination.index, 0, reorderedSection);

    onSectionsUpdate(newSections);
  }, [sections, onSectionsUpdate]);

  const toggleSectionVisibility = useCallback((sectionId: string) => {
    const updatedSections = sections.map(section =>
      section.id === sectionId
        ? { ...section, isVisible: !section.isVisible }
        : section
    );
    onSectionsUpdate(updatedSections);
  }, [sections, onSectionsUpdate]);

  const removeSection = useCallback((sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (section?.isRequired) {
      alert('This section is required and cannot be removed.');
      return;
    }
    
    const updatedSections = sections.filter(section => section.id !== sectionId);
    onSectionsUpdate(updatedSections);
  }, [sections, onSectionsUpdate]);

  const getSectionPreview = (section: DraggableSection) => {
    const content = section.content;
    switch (section.type) {
      case 'header':
        return `${content?.name || 'Your Name'} • ${content?.title || 'Your Title'}`;
      case 'summary':
        return content?.summary?.substring(0, 100) + '...' || 'Professional summary...';
      case 'experience': {
        const expCount = content?.experience?.length || 0;
        return `${expCount} work experience${expCount !== 1 ? 's' : ''}`;
      }
      case 'education': {
        const eduCount = content?.education?.length || 0;
        return `${eduCount} education entr${eduCount !== 1 ? 'ies' : 'y'}`;
      }
      case 'skills': {
        const skillCount = content?.skills?.length || 0;
        return `${skillCount} skill${skillCount !== 1 ? 's' : ''}`;
      }
      case 'projects': {
        const projCount = content?.projects?.length || 0;
        return `${projCount} project${projCount !== 1 ? 's' : ''}`;
      }
      default:
        return 'Custom content...';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Resume Builder</h2>
          <p className="text-gray-600">Drag and drop sections to customize your resume layout</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
            className="flex items-center space-x-2"
          >
            <EyeIcon className="h-4 w-4" />
            <span>{previewMode ? 'Edit Mode' : 'Preview'}</span>
          </Button>
          <Button
            onClick={onSectionAdd}
            className="flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Section</span>
          </Button>
        </div>
      </div>

      {/* Template Info */}
      {template && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-blue-600 font-semibold">Template:</span>
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">{template.name}</h3>
              <p className="text-sm text-blue-700">{template.description}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Drag and Drop Interface */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="resume-sections">
          {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={`space-y-3 p-4 rounded-lg border-2 border-dashed transition-colors ${
                snapshot.isDraggingOver
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 bg-gray-50'
              }`}
            >
              {sections.map((section, index) => (
                <Draggable
                  key={section.id}
                  draggableId={section.id}
                  index={index}
                  isDragDisabled={previewMode}
                >
                  {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`p-4 rounded-xl border transition-all duration-200 ${
                        snapshot.isDragging
                          ? 'shadow-lg rotate-2 bg-white border-blue-300'
                          : 'shadow-sm hover:shadow-md bg-white border-gray-200'
                      } ${
                        !section.isVisible
                          ? 'opacity-50 bg-gray-100'
                          : 'bg-white'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        {/* Section Info */}
                        <div className="flex items-center space-x-4 flex-1">
                          {!previewMode && (
                            <div
                              {...provided.dragHandleProps}
                              className="p-1 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing"
                            >
                              <GripVerticalIcon className="h-4 w-4 text-gray-400" />
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{SECTION_ICONS[section.type]}</span>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h3 className="font-semibold text-gray-900">{section.title}</h3>
                                {section.isRequired && (
                                  <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded-full">
                                    Required
                                  </span>
                                )}
                                {!section.isVisible && (
                                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                                    Hidden
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 mt-1">
                                {SECTION_DESCRIPTIONS[section.type]}
                              </p>
                              {previewMode && (
                                <p className="text-sm text-gray-800 mt-2 font-medium">
                                  {getSectionPreview(section)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Section Controls */}
                        {!previewMode && (
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleSectionVisibility(section.id)}
                              className="flex items-center space-x-1"
                            >
                              <EyeIcon className={`h-4 w-4 ${!section.isVisible ? 'text-gray-400' : 'text-blue-600'}`} />
                              <span className="text-xs">
                                {section.isVisible ? 'Hide' : 'Show'}
                              </span>
                            </Button>

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onSectionEdit(section.id)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              Edit
                            </Button>

                            {!section.isRequired && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => removeSection(section.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Section Content Preview */}
                      {previewMode && section.isVisible && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-700">
                            {getSectionPreview(section)}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}

              {/* Empty State */}
              {sections.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <span className="text-6xl">📄</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No sections added yet
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Start building your resume by adding sections
                  </p>
                  <Button onClick={onSectionAdd} className="flex items-center space-x-2 mx-auto">
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Your First Section</span>
                  </Button>
                </div>
              )}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Tips */}
      {!previewMode && sections.length > 0 && (
        <Card className="p-4 bg-yellow-50 border-yellow-200">
          <h4 className="font-medium text-yellow-800 mb-2">💡 Tips for Building Your Resume</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Drag sections to reorder them based on importance</li>
            <li>• Hide sections you don't need without deleting content</li>
            <li>• Required sections cannot be removed but can be hidden</li>
            <li>• Use preview mode to see how your resume flows</li>
          </ul>
        </Card>
      )}
    </div>
  );
}