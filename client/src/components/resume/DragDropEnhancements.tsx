import React from 'react';
import { motion } from 'framer-motion';
import { GripVerticalIcon } from 'lucide-react';

// Enhanced drag handle with better visual feedback
export const DragHandle: React.FC<{ isDragging?: boolean; className?: string }> = ({ 
  isDragging = false, 
  className = "" 
}) => (
  <motion.div
    className={`p-2 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing transition-colors ${className}`}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.95 }}
    animate={{ 
      rotateZ: isDragging ? 5 : 0,
      scale: isDragging ? 1.1 : 1
    }}
  >
    <GripVerticalIcon className={`h-4 w-4 ${isDragging ? 'text-blue-500' : 'text-gray-400'}`} />
  </motion.div>
);

// Drop zone indicator with snap-to-grid visual feedback
export const DropZoneIndicator: React.FC<{ 
  isActive: boolean; 
  position: 'above' | 'below' | 'between';
  snapToGrid?: boolean;
}> = ({ isActive, position, snapToGrid = false }) => {
  if (!isActive) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ 
        opacity: 1, 
        height: 'auto',
        scale: snapToGrid ? [1, 1.02, 1] : 1
      }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.2 }}
      className={`
        flex items-center justify-center py-2 px-4 mx-2 rounded-lg border-2 border-dashed
        ${position === 'above' ? 'mb-2' : position === 'below' ? 'mt-2' : 'my-2'}
        ${snapToGrid ? 'border-green-400 bg-green-50' : 'border-blue-400 bg-blue-50'}
      `}
    >
      <div className={`text-sm font-medium ${snapToGrid ? 'text-green-600' : 'text-blue-600'}`}>
        {snapToGrid ? '⊞ Snap to Grid' : '↓ Drop section here'}
      </div>
    </motion.div>
  );
};

// Enhanced section card with better animations and visual feedback
export const SectionCard: React.FC<{
  isDragging: boolean;
  isPreview: boolean;
  isVisible: boolean;
  children: React.ReactNode;
  className?: string;
}> = ({ isDragging, isPreview, isVisible, children, className = "" }) => (
  <motion.div
    layout
    initial={{ opacity: 0, y: 20 }}
    animate={{ 
      opacity: isVisible ? 1 : 0.5,
      y: 0,
      scale: isDragging ? 1.02 : 1,
      rotateZ: isDragging ? 1 : 0,
      boxShadow: isDragging 
        ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
    }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ 
      type: "spring", 
      stiffness: 300, 
      damping: 30 
    }}
    className={`
      rounded-xl border transition-all duration-200 
      ${isDragging 
        ? 'border-blue-300 bg-white z-10' 
        : 'border-gray-200 hover:border-gray-300'
      }
      ${!isVisible ? 'bg-gray-100' : 'bg-white'}
      ${isPreview ? 'cursor-default' : 'cursor-move'}
      ${className}
    `}
  >
    {children}
  </motion.div>
);

// Touch-friendly mobile enhancements
export const TouchEnhancements = {
  // Touch feedback for mobile devices
  touchFeedback: {
    initial: { scale: 1 },
    whileTap: { scale: 0.98 },
    transition: { duration: 0.1 }
  },
  
  // Enhanced touch area for small screens
  touchTarget: "min-h-[44px] min-w-[44px]",
  
  // Mobile-optimized spacing
  mobileSpacing: "touch-manipulation select-none"
};

// Accessibility enhancements
export const AccessibilityEnhancements = {
  // ARIA labels for drag and drop
  dragHandleAria: {
    'aria-label': 'Drag handle to reorder section',
    'role': 'button',
    'tabIndex': 0
  },
  
  // Keyboard navigation support
  keyboardNavigation: {
    onKeyDown: (e: React.KeyboardEvent, onMoveUp: () => void, onMoveDown: () => void) => {
      if (e.key === 'ArrowUp' && e.ctrlKey) {
        e.preventDefault();
        onMoveUp();
      } else if (e.key === 'ArrowDown' && e.ctrlKey) {
        e.preventDefault();
        onMoveDown();
      }
    }
  },
  
  // Screen reader announcements
  announcements: {
    sectionMoved: (sectionName: string, newPosition: number) => 
      `${sectionName} moved to position ${newPosition}`,
    sectionAdded: (sectionName: string) => 
      `${sectionName} section added to resume`,
    sectionRemoved: (sectionName: string) => 
      `${sectionName} section removed from resume`
  }
};

// Snap-to-grid helper
export const SnapToGrid = {
  // Calculate snap position based on mouse position
  calculateSnapPosition: (mouseY: number, containerRef: React.RefObject<HTMLElement>) => {
    if (!containerRef.current) return null;
    
    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    const relativeY = mouseY - rect.top;
    const gridSize = 60; // Approximate height of a section
    
    return Math.round(relativeY / gridSize) * gridSize;
  },
  
  // Visual grid overlay
  GridOverlay: React.forwardRef<HTMLDivElement, { isActive: boolean }>(
    ({ isActive }, ref) => (
      <div
        ref={ref}
        className={`
          absolute inset-0 pointer-events-none transition-opacity duration-200
          ${isActive ? 'opacity-20' : 'opacity-0'}
        `}
        style={{
          backgroundImage: 'repeating-linear-gradient(0deg, transparent, transparent 59px, #3b82f6 59px, #3b82f6 60px)',
          backgroundSize: '100% 60px'
        }}
      />
    )
  )
};

// Performance optimizations
export const PerformanceEnhancements = {
  // Memoized section renderer
  MemoizedSection: React.memo<{
    section: any;
    isEditing: boolean;
    children: React.ReactNode;
  }>(({ section, isEditing, children }) => {
    return <>{children}</>;
  }, (prevProps, nextProps) => {
    // Only re-render if section content or editing state changes
    return (
      prevProps.section === nextProps.section &&
      prevProps.isEditing === nextProps.isEditing
    );
  }),
  
  // Debounced update function
  useDebouncedUpdate: (callback: Function, delay: number = 300) => {
    const timeoutRef = React.useRef<NodeJS.Timeout>();
    
    return React.useCallback((...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }, [callback, delay]);
  }
};