import React, { useState } from 'react';
import type { SkillsContent, Skill } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { PencilIcon, SaveIcon, XIcon, PlusIcon, TrashIcon } from 'lucide-react';

interface SkillsModuleProps {
  content: SkillsContent;
  isEditing: boolean;
  onSave: (content: SkillsContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const SkillsModule: React.FC<SkillsModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<SkillsContent>(content);

  const handleSave = () => {
    // Organize skills by categories
    const categorizedSkills = {
      technical: editContent.skills.filter(skill => skill.category === 'technical'),
      soft: editContent.skills.filter(skill => skill.category === 'soft'),
      languages: editContent.skills.filter(skill => skill.category === 'language'),
      other: editContent.skills.filter(skill => skill.category === 'other'),
    };

    onSave({
      ...editContent,
      categories: categorizedSkills
    });
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const addSkill = () => {
    const newSkill: Skill = {
      id: Date.now().toString(),
      name: '',
      level: 'intermediate',
      category: 'technical'
    };

    setEditContent(prev => ({
      ...prev,
      skills: [...prev.skills, newSkill]
    }));
  };

  const removeSkill = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill.id !== id)
    }));
  };

  const updateSkill = (id: string, field: keyof Skill, value: any) => {
    setEditContent(prev => ({
      ...prev,
      skills: prev.skills.map(skill =>
        skill.id === id ? { ...skill, [field]: value } : skill
      )
    }));
  };

  const getLevelColor = (level: Skill['level']) => {
    switch (level) {
      case 'beginner': return 'bg-red-100 text-red-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-blue-100 text-blue-800';
      case 'expert': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Skills</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {editContent.skills.map((skill, index) => (
            <div key={skill.id} className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Skill {index + 1}</h4>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => removeSkill(skill.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Skill Name</label>
                  <Input
                    value={skill.name}
                    onChange={(e) => updateSkill(skill.id, 'name', e.target.value)}
                    placeholder="e.g., JavaScript, Leadership, Spanish"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={skill.category}
                    onChange={(e) => updateSkill(skill.id, 'category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="technical">Technical</option>
                    <option value="soft">Soft Skills</option>
                    <option value="language">Languages</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Proficiency Level</label>
                  <select
                    value={skill.level}
                    onChange={(e) => updateSkill(skill.id, 'level', e.target.value as Skill['level'])}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="expert">Expert</option>
                  </select>
                </div>
              </div>
            </div>
          ))}

          <Button onClick={addSkill} className="w-full flex items-center justify-center space-x-2">
            <PlusIcon className="h-4 w-4" />
            <span>Add Skill</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900">Skills</h3>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      {content.skills.length === 0 ? (
        <p className="text-gray-500 italic">No skills added yet.</p>
      ) : (
        <div className="space-y-6">
          {/* Technical Skills */}
          {content.categories?.technical?.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Technical Skills</h4>
              <div className="flex flex-wrap gap-2">
                {content.categories.technical.map((skill) => (
                  <div key={skill.id} className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                      {skill.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Soft Skills */}
          {content.categories?.soft?.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Soft Skills</h4>
              <div className="flex flex-wrap gap-2">
                {content.categories.soft.map((skill) => (
                  <div key={skill.id} className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                      {skill.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Languages */}
          {content.categories?.languages?.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Languages</h4>
              <div className="flex flex-wrap gap-2">
                {content.categories.languages.map((skill) => (
                  <div key={skill.id} className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                      {skill.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Other Skills */}
          {content.categories?.other?.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Other Skills</h4>
              <div className="flex flex-wrap gap-2">
                {content.categories.other.map((skill) => (
                  <div key={skill.id} className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                      {skill.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fallback for uncategorized skills */}
          {(!content.categories || Object.values(content.categories).every(cat => cat.length === 0)) && (
            <div className="flex flex-wrap gap-2">
              {content.skills.map((skill) => (
                <div key={skill.id} className="flex items-center space-x-2">
                  <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                    {skill.name}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(skill.level)}`}>
                    {skill.level}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SkillsModule;