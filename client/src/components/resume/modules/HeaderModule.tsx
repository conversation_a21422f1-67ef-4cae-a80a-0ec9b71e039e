import React, { useState } from 'react';
import type { HeaderContent } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { Pencil as PencilIcon, Save as SaveIcon, X as XIcon } from 'lucide-react';

interface HeaderModuleProps {
  content: HeaderContent;
  isEditing: boolean;
  onSave: (content: HeaderContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const HeaderModule: React.FC<HeaderModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<HeaderContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const updateContent = (field: keyof HeaderContent, value: any) => {
    setEditContent(prev => ({ ...prev, [field]: value }));
  };

  const updateContact = (field: keyof HeaderContent['contact'], value: string) => {
    setEditContent(prev => ({
      ...prev,
      contact: { ...prev.contact, [field]: value }
    }));
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Header Information</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
            <Input
              value={editContent.name}
              onChange={(e) => updateContent('name', e.target.value)}
              placeholder="Enter your full name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Professional Title</label>
            <Input
              value={editContent.title}
              onChange={(e) => updateContent('title', e.target.value)}
              placeholder="e.g., Software Engineer, Marketing Manager"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <Input
              type="email"
              value={editContent.contact.email}
              onChange={(e) => updateContact('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            <Input
              value={editContent.contact.phone}
              onChange={(e) => updateContact('phone', e.target.value)}
              placeholder="+****************"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
            <Input
              value={editContent.contact.location}
              onChange={(e) => updateContact('location', e.target.value)}
              placeholder="City, State, Country"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Website (Optional)</label>
            <Input
              value={editContent.contact.website || ''}
              onChange={(e) => updateContact('website', e.target.value)}
              placeholder="https://yourwebsite.com"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn (Optional)</label>
            <Input
              value={editContent.contact.linkedin || ''}
              onChange={(e) => updateContact('linkedin', e.target.value)}
              placeholder="https://linkedin.com/in/yourprofile"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">GitHub (Optional)</label>
            <Input
              value={editContent.contact.github || ''}
              onChange={(e) => updateContact('github', e.target.value)}
              placeholder="https://github.com/yourusername"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">{content.name || 'Your Name'}</h1>
          <p className="text-lg text-gray-600 mt-1">{content.title || 'Your Professional Title'}</p>
        </div>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
        <div className="flex items-center space-x-2">
          <span className="font-medium">📧</span>
          <span>{content.contact.email || 'Email not provided'}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="font-medium">📞</span>
          <span>{content.contact.phone || 'Phone not provided'}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="font-medium">📍</span>
          <span>{content.contact.location || 'Location not provided'}</span>
        </div>

        {content.contact.website && (
          <div className="flex items-center space-x-2">
            <span className="font-medium">🌐</span>
            <a href={content.contact.website} target="_blank" rel="noopener noreferrer" 
               className="text-blue-600 hover:text-blue-800">Website</a>
          </div>
        )}

        {content.contact.linkedin && (
          <div className="flex items-center space-x-2">
            <span className="font-medium">💼</span>
            <a href={content.contact.linkedin} target="_blank" rel="noopener noreferrer" 
               className="text-blue-600 hover:text-blue-800">LinkedIn</a>
          </div>
        )}

        {content.contact.github && (
          <div className="flex items-center space-x-2">
            <span className="font-medium">💻</span>
            <a href={content.contact.github} target="_blank" rel="noopener noreferrer" 
               className="text-blue-600 hover:text-blue-800">GitHub</a>
          </div>
        )}
      </div>
    </div>
  );
};

export default HeaderModule;