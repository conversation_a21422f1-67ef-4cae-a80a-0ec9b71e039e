import React, { useState } from 'react';
import type { SummaryContent } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import { PencilIcon, SaveIcon, XIcon } from 'lucide-react';

interface SummaryModuleProps {
  content: SummaryContent;
  isEditing: boolean;
  onSave: (content: SummaryContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const SummaryModule: React.FC<SummaryModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<SummaryContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const updateContent = (field: keyof SummaryContent, value: string) => {
    setEditContent(prev => ({ ...prev, [field]: value }));
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Professional Summary</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Professional Summary
            </label>
            <textarea
              value={editContent.summary}
              onChange={(e) => updateContent('summary', e.target.value)}
              placeholder="Write a compelling professional summary that highlights your key strengths, experience, and career objectives..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              rows={6}
            />
            <p className="text-sm text-gray-500 mt-1">
              Tip: Keep it concise (3-4 sentences) and focus on your most relevant qualifications.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Career Objective (Optional)
            </label>
            <textarea
              value={editContent.objective || ''}
              onChange={(e) => updateContent('objective', e.target.value)}
              placeholder="Describe your career goals and what you're looking to achieve in your next role..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
            <p className="text-sm text-gray-500 mt-1">
              Note: Career objectives are more common for entry-level positions or career changes.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900">Professional Summary</h3>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      <div className="space-y-4">
        {content.summary ? (
          <div>
            <p className="text-gray-700 leading-relaxed">{content.summary}</p>
          </div>
        ) : (
          <p className="text-gray-500 italic">No professional summary added yet.</p>
        )}

        {content.objective && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-2">Career Objective</h4>
            <p className="text-gray-700 leading-relaxed">{content.objective}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SummaryModule;