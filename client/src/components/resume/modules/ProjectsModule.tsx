import React, { useState } from 'react';
import type { ProjectsContent, Project } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { Pencil as PencilIcon, Save as SaveIcon, X as XIcon, Plus as PlusIcon, Trash as TrashIcon, ExternalLink as ExternalLinkIcon, GitBranch as GitBranchIcon } from 'lucide-react';

interface ProjectsModuleProps {
  content: ProjectsContent;
  isEditing: boolean;
  onSave: (content: ProjectsContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const ProjectsModule: React.FC<ProjectsModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<ProjectsContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const addProject = () => {
    const newProject: Project = {
      id: Date.now().toString(),
      name: '',
      description: '',
      technologies: [],
      startDate: '',
      endDate: '',
      link: '',
      repository: '',
      achievements: ['']
    };

    setEditContent(prev => ({
      ...prev,
      projects: [...prev.projects, newProject]
    }));
  };

  const removeProject = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.filter(project => project.id !== id)
    }));
  };

  const updateProject = (id: string, field: keyof Project, value: any) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === id ? { ...project, [field]: value } : project
      )
    }));
  };

  const addTechnology = (projectId: string) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? { ...project, technologies: [...project.technologies, ''] }
          : project
      )
    }));
  };

  const updateTechnology = (projectId: string, techIndex: number, value: string) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? {
              ...project,
              technologies: project.technologies.map((tech, index) =>
                index === techIndex ? value : tech
              )
            }
          : project
      )
    }));
  };

  const removeTechnology = (projectId: string, techIndex: number) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? {
              ...project,
              technologies: project.technologies.filter((_, index) => index !== techIndex)
            }
          : project
      )
    }));
  };

  const addAchievement = (projectId: string) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? { ...project, achievements: [...project.achievements, ''] }
          : project
      )
    }));
  };

  const updateAchievement = (projectId: string, achievementIndex: number, value: string) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? {
              ...project,
              achievements: project.achievements.map((achievement, index) =>
                index === achievementIndex ? value : achievement
              )
            }
          : project
      )
    }));
  };

  const removeAchievement = (projectId: string, achievementIndex: number) => {
    setEditContent(prev => ({
      ...prev,
      projects: prev.projects.map(project =>
        project.id === projectId
          ? {
              ...project,
              achievements: project.achievements.filter((_, index) => index !== achievementIndex)
            }
          : project
      )
    }));
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Projects</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {editContent.projects.map((project, index) => (
            <div key={project.id} className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Project {index + 1}</h4>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => removeProject(project.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                  <Input
                    value={project.name}
                    onChange={(e) => updateProject(project.id, 'name', e.target.value)}
                    placeholder="Project name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <Input
                    type="month"
                    value={project.startDate}
                    onChange={(e) => updateProject(project.id, 'startDate', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date (Optional)</label>
                  <Input
                    type="month"
                    value={project.endDate || ''}
                    onChange={(e) => updateProject(project.id, 'endDate', e.target.value)}
                    placeholder="Leave empty if ongoing"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project Link (Optional)</label>
                  <Input
                    value={project.link || ''}
                    onChange={(e) => updateProject(project.id, 'link', e.target.value)}
                    placeholder="https://project-demo.com"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Repository (Optional)</label>
                  <Input
                    value={project.repository || ''}
                    onChange={(e) => updateProject(project.id, 'repository', e.target.value)}
                    placeholder="https://github.com/username/project"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={project.description}
                  onChange={(e) => updateProject(project.id, 'description', e.target.value)}
                  placeholder="Brief description of the project and your role..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">Technologies Used</label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addTechnology(project.id)}
                    className="flex items-center space-x-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Technology</span>
                  </Button>
                </div>

                {project.technologies.map((tech, techIndex) => (
                  <div key={techIndex} className="flex items-center space-x-2">
                    <Input
                      value={tech}
                      onChange={(e) => updateTechnology(project.id, techIndex, e.target.value)}
                      placeholder="e.g., React, Node.js, MongoDB"
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeTechnology(project.id, techIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">Key Achievements</label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addAchievement(project.id)}
                    className="flex items-center space-x-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Achievement</span>
                  </Button>
                </div>

                {project.achievements.map((achievement, achievementIndex) => (
                  <div key={achievementIndex} className="flex items-center space-x-2">
                    <Input
                      value={achievement}
                      onChange={(e) => updateAchievement(project.id, achievementIndex, e.target.value)}
                      placeholder="Describe a key result or feature..."
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeAchievement(project.id, achievementIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <Button onClick={addProject} className="w-full flex items-center justify-center space-x-2">
            <PlusIcon className="h-4 w-4" />
            <span>Add Project</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900">Projects</h3>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      {content.projects.length === 0 ? (
        <p className="text-gray-500 italic">No projects added yet.</p>
      ) : (
        <div className="space-y-6">
          {content.projects.map((project) => (
            <div key={project.id} className="border-l-4 border-purple-500 pl-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold text-lg text-gray-900">{project.name}</h4>
                  <div className="flex items-center space-x-4 mt-1">
                    {project.link && (
                      <a
                        href={project.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 text-purple-600 hover:text-purple-800 text-sm"
                      >
                        <ExternalLinkIcon className="h-4 w-4" />
                        <span>Live Demo</span>
                      </a>
                    )}
                    {project.repository && (
                      <a
                        href={project.repository}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 text-purple-600 hover:text-purple-800 text-sm"
                      >
                        <GitBranchIcon className="h-4 w-4" />
                        <span>Repository</span>
                      </a>
                    )}
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  {project.startDate} - {project.endDate || 'Present'}
                </div>
              </div>

              <p className="text-gray-700 mb-3">{project.description}</p>

              {project.technologies.filter(tech => tech.trim()).length > 0 && (
                <div className="mb-3">
                  <h5 className="font-medium text-gray-900 mb-2">Technologies:</h5>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies
                      .filter(tech => tech.trim())
                      .map((tech, index) => (
                        <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm">
                          {tech}
                        </span>
                      ))}
                  </div>
                </div>
              )}

              {project.achievements.filter(achievement => achievement.trim()).length > 0 && (
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {project.achievements
                    .filter(achievement => achievement.trim())
                    .map((achievement, index) => (
                      <li key={index}>{achievement}</li>
                    ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectsModule;