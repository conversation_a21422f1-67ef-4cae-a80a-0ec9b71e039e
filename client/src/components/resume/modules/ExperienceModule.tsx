import React, { useState } from 'react';
import type { ExperienceContent, WorkExperience } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { PencilIcon, SaveIcon, XIcon, PlusIcon, TrashIcon } from 'lucide-react';

interface ExperienceModuleProps {
  content: ExperienceContent;
  isEditing: boolean;
  onSave: (content: ExperienceContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const ExperienceModule: React.FC<ExperienceModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<ExperienceContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const addExperience = () => {
    const newExperience: WorkExperience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      achievements: [''],
      description: ''
    };

    setEditContent(prev => ({
      ...prev,
      experiences: [...prev.experiences, newExperience]
    }));
  };

  const removeExperience = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      experiences: prev.experiences.filter(exp => exp.id !== id)
    }));
  };

  const updateExperience = (id: string, field: keyof WorkExperience, value: any) => {
    setEditContent(prev => ({
      ...prev,
      experiences: prev.experiences.map(exp =>
        exp.id === id ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const addAchievement = (experienceId: string) => {
    setEditContent(prev => ({
      ...prev,
      experiences: prev.experiences.map(exp =>
        exp.id === experienceId
          ? { ...exp, achievements: [...exp.achievements, ''] }
          : exp
      )
    }));
  };

  const updateAchievement = (experienceId: string, achievementIndex: number, value: string) => {
    setEditContent(prev => ({
      ...prev,
      experiences: prev.experiences.map(exp =>
        exp.id === experienceId
          ? {
              ...exp,
              achievements: exp.achievements.map((achievement, index) =>
                index === achievementIndex ? value : achievement
              )
            }
          : exp
      )
    }));
  };

  const removeAchievement = (experienceId: string, achievementIndex: number) => {
    setEditContent(prev => ({
      ...prev,
      experiences: prev.experiences.map(exp =>
        exp.id === experienceId
          ? {
              ...exp,
              achievements: exp.achievements.filter((_, index) => index !== achievementIndex)
            }
          : exp
      )
    }));
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Work Experience</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {editContent.experiences.map((experience, index) => (
            <div key={experience.id} className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Experience {index + 1}</h4>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => removeExperience(experience.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                  <Input
                    value={experience.company}
                    onChange={(e) => updateExperience(experience.id, 'company', e.target.value)}
                    placeholder="Company name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
                  <Input
                    value={experience.position}
                    onChange={(e) => updateExperience(experience.id, 'position', e.target.value)}
                    placeholder="Job title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                  <Input
                    value={experience.location}
                    onChange={(e) => updateExperience(experience.id, 'location', e.target.value)}
                    placeholder="City, State"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <Input
                    type="month"
                    value={experience.startDate}
                    onChange={(e) => updateExperience(experience.id, 'startDate', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <Input
                    type="month"
                    value={experience.endDate}
                    onChange={(e) => updateExperience(experience.id, 'endDate', e.target.value)}
                    disabled={experience.current}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`current-${experience.id}`}
                    checked={experience.current}
                    onChange={(e) => updateExperience(experience.id, 'current', e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor={`current-${experience.id}`} className="text-sm text-gray-700">
                    Currently employed here
                  </label>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                <textarea
                  value={experience.description || ''}
                  onChange={(e) => updateExperience(experience.id, 'description', e.target.value)}
                  placeholder="Brief description of the role..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">Key Achievements</label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addAchievement(experience.id)}
                    className="flex items-center space-x-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Achievement</span>
                  </Button>
                </div>

                {experience.achievements.map((achievement, achievementIndex) => (
                  <div key={achievementIndex} className="flex items-center space-x-2">
                    <Input
                      value={achievement}
                      onChange={(e) => updateAchievement(experience.id, achievementIndex, e.target.value)}
                      placeholder="Describe a key achievement or responsibility..."
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeAchievement(experience.id, achievementIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <Button onClick={addExperience} className="w-full flex items-center justify-center space-x-2">
            <PlusIcon className="h-4 w-4" />
            <span>Add Experience</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900">Work Experience</h3>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      {content.experiences.length === 0 ? (
        <p className="text-gray-500 italic">No work experience added yet.</p>
      ) : (
        <div className="space-y-6">
          {content.experiences.map((experience) => (
            <div key={experience.id} className="border-l-4 border-blue-500 pl-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold text-lg text-gray-900">{experience.position}</h4>
                  <p className="text-blue-600 font-medium">{experience.company}</p>
                  <p className="text-sm text-gray-600">{experience.location}</p>
                </div>
                <div className="text-sm text-gray-500">
                  {experience.startDate} - {experience.current ? 'Present' : experience.endDate}
                </div>
              </div>

              {experience.description && (
                <p className="text-gray-700 mb-3">{experience.description}</p>
              )}

              {experience.achievements.filter(achievement => achievement.trim()).length > 0 && (
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {experience.achievements
                    .filter(achievement => achievement.trim())
                    .map((achievement, index) => (
                      <li key={index}>{achievement}</li>
                    ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExperienceModule;