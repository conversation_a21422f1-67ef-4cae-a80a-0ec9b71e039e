import React, { useState } from 'react';
import type { EducationContent, Education } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { PencilIcon, SaveIcon, XIcon, PlusIcon, TrashIcon } from 'lucide-react';

interface EducationModuleProps {
  content: EducationContent;
  isEditing: boolean;
  onSave: (content: EducationContent) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const EducationModule: React.FC<EducationModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit
}) => {
  const [editContent, setEditContent] = useState<EducationContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      gpa: '',
      honors: '',
      relevantCourses: []
    };

    setEditContent(prev => ({
      ...prev,
      educations: [...prev.educations, newEducation]
    }));
  };

  const removeEducation = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      educations: prev.educations.filter(edu => edu.id !== id)
    }));
  };

  const updateEducation = (id: string, field: keyof Education, value: any) => {
    setEditContent(prev => ({
      ...prev,
      educations: prev.educations.map(edu =>
        edu.id === id ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const addCourse = (educationId: string) => {
    setEditContent(prev => ({
      ...prev,
      educations: prev.educations.map(edu =>
        edu.id === educationId
          ? { ...edu, relevantCourses: [...(edu.relevantCourses || []), ''] }
          : edu
      )
    }));
  };

  const updateCourse = (educationId: string, courseIndex: number, value: string) => {
    setEditContent(prev => ({
      ...prev,
      educations: prev.educations.map(edu =>
        edu.id === educationId
          ? {
              ...edu,
              relevantCourses: edu.relevantCourses?.map((course, index) =>
                index === courseIndex ? value : course
              ) || []
            }
          : edu
      )
    }));
  };

  const removeCourse = (educationId: string, courseIndex: number) => {
    setEditContent(prev => ({
      ...prev,
      educations: prev.educations.map(edu =>
        edu.id === educationId
          ? {
              ...edu,
              relevantCourses: edu.relevantCourses?.filter((_, index) => index !== courseIndex) || []
            }
          : edu
      )
    }));
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit Education</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {editContent.educations.map((education, index) => (
            <div key={education.id} className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Education {index + 1}</h4>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => removeEducation(education.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Institution</label>
                  <Input
                    value={education.institution}
                    onChange={(e) => updateEducation(education.id, 'institution', e.target.value)}
                    placeholder="University or School name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Degree</label>
                  <Input
                    value={education.degree}
                    onChange={(e) => updateEducation(education.id, 'degree', e.target.value)}
                    placeholder="e.g., Bachelor's, Master's, PhD"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Field of Study</label>
                  <Input
                    value={education.field}
                    onChange={(e) => updateEducation(education.id, 'field', e.target.value)}
                    placeholder="e.g., Computer Science, Business Administration"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <Input
                    type="month"
                    value={education.startDate}
                    onChange={(e) => updateEducation(education.id, 'startDate', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <Input
                    type="month"
                    value={education.endDate}
                    onChange={(e) => updateEducation(education.id, 'endDate', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">GPA (Optional)</label>
                  <Input
                    value={education.gpa || ''}
                    onChange={(e) => updateEducation(education.id, 'gpa', e.target.value)}
                    placeholder="e.g., 3.8/4.0"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Honors/Awards (Optional)</label>
                <Input
                  value={education.honors || ''}
                  onChange={(e) => updateEducation(education.id, 'honors', e.target.value)}
                  placeholder="e.g., Magna Cum Laude, Dean's List"
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">Relevant Courses (Optional)</label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addCourse(education.id)}
                    className="flex items-center space-x-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Course</span>
                  </Button>
                </div>

                {education.relevantCourses?.map((course, courseIndex) => (
                  <div key={courseIndex} className="flex items-center space-x-2">
                    <Input
                      value={course}
                      onChange={(e) => updateCourse(education.id, courseIndex, e.target.value)}
                      placeholder="Course name..."
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeCourse(education.id, courseIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <Button onClick={addEducation} className="w-full flex items-center justify-center space-x-2">
            <PlusIcon className="h-4 w-4" />
            <span>Add Education</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900">Education</h3>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      {content.educations.length === 0 ? (
        <p className="text-gray-500 italic">No education information added yet.</p>
      ) : (
        <div className="space-y-6">
          {content.educations.map((education) => (
            <div key={education.id} className="border-l-4 border-green-500 pl-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold text-lg text-gray-900">{education.degree} in {education.field}</h4>
                  <p className="text-green-600 font-medium">{education.institution}</p>
                  {education.honors && (
                    <p className="text-sm text-gray-600 italic">{education.honors}</p>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  {education.startDate} - {education.endDate}
                </div>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                {education.gpa && (
                  <span className="flex items-center">
                    <strong>GPA:</strong>&nbsp;{education.gpa}
                  </span>
                )}
              </div>

              {education.relevantCourses && education.relevantCourses.filter(course => course.trim()).length > 0 && (
                <div className="mt-3">
                  <h5 className="font-medium text-gray-900 mb-2">Relevant Courses:</h5>
                  <div className="flex flex-wrap gap-2">
                    {education.relevantCourses
                      .filter(course => course.trim())
                      .map((course, index) => (
                        <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                          {course}
                        </span>
                      ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EducationModule;