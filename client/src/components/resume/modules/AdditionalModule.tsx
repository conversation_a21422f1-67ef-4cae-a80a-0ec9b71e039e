import React, { useState } from 'react';
import type { AdditionalContent, Award, Language, Publication, VolunteerWork } from '../../../types/resumeModules';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import { PencilIcon, SaveIcon, XIcon, PlusIcon, TrashIcon, AwardIcon, GlobeIcon, BookIcon, HeartIcon, ShieldCheckIcon } from 'lucide-react';

interface AdditionalModuleProps {
  content: AdditionalContent;
  isEditing: boolean;
  onSave: (content: AdditionalContent) => void;
  onCancel: () => void;
  onEdit: () => void;
  sectionType: 'awards' | 'languages' | 'publications' | 'volunteer' | 'certifications';
}

const AdditionalModule: React.FC<AdditionalModuleProps> = ({
  content,
  isEditing,
  onSave,
  onCancel,
  onEdit,
  sectionType
}) => {
  const [editContent, setEditContent] = useState<AdditionalContent>(content);

  const handleSave = () => {
    onSave(editContent);
  };

  const handleCancel = () => {
    setEditContent(content);
    onCancel();
  };

  // Awards functions
  const addAward = () => {
    const newAward: Award = {
      id: Date.now().toString(),
      title: '',
      issuer: '',
      date: '',
      description: ''
    };

    setEditContent(prev => ({
      ...prev,
      awards: [...(prev.awards || []), newAward]
    }));
  };

  const removeAward = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      awards: prev.awards?.filter(award => award.id !== id) || []
    }));
  };

  const updateAward = (id: string, field: keyof Award, value: string) => {
    setEditContent(prev => ({
      ...prev,
      awards: prev.awards?.map(award =>
        award.id === id ? { ...award, [field]: value } : award
      ) || []
    }));
  };

  // Languages functions
  const addLanguage = () => {
    const newLanguage: Language = {
      id: Date.now().toString(),
      name: '',
      proficiency: 'conversational'
    };

    setEditContent(prev => ({
      ...prev,
      languages: [...(prev.languages || []), newLanguage]
    }));
  };

  const removeLanguage = (id: string) => {
    setEditContent(prev => ({
      ...prev,
      languages: prev.languages?.filter(lang => lang.id !== id) || []
    }));
  };

  const updateLanguage = (id: string, field: keyof Language, value: any) => {
    setEditContent(prev => ({
      ...prev,
      languages: prev.languages?.map(lang =>
        lang.id === id ? { ...lang, [field]: value } : lang
      ) || []
    }));
  };

  // Publications functions - temporarily simplified
  const addPublication = () => {
    const newPublication: Publication = {
      id: Date.now().toString(),
      title: '',
      publisher: '',
      date: '',
      url: '',
      description: ''
    };

    setEditContent(prev => ({
      ...prev,
      publications: [...(prev.publications || []), newPublication]
    }));
  };

  // Volunteer work functions - temporarily simplified
  const addVolunteerWork = () => {
    const newVolunteerWork: VolunteerWork = {
      id: Date.now().toString(),
      organization: '',
      role: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
      achievements: ['']
    };

    setEditContent(prev => ({
      ...prev,
      volunteerWork: [...(prev.volunteerWork || []), newVolunteerWork]
    }));
  };

  // Certifications functions - temporarily simplified
  const addCertification = () => {
    const newCertification = {
      id: Date.now().toString(),
      name: '',
      issuer: '',
      date: '',
      expiryDate: '',
      url: ''
    };

    setEditContent(prev => ({
      ...prev,
      certifications: [...(prev.certifications || []), newCertification]
    }));
  };

  const getSectionConfig = () => {
    switch (sectionType) {
      case 'awards':
        return {
          title: 'Awards & Honors',
          icon: AwardIcon,
          color: 'yellow',
          data: editContent.awards || [],
          addFunction: addAward
        };
      case 'languages':
        return {
          title: 'Languages',
          icon: GlobeIcon,
          color: 'blue',
          data: editContent.languages || [],
          addFunction: addLanguage
        };
      case 'publications':
        return {
          title: 'Publications',
          icon: BookIcon,
          color: 'indigo',
          data: editContent.publications || [],
          addFunction: addPublication
        };
      case 'volunteer':
        return {
          title: 'Volunteer Work',
          icon: HeartIcon,
          color: 'pink',
          data: editContent.volunteerWork || [],
          addFunction: addVolunteerWork
        };
      case 'certifications':
        return {
          title: 'Certifications',
          icon: ShieldCheckIcon,
          color: 'green',
          data: editContent.certifications || [],
          addFunction: addCertification
        };
    }
  };

  const config = getSectionConfig();

  const renderEditingForm = () => {
    switch (sectionType) {
      case 'awards':
        return (
          <div className="space-y-4">
            {(editContent.awards || []).map((award, index) => (
              <div key={award.id} className="p-4 border rounded-lg bg-white">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium text-gray-900">Award {index + 1}</h4>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => removeAward(award.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Award Title</label>
                    <Input
                      value={award.title}
                      onChange={(e) => updateAward(award.id, 'title', e.target.value)}
                      placeholder="Award name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issuer</label>
                    <Input
                      value={award.issuer}
                      onChange={(e) => updateAward(award.id, 'issuer', e.target.value)}
                      placeholder="Organization or institution"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <Input
                      type="month"
                      value={award.date}
                      onChange={(e) => updateAward(award.id, 'date', e.target.value)}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea
                      value={award.description || ''}
                      onChange={(e) => updateAward(award.id, 'description', e.target.value)}
                      placeholder="Brief description..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'languages':
        return (
          <div className="space-y-4">
            {(editContent.languages || []).map((language, index) => (
              <div key={language.id} className="p-4 border rounded-lg bg-white">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium text-gray-900">Language {index + 1}</h4>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => removeLanguage(language.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Language</label>
                    <Input
                      value={language.name}
                      onChange={(e) => updateLanguage(language.id, 'name', e.target.value)}
                      placeholder="e.g., Spanish, French, Mandarin"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Proficiency</label>
                    <select
                      value={language.proficiency}
                      onChange={(e) => updateLanguage(language.id, 'proficiency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="basic">Basic</option>
                      <option value="conversational">Conversational</option>
                      <option value="fluent">Fluent</option>
                      <option value="native">Native</option>
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

      default:
        return <div>Section editing coming soon...</div>;
    }
  };

  const renderDisplayContent = () => {
    switch (sectionType) {
      case 'awards':
        return (
          <div className="space-y-4">
            {(content.awards || []).map((award) => (
              <div key={award.id} className="border-l-4 border-yellow-500 pl-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-gray-900">{award.title}</h4>
                    <p className="text-yellow-600 font-medium">{award.issuer}</p>
                    {award.description && (
                      <p className="text-gray-700 mt-1">{award.description}</p>
                    )}
                  </div>
                  <span className="text-sm text-gray-500">{award.date}</span>
                </div>
              </div>
            ))}
          </div>
        );

      case 'languages':
        return (
          <div className="flex flex-wrap gap-3">
            {(content.languages || []).map((language) => (
              <div key={language.id} className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {language.name}
                </span>
                <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                  {language.proficiency}
                </span>
              </div>
            ))}
          </div>
        );

      default:
        return <div>Display content coming soon...</div>;
    }
  };

  if (isEditing) {
    return (
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg">Edit {config.title}</h3>
          <div className="flex space-x-2">
            <Button size="sm" onClick={handleSave} className="flex items-center space-x-1">
              <SaveIcon className="h-4 w-4" />
              <span>Save</span>
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel} className="flex items-center space-x-1">
              <XIcon className="h-4 w-4" />
              <span>Cancel</span>
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {renderEditingForm()}
          
          <Button onClick={config.addFunction} className="w-full flex items-center justify-center space-x-2">
            <PlusIcon className="h-4 w-4" />
            <span>Add {sectionType === 'volunteer' ? 'Volunteer Experience' : sectionType.slice(0, -1)}</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <config.icon className="h-5 w-5 text-gray-600" />
          <h3 className="text-xl font-semibold text-gray-900">{config.title}</h3>
        </div>
        <Button size="sm" variant="outline" onClick={onEdit} className="flex items-center space-x-1">
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </div>

      {config.data.length === 0 ? (
        <p className="text-gray-500 italic">No {config.title.toLowerCase()} added yet.</p>
      ) : (
        renderDisplayContent()
      )}
    </div>
  );
};

export default AdditionalModule;