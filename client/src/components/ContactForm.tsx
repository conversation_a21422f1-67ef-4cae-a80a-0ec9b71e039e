import React, { useState } from 'react';
import { z } from 'zod';
import Input from './ui/Input';
import Button from './ui/Button';
import toast from 'react-hot-toast';
import { trackContactFormSubmitted, trackEvent } from '../utils/analytics';

// Validation schema using Zod
const contactSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters long')
    .max(100, 'Name must be less than 100 characters')
    .trim(),
  email: z.string()
    .email('Please enter a valid email address')
    .toLowerCase()
    .trim(),
  subject: z.string()
    .min(5, 'Subject must be at least 5 characters long')
    .max(200, 'Subject must be less than 200 characters')
    .trim(),
  message: z.string()
    .min(10, 'Message must be at least 10 characters long')
    .max(2000, 'Message must be less than 2000 characters')
    .trim(),
});

type ContactFormData = z.infer<typeof contactSchema>;

interface ContactFormProps {
  className?: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ className = '' }) => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const [errors, setErrors] = useState<Partial<Record<keyof ContactFormData, string>>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateField = (field: keyof ContactFormData, value: string) => {
    try {
      const fieldSchema = contactSchema.shape[field];
      fieldSchema.parse(value);
      setErrors(prev => ({ ...prev, [field]: undefined }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ ...prev, [field]: error.errors[0]?.message }));
      }
    }
  };

  const handleInputChange = (field: keyof ContactFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear submitted state when user starts typing again
    if (isSubmitted) {
      setIsSubmitted(false);
    }
    
    // Real-time validation on blur or after user stops typing
    if (value.trim() !== '') {
      validateField(field, value);
    } else {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleBlur = (field: keyof ContactFormData) => (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = e.target.value;
    if (value.trim() !== '') {
      validateField(field, value);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate entire form
      const validatedData = contactSchema.parse(formData);
      
      // Clear any existing errors
      setErrors({});

      // Submit form data
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send message');
      }

      // Success
      setIsSubmitted(true);
      setFormData({ name: '', email: '', subject: '', message: '' });
      toast.success('Message sent successfully! We\'ll get back to you soon.');
      
      // Track successful form submission
      trackContactFormSubmitted(true);
      trackEvent({
        action: 'form_completed',
        category: 'engagement',
        label: 'contact_form',
        custom_parameters: {
          form_type: 'contact',
          subject_category: validatedData.subject.substring(0, 20), // First 20 chars of subject
        },
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        // Handle validation errors
        const validationErrors: Partial<Record<keyof ContactFormData, string>> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            validationErrors[err.path[0] as keyof ContactFormData] = err.message;
          }
        });
        setErrors(validationErrors);
        toast.error('Please fix the errors in the form');
      } else {
        // Handle API errors
        console.error('Contact form submission error:', error);
        toast.error(error instanceof Error ? error.message : 'Failed to send message. Please try again.');
        
        // Track failed form submission
        trackContactFormSubmitted(false);
        trackEvent({
          action: 'form_error',
          category: 'engagement',
          label: 'contact_form',
          custom_parameters: {
            error_type: error instanceof Error ? error.name : 'unknown',
            error_message: error instanceof Error ? error.message.substring(0, 100) : 'unknown_error',
          },
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <span className="text-2xl">✅</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Message Sent Successfully!
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Thank you for reaching out. We'll get back to you within 24 hours.
          </p>
          <Button
            variant="outline"
            onClick={() => setIsSubmitted(false)}
          >
            Send Another Message
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Get in Touch
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Have a question or want to work together? We'd love to hear from you.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Full Name"
            type="text"
            value={formData.name}
            onChange={handleInputChange('name')}
            onBlur={handleBlur('name')}
            error={errors.name}
            placeholder="Your full name"
            leftIcon="👤"
            inputSize="lg"
            required
          />

          <Input
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            onBlur={handleBlur('email')}
            error={errors.email}
            placeholder="<EMAIL>"
            leftIcon="✉️"
            inputSize="lg"
            required
          />
        </div>

        <Input
          label="Subject"
          type="text"
          value={formData.subject}
          onChange={handleInputChange('subject')}
          onBlur={handleBlur('subject')}
          error={errors.subject}
          placeholder="What's this about?"
          leftIcon="📝"
          inputSize="lg"
          required
        />

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Message
          </label>
          <div className="relative">
            <textarea
              value={formData.message}
              onChange={handleInputChange('message')}
              onBlur={handleBlur('message')}
              placeholder="Tell us more about your project or question..."
              rows={6}
              className={`
                w-full px-5 py-4 text-base rounded-xl transition-all duration-200 border 
                focus:outline-none focus:ring-2 focus:ring-primary-500
                border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 
                focus:border-primary-500 text-gray-900 dark:text-gray-100
                placeholder:text-gray-500 dark:placeholder:text-gray-400
                disabled:opacity-50 disabled:cursor-not-allowed
                disabled:bg-gray-50 dark:disabled:bg-gray-800
                ${errors.message ? 'border-error-500 focus:border-error-500 focus:ring-error-500' : ''}
              `}
              required
            />
          </div>
          {errors.message && (
            <p className="text-sm text-error-600 dark:text-error-400 flex items-center">
              <span className="mr-1">⚠️</span>
              {errors.message}
            </p>
          )}
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {formData.message.length}/2000 characters
          </p>
        </div>

        <Button
          type="submit"
          variant="primary"
          size="lg"
          isLoading={isLoading}
          loadingText="Sending..."
          className="w-full"
          leftIcon={!isLoading ? "🚀" : undefined}
        >
          Send Message
        </Button>
      </form>
    </div>
  );
};

export default ContactForm;