import { useState, useCallback, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { AnimatePresence, motion } from 'framer-motion';
import {
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  UndoIcon,
  RedoIcon,
  SettingsIcon,
  GridIcon,
  Smartphone,
  Tablet,
  Monitor,
  SaveIcon,
  DownloadIcon
} from 'lucide-react';

import type { RootState } from '../../../store';
import {
  addSection,
  removeSection,
  toggleSectionVisibility,
  setPreviewMode,
  undo,
  redo
} from '../../../store/modularResumeSlice';

import type { ModularSection, ModularTemplate } from '../../../types/resumeModules';
import { useDragDrop, useKeyboardNavigation, useTouchDragDrop } from '../../../hooks/useDragDrop';

import Card from '../../ui/Card';
import Button from '../../ui/Button';
import HeaderModule from '../../resume/modules/HeaderModule';
import SummaryModule from '../../resume/modules/SummaryModule';
import ExperienceModule from '../../resume/modules/ExperienceModule';
import SkillsModule from '../../resume/modules/SkillsModule';
import EducationModule from '../../resume/modules/EducationModule';
import ProjectsModule from '../../resume/modules/ProjectsModule';
import AdditionalModule from '../../resume/modules/AdditionalModule';
import RealTimePreview from '../../resume/RealTimePreview';

// Enhanced section configurations with more granular control
const ENHANCED_SECTION_CONFIGS = {
  header: {
    icon: '👤',
    name: 'Header',
    color: 'blue',
    isRequired: true,
    defaultVisible: true,
    category: 'basic'
  },
  summary: {
    icon: '📝',
    name: 'Professional Summary',
    color: 'green',
    isRequired: false,
    defaultVisible: true,
    category: 'basic'
  },
  experience: {
    icon: '💼',
    name: 'Work Experience',
    color: 'purple',
    isRequired: true,
    defaultVisible: true,
    category: 'basic'
  },
  education: {
    icon: '🎓',
    name: 'Education',
    color: 'yellow',
    isRequired: false,
    defaultVisible: true,
    category: 'basic'
  },
  skills: {
    icon: '⚡',
    name: 'Skills',
    color: 'red',
    isRequired: false,
    defaultVisible: true,
    category: 'basic'
  },
  projects: {
    icon: '🚀',
    name: 'Projects',
    color: 'indigo',
    isRequired: false,
    defaultVisible: true,
    category: 'showcase'
  },
  awards: {
    icon: '🏆',
    name: 'Awards & Achievements',
    color: 'orange',
    isRequired: false,
    defaultVisible: false,
    category: 'additional'
  },
  languages: {
    icon: '🌍',
    name: 'Languages',
    color: 'cyan',
    isRequired: false,
    defaultVisible: false,
    category: 'additional'
  },
  publications: {
    icon: '📚',
    name: 'Publications',
    color: 'pink',
    isRequired: false,
    defaultVisible: false,
    category: 'additional'
  },
  volunteer: {
    icon: '🤝',
    name: 'Volunteer Work',
    color: 'emerald',
    isRequired: false,
    defaultVisible: false,
    category: 'additional'
  },
  certifications: {
    icon: '📜',
    name: 'Certifications',
    color: 'violet',
    isRequired: false,
    defaultVisible: false,
    category: 'professional'
  },
  custom: {
    icon: '✨',
    name: 'Custom Section',
    color: 'gray',
    isRequired: false,
    defaultVisible: true,
    category: 'custom'
  }
} as const;

interface EnhancedModularTemplateBuilderProps {
  template?: ModularTemplate;
  className?: string;
  onSave?: (template: ModularTemplate) => void;
  onExport?: (format: 'pdf' | 'docx' | 'json') => void;
}

export default function EnhancedModularTemplateBuilder({
  template,
  className = '',
  onSave,
  onExport
}: EnhancedModularTemplateBuilderProps) {
  const dispatch = useDispatch();
  
  const { currentTemplate, isPreviewMode, undoStack, redoStack } = useSelector(
    (state: RootState) => state.modularResume
  );

  const activeTemplate = currentTemplate || template;
  const sections = activeTemplate?.sections || [];

  // Enhanced state management
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [showPreview, setShowPreview] = useState(false);
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);
  const [dragSettings, setDragSettings] = useState({
    snapToGrid: true,
    showGridLines: false,
    autoSpacing: true
  });
  const [customSectionDialog, setCustomSectionDialog] = useState(false);

  // Enhanced drag-and-drop with our custom hook
  const { state: dragState, handlers: dragHandlers, announcerRef } = useDragDrop({
    sections,
    enableSnapToGrid: dragSettings.snapToGrid,
    enableAnnouncements: true
  });

  const { handleKeyDown } = useKeyboardNavigation(sections, (fromIndex, toIndex) => {
    dragHandlers.onDragEnd({
      draggableId: sections[fromIndex].id,
      type: 'section',
      source: { index: fromIndex, droppableId: 'sections' },
      destination: { index: toIndex, droppableId: 'sections' },
      reason: 'DROP'
    });
  });

  const { touchHandlers } = useTouchDragDrop();

  // Section grouping by category for better organization
  const sectionsByCategory = useMemo(() => {
    const grouped = Object.entries(ENHANCED_SECTION_CONFIGS).reduce((acc, [type, config]) => {
      if (!acc[config.category]) {
        acc[config.category] = [];
      }
      acc[config.category].push({ type, config });
      return acc;
    }, {} as Record<string, Array<{ type: string; config: typeof ENHANCED_SECTION_CONFIGS[keyof typeof ENHANCED_SECTION_CONFIGS] }>>);
    
    return grouped;
  }, []);

  // Enhanced section addition with better defaults
  const addNewSection = useCallback((type: keyof typeof ENHANCED_SECTION_CONFIGS, customTitle?: string) => {
    const config = ENHANCED_SECTION_CONFIGS[type];
    const newSection: ModularSection = {
      id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      title: customTitle || config.name,
      content: getDefaultContent(type),
      isRequired: config.isRequired,
      isVisible: config.defaultVisible,
      order: sections.length,
      styling: getDefaultStyling(type)
    };

    dispatch(addSection(newSection));
    setSelectedSectionId(newSection.id);
  }, [sections.length, dispatch]);

  // Enhanced section rendering with better performance
  const renderSectionModule = useCallback((section: ModularSection) => {
    const props = {
      section,
      isEditing: selectedSectionId === section.id,
      onUpdate: (updatedSection: ModularSection) => {
        // Handle section update
      },
      onEdit: () => setSelectedSectionId(section.id),
      onCancel: () => setSelectedSectionId(null)
    };

    switch (section.type) {
      case 'header':
        return <HeaderModule {...props} />;
      case 'summary':
        return <SummaryModule {...props} />;
      case 'experience':
        return <ExperienceModule {...props} />;
      case 'skills':
        return <SkillsModule {...props} />;
      case 'education':
        return <EducationModule {...props} />;
      case 'projects':
        return <ProjectsModule {...props} />;
      default:
        return <AdditionalModule {...props} />;
    }
  }, [selectedSectionId]);

  // Enhanced section card with better visual feedback
  const renderSectionCard = useCallback((section: ModularSection, index: number, isDragging: boolean) => {
    const config = ENHANCED_SECTION_CONFIGS[section.type];
    
    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.2 }}
        className={`
          relative group rounded-xl border-2 transition-all duration-200
          ${isDragging 
            ? 'border-blue-400 shadow-lg bg-white z-10 scale-105' 
            : 'border-gray-200 hover:border-gray-300 bg-white'
          }
          ${!section.isVisible ? 'opacity-60 bg-gray-50' : ''}
          ${dragSettings.snapToGrid ? 'snap-to-grid' : ''}
        `}
        style={{
          background: isDragging 
            ? `linear-gradient(135deg, ${config.color === 'blue' ? '#eff6ff' : '#f9fafb'} 0%, white 100%)`
            : undefined
        }}
        {...touchHandlers}
      >
        {/* Enhanced drag handle with better accessibility */}
        <div 
          className="absolute left-2 top-1/2 transform -translate-y-1/2 p-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
          role="button"
          tabIndex={0}
          aria-label={`Drag to reorder ${section.title} section`}
          onKeyDown={(e) => handleKeyDown(e, index)}
        >
          <div className="flex flex-col space-y-1">
            {[1, 2, 3].map(i => (
              <div key={i} className="w-1 h-1 bg-gray-400 rounded-full"></div>
            ))}
          </div>
        </div>

        {/* Section header with enhanced controls */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <div className="flex items-center space-x-3 ml-6">
            <span className="text-2xl" role="img" aria-label={config.name}>
              {config.icon}
            </span>
            <div>
              <h3 className="font-semibold text-gray-900">{section.title}</h3>
              <p className="text-sm text-gray-500 capitalize">{config.category}</p>
            </div>
            {section.isRequired && (
              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                Required
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Enhanced visibility toggle */}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => dispatch(toggleSectionVisibility(section.id))}
              className={`${section.isVisible ? 'text-green-600' : 'text-gray-400'}`}
              aria-label={section.isVisible ? 'Hide section' : 'Show section'}
            >
              {section.isVisible ? <EyeIcon size={16} /> : <EyeSlashIcon size={16} />}
            </Button>

            {/* Settings button */}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setSelectedSectionId(section.id)}
              aria-label={`Customize ${section.title} section`}
            >
              <SettingsIcon size={16} />
            </Button>

            {/* Enhanced delete button with confirmation */}
            {!section.isRequired && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  if (window.confirm(`Remove ${section.title} section?`)) {
                    dispatch(removeSection(section.id));
                  }
                }}
                className="text-red-600 hover:bg-red-50"
                aria-label={`Remove ${section.title} section`}
              >
                <TrashIcon size={16} />
              </Button>
            )}
          </div>
        </div>

        {/* Section content */}
        <div className="p-4">
          {renderSectionModule(section)}
        </div>

        {/* Enhanced drop indicators */}
        {dragState.dropZone === `index-${index}` && (
          <div className="absolute -top-2 left-0 right-0 h-1 bg-blue-400 rounded-full shadow-lg"></div>
        )}
        {dragState.dropZone === `index-${index + 1}` && (
          <div className="absolute -bottom-2 left-0 right-0 h-1 bg-blue-400 rounded-full shadow-lg"></div>
        )}
      </motion.div>
    );
  }, [
    selectedSectionId,
    dragSettings.snapToGrid,
    dragState.dropZone,
    touchHandlers,
    handleKeyDown,
    dispatch,
    renderSectionModule
  ]);

  if (!activeTemplate) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium mb-2">No Template Selected</h3>
          <p className="text-sm">Select a template to start building your resume</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`enhanced-modular-template-builder ${className}`}>
      {/* Enhanced toolbar with better responsive design */}
      <Card className="mb-6 sticky top-0 z-20 bg-white/80 backdrop-blur-sm">
        <div className="p-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Left section - Template info and mode toggles */}
          <div className="flex items-center space-x-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">{activeTemplate.name}</h2>
              <p className="text-sm text-gray-500">{sections.length} sections</p>
            </div>

            {/* Preview mode toggle */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <Button
                size="sm"
                variant={!isPreviewMode ? 'primary' : 'ghost'}
                onClick={() => dispatch(setPreviewMode(false))}
                className="text-xs"
              >
                Edit
              </Button>
              <Button
                size="sm"
                variant={isPreviewMode ? 'primary' : 'ghost'}
                onClick={() => dispatch(setPreviewMode(true))}
                className="text-xs"
              >
                Preview
              </Button>
            </div>

            {/* Device view toggles */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              {[
                { mode: 'desktop', icon: Monitor, label: 'Desktop' },
                { mode: 'tablet', icon: Tablet, label: 'Tablet' },
                { mode: 'mobile', icon: Smartphone, label: 'Mobile' }
              ].map(({ mode, icon: Icon, label }) => (
                <Button
                  key={mode}
                  size="sm"
                  variant={viewMode === mode ? 'primary' : 'ghost'}
                  onClick={() => setViewMode(mode as any)}
                  className="text-xs p-2"
                  aria-label={`Switch to ${label} view`}
                >
                  <Icon size={14} />
                </Button>
              ))}
            </div>
          </div>

          {/* Right section - Actions */}
          <div className="flex items-center space-x-2">
            {/* Drag settings */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <Button
                size="sm"
                variant={dragSettings.snapToGrid ? 'primary' : 'ghost'}
                onClick={() => setDragSettings(prev => ({ ...prev, snapToGrid: !prev.snapToGrid }))}
                className="text-xs p-2"
                aria-label="Toggle snap to grid"
              >
                <GridIcon size={14} />
              </Button>
            </div>

            {/* Undo/Redo */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => dispatch(undo())}
                disabled={undoStack.length === 0}
                className="text-xs p-2"
                aria-label="Undo last action"
              >
                <UndoIcon size={14} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => dispatch(redo())}
                disabled={redoStack.length === 0}
                className="text-xs p-2"
                aria-label="Redo last action"
              >
                <RedoIcon size={14} />
              </Button>
            </div>

            {/* Save and Export */}
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onSave?.(activeTemplate)}
                className="text-xs"
              >
                <SaveIcon size={14} className="mr-1" />
                Save
              </Button>
              <Button
                size="sm"
                variant="primary"
                onClick={() => onExport?.('pdf')}
                className="text-xs"
              >
                <DownloadIcon size={14} className="mr-1" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main editor area */}
        <div className="lg:col-span-2">
          {!isPreviewMode ? (
            <>
              {/* Enhanced section palette */}
              <Card className="mb-6">
                <div className="p-4 border-b border-gray-100">
                  <h3 className="font-semibold text-gray-900 mb-2">Add Sections</h3>
                  <p className="text-sm text-gray-500">Drag sections to reorder them</p>
                </div>
                
                <div className="p-4 space-y-4">
                  {Object.entries(sectionsByCategory).map(([category, items]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-gray-700 mb-2 capitalize">
                        {category} Sections
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {items.map(({ type, config }) => (
                          <Button
                            key={type}
                            size="sm"
                            variant="outline"
                            onClick={() => addNewSection(type as keyof typeof ENHANCED_SECTION_CONFIGS)}
                            className="text-left p-3 h-auto"
                            disabled={sections.some(s => s.type === type && type !== 'custom')}
                          >
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">{config.icon}</span>
                              <span className="text-sm">{config.name}</span>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Enhanced drag-and-drop sections */}
              <DragDropContext
                onDragStart={dragHandlers.onDragStart}
                onDragUpdate={dragHandlers.onDragUpdate}
                onDragEnd={dragHandlers.onDragEnd}
              >
                <Droppable droppableId="sections">
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`space-y-4 min-h-[200px] relative ${
                        snapshot.isDraggingOver ? 'bg-blue-50/30 rounded-lg' : ''
                      }`}
                    >
                      <AnimatePresence>
                        {sections.map((section, index) => (
                          <Draggable
                            key={section.id}
                            draggableId={section.id}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                {renderSectionCard(section, index, snapshot.isDragging)}
                              </div>
                            )}
                          </Draggable>
                        ))}
                      </AnimatePresence>
                      {provided.placeholder}

                      {sections.length === 0 && (
                        <div className="text-center py-12 text-gray-400 border-2 border-dashed border-gray-200 rounded-lg">
                          <div className="text-4xl mb-4">📝</div>
                          <h3 className="text-lg font-medium mb-2">Start Building Your Resume</h3>
                          <p className="text-sm">Add sections from the palette above</p>
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </>
          ) : (
            <div className="bg-gray-100 rounded-lg p-6">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-4">👁️</div>
                <h3 className="text-lg font-medium mb-2">Preview Mode</h3>
                <p className="text-sm">Switch to Edit mode to modify sections</p>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced sidebar with real-time preview */}
        <div className="lg:col-span-1">
          <div className="sticky top-24">
            <RealTimePreview 
              isVisible={true}
              className="h-[calc(100vh-12rem)] overflow-auto"
            />
          </div>
        </div>
      </div>

      {/* Screen reader announcements */}
      <div
        ref={announcerRef}
        className="sr-only"
        aria-live="assertive"
        aria-atomic="true"
      />
    </div>
  );
}

// Helper functions
function getDefaultContent(type: keyof typeof ENHANCED_SECTION_CONFIGS): any {
  switch (type) {
    case 'header':
      return {
        name: '',
        title: '',
        contact: {
          email: '',
          phone: '',
          location: '',
          website: '',
          linkedin: '',
          github: ''
        }
      };
    case 'summary':
      return { summary: '', objective: '' };
    case 'experience':
      return { experiences: [] };
    case 'education':
      return { education: [] };
    case 'skills':
      return { skills: [] };
    case 'projects':
      return { projects: [] };
    default:
      return {};
  }
}

function getDefaultStyling(type: keyof typeof ENHANCED_SECTION_CONFIGS) {
  const config = ENHANCED_SECTION_CONFIGS[type];
  return {
    accentColor: config.color,
    spacing: 'normal',
    layout: 'default'
  };
}