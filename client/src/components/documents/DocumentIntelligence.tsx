import { useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface DocumentIntelligenceProps {
  onDocumentParsed?: (data: any) => void;
}

export default function DocumentIntelligence({ onDocumentParsed }: DocumentIntelligenceProps) {
  const { token } = useSelector((state: RootState) => state.auth);
  const [activeTab, setActiveTab] = useState<'parse' | 'cover-letter' | 'portfolio' | 'documents'>('parse');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [documents, setDocuments] = useState<any[]>([]);

  // File upload state
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Cover letter state
  const [jobDescription, setJobDescription] = useState('');
  const [companyName, setCompanyName] = useState('');

  // Portfolio state
  const [projects, setProjects] = useState<any[]>([{
    name: '',
    description: '',
    technologies: '',
    url: ''
  }]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleParseResume = async () => {
    if (!selectedFile) return;
    
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('resume', selectedFile);

      const response = await fetch('/api/documents/parse-resume', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
        if (onDocumentParsed) {
          onDocumentParsed(data.data);
        }
      }
    } catch (error) {
      console.error('Resume parsing failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateCoverLetter = async () => {
    if (!jobDescription || !companyName) return;
    
    setLoading(true);
    try {
      // Use parsed resume data or default
      const resumeData = results || {
        personalInfo: { name: 'User' },
        experience: [],
        skills: []
      };

      const response = await fetch('/api/documents/generate-cover-letter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          resumeData,
          jobDescription,
          companyName
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Cover letter generation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePortfolio = async () => {
    setLoading(true);
    try {
      const personalInfo = results?.personalInfo || { name: 'User' };

      const response = await fetch('/api/documents/portfolio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          projects,
          personalInfo,
          theme: 'modern'
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Portfolio creation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents/documents', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setDocuments(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error);
    }
  };

  const addProject = () => {
    setProjects([...projects, {
      name: '',
      description: '',
      technologies: '',
      url: ''
    }]);
  };

  const updateProject = (index: number, field: string, value: string) => {
    const updatedProjects = projects.map((project, i) => 
      i === index ? { ...project, [field]: value } : project
    );
    setProjects(updatedProjects);
  };

  const removeProject = (index: number) => {
    setProjects(projects.filter((_, i) => i !== index));
  };

  const tabs = [
    { id: 'parse', label: 'Parse Resume', icon: '📄' },
    { id: 'cover-letter', label: 'Cover Letter', icon: '✉️' },
    { id: 'portfolio', label: 'Portfolio', icon: '💼' },
    { id: 'documents', label: 'My Documents', icon: '📁' }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Document Intelligence Suite</h2>
          <p className="text-gray-600 mt-1">Parse resumes, generate cover letters, and manage your documents</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id as any);
                  setResults(null);
                  if (tab.id === 'documents') {
                    fetchDocuments();
                  }
                }}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Parse Resume Tab */}
          {activeTab === 'parse' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Upload and Parse Resume</h3>
              
              <div
                className={`relative border-2 border-dashed rounded-lg p-8 text-center ${
                  dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <div className="space-y-4">
                  <div className="text-6xl">📄</div>
                  <div>
                    <p className="text-lg font-medium text-gray-900">
                      Drop your resume here or click to browse
                    </p>
                    <p className="text-gray-600">
                      Supports PDF, DOC, DOCX, and TXT files up to 10MB
                    </p>
                  </div>
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={handleFileSelect}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              </div>

              {selectedFile && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="font-medium">Selected file:</p>
                  <p className="text-gray-600">{selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)</p>
                  <button
                    onClick={handleParseResume}
                    disabled={loading}
                    className="mt-3 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Parsing...' : 'Parse Resume'}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Cover Letter Tab */}
          {activeTab === 'cover-letter' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Generate Cover Letter</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Google"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Job Description
                </label>
                <textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 h-32"
                  placeholder="Paste the job description here..."
                />
              </div>

              <button
                onClick={handleGenerateCoverLetter}
                disabled={loading || !jobDescription || !companyName}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Generating...' : 'Generate Cover Letter'}
              </button>
            </div>
          )}

          {/* Portfolio Tab */}
          {activeTab === 'portfolio' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Create Portfolio</h3>
              
              <div className="space-y-4">
                {projects.map((project, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium">Project {index + 1}</h4>
                      {projects.length > 1 && (
                        <button
                          onClick={() => removeProject(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <input
                        type="text"
                        placeholder="Project Name"
                        value={project.name}
                        onChange={(e) => updateProject(index, 'name', e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2"
                      />
                      <input
                        type="url"
                        placeholder="Project URL"
                        value={project.url}
                        onChange={(e) => updateProject(index, 'url', e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                    
                    <textarea
                      placeholder="Project Description"
                      value={project.description}
                      onChange={(e) => updateProject(index, 'description', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 mt-4 h-20"
                    />
                    
                    <input
                      type="text"
                      placeholder="Technologies (comma-separated)"
                      value={project.technologies}
                      onChange={(e) => updateProject(index, 'technologies', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 mt-4"
                    />
                  </div>
                ))}
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={addProject}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                >
                  Add Project
                </button>
                
                <button
                  onClick={handleCreatePortfolio}
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Creating...' : 'Create Portfolio'}
                </button>
              </div>
            </div>
          )}

          {/* My Documents Tab */}
          {activeTab === 'documents' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">My Documents</h3>
              
              {documents.length === 0 ? (
                <p className="text-gray-600">No documents found. Start by parsing a resume or generating a cover letter.</p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {documents.map((doc, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{doc.original_filename}</h4>
                        <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                          {doc.document_type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Created: {new Date(doc.created_at).toLocaleDateString()}
                      </p>
                      {doc.confidence_score && (
                        <p className="text-sm text-gray-600">
                          Confidence: {(doc.confidence_score * 100).toFixed(0)}%
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Results Display */}
          {results && (
            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-semibold mb-4">Results</h4>
              <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96 bg-white p-4 rounded border">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}