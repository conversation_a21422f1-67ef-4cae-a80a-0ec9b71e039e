import { useState } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';

interface CareerCoachProps {
  onClose?: () => void;
}

interface CareerGoals {
  targetRole: string;
  targetIndustry: string;
  timeline: string;
  salaryRange: string;
}

export default function CareerCoach({ onClose }: CareerCoachProps) {
  const { user, token } = useSelector((state: RootState) => state.auth);
  const [activeTab, setActiveTab] = useState<'analysis' | 'skills' | 'interview' | 'salary' | 'linkedin'>('analysis');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  // Career Path Analysis
  const [careerGoals, setCareerGoals] = useState<CareerGoals>({
    targetRole: '',
    targetIndustry: '',
    timeline: '',
    salaryRange: ''
  });

  // Skill Gap Analysis
  const [currentSkills, setCurrentSkills] = useState<string[]>([]);
  const [targetRole, setTargetRole] = useState('');

  // Interview Preparation
  const [jobDescription, setJobDescription] = useState('');
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  const handleCareerAnalysis = async () => {
    setLoading(true);
    try {
      // Get user's resume data (simplified for demo)
      const resumeData = {
        name: user?.name || 'User',
        title: 'Current Role',
        experience: [],
        skills: [],
        education: []
      };

      const response = await fetch('/api/career-coach/analyze-career-path', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          resumeData,
          careerGoals
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Career analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSkillGapAnalysis = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/career-coach/skill-gap-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          currentSkills,
          targetRole,
          industry: '' // Default empty industry for now
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Skill gap analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInterviewPrep = async () => {
    setLoading(true);
    try {
      const resumeData = {
        name: user?.name || 'User',
        title: 'Current Role',
        experience: [],
        skills: []
      };

      const response = await fetch('/api/career-coach/interview-prep', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          jobDescription,
          resumeData,
          difficulty
        })
      });

      const data = await response.json();
      if (data.success) {
        setResults(data.data);
      }
    } catch (error) {
      console.error('Interview prep failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'analysis', label: 'Career Analysis', icon: '📊' },
    { id: 'skills', label: 'Skill Gaps', icon: '🎯' },
    { id: 'interview', label: 'Interview Prep', icon: '💼' }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">AI Career Coach</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id as any);
                  setResults(null);
                }}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Career Path Analysis */}
          {activeTab === 'analysis' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Career Path Analysis</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Role
                  </label>
                  <input
                    type="text"
                    value={careerGoals.targetRole}
                    onChange={(e) => setCareerGoals({...careerGoals, targetRole: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Senior Software Engineer"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Industry
                  </label>
                  <input
                    type="text"
                    value={careerGoals.targetIndustry}
                    onChange={(e) => setCareerGoals({...careerGoals, targetIndustry: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Technology"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timeline
                  </label>
                  <select
                    value={careerGoals.timeline}
                    onChange={(e) => setCareerGoals({...careerGoals, timeline: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Select timeline</option>
                    <option value="6 months">6 months</option>
                    <option value="1 year">1 year</option>
                    <option value="2 years">2 years</option>
                    <option value="5 years">5 years</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Salary Range
                  </label>
                  <input
                    type="text"
                    value={careerGoals.salaryRange}
                    onChange={(e) => setCareerGoals({...careerGoals, salaryRange: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., $80k - $120k"
                  />
                </div>
              </div>
              <button
                onClick={handleCareerAnalysis}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Analyzing...' : 'Analyze Career Path'}
              </button>
            </div>
          )}

          {/* Skill Gap Analysis */}
          {activeTab === 'skills' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Skill Gap Analysis</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Skills (comma-separated)
                  </label>
                  <textarea
                    value={currentSkills.join(', ')}
                    onChange={(e) => setCurrentSkills(e.target.value.split(', ').filter(s => s.trim()))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 h-24"
                    placeholder="JavaScript, React, Node.js"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Role
                  </label>
                  <input
                    type="text"
                    value={targetRole}
                    onChange={(e) => setTargetRole(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Full Stack Developer"
                  />
                </div>
              </div>
              <button
                onClick={handleSkillGapAnalysis}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Analyzing...' : 'Analyze Skill Gaps'}
              </button>
            </div>
          )}

          {/* Interview Preparation */}
          {activeTab === 'interview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Interview Preparation</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Job Description
                </label>
                <textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 h-32"
                  placeholder="Paste the job description here..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Difficulty Level
                </label>
                <select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value as any)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
              </div>
              <button
                onClick={handleInterviewPrep}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Generating...' : 'Generate Interview Questions'}
              </button>
            </div>
          )}

          {/* Results Display */}
          {results && (
            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-semibold mb-4">Results</h4>
              <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}