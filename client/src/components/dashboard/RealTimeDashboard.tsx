import React, { useState, useEffect, useCallback } from 'react';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Container, 
  Cpu, 
  HardDrive, 
  Memory, 
  Network, 
  Shield,
  TrendingUp,
  Zap
} from 'lucide-react';

interface MetricData {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'healthy' | 'warning' | 'critical';
}

interface AlertData {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

interface DashboardData {
  timestamp: number;
  system: {
    cpu: { value: number; lastUpdated: number };
    memory: { value: number; lastUpdated: number };
    disk: { value: number; lastUpdated: number };
    uptime: number;
  };
  application: {
    activeJobs: { value: number };
    completedJobs: { value: number };
    failedJobs: { value: number };
    activeContainers: { value: number };
    activeSandboxes: { value: number };
  };
  alerts: {
    active: AlertData[];
    total: number;
  };
  anomalies: {
    recent: any[];
    total: number;
  };
  recommendations: any[];
}

export const RealTimeDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');

  // WebSocket connection for real-time updates
  useEffect(() => {
    const ws = new WebSocket(`ws://${window.location.host}/ws/dashboard`);
    
    ws.onopen = () => {
      setIsConnected(true);
      console.log('📡 Dashboard WebSocket connected');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        setDashboardData(data);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error parsing dashboard data:', error);
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
      console.log('📡 Dashboard WebSocket disconnected');
    };

    ws.onerror = (error) => {
      console.error('Dashboard WebSocket error:', error);
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, []);

  // Fetch initial data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/monitoring/dashboard');
        const data = await response.json();
        setDashboardData(data);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    };

    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // Fallback every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const acknowledgeAlert = useCallback(async (alertId: string) => {
    try {
      await fetch(`/api/monitoring/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      });
      
      // Update local state
      setDashboardData(prev => {
        if (!prev) return prev;
        
        return {
          ...prev,
          alerts: {
            ...prev.alerts,
            active: prev.alerts.active.map(alert => 
              alert.id === alertId ? { ...alert, acknowledged: true } : alert
            )
          }
        };
      });
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  }, []);

  const getMetricStatus = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'critical';
    if (value >= thresholds.warning) return 'warning';
    return 'healthy';
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (!dashboardData) {
    return (
      <div className=\"flex items-center justify-center h-64\">
        <div className=\"text-center\">
          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>
          <p className=\"mt-4 text-gray-600\">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className=\"space-y-6\">
      {/* Header */}
      <div className=\"flex items-center justify-between\">
        <div>
          <h1 className=\"text-2xl font-bold text-gray-900\">Real-Time Dashboard</h1>
          <p className=\"text-gray-600\">
            System monitoring and job execution status
          </p>
        </div>
        <div className=\"flex items-center space-x-4\">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${ 
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          {lastUpdate && (
            <p className=\"text-sm text-gray-500\">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </p>
          )}
        </div>
      </div>

      {/* System Metrics */}
      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">
        {/* CPU Usage */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <div className=\"flex items-center justify-between\">
            <div>
              <p className=\"text-sm font-medium text-gray-600\">CPU Usage</p>
              <p className=\"text-2xl font-bold text-gray-900\">
                {dashboardData.system.cpu.value?.toFixed(1) || 0}%
              </p>
            </div>
            <div className={`p-3 rounded-full ${
              getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'critical' 
                ? 'bg-red-100' 
                : getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'warning' 
                ? 'bg-yellow-100' 
                : 'bg-green-100'
            }`}>
              <Cpu className={`h-6 w-6 ${
                getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'critical' 
                  ? 'text-red-600' 
                  : getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'warning' 
                  ? 'text-yellow-600' 
                  : 'text-green-600'
              }`} />
            </div>
          </div>
          <div className=\"mt-4\">
            <div className=\"w-full bg-gray-200 rounded-full h-2\">
              <div 
                className={`h-2 rounded-full ${
                  getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'critical' 
                    ? 'bg-red-600' 
                    : getMetricStatus(dashboardData.system.cpu.value || 0, { warning: 70, critical: 85 }) === 'warning' 
                    ? 'bg-yellow-600' 
                    : 'bg-green-600'
                }`}
                style={{ width: `${Math.min(dashboardData.system.cpu.value || 0, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Memory Usage */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <div className=\"flex items-center justify-between\">
            <div>
              <p className=\"text-sm font-medium text-gray-600\">Memory Usage</p>
              <p className=\"text-2xl font-bold text-gray-900\">
                {dashboardData.system.memory.value?.toFixed(1) || 0}%
              </p>
            </div>
            <div className={`p-3 rounded-full ${
              getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'critical' 
                ? 'bg-red-100' 
                : getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'warning' 
                ? 'bg-yellow-100' 
                : 'bg-green-100'
            }`}>
              <Memory className={`h-6 w-6 ${
                getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'critical' 
                  ? 'text-red-600' 
                  : getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'warning' 
                  ? 'text-yellow-600' 
                  : 'text-green-600'
              }`} />
            </div>
          </div>
          <div className=\"mt-4\">
            <div className=\"w-full bg-gray-200 rounded-full h-2\">
              <div 
                className={`h-2 rounded-full ${
                  getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'critical' 
                    ? 'bg-red-600' 
                    : getMetricStatus(dashboardData.system.memory.value || 0, { warning: 75, critical: 90 }) === 'warning' 
                    ? 'bg-yellow-600' 
                    : 'bg-green-600'
                }`}
                style={{ width: `${Math.min(dashboardData.system.memory.value || 0, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Active Containers */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <div className=\"flex items-center justify-between\">
            <div>
              <p className=\"text-sm font-medium text-gray-600\">Active Containers</p>
              <p className=\"text-2xl font-bold text-gray-900\">
                {dashboardData.application.activeContainers?.value || 0}
              </p>
            </div>
            <div className=\"p-3 rounded-full bg-blue-100\">
              <Container className=\"h-6 w-6 text-blue-600\" />
            </div>
          </div>
          <p className=\"text-sm text-gray-500 mt-2\">
            {dashboardData.application.activeSandboxes?.value || 0} secure sandboxes
          </p>
        </div>

        {/* System Uptime */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <div className=\"flex items-center justify-between\">
            <div>
              <p className=\"text-sm font-medium text-gray-600\">System Uptime</p>
              <p className=\"text-2xl font-bold text-gray-900\">
                {formatUptime(dashboardData.system.uptime)}
              </p>
            </div>
            <div className=\"p-3 rounded-full bg-green-100\">
              <Clock className=\"h-6 w-6 text-green-600\" />
            </div>
          </div>
          <p className=\"text-sm text-gray-500 mt-2\">
            Since last restart
          </p>
        </div>
      </div>

      {/* Job Execution Status */}
      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">
        {/* Job Statistics */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Job Execution Status</h3>
          <div className=\"space-y-4\">
            <div className=\"flex items-center justify-between\">
              <div className=\"flex items-center space-x-3\">
                <div className=\"p-2 rounded-full bg-blue-100\">
                  <Activity className=\"h-4 w-4 text-blue-600\" />
                </div>
                <span className=\"text-sm font-medium text-gray-700\">Active Jobs</span>
              </div>
              <span className=\"text-lg font-bold text-blue-600\">
                {dashboardData.application.activeJobs?.value || 0}
              </span>
            </div>
            
            <div className=\"flex items-center justify-between\">
              <div className=\"flex items-center space-x-3\">
                <div className=\"p-2 rounded-full bg-green-100\">
                  <CheckCircle className=\"h-4 w-4 text-green-600\" />
                </div>
                <span className=\"text-sm font-medium text-gray-700\">Completed Jobs</span>
              </div>
              <span className=\"text-lg font-bold text-green-600\">
                {dashboardData.application.completedJobs?.value || 0}
              </span>
            </div>
            
            <div className=\"flex items-center justify-between\">
              <div className=\"flex items-center space-x-3\">
                <div className=\"p-2 rounded-full bg-red-100\">
                  <AlertTriangle className=\"h-4 w-4 text-red-600\" />
                </div>
                <span className=\"text-sm font-medium text-gray-700\">Failed Jobs</span>
              </div>
              <span className=\"text-lg font-bold text-red-600\">
                {dashboardData.application.failedJobs?.value || 0}
              </span>
            </div>
            
            {/* Success Rate */}
            <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">
              <div className=\"flex items-center justify-between mb-2\">
                <span className=\"text-sm font-medium text-gray-700\">Success Rate</span>
                <span className=\"text-sm font-bold text-gray-900\">
                  {(() => {
                    const completed = dashboardData.application.completedJobs?.value || 0;
                    const failed = dashboardData.application.failedJobs?.value || 0;
                    const total = completed + failed;
                    return total > 0 ? `${((completed / total) * 100).toFixed(1)}%` : '0%';
                  })()}
                </span>
              </div>
              <div className=\"w-full bg-gray-200 rounded-full h-2\">
                <div 
                  className=\"h-2 rounded-full bg-green-600\"
                  style={{ 
                    width: `${(() => {
                      const completed = dashboardData.application.completedJobs?.value || 0;
                      const failed = dashboardData.application.failedJobs?.value || 0;
                      const total = completed + failed;
                      return total > 0 ? (completed / total) * 100 : 0;
                    })()}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className=\"bg-white rounded-lg shadow p-6\">
          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Active Alerts</h3>
          {dashboardData.alerts.active.length === 0 ? (
            <div className=\"text-center py-8\">
              <Shield className=\"h-12 w-12 text-green-500 mx-auto mb-2\" />
              <p className=\"text-gray-600\">No active alerts</p>
              <p className=\"text-sm text-gray-500\">System is running normally</p>
            </div>
          ) : (
            <div className=\"space-y-3 max-h-64 overflow-y-auto\">
              {dashboardData.alerts.active.map((alert) => (
                <div key={alert.id} className={`p-3 rounded-lg border-l-4 ${
                  alert.severity === 'critical' ? 'border-red-500 bg-red-50' :
                  alert.severity === 'high' ? 'border-orange-500 bg-orange-50' :
                  alert.severity === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                  'border-blue-500 bg-blue-50'
                }`}>
                  <div className=\"flex items-start justify-between\">
                    <div className=\"flex-1\">
                      <div className=\"flex items-center space-x-2\">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                        <span className=\"text-xs text-gray-500\">
                          {new Date(alert.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className=\"text-sm text-gray-900 mt-1\">{alert.message}</p>
                    </div>
                    {!alert.acknowledged && (
                      <button
                        onClick={() => acknowledgeAlert(alert.id)}
                        className=\"ml-2 px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700\"
                      >
                        Acknowledge
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Performance Recommendations */}
      {dashboardData.recommendations.length > 0 && (
        <div className=\"bg-white rounded-lg shadow p-6\">
          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Performance Recommendations</h3>
          <div className=\"space-y-4\">
            {dashboardData.recommendations.slice(0, 3).map((rec, index) => (
              <div key={index} className=\"border-l-4 border-blue-500 bg-blue-50 p-4 rounded-lg\">
                <div className=\"flex items-start space-x-3\">
                  <TrendingUp className=\"h-5 w-5 text-blue-600 mt-0.5\" />
                  <div className=\"flex-1\">
                    <h4 className=\"text-sm font-medium text-blue-900\">{rec.title}</h4>
                    <p className=\"text-sm text-blue-700 mt-1\">{rec.description}</p>
                    {rec.recommendations && (
                      <ul className=\"mt-2 text-sm text-blue-700 list-disc list-inside\">
                        {rec.recommendations.slice(0, 2).map((item: string, idx: number) => (
                          <li key={idx}>{item}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\">
                    {rec.confidence ? `${(rec.confidence * 100).toFixed(0)}%` : 'High'} confidence
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeDashboard;