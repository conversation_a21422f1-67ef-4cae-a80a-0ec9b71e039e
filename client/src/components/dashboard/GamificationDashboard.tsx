import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { TrophyIcon, TargetIcon, AwardIcon, FlameIcon } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlocked: boolean;
  progress: number;
  maxProgress: number;
  unlockedAt?: string;
  rarity: 'bronze' | 'silver' | 'gold' | 'platinum';
}

interface Goal {
  id: string;
  title: string;
  description: string;
  target: number;
  current: number;
  deadline: string;
  category: 'applications' | 'responses' | 'interviews' | 'skills';
  priority: 'low' | 'medium' | 'high';
}

interface UserStats {
  level: number;
  xp: number;
  xpToNextLevel: number;
  totalApplications: number;
  responseRate: number;
  interviewRate: number;
  streak: number;
  completedGoals: number;
}

export default function GamificationDashboard() {
  const { token } = useSelector((state: RootState) => state.auth);
  const [userStats, setUserStats] = useState<UserStats>({
    level: 1,
    xp: 0,
    xpToNextLevel: 100,
    totalApplications: 0,
    responseRate: 0,
    interviewRate: 0,
    streak: 0,
    completedGoals: 0
  });
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [goals, setGoals] = useState<Goal[]>([]);
  const [showNewGoalForm, setShowNewGoalForm] = useState(false);
  const [newGoal, setNewGoal] = useState({
    title: '',
    description: '',
    target: 1,
    deadline: '',
    category: 'applications' as Goal['category'],
    priority: 'medium' as Goal['priority']
  });

  useEffect(() => {
    if (token) {
      fetchUserStats();
      fetchAchievements();
      fetchGoals();
    }
  }, [token]);

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/gamification/stats', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.ok) {
        const result = await response.json();
        setUserStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  };

  const fetchAchievements = async () => {
    try {
      const response = await fetch('/api/gamification/achievements', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.ok) {
        const result = await response.json();
        setAchievements(result.data);
      }
    } catch (error) {
      console.error('Error fetching achievements:', error);
    }
  };

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/gamification/goals', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.ok) {
        const result = await response.json();
        setGoals(result.data);
      }
    } catch (error) {
      console.error('Error fetching goals:', error);
    }
  };

  const handleCreateGoal = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/gamification/goals', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newGoal)
      });

      if (response.ok) {
        setShowNewGoalForm(false);
        setNewGoal({
          title: '',
          description: '',
          target: 1,
          deadline: '',
          category: 'applications',
          priority: 'medium'
        });
        fetchGoals();
      }
    } catch (error) {
      console.error('Error creating goal:', error);
    }
  };

  const updateGoalProgress = async (goalId: string, progress: number) => {
    try {
      const response = await fetch(`/api/gamification/goals/${goalId}/progress`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ current: progress })
      });

      if (response.ok) {
        fetchGoals();
        fetchUserStats(); // Refresh stats in case of level up
      }
    } catch (error) {
      console.error('Error updating goal progress:', error);
    }
  };

  const getRarityColor = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'bronze': return 'text-amber-600 bg-amber-100';
      case 'silver': return 'text-gray-600 bg-gray-100';
      case 'gold': return 'text-yellow-600 bg-yellow-100';
      case 'platinum': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: Goal['priority']) => {
    switch (priority) {
      case 'high': return 'border-red-500 bg-red-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-green-500 bg-green-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const xpProgress = (userStats.xp / userStats.xpToNextLevel) * 100;

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* User Level & XP */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold">Level {userStats.level} Career Hunter</h2>
            <p className="text-blue-100">Keep pushing forward to reach the next level!</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{userStats.xp} XP</div>
            <div className="text-sm text-blue-100">{userStats.xpToNextLevel - userStats.xp} XP to next level</div>
          </div>
        </div>
        
        <div className="w-full bg-white bg-opacity-20 rounded-full h-3 mb-4">
          <div 
            className="bg-white h-3 rounded-full transition-all duration-500"
            style={{ width: `${xpProgress}%` }}
          ></div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-xl font-bold">{userStats.totalApplications}</div>
            <div className="text-sm text-blue-100">Applications</div>
          </div>
          <div>
            <div className="text-xl font-bold">{userStats.responseRate}%</div>
            <div className="text-sm text-blue-100">Response Rate</div>
          </div>
          <div>
            <div className="text-xl font-bold flex items-center justify-center gap-1">
              <FlameIcon className="w-5 h-5" />
              {userStats.streak}
            </div>
            <div className="text-sm text-blue-100">Day Streak</div>
          </div>
          <div>
            <div className="text-xl font-bold">{userStats.completedGoals}</div>
            <div className="text-sm text-blue-100">Goals Completed</div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Achievements */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <TrophyIcon className="w-6 h-6 text-yellow-500" />
              Achievements
            </h3>
            <span className="text-sm text-gray-500">
              {achievements.filter(a => a.unlocked).length} / {achievements.length}
            </span>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {achievements.map((achievement) => (
              <div
                key={achievement.id}
                className={`p-3 rounded-lg border ${
                  achievement.unlocked ? 'bg-white border-gray-200' : 'bg-gray-50 border-gray-100 opacity-60'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${getRarityColor(achievement.rarity)}`}>
                    <span className="text-lg">{achievement.icon}</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{achievement.title}</h4>
                    <p className="text-sm text-gray-600">{achievement.description}</p>
                    {!achievement.unlocked && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {achievement.progress} / {achievement.maxProgress}
                        </p>
                      </div>
                    )}
                    {achievement.unlocked && achievement.unlockedAt && (
                      <p className="text-xs text-green-600 mt-1">
                        Unlocked {new Date(achievement.unlockedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Goals */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <TargetIcon className="w-6 h-6 text-blue-500" />
              Active Goals
            </h3>
            <Button
              onClick={() => setShowNewGoalForm(true)}
              size="sm"
              variant="outline"
            >
              Add Goal
            </Button>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {goals.map((goal) => {
              const progress = (goal.current / goal.target) * 100;
              const isCompleted = goal.current >= goal.target;
              
              return (
                <div
                  key={goal.id}
                  className={`p-3 rounded-lg border-l-4 ${getPriorityColor(goal.priority)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{goal.title}</h4>
                    {isCompleted && (
                      <AwardIcon className="w-5 h-5 text-green-500" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{goal.description}</p>
                  
                  <div className="mb-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{goal.current} / {goal.target}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${isCompleted ? 'bg-green-500' : 'bg-blue-500'}`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                    <span className="capitalize">{goal.priority} priority</span>
                  </div>

                  {!isCompleted && (
                    <div className="mt-2 flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateGoalProgress(goal.id, goal.current + 1)}
                        className="text-xs"
                      >
                        +1
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateGoalProgress(goal.id, goal.target)}
                        className="text-xs"
                      >
                        Complete
                      </Button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Card>
      </div>

      {/* New Goal Form Modal */}
      {showNewGoalForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <h3 className="text-xl font-semibold mb-4">Create New Goal</h3>
            
            <form onSubmit={handleCreateGoal} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input
                  type="text"
                  required
                  value={newGoal.title}
                  onChange={(e) => setNewGoal({ ...newGoal, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Apply to 10 jobs this month"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  rows={2}
                  value={newGoal.description}
                  onChange={(e) => setNewGoal({ ...newGoal, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Optional description..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Target</label>
                  <input
                    type="number"
                    min="1"
                    required
                    value={newGoal.target}
                    onChange={(e) => setNewGoal({ ...newGoal, target: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={newGoal.category}
                    onChange={(e) => setNewGoal({ ...newGoal, category: e.target.value as Goal['category'] })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="applications">Applications</option>
                    <option value="responses">Responses</option>
                    <option value="interviews">Interviews</option>
                    <option value="skills">Skills</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                  <input
                    type="date"
                    required
                    value={newGoal.deadline}
                    onChange={(e) => setNewGoal({ ...newGoal, deadline: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <select
                    value={newGoal.priority}
                    onChange={(e) => setNewGoal({ ...newGoal, priority: e.target.value as Goal['priority'] })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-3">
                <Button type="submit" className="flex-1">
                  Create Goal
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewGoalForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Card>
        </div>
      )}
    </div>
  );
}