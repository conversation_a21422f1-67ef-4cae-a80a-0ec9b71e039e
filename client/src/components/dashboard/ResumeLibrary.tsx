import { FileText, Eye, Edit, Download, Copy, Star } from 'lucide-react';

interface Resume {
  id: string;
  title: string;
  template: string;
  lastModified: Date;
  isStarred?: boolean;
  thumbnail?: string;
  status: 'draft' | 'published' | 'archived';
  applications?: number;
}

interface ResumeLibraryProps {
  resumes?: Resume[];
  onResumeSelect?: (resume: Resume) => void;
  onResumeEdit?: (resume: Resume) => void;
  onResumeToggleStar?: (resumeId: string) => void;
  className?: string;
}

export default function ResumeLibrary({ 
  resumes = [], 
  onResumeSelect, 
  onResumeEdit,
  onResumeToggleStar,
  className = '' 
}: ResumeLibraryProps) {
  // Mock data for demonstration
  const mockResumes: Resume[] = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      template: 'Modern Professional',
      lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isStarred: true,
      status: 'published',
      applications: 15,
    },
    {
      id: '2',
      title: 'Full Stack Engineer',
      template: 'Creative Tech',
      lastModified: new Date(Date.now() - 24 * 60 * 60 * 1000),
      isStarred: false,
      status: 'draft',
      applications: 0,
    },
    {
      id: '3',
      title: 'React Specialist',
      template: 'Clean Minimalist',
      lastModified: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      isStarred: true,
      status: 'published',
      applications: 8,
    },
    {
      id: '4',
      title: 'Lead Developer',
      template: 'Executive',
      lastModified: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      isStarred: false,
      status: 'archived',
      applications: 22,
    }
  ];

  const displayResumes = resumes.length > 0 ? resumes : mockResumes;

  const formatLastModified = (timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const getStatusColor = (status: Resume['status']) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleResumeClick = (resume: Resume) => {
    onResumeSelect?.(resume);
  };

  const handleEditClick = (e: React.MouseEvent, resume: Resume) => {
    e.stopPropagation();
    onResumeEdit?.(resume);
  };

  const handleStarClick = (e: React.MouseEvent, resumeId: string) => {
    e.stopPropagation();
    onResumeToggleStar?.(resumeId);
  };

  return (
    <div className={`card-enhanced p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Resume Library</h3>
        </div>
        <button className="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors">
          + New Resume
        </button>
      </div>

      {displayResumes.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400 mb-6">No resumes in your library yet</p>
          <button className="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors">
            Create Your First Resume
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {displayResumes.map((resume, index) => (
            <div 
              key={resume.id}
              className="group border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-lg hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 cursor-pointer animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => handleResumeClick(resume)}
            >
              {/* Resume Thumbnail */}
              <div className="relative mb-4">
                <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center">
                  {resume.thumbnail ? (
                    <img 
                      src={resume.thumbnail} 
                      alt={resume.title}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <FileText className="h-12 w-12 text-gray-400" />
                  )}
                </div>
                
                {/* Star button */}
                <button
                  onClick={(e) => handleStarClick(e, resume.id)}
                  className={`absolute top-2 right-2 p-1.5 rounded-full transition-all ${
                    resume.isStarred 
                      ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400' 
                      : 'bg-white dark:bg-gray-800 text-gray-400 hover:text-yellow-500'
                  } shadow-sm hover:shadow-md`}
                >
                  <Star className={`h-3 w-3 ${resume.isStarred ? 'fill-current' : ''}`} />
                </button>

                {/* Status badge */}
                <span className={`absolute bottom-2 left-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(resume.status)}`}>
                  {resume.status.charAt(0).toUpperCase() + resume.status.slice(1)}
                </span>
              </div>

              {/* Resume Details */}
              <div className="space-y-2">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                  {resume.title}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {resume.template}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Modified {formatLastModified(resume.lastModified)}
                </p>
                
                {resume.applications !== undefined && (
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Used in {resume.applications} applications
                  </p>
                )}
              </div>

              {/* Action buttons (visible on hover) */}
              <div className="flex items-center justify-between mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={(e) => handleEditClick(e, resume)}
                    className="p-1.5 text-gray-400 hover:text-primary-600 transition-colors"
                    title="Edit resume"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => e.stopPropagation()}
                    className="p-1.5 text-gray-400 hover:text-primary-600 transition-colors"
                    title="View resume"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => e.stopPropagation()}
                    className="p-1.5 text-gray-400 hover:text-primary-600 transition-colors"
                    title="Download"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => e.stopPropagation()}
                    className="p-1.5 text-gray-400 hover:text-primary-600 transition-colors"
                    title="Duplicate"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}