import { Play, Pause, Settings, Bar<PERSON><PERSON>3, Target, Clock } from 'lucide-react';

interface JobLoop {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'completed' | 'draft';
  applicationsToday: number;
  totalApplications: number;
  responseRate: number;
  target: number;
  lastActivity: Date;
}

interface LoopStatusProps {
  loops?: JobLoop[];
  className?: string;
}

export default function LoopStatus({ loops = [], className = '' }: LoopStatusProps) {
  // Mock data for demonstration
  const mockLoops: JobLoop[] = [
    {
      id: '1',
      name: 'Senior Frontend Developer',
      status: 'active',
      applicationsToday: 3,
      totalApplications: 47,
      responseRate: 12.8,
      target: 5,
      lastActivity: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    },
    {
      id: '2',
      name: 'Full Stack Engineer',
      status: 'paused',
      applicationsToday: 0,
      totalApplications: 23,
      responseRate: 8.7,
      target: 3,
      lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    },
    {
      id: '3',
      name: 'React Developer',
      status: 'active',
      applicationsToday: 2,
      totalApplications: 15,
      responseRate: 20.0,
      target: 4,
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    }
  ];

  const displayLoops = loops.length > 0 ? loops : mockLoops;

  const getStatusIcon = (status: JobLoop['status']) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4 text-green-600" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      case 'completed':
        return <Target className="h-4 w-4 text-blue-600" />;
      case 'draft':
        return <Settings className="h-4 w-4 text-gray-600" />;
      default:
        return <Settings className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: JobLoop['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatLastActivity = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) {
        return `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
      }
    }
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  return (
    <div className={`card-enhanced p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Job Search Loops</h3>
        </div>
        <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
          Manage Loops
        </button>
      </div>

      {displayLoops.length === 0 ? (
        <div className="text-center py-8">
          <Target className="h-8 w-8 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400 mb-3">No active job search loops</p>
          <button className="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors">
            Create Your First Loop
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {displayLoops.map((loop, index) => (
            <div 
              key={loop.id} 
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(loop.status)}
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {loop.name}
                  </h4>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(loop.status)}`}>
                  {loop.status.charAt(0).toUpperCase() + loop.status.slice(1)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="mb-3">
                <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                  <span>Today's Progress</span>
                  <span>{loop.applicationsToday}/{loop.target}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${calculateProgress(loop.applicationsToday, loop.target)}%` }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {loop.totalApplications}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Total Applied</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {loop.responseRate}%
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Response Rate</p>
                </div>
                <div className="flex items-center justify-center space-x-1">
                  <Clock className="h-3 w-3 text-gray-400" />
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {formatLastActivity(loop.lastActivity)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}