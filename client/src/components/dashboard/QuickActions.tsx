import React, { useState } from 'react';
import { Plus, Zap, Target, FileText, Search, Settings } from 'lucide-react';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}

interface QuickActionsProps {
  onCreateResume?: () => void;
  onSetupLoop?: () => void;
  onAIOptimize?: () => void;
  onSearchJobs?: () => void;
  customActions?: QuickAction[];
  className?: string;
}

export default function QuickActions({ 
  onCreateResume,
  onSetupLoop,
  onAIOptimize,
  onSearchJobs,
  customActions = [],
  className = '' 
}: QuickActionsProps) {
  const [showModal, setShowModal] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);

  const defaultActions: QuickAction[] = [
    {
      id: 'create-resume',
      title: 'Create Resume',
      description: 'Start building a new resume with AI assistance',
      icon: <FileText className="h-5 w-5" />,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => {
        onCreateResume?.();
        handleActionClick('create-resume');
      }
    },
    {
      id: 'setup-loop',
      title: 'Setup Job Loop',
      description: 'Configure automated job search and applications',
      icon: <Target className="h-5 w-5" />,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => {
        onSetupLoop?.();
        handleActionClick('setup-loop');
      }
    },
    {
      id: 'ai-optimize',
      title: 'AI Optimize',
      description: 'Enhance your existing resumes with AI suggestions',
      icon: <Zap className="h-5 w-5" />,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => {
        onAIOptimize?.();
        handleActionClick('ai-optimize');
      }
    },
    {
      id: 'search-jobs',
      title: 'Smart Job Search',
      description: 'Find relevant opportunities with AI matching',
      icon: <Search className="h-5 w-5" />,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => {
        onSearchJobs?.();
        handleActionClick('search-jobs');
      }
    }
  ];

  const allActions = [...defaultActions, ...customActions];

  const handleActionClick = (actionId: string) => {
    setSelectedAction(actionId);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedAction(null);
  };

  const renderActionModal = () => {
    if (!showModal || !selectedAction) return null;

    const action = allActions.find(a => a.id === selectedAction);
    if (!action) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6 animate-scale-in">
          <div className="flex items-center space-x-3 mb-4">
            <div className={`p-3 rounded-lg ${action.color.split(' ')[0]} text-white`}>
              {action.icon}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {action.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {action.description}
              </p>
            </div>
          </div>

          {/* Modal content based on action type */}
          <div className="mb-6">
            {selectedAction === 'create-resume' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Choose how you'd like to create your resume:
                </p>
                <div className="space-y-2">
                  <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="font-medium text-gray-900 dark:text-white">Start from Template</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Choose from 100+ professional templates</div>
                  </button>
                  <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="font-medium text-gray-900 dark:text-white">AI-Powered Builder</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Let AI create your resume from job descriptions</div>
                  </button>
                  <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="font-medium text-gray-900 dark:text-white">Import & Enhance</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Upload existing resume for enhancement</div>
                  </button>
                </div>
              </div>
            )}

            {selectedAction === 'setup-loop' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Set up an automated job search loop:
                </p>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Job Title
                    </label>
                    <input 
                      type="text" 
                      placeholder="e.g., Senior Frontend Developer"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Applications per day
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                      <option value="3">3 applications</option>
                      <option value="5">5 applications</option>
                      <option value="10">10 applications</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {selectedAction === 'ai-optimize' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Select resumes to optimize with AI:
                </p>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {['Senior Frontend Developer', 'Full Stack Engineer', 'React Specialist'].map((resume, index) => (
                    <label key={index} className="flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                      <input type="checkbox" className="rounded border-gray-300" />
                      <span className="text-sm text-gray-900 dark:text-white">{resume}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {selectedAction === 'search-jobs' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Configure your job search preferences:
                </p>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Keywords
                    </label>
                    <input 
                      type="text" 
                      placeholder="React, TypeScript, Frontend"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Location
                    </label>
                    <input 
                      type="text" 
                      placeholder="Remote, New York, San Francisco"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Modal actions */}
          <div className="flex space-x-3">
            <button
              onClick={closeModal}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                action.action();
                closeModal();
              }}
              className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors ${action.color}`}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={`card-enhanced p-6 ${className}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5 text-primary-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h3>
          </div>
          <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
            <Settings className="h-4 w-4" />
          </button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {allActions.map((action, index) => (
            <button
              key={action.id}
              onClick={action.action}
              className={`group p-4 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-all duration-200 animate-fade-in ${action.color.replace('bg-', 'hover:bg-').replace('hover:bg-', 'hover:border-')} hover:border-opacity-50`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                  {action.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {action.title}
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                    {action.description}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Recent actions */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Recent Actions</h4>
          <div className="space-y-2">
            <div className="flex items-center space-x-3 text-sm">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">Created "Senior Frontend Developer" resume</span>
              <span className="text-gray-500 dark:text-gray-500 text-xs">2h ago</span>
            </div>
            <div className="flex items-center space-x-3 text-sm">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">Started job search loop</span>
              <span className="text-gray-500 dark:text-gray-500 text-xs">1d ago</span>
            </div>
          </div>
        </div>
      </div>

      {renderActionModal()}
    </>
  );
}