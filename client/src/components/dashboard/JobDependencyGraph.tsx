import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  RefreshCw, 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Filter,
  Download,
  Info,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';

interface DependencyNode {
  id: string;
  label: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'skipped';
  priority: number;
  retryCount: number;
  executionTime?: number;
  company?: string;
  position?: string;
  platform?: string;
  level?: number; // For hierarchical layout
}

interface DependencyEdge {
  from: string;
  to: string;
  type: 'dependency' | 'conditional';
  condition?: string;
}

interface DependencyGraphData {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
}

interface ExecutionStats {
  total: number;
  completed: number;
  failed: number;
  cancelled: number;
  skipped: number;
  running: number;
  pending: number;
  successRate: number;
  cascadeFailures: number;
}

export const JobDependencyGraph: React.FC = () => {
  const [graphData, setGraphData] = useState<DependencyGraphData>({ nodes: [], edges: [] });
  const [executionStats, setExecutionStats] = useState<ExecutionStats>({
    total: 0,
    completed: 0,
    failed: 0,
    cancelled: 0,
    skipped: 0,
    running: 0,
    pending: 0,
    successRate: 0,
    cascadeFailures: 0
  });
  
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [zoom, setZoom] = useState(1);
  const [viewMode, setViewMode] = useState<'hierarchical' | 'force' | 'circular'>('hierarchical');

  const svgRef = useRef<SVGSVGElement>(null);
  const graphContainerRef = useRef<HTMLDivElement>(null);

  // Platform colors
  const platformColors: Record<string, string> = {
    linkedin: '#0077B5',
    indeed: '#2557A7',
    glassdoor: '#0CAA41',
    monster: '#6E46AE',
    careerbuilder: '#F37A1F',
    default: '#6B7280'
  };

  // Status colors and icons
  const statusConfig = {
    pending: { color: '#6B7280', bgColor: '#F3F4F6', icon: Clock },
    running: { color: '#3B82F6', bgColor: '#DBEAFE', icon: RefreshCw },
    completed: { color: '#10B981', bgColor: '#D1FAE5', icon: CheckCircle },
    failed: { color: '#EF4444', bgColor: '#FEE2E2', icon: XCircle },
    cancelled: { color: '#6B7280', bgColor: '#F3F4F6', icon: Square },
    skipped: { color: '#F59E0B', bgColor: '#FEF3C7', icon: AlertTriangle }
  };

  // Fetch dependency graph data
  const fetchGraphData = useCallback(async () => {
    try {
      const [graphResponse, statsResponse] = await Promise.all([
        fetch('/api/dependencies/graph'),
        fetch('/api/dependencies/status')
      ]);

      if (graphResponse.ok && statsResponse.ok) {
        const graphData = await graphResponse.json();
        const statsData = await statsResponse.json();
        
        setGraphData(graphData);
        setExecutionStats(statsData);
      }
    } catch (error) {
      console.error('Error fetching dependency graph data:', error);
    }
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    fetchGraphData();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchGraphData, refreshInterval);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [fetchGraphData, autoRefresh, refreshInterval]);

  // Calculate layout positions
  const calculateLayout = useCallback((nodes: DependencyNode[], edges: DependencyEdge[]) => {
    const nodePositions = new Map<string, { x: number; y: number }>();
    const nodeSize = 120;
    const levelHeight = 150;
    const nodeSpacing = 200;

    if (viewMode === 'hierarchical') {
      // Group nodes by dependency level
      const levels = new Map<number, DependencyNode[]>();
      const nodeDependencies = new Map<string, string[]>();
      
      // Build dependency map
      edges.forEach(edge => {
        const deps = nodeDependencies.get(edge.to) || [];
        deps.push(edge.from);
        nodeDependencies.set(edge.to, deps);
      });
      
      // Calculate levels
      const calculateLevel = (nodeId: string, visited = new Set<string>()): number => {
        if (visited.has(nodeId)) return 0; // Circular dependency
        visited.add(nodeId);
        
        const deps = nodeDependencies.get(nodeId) || [];
        if (deps.length === 0) return 0;
        
        const maxDepLevel = Math.max(...deps.map(dep => calculateLevel(dep, new Set(visited))));
        return maxDepLevel + 1;
      };
      
      nodes.forEach(node => {
        const level = calculateLevel(node.id);
        const levelNodes = levels.get(level) || [];
        levelNodes.push(node);
        levels.set(level, levelNodes);
      });
      
      // Position nodes
      const maxLevel = Math.max(...levels.keys());
      levels.forEach((levelNodes, level) => {
        const y = (maxLevel - level) * levelHeight + 100;
        levelNodes.forEach((node, index) => {
          const totalWidth = (levelNodes.length - 1) * nodeSpacing;
          const startX = (800 - totalWidth) / 2; // Center horizontally
          const x = startX + index * nodeSpacing;
          nodePositions.set(node.id, { x, y });
        });
      });
    } else if (viewMode === 'circular') {
      // Circular layout
      const centerX = 400;
      const centerY = 300;
      const radius = Math.min(200, nodes.length * 20);
      
      nodes.forEach((node, index) => {
        const angle = (index / nodes.length) * 2 * Math.PI;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        nodePositions.set(node.id, { x, y });
      });
    } else {
      // Force-directed layout (simplified)
      nodes.forEach((node, index) => {
        const cols = Math.ceil(Math.sqrt(nodes.length));
        const row = Math.floor(index / cols);
        const col = index % cols;
        const x = col * nodeSpacing + 100;
        const y = row * levelHeight + 100;
        nodePositions.set(node.id, { x, y });
      });
    }

    return nodePositions;
  }, [viewMode]);

  // Filter nodes based on status
  const filteredNodes = graphData.nodes.filter(node => 
    filterStatus === 'all' || node.status === filterStatus
  );

  const nodePositions = calculateLayout(filteredNodes, graphData.edges);

  // Control functions
  const startExecution = async () => {
    try {
      setIsExecuting(true);
      await fetch('/api/dependencies/start', { method: 'POST' });
      setAutoRefresh(true);
    } catch (error) {
      console.error('Error starting execution:', error);
      setIsExecuting(false);
    }
  };

  const pauseExecution = async () => {
    try {
      await fetch('/api/dependencies/pause', { method: 'POST' });
      setIsExecuting(false);
    } catch (error) {
      console.error('Error pausing execution:', error);
    }
  };

  const stopExecution = async () => {
    try {
      await fetch('/api/dependencies/stop', { method: 'POST' });
      setIsExecuting(false);
      setAutoRefresh(false);
    } catch (error) {
      console.error('Error stopping execution:', error);
    }
  };

  const resetGraph = async () => {
    try {
      await fetch('/api/dependencies/reset', { method: 'POST' });
      await fetchGraphData();
      setIsExecuting(false);
    } catch (error) {
      console.error('Error resetting graph:', error);
    }
  };

  const exportGraph = () => {
    const svgElement = svgRef.current;
    if (!svgElement) return;

    const svgData = new XMLSerializer().serializeToString(svgElement);
    const blob = new Blob([svgData], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'dependency-graph.svg';
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const getNodeDetails = (nodeId: string) => {
    return graphData.nodes.find(n => n.id === nodeId);
  };

  const getNodeConnections = (nodeId: string) => {
    const incoming = graphData.edges.filter(e => e.to === nodeId);
    const outgoing = graphData.edges.filter(e => e.from === nodeId);
    return { incoming, outgoing };
  };

  const selectedNodeDetails = selectedNode ? getNodeDetails(selectedNode) : null;
  const selectedNodeConnections = selectedNode ? getNodeConnections(selectedNode) : null;

  return (
    <div className=\"h-screen flex flex-col bg-gray-50\">
      {/* Header */}
      <div className=\"bg-white border-b border-gray-200 p-4\">
        <div className=\"flex items-center justify-between\">
          <div>
            <h1 className=\"text-xl font-bold text-gray-900\">Job Dependency Graph</h1>
            <p className=\"text-sm text-gray-600\">
              Visualize and monitor job execution dependencies
            </p>
          </div>
          
          <div className=\"flex items-center space-x-4\">
            {/* Execution Controls */}
            <div className=\"flex items-center space-x-2\">
              <button
                onClick={startExecution}
                disabled={isExecuting}
                className=\"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2\"
              >
                <Play className=\"h-4 w-4\" />
                <span>Start</span>
              </button>
              
              <button
                onClick={pauseExecution}
                disabled={!isExecuting}
                className=\"px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50 flex items-center space-x-2\"
              >
                <Pause className=\"h-4 w-4\" />
                <span>Pause</span>
              </button>
              
              <button
                onClick={stopExecution}
                className=\"px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center space-x-2\"
              >
                <Square className=\"h-4 w-4\" />
                <span>Stop</span>
              </button>
              
              <button
                onClick={resetGraph}
                className=\"px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2\"
              >
                <RotateCcw className=\"h-4 w-4\" />
                <span>Reset</span>
              </button>
            </div>

            {/* View Controls */}
            <div className=\"flex items-center space-x-2 border-l border-gray-300 pl-4\">
              <select
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value as any)}
                className=\"px-2 py-1 border border-gray-300 rounded text-sm\"
              >
                <option value=\"hierarchical\">Hierarchical</option>
                <option value=\"force\">Force-directed</option>
                <option value=\"circular\">Circular</option>
              </select>
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className=\"px-2 py-1 border border-gray-300 rounded text-sm\"
              >
                <option value=\"all\">All Status</option>
                <option value=\"pending\">Pending</option>
                <option value=\"running\">Running</option>
                <option value=\"completed\">Completed</option>
                <option value=\"failed\">Failed</option>
                <option value=\"cancelled\">Cancelled</option>
                <option value=\"skipped\">Skipped</option>
              </select>
              
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`p-2 rounded ${autoRefresh ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
                title={autoRefresh ? 'Disable auto-refresh' : 'Enable auto-refresh'}
              >
                <RefreshCw className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />
              </button>
              
              <button
                onClick={exportGraph}
                className=\"p-2 text-gray-600 hover:text-gray-800\"
                title=\"Export graph\"
              >
                <Download className=\"h-4 w-4\" />
              </button>
            </div>
          </div>
        </div>

        {/* Stats Bar */}
        <div className=\"mt-4 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 text-center\">
          <div className=\"bg-gray-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-gray-900\">{executionStats.total}</div>
            <div className=\"text-xs text-gray-600\">Total</div>
          </div>
          <div className=\"bg-blue-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-blue-900\">{executionStats.running}</div>
            <div className=\"text-xs text-blue-600\">Running</div>
          </div>
          <div className=\"bg-green-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-green-900\">{executionStats.completed}</div>
            <div className=\"text-xs text-green-600\">Completed</div>
          </div>
          <div className=\"bg-red-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-red-900\">{executionStats.failed}</div>
            <div className=\"text-xs text-red-600\">Failed</div>
          </div>
          <div className=\"bg-yellow-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-yellow-900\">{executionStats.pending}</div>
            <div className=\"text-xs text-yellow-600\">Pending</div>
          </div>
          <div className=\"bg-gray-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-gray-900\">{executionStats.cancelled}</div>
            <div className=\"text-xs text-gray-600\">Cancelled</div>
          </div>
          <div className=\"bg-orange-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-orange-900\">{executionStats.skipped}</div>
            <div className=\"text-xs text-orange-600\">Skipped</div>
          </div>
          <div className=\"bg-indigo-100 rounded-lg p-3\">
            <div className=\"text-lg font-bold text-indigo-900\">{executionStats.successRate.toFixed(1)}%</div>
            <div className=\"text-xs text-indigo-600\">Success Rate</div>
          </div>
        </div>
      </div>

      <div className=\"flex-1 flex\">
        {/* Graph Visualization */}
        <div className=\"flex-1 relative\" ref={graphContainerRef}>
          <svg
            ref={svgRef}
            className=\"w-full h-full\"
            viewBox=\"0 0 800 600\"
            style={{ transform: `scale(${zoom})` }}
          >
            {/* Definitions */}
            <defs>
              <marker
                id=\"arrowhead\"
                markerWidth=\"10\"
                markerHeight=\"7\"
                refX=\"9\"
                refY=\"3.5\"
                orient=\"auto\"
              >
                <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#6B7280\" />
              </marker>
              
              <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">
                <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"3\" floodOpacity=\"0.3\" />
              </filter>
            </defs>

            {/* Edges */}
            {graphData.edges.map((edge, index) => {
              const fromPos = nodePositions.get(edge.from);
              const toPos = nodePositions.get(edge.to);
              
              if (!fromPos || !toPos) return null;
              
              const fromNode = graphData.nodes.find(n => n.id === edge.from);
              const toNode = graphData.nodes.find(n => n.id === edge.to);
              
              if (!fromNode || !toNode) return null;
              
              // Skip if filtered out
              if (filterStatus !== 'all' && fromNode.status !== filterStatus && toNode.status !== filterStatus) {
                return null;
              }
              
              const strokeColor = edge.type === 'conditional' ? '#F59E0B' : '#6B7280';
              const strokeWidth = edge.type === 'conditional' ? 2 : 1;
              const strokeDasharray = edge.type === 'conditional' ? '5,5' : 'none';
              
              return (
                <line
                  key={`edge-${index}`}
                  x1={fromPos.x + 60}
                  y1={fromPos.y + 30}
                  x2={toPos.x + 60}
                  y2={toPos.y + 30}
                  stroke={strokeColor}
                  strokeWidth={strokeWidth}
                  strokeDasharray={strokeDasharray}
                  markerEnd=\"url(#arrowhead)\"
                />
              );
            })}

            {/* Nodes */}
            {filteredNodes.map((node) => {
              const position = nodePositions.get(node.id);
              if (!position) return null;
              
              const config = statusConfig[node.status];
              const StatusIcon = config.icon;
              const platformColor = platformColors[node.platform || 'default'];
              
              return (
                <g key={node.id}>
                  {/* Node background */}
                  <rect
                    x={position.x}
                    y={position.y}
                    width=\"120\"
                    height=\"60\"
                    rx=\"8\"
                    fill={config.bgColor}
                    stroke={config.color}
                    strokeWidth={selectedNode === node.id ? 3 : 1}
                    filter=\"url(#shadow)\"
                    className=\"cursor-pointer\"
                    onClick={() => setSelectedNode(node.id)}
                  />
                  
                  {/* Platform indicator */}
                  <rect
                    x={position.x}
                    y={position.y}
                    width=\"120\"
                    height=\"4\"
                    rx=\"4\"
                    fill={platformColor}
                  />
                  
                  {/* Status icon */}
                  <circle
                    cx={position.x + 15}
                    cy={position.y + 20}
                    r=\"8\"
                    fill={config.color}
                  />
                  
                  {/* Priority indicator */}
                  <circle
                    cx={position.x + 105}
                    cy={position.y + 15}
                    r=\"6\"
                    fill={node.priority >= 8 ? '#DC2626' : node.priority >= 5 ? '#F59E0B' : '#6B7280'}
                  />
                  <text
                    x={position.x + 105}
                    y={position.y + 19}
                    textAnchor=\"middle\"
                    fill=\"white\"
                    fontSize=\"8\"
                    fontWeight=\"bold\"
                  >
                    {node.priority}
                  </text>
                  
                  {/* Node label */}
                  <text
                    x={position.x + 60}
                    y={position.y + 25}
                    textAnchor=\"middle\"
                    fill={config.color}
                    fontSize=\"10\"
                    fontWeight=\"500\"
                    className=\"pointer-events-none\"
                  >
                    {node.label.length > 15 ? `${node.label.substring(0, 15)}...` : node.label}
                  </text>
                  
                  {/* Company name */}
                  {node.company && (
                    <text
                      x={position.x + 60}
                      y={position.y + 40}
                      textAnchor=\"middle\"
                      fill=\"#6B7280\"
                      fontSize=\"8\"
                      className=\"pointer-events-none\"
                    >
                      {node.company.length > 18 ? `${node.company.substring(0, 18)}...` : node.company}
                    </text>
                  )}
                  
                  {/* Execution time */}
                  {node.executionTime && (
                    <text
                      x={position.x + 60}
                      y={position.y + 52}
                      textAnchor=\"middle\"
                      fill=\"#6B7280\"
                      fontSize=\"7\"
                      className=\"pointer-events-none\"
                    >
                      {(node.executionTime / 1000).toFixed(1)}s
                    </text>
                  )}
                  
                  {/* Retry indicator */}
                  {node.retryCount > 0 && (
                    <text
                      x={position.x + 90}
                      y={position.y + 52}
                      textAnchor=\"middle\"
                      fill=\"#F59E0B\"
                      fontSize=\"7\"
                      className=\"pointer-events-none\"
                    >
                      R{node.retryCount}
                    </text>
                  )}
                </g>
              );
            })}
          </svg>

          {/* Zoom Controls */}
          <div className=\"absolute bottom-4 right-4 bg-white rounded-lg shadow-md p-2 flex items-center space-x-2\">
            <button
              onClick={() => setZoom(prev => Math.max(0.5, prev - 0.1))}
              className=\"p-1 text-gray-600 hover:text-gray-800\"
            >
              <ZoomOut className=\"h-4 w-4\" />
            </button>
            <span className=\"text-sm text-gray-600 min-w-12 text-center\">
              {(zoom * 100).toFixed(0)}%
            </span>
            <button
              onClick={() => setZoom(prev => Math.min(2, prev + 0.1))}
              className=\"p-1 text-gray-600 hover:text-gray-800\"
            >
              <ZoomIn className=\"h-4 w-4\" />
            </button>
            <button
              onClick={() => setZoom(1)}
              className=\"p-1 text-gray-600 hover:text-gray-800\"
            >
              <Maximize className=\"h-4 w-4\" />
            </button>
          </div>
        </div>

        {/* Details Panel */}
        {selectedNodeDetails && (
          <div className=\"w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto\">
            <div className=\"flex items-center justify-between mb-4\">
              <h3 className=\"text-lg font-medium text-gray-900\">Node Details</h3>
              <button
                onClick={() => setSelectedNode(null)}
                className=\"text-gray-400 hover:text-gray-600\"
              >
                ×
              </button>
            </div>
            
            <div className=\"space-y-4\">
              {/* Status */}
              <div className=\"flex items-center space-x-3\">
                <div className={`p-2 rounded-full`} style={{ backgroundColor: statusConfig[selectedNodeDetails.status].bgColor }}>
                  {React.createElement(statusConfig[selectedNodeDetails.status].icon, {
                    className: 'h-4 w-4',
                    style: { color: statusConfig[selectedNodeDetails.status].color }
                  })}
                </div>
                <div>
                  <div className=\"font-medium text-gray-900\">{selectedNodeDetails.status.charAt(0).toUpperCase() + selectedNodeDetails.status.slice(1)}</div>
                  <div className=\"text-sm text-gray-600\">Current Status</div>
                </div>
              </div>
              
              {/* Basic Info */}
              <div className=\"bg-gray-50 rounded-lg p-3 space-y-2\">
                <div className=\"flex justify-between\">
                  <span className=\"text-sm text-gray-600\">Priority:</span>
                  <span className=\"text-sm font-medium\">{selectedNodeDetails.priority}</span>
                </div>
                {selectedNodeDetails.company && (
                  <div className=\"flex justify-between\">
                    <span className=\"text-sm text-gray-600\">Company:</span>
                    <span className=\"text-sm font-medium\">{selectedNodeDetails.company}</span>
                  </div>
                )}
                {selectedNodeDetails.position && (
                  <div className=\"flex justify-between\">
                    <span className=\"text-sm text-gray-600\">Position:</span>
                    <span className=\"text-sm font-medium\">{selectedNodeDetails.position}</span>
                  </div>
                )}
                {selectedNodeDetails.platform && (
                  <div className=\"flex justify-between\">
                    <span className=\"text-sm text-gray-600\">Platform:</span>
                    <div className=\"flex items-center space-x-1\">
                      <div 
                        className=\"w-2 h-2 rounded-full\"
                        style={{ backgroundColor: platformColors[selectedNodeDetails.platform] }}
                      ></div>
                      <span className=\"text-sm font-medium\">{selectedNodeDetails.platform}</span>
                    </div>
                  </div>
                )}
                {selectedNodeDetails.executionTime && (
                  <div className=\"flex justify-between\">
                    <span className=\"text-sm text-gray-600\">Execution Time:</span>
                    <span className=\"text-sm font-medium\">{(selectedNodeDetails.executionTime / 1000).toFixed(2)}s</span>
                  </div>
                )}
                {selectedNodeDetails.retryCount > 0 && (
                  <div className=\"flex justify-between\">
                    <span className=\"text-sm text-gray-600\">Retry Count:</span>
                    <span className=\"text-sm font-medium\">{selectedNodeDetails.retryCount}</span>
                  </div>
                )}
              </div>
              
              {/* Dependencies */}
              {selectedNodeConnections && (
                <div className=\"space-y-3\">
                  {selectedNodeConnections.incoming.length > 0 && (
                    <div>
                      <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Dependencies</h4>
                      <div className=\"space-y-1\">
                        {selectedNodeConnections.incoming.map((edge, index) => {
                          const depNode = graphData.nodes.find(n => n.id === edge.from);
                          return (
                            <div key={index} className=\"flex items-center space-x-2 text-sm\">
                              <div 
                                className=\"w-2 h-2 rounded-full\"
                                style={{ backgroundColor: statusConfig[depNode?.status || 'pending'].color }}
                              ></div>
                              <span>{depNode?.label || 'Unknown'}</span>
                              {edge.type === 'conditional' && (
                                <span className=\"text-xs text-yellow-600\">(conditional)</span>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  
                  {selectedNodeConnections.outgoing.length > 0 && (
                    <div>
                      <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Dependents</h4>
                      <div className=\"space-y-1\">
                        {selectedNodeConnections.outgoing.map((edge, index) => {
                          const depNode = graphData.nodes.find(n => n.id === edge.to);
                          return (
                            <div key={index} className=\"flex items-center space-x-2 text-sm\">
                              <div 
                                className=\"w-2 h-2 rounded-full\"
                                style={{ backgroundColor: statusConfig[depNode?.status || 'pending'].color }}
                              ></div>
                              <span>{depNode?.label || 'Unknown'}</span>
                              {edge.type === 'conditional' && (
                                <span className=\"text-xs text-yellow-600\">(conditional)</span>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default JobDependencyGraph;