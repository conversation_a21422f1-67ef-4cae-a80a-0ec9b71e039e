import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { logout } from '../../store/authSlice';
import { fetchResumes } from '../../store/resumeSlice';
import type { AppDispatch, RootState } from '../../store';
import { useTheme } from '../../contexts/ThemeContext';
import { Button, SkeletonCard } from '../ui';
import PWABanner from '../ui/PWABanner';
import Resume from '../Resume';
import AIEnhancement from '../ai/AIEnhancement';
import JobBoard from '../jobs/JobBoard';
import AdvancedAnalyticsDashboard from '../analytics/AdvancedAnalyticsDashboard';
import TemplateBuilder from '../templates/TemplateBuilder';
import GamificationDashboard from './GamificationDashboard';
import CareerCoach from '../career-coach/CareerCoach';
import DocumentIntelligence from '../documents/DocumentIntelligence';
import JobMatching from '../job-matching/JobMatching';
import ActivityOverview from './ActivityOverview';
import LoopStatus from './LoopStatus';
import ResumeLibrary from './ResumeLibrary';
import AnalyticsSummary from './AnalyticsSummary';
import QuickActions from './QuickActions';
import { useRealTimeUpdates } from '../../hooks/useAPI';

export default function Dashboard() {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { resumes, isLoading } = useSelector((state: RootState) => state.resume);
  const { theme, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('overview');

  // Real-time updates
  const { updates, isConnected } = useRealTimeUpdates(['applications', 'responses', 'resume_updates']);
  
  // Dashboard data from API (unused for now, but ready for integration)
  // const { data: dashboardData, loading: dashboardLoading } = useDashboardData();

  useEffect(() => {
    dispatch(fetchResumes());
  }, [dispatch]);

  // Handle real-time updates
  useEffect(() => {
    if (updates.length > 0) {
      const latestUpdate = updates[updates.length - 1];
      console.log('Received real-time update:', latestUpdate);
      // Handle different types of updates
      if (latestUpdate.type === 'application_status') {
        // Refresh application data
      } else if (latestUpdate.type === 'resume_update') {
        // Refresh resume data
        dispatch(fetchResumes());
      }
    }
  }, [updates, dispatch]);

  const handleLogout = () => {
    dispatch(logout());
  };

  const legacyResumeData = {
    name: 'Sample User',
    title: 'Software Engineer',
    summary: 'Experienced developer with full stack expertise',
    skills: ['JavaScript', 'React', 'Node.js'],
    experience: [
      {
        company: 'Tech Corp',
        role: 'Developer',
        start: '2021',
        end: '2024',
      },
    ],
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Enhanced Navigation Header */}
      <nav className="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 transition-colors duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center micro-pulse">
                  <span className="text-white font-bold text-sm">CV</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">CVLeap</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Welcome, {user?.name}</span>
              
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg glass hover:glass-heavy text-gray-600 dark:text-gray-300 transition-all duration-200 focus-enhanced micro-bounce"
                aria-label="Toggle theme"
              >
                {theme === 'light' ? '🌙' : '☀️'}
              </button>
              
              {/* Logout Button */}
              <Button
                onClick={handleLogout}
                variant="secondary"
                size="md"
                className="focus-enhanced"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* PWA Banner */}
          <PWABanner className="mb-6" />

          {/* Connection Status */}
          {!isConnected && (
            <div className="mb-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-yellow-700 dark:text-yellow-300">
                  Reconnecting to real-time updates...
                </span>
              </div>
            </div>
          )}
          {/* Enhanced Tab Navigation */}
          <div className="mb-8">
            <div className="glass-heavy rounded-2xl p-2 shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-300">
              <nav className="flex flex-wrap gap-1" aria-label="Tabs">
                {[
                  { id: 'overview', name: 'Dashboard Overview', icon: '🏠' },
                  { id: 'resumes', name: 'Resumes', icon: '📄' },
                  { id: 'templates', name: 'Smart Templates', icon: '🎨' },
                  { id: 'gamification', name: 'Progress & Goals', icon: '🏆' },
                  { id: 'analytics', name: 'Advanced Analytics', icon: '📊' },
                  { id: 'ai', name: 'AI Enhancement', icon: '🤖' },
                  { id: 'career-coach', name: 'Career Coach', icon: '🎯' },
                  { id: 'documents', name: 'Document Intelligence', icon: '📋' },
                  { id: 'job-matching', name: 'Job Matching', icon: '🔍' },
                  { id: 'jobs', name: 'Job Board', icon: '💼' },
                  { id: 'legacy', name: 'Legacy Editor', icon: '⚙️' },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`nav-tab-enhanced micro-bounce ${activeTab === tab.id ? 'active' : ''} focus-enhanced`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="hidden sm:inline">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Enhanced Tab Content */}
          <div className="animate-fade-in">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Welcome Section */}
                <div className="card-enhanced p-8 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 border border-primary-200 dark:border-primary-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h1 className="text-2xl font-bold text-primary-900 dark:text-primary-100 mb-2">
                        Welcome back, {user?.name || 'Professional'}! 👋
                      </h1>
                      <p className="text-primary-700 dark:text-primary-300">
                        Ready to accelerate your job search? Let's make today count.
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-primary-600 dark:text-primary-400">Today</p>
                      <p className="text-lg font-semibold text-primary-900 dark:text-primary-100">
                        {new Date().toLocaleDateString('en-US', { 
                          weekday: 'long', 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <QuickActions 
                  onCreateResume={() => setActiveTab('resumes')}
                  onSetupLoop={() => setActiveTab('jobs')}
                  onAIOptimize={() => setActiveTab('ai')}
                  onSearchJobs={() => setActiveTab('job-matching')}
                />

                {/* Main Dashboard Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Left Column - Activity & Status */}
                  <div className="lg:col-span-2 space-y-8">
                    <ActivityOverview />
                    <ResumeLibrary 
                      onResumeSelect={(resume) => console.log('Selected resume:', resume)}
                      onResumeEdit={() => setActiveTab('resumes')}
                    />
                  </div>

                  {/* Right Column - Loop Status & Analytics */}
                  <div className="space-y-8">
                    <LoopStatus />
                    <AnalyticsSummary />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'resumes' && (
              <div className="card-enhanced p-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white">Your Resumes</h2>
                  <Button
                    variant="primary"
                    size="lg"
                    leftIcon="➕"
                    className="focus-enhanced"
                  >
                    Create Resume
                  </Button>
                </div>
                
                {isLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, index) => (
                      <SkeletonCard key={index} />
                    ))}
                  </div>
                ) : resumes.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="text-6xl mb-4">📄</div>
                    <p className="text-fluid-base text-gray-600 dark:text-gray-400 mb-6">You haven't created any resumes yet.</p>
                    <Button
                      variant="primary"
                      size="xl"
                      className="focus-enhanced"
                    >
                      Create Your First Resume
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {resumes.map((resume, index) => (
                      <div 
                        key={resume.id} 
                        className="card-enhanced p-6 cursor-pointer micro-bounce animate-fade-in"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{resume.title}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                          Last updated: {new Date().toLocaleDateString()}
                        </p>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-primary-600 dark:text-primary-400 font-medium">View Resume</span>
                          <span className="text-lg transition-transform duration-200 group-hover:translate-x-1">→</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'templates' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Smart Resume Templates</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Choose from our collection of ATS-optimized, industry-specific templates designed for maximum impact.
                  </p>
                </div>
                <TemplateBuilder onTemplateSelect={(template) => {
                  console.log('Selected template:', template);
                  // Handle template selection
                }} />
              </div>
            )}

            {activeTab === 'gamification' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Progress & Goals</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Track your job search progress, set goals, and unlock achievements on your career journey.
                  </p>
                </div>
                <GamificationDashboard />
              </div>
            )}

            {activeTab === 'analytics' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Advanced Analytics Dashboard</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Get comprehensive insights into your job search performance, market trends, and optimization opportunities.
                  </p>
                </div>
                <AdvancedAnalyticsDashboard />
              </div>
            )}

            {activeTab === 'ai' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">AI Enhancement Tools</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Use AI to enhance your resume content, analyze ATS compatibility, and get skill suggestions.
                  </p>
                </div>
                <AIEnhancement resumeData={legacyResumeData} />
              </div>
            )}

            {activeTab === 'career-coach' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">AI Career Coach</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Get personalized career advice, skill gap analysis, interview preparation, and salary negotiation strategies.
                  </p>
                </div>
                <CareerCoach />
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Document Intelligence Suite</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Parse resumes, generate cover letters, create portfolios, and manage document versions with AI.
                  </p>
                </div>
                <DocumentIntelligence onDocumentParsed={(data) => {
                  console.log('Document parsed:', data);
                  // Handle parsed document data
                }} />
              </div>
            )}

            {activeTab === 'job-matching' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Advanced Job Matching Engine</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Get AI-powered job match scores, personalized recommendations, culture fit analysis, and market insights.
                  </p>
                </div>
                <JobMatching onMatchFound={(match) => {
                  console.log('Job match found:', match);
                  // Handle job match results
                }} />
              </div>
            )}

            {activeTab === 'jobs' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Job Board</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    Track your job applications and manage your job search progress.
                  </p>
                </div>
                <JobBoard />
              </div>
            )}

            {activeTab === 'legacy' && (
              <div className="card-enhanced p-8">
                <div className="mb-6">
                  <h2 className="text-fluid-lg font-bold text-gray-900 dark:text-white mb-2">Legacy Resume Editor</h2>
                  <p className="text-fluid-base text-gray-600 dark:text-gray-400">
                    This is the original resume editor for backward compatibility.
                  </p>
                </div>
                <Resume />
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}