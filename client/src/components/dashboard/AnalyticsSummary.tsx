import { TrendingUp, Send, Eye, MessageCircle, Calendar, Users, Target, Zap } from 'lucide-react';

interface KPI {
  id: string;
  label: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color: string;
}

interface AnalyticsSummaryProps {
  customKPIs?: KPI[];
  timeRange?: 'week' | 'month' | 'quarter';
  className?: string;
}

export default function AnalyticsSummary({ 
  customKPIs = [], 
  timeRange = 'week',
  className = '' 
}: AnalyticsSummaryProps) {
  // Mock KPI data
  const mockKPIs: KPI[] = [
    {
      id: 'applications',
      label: 'Applications Sent',
      value: 47,
      change: 23.1,
      changeType: 'increase',
      icon: <Send className="h-5 w-5" />,
      color: 'bg-blue-500'
    },
    {
      id: 'views',
      label: 'Profile Views',
      value: 156,
      change: 12.3,
      changeType: 'increase',
      icon: <Eye className="h-5 w-5" />,
      color: 'bg-green-500'
    },
    {
      id: 'responses',
      label: 'Responses',
      value: 8,
      change: -5.2,
      changeType: 'decrease',
      icon: <MessageCircle className="h-5 w-5" />,
      color: 'bg-yellow-500'
    },
    {
      id: 'interviews',
      label: 'Interviews',
      value: 3,
      change: 50.0,
      changeType: 'increase',
      icon: <Calendar className="h-5 w-5" />,
      color: 'bg-purple-500'
    },
    {
      id: 'response_rate',
      label: 'Response Rate',
      value: '17.2%',
      change: 2.8,
      changeType: 'increase',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'bg-indigo-500'
    },
    {
      id: 'network',
      label: 'Network Growth',
      value: 24,
      change: 8.7,
      changeType: 'increase',
      icon: <Users className="h-5 w-5" />,
      color: 'bg-pink-500'
    },
    {
      id: 'goal_progress',
      label: 'Goal Progress',
      value: '68%',
      change: 15.4,
      changeType: 'increase',
      icon: <Target className="h-5 w-5" />,
      color: 'bg-orange-500'
    },
    {
      id: 'optimization',
      label: 'AI Optimizations',
      value: 12,
      change: 0,
      changeType: 'neutral',
      icon: <Zap className="h-5 w-5" />,
      color: 'bg-cyan-500'
    }
  ];

  const displayKPIs = customKPIs.length > 0 ? customKPIs : mockKPIs;

  const getChangeIcon = (changeType: KPI['changeType']) => {
    if (changeType === 'increase') {
      return '↗️';
    } else if (changeType === 'decrease') {
      return '↘️';
    }
    return '➡️';
  };

  const getChangeColor = (changeType: KPI['changeType']) => {
    if (changeType === 'increase') {
      return 'text-green-600 dark:text-green-400';
    } else if (changeType === 'decrease') {
      return 'text-red-600 dark:text-red-400';
    }
    return 'text-gray-600 dark:text-gray-400';
  };

  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'quarter':
        return 'This Quarter';
      default:
        return 'This Week';
    }
  };

  return (
    <div className={`card-enhanced p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Analytics Summary</h3>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-600 dark:text-gray-400">{getTimeRangeLabel()}</span>
          <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
            View Details
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {displayKPIs.map((kpi, index) => (
          <div 
            key={kpi.id}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 animate-fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Icon and change indicator */}
            <div className="flex items-center justify-between mb-3">
              <div className={`p-2 rounded-lg ${kpi.color} text-white`}>
                {kpi.icon}
              </div>
              {kpi.change !== 0 && (
                <div className={`flex items-center space-x-1 ${getChangeColor(kpi.changeType)}`}>
                  <span className="text-xs font-medium">
                    {kpi.changeType === 'increase' ? '+' : ''}{kpi.change}%
                  </span>
                  <span className="text-xs">{getChangeIcon(kpi.changeType)}</span>
                </div>
              )}
            </div>

            {/* Value and label */}
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {kpi.value}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {kpi.label}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Quick insights */}
      <div className="mt-6 p-4 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg border border-primary-200 dark:border-primary-700">
        <div className="flex items-start space-x-3">
          <div className="p-2 bg-primary-100 dark:bg-primary-800 rounded-lg">
            <Zap className="h-4 w-4 text-primary-600 dark:text-primary-400" />
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-primary-900 dark:text-primary-100 mb-1">
              Performance Insights
            </h4>
            <p className="text-sm text-primary-700 dark:text-primary-300">
              Your response rate is 23% above average this week. Consider applying to 2-3 more positions daily to maximize momentum.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}