import React, { useState, useCallback, useRef, useEffect } from 'react';
import { 
  Plus, 
  Trash2, 
  Play, 
  Save, 
  Download, 
  Upload,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  ArrowRight,
  GitBranch,
  Zap
} from 'lucide-react';

interface JobNode {
  id: string;
  type: 'job' | 'condition' | 'parallel' | 'sequence';
  position: { x: number; y: number };
  data: {
    title: string;
    company?: string;
    position?: string;
    platform?: string;
    url?: string;
    priority?: number;
    conditions?: any;
    retryCount?: number;
    timeout?: number;
  };
  dependencies: string[];
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
}

interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type: 'dependency' | 'conditional' | 'parallel';
  conditions?: any;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: JobNode[];
  edges: WorkflowEdge[];
  settings: {
    maxParallelJobs: number;
    retryAttempts: number;
    timeout: number;
  };
  created: string;
  modified: string;
}

export const JobWorkflowBuilder: React.FC = () => {
  const [workflow, setWorkflow] = useState<Workflow>({
    id: '',
    name: 'New Workflow',
    description: '',
    nodes: [],
    edges: [],
    settings: {
      maxParallelJobs: 5,
      retryAttempts: 3,
      timeout: 600000
    },
    created: new Date().toISOString(),
    modified: new Date().toISOString()
  });

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const [showNodePalette, setShowNodePalette] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'completed' | 'failed'>('idle');

  const canvasRef = useRef<HTMLDivElement>(null);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);

  // Node palette items
  const nodePaletteItems = [
    { type: 'job', title: 'Job Application', icon: '💼', description: 'Apply to a specific job' },
    { type: 'condition', title: 'Condition', icon: '🔀', description: 'Conditional execution' },
    { type: 'parallel', title: 'Parallel Group', icon: '🔄', description: 'Execute jobs in parallel' },
    { type: 'sequence', title: 'Sequence', icon: '📋', description: 'Execute jobs in sequence' }
  ];

  // Platform options
  const platformOptions = [
    { value: 'linkedin', label: 'LinkedIn', color: '#0077B5' },
    { value: 'indeed', label: 'Indeed', color: '#2557A7' },
    { value: 'glassdoor', label: 'Glassdoor', color: '#0CAA41' },
    { value: 'monster', label: 'Monster', color: '#6E46AE' },
    { value: 'careerbuilder', label: 'CareerBuilder', color: '#F37A1F' }
  ];

  const addNode = useCallback((type: string, position: { x: number; y: number }) => {
    const newNode: JobNode = {
      id: `node_${Date.now()}`,
      type: type as JobNode['type'],
      position,
      data: {
        title: type === 'job' ? 'New Job Application' : `New ${type}`,
        priority: 5,
        retryCount: 3,
        timeout: 600000
      },
      dependencies: [],
      status: 'pending'
    };

    setWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      modified: new Date().toISOString()
    }));

    setSelectedNode(newNode.id);
    setShowNodePalette(false);
  }, []);

  const updateNode = useCallback((nodeId: string, updates: Partial<JobNode>) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      ),
      modified: new Date().toISOString()
    }));
  }, []);

  const deleteNode = useCallback((nodeId: string) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(node => node.id !== nodeId),
      edges: prev.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId),
      modified: new Date().toISOString()
    }));
    setSelectedNode(null);
  }, []);

  const addConnection = useCallback((sourceId: string, targetId: string) => {
    // Prevent self-connections and duplicate connections
    if (sourceId === targetId) return;
    if (workflow.edges.some(edge => edge.source === sourceId && edge.target === targetId)) return;

    const newEdge: WorkflowEdge = {
      id: `edge_${Date.now()}`,
      source: sourceId,
      target: targetId,
      type: 'dependency'
    };

    setWorkflow(prev => ({
      ...prev,
      edges: [...prev.edges, newEdge],
      modified: new Date().toISOString()
    }));

    // Update target node dependencies
    updateNode(targetId, {
      dependencies: [...(workflow.nodes.find(n => n.id === targetId)?.dependencies || []), sourceId]
    });
  }, [workflow.edges, workflow.nodes, updateNode]);

  const handleNodeMouseDown = useCallback((e: React.MouseEvent, nodeId: string) => {
    e.preventDefault();
    
    if (isConnecting) {
      if (connectionSource && connectionSource !== nodeId) {
        addConnection(connectionSource, nodeId);
        setIsConnecting(false);
        setConnectionSource(null);
      }
      return;
    }

    setDraggedNode(nodeId);
    setSelectedNode(nodeId);
    
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  }, [isConnecting, connectionSource, addConnection]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!draggedNode || !canvasRef.current) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const newPosition = {
      x: (e.clientX - canvasRect.left - dragOffset.x) / zoom,
      y: (e.clientY - canvasRect.top - dragOffset.y) / zoom
    };

    updateNode(draggedNode, { position: newPosition });
  }, [draggedNode, dragOffset, zoom, updateNode]);

  const handleMouseUp = useCallback(() => {
    setDraggedNode(null);
  }, []);

  const startConnection = useCallback((nodeId: string) => {
    setIsConnecting(true);
    setConnectionSource(nodeId);
  }, []);

  const executeWorkflow = useCallback(async () => {
    setExecutionStatus('running');
    
    try {
      const response = await fetch('/api/enhanced-applications/execute-workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          workflow: {
            ...workflow,
            nodes: workflow.nodes.map(node => ({
              ...node,
              status: 'pending'
            }))
          }
        })
      });

      if (response.ok) {
        setExecutionStatus('completed');
        // Here you would typically start polling for status updates
        // or connect to a WebSocket for real-time updates
      } else {
        setExecutionStatus('failed');
      }
    } catch (error) {
      console.error('Workflow execution failed:', error);
      setExecutionStatus('failed');
    }
  }, [workflow]);

  const saveWorkflow = useCallback(async () => {
    try {
      const response = await fetch('/api/workflows', {
        method: workflow.id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflow)
      });

      if (response.ok) {
        const savedWorkflow = await response.json();
        setWorkflow(savedWorkflow);
        alert('Workflow saved successfully!');
      } else {
        alert('Failed to save workflow');
      }
    } catch (error) {
      console.error('Save workflow failed:', error);
      alert('Failed to save workflow');
    }
  }, [workflow]);

  const exportWorkflow = useCallback(() => {
    const dataStr = JSON.stringify(workflow, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${workflow.name.replace(/\s+/g, '_')}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }, [workflow]);

  const importWorkflow = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedWorkflow = JSON.parse(e.target?.result as string);
        setWorkflow({
          ...importedWorkflow,
          id: '', // Reset ID for new workflow
          modified: new Date().toISOString()
        });
      } catch (error) {
        alert('Invalid workflow file');
      }
    };
    reader.readAsText(file);
  }, []);

  const getNodeStatusColor = (status?: string) => {
    switch (status) {
      case 'running': return 'border-blue-500 bg-blue-50';
      case 'completed': return 'border-green-500 bg-green-50';
      case 'failed': return 'border-red-500 bg-red-50';
      case 'cancelled': return 'border-gray-500 bg-gray-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const getNodeStatusIcon = (status?: string) => {
    switch (status) {
      case 'running': return <Clock className=\"h-4 w-4 text-blue-600\" />;
      case 'completed': return <CheckCircle className=\"h-4 w-4 text-green-600\" />;
      case 'failed': return <AlertCircle className=\"h-4 w-4 text-red-600\" />;
      default: return null;
    }
  };

  const selectedNodeData = selectedNode ? workflow.nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className=\"h-screen flex flex-col bg-gray-100\">
      {/* Header */}
      <div className=\"bg-white border-b border-gray-200 p-4\">
        <div className=\"flex items-center justify-between\">
          <div className=\"flex items-center space-x-4\">
            <input
              type=\"text\"
              value={workflow.name}
              onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
              className=\"text-xl font-bold bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded px-2\"
            />
            <span className={`px-2 py-1 rounded text-sm font-medium ${
              executionStatus === 'running' ? 'bg-blue-100 text-blue-800' :
              executionStatus === 'completed' ? 'bg-green-100 text-green-800' :
              executionStatus === 'failed' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {executionStatus.charAt(0).toUpperCase() + executionStatus.slice(1)}
            </span>
          </div>
          
          <div className=\"flex items-center space-x-2\">
            <button
              onClick={() => setShowNodePalette(true)}
              className=\"px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center space-x-2\"
            >
              <Plus className=\"h-4 w-4\" />
              <span>Add Node</span>
            </button>
            
            <button
              onClick={executeWorkflow}
              disabled={executionStatus === 'running' || workflow.nodes.length === 0}
              className=\"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2\"
            >
              <Play className=\"h-4 w-4\" />
              <span>Execute</span>
            </button>
            
            <button
              onClick={saveWorkflow}
              className=\"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2\"
            >
              <Save className=\"h-4 w-4\" />
              <span>Save</span>
            </button>
            
            <div className=\"flex items-center space-x-1\">
              <button
                onClick={exportWorkflow}
                className=\"p-2 text-gray-600 hover:text-gray-800\"
                title=\"Export Workflow\"
              >
                <Download className=\"h-4 w-4\" />
              </button>
              
              <label className=\"p-2 text-gray-600 hover:text-gray-800 cursor-pointer\" title=\"Import Workflow\">
                <Upload className=\"h-4 w-4\" />
                <input
                  type=\"file\"
                  accept=\".json\"
                  onChange={importWorkflow}
                  className=\"hidden\"
                />
              </label>
              
              <button
                onClick={() => setShowSettings(true)}
                className=\"p-2 text-gray-600 hover:text-gray-800\"
                title=\"Settings\"
              >
                <Settings className=\"h-4 w-4\" />
              </button>
            </div>
          </div>
        </div>
        
        <input
          type=\"text\"
          value={workflow.description}
          onChange={(e) => setWorkflow(prev => ({ ...prev, description: e.target.value }))}
          placeholder=\"Workflow description...\"
          className=\"mt-2 w-full text-sm text-gray-600 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded px-2\"
        />
      </div>

      <div className=\"flex-1 flex\">
        {/* Canvas */}
        <div className=\"flex-1 relative overflow-hidden\">
          <div
            ref={canvasRef}
            className=\"w-full h-full bg-gray-50 relative cursor-grab\"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            style={{ transform: `scale(${zoom}) translate(${canvasOffset.x}px, ${canvasOffset.y}px)` }}
          >
            {/* Grid */}
            <svg className=\"absolute inset-0 w-full h-full pointer-events-none\">
              <defs>
                <pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">
                  <path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"#e5e7eb\" strokeWidth=\"1\" />
                </pattern>
              </defs>
              <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />
            </svg>

            {/* Edges */}
            <svg className=\"absolute inset-0 w-full h-full pointer-events-none\">
              {workflow.edges.map((edge) => {
                const sourceNode = workflow.nodes.find(n => n.id === edge.source);
                const targetNode = workflow.nodes.find(n => n.id === edge.target);
                
                if (!sourceNode || !targetNode) return null;
                
                const sourceX = sourceNode.position.x + 100; // Node width / 2
                const sourceY = sourceNode.position.y + 40; // Node height / 2
                const targetX = targetNode.position.x;
                const targetY = targetNode.position.y + 40;
                
                return (
                  <g key={edge.id}>
                    <path
                      d={`M ${sourceX} ${sourceY} Q ${(sourceX + targetX) / 2} ${sourceY} ${targetX} ${targetY}`}
                      fill=\"none\"
                      stroke=\"#6b7280\"
                      strokeWidth=\"2\"
                      markerEnd=\"url(#arrowhead)\"
                    />
                  </g>
                );
              })}
              
              {/* Arrow marker */}
              <defs>
                <marker
                  id=\"arrowhead\"
                  markerWidth=\"10\"
                  markerHeight=\"7\"
                  refX=\"9\"
                  refY=\"3.5\"
                  orient=\"auto\"
                >
                  <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#6b7280\" />
                </marker>
              </defs>
            </svg>

            {/* Nodes */}
            {workflow.nodes.map((node) => (
              <div
                key={node.id}
                className={`absolute w-48 bg-white border-2 rounded-lg shadow-md cursor-pointer select-none ${
                  getNodeStatusColor(node.status)
                } ${selectedNode === node.id ? 'ring-2 ring-indigo-500' : ''}`}
                style={{
                  left: node.position.x,
                  top: node.position.y,
                  zIndex: selectedNode === node.id ? 10 : 1
                }}
                onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
              >
                <div className=\"p-3\">
                  <div className=\"flex items-center justify-between mb-2\">
                    <div className=\"flex items-center space-x-2\">
                      <span className=\"text-lg\">
                        {node.type === 'job' ? '💼' : 
                         node.type === 'condition' ? '🔀' :
                         node.type === 'parallel' ? '🔄' : '📋'}
                      </span>
                      {getNodeStatusIcon(node.status)}
                    </div>
                    <div className=\"flex items-center space-x-1\">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          startConnection(node.id);
                        }}
                        className=\"p-1 text-gray-400 hover:text-blue-600\"
                        title=\"Connect\"
                      >
                        <GitBranch className=\"h-3 w-3\" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteNode(node.id);
                        }}
                        className=\"p-1 text-gray-400 hover:text-red-600\"
                        title=\"Delete\"
                      >
                        <Trash2 className=\"h-3 w-3\" />
                      </button>
                    </div>
                  </div>
                  
                  <h4 className=\"font-medium text-sm text-gray-900 mb-1\">
                    {node.data.title}
                  </h4>
                  
                  {node.type === 'job' && (
                    <div className=\"text-xs text-gray-600 space-y-1\">
                      {node.data.company && <div>🏢 {node.data.company}</div>}
                      {node.data.platform && (
                        <div className=\"flex items-center space-x-1\">
                          <div 
                            className=\"w-2 h-2 rounded-full\"
                            style={{ 
                              backgroundColor: platformOptions.find(p => p.value === node.data.platform)?.color || '#gray' 
                            }}
                          ></div>
                          <span>{platformOptions.find(p => p.value === node.data.platform)?.label}</span>
                        </div>
                      )}
                      <div className=\"flex items-center space-x-1\">
                        <Zap className=\"h-3 w-3\" />
                        <span>Priority: {node.data.priority || 5}</span>
                      </div>
                    </div>
                  )}
                  
                  {node.dependencies.length > 0 && (
                    <div className=\"mt-2 text-xs text-gray-500\">
                      Depends on {node.dependencies.length} node(s)
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Canvas Controls */}
          <div className=\"absolute bottom-4 left-4 bg-white rounded-lg shadow-md p-2 flex items-center space-x-2\">
            <button
              onClick={() => setZoom(prev => Math.max(0.5, prev - 0.1))}
              className=\"p-1 text-gray-600 hover:text-gray-800\"
            >
              -
            </button>
            <span className=\"text-sm text-gray-600 min-w-12 text-center\">
              {(zoom * 100).toFixed(0)}%
            </span>
            <button
              onClick={() => setZoom(prev => Math.min(2, prev + 0.1))}
              className=\"p-1 text-gray-600 hover:text-gray-800\"
            >
              +
            </button>
          </div>
        </div>

        {/* Property Panel */}
        {selectedNodeData && (
          <div className=\"w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto\">
            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">
              Node Properties
            </h3>
            
            <div className=\"space-y-4\">
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Title
                </label>
                <input
                  type=\"text\"
                  value={selectedNodeData.data.title}
                  onChange={(e) => updateNode(selectedNode!, {
                    data: { ...selectedNodeData.data, title: e.target.value }
                  })}
                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                />
              </div>
              
              {selectedNodeData.type === 'job' && (
                <>
                  <div>
                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                      Company
                    </label>
                    <input
                      type=\"text\"
                      value={selectedNodeData.data.company || ''}
                      onChange={(e) => updateNode(selectedNode!, {
                        data: { ...selectedNodeData.data, company: e.target.value }
                      })}
                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                    />
                  </div>
                  
                  <div>
                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                      Position
                    </label>
                    <input
                      type=\"text\"
                      value={selectedNodeData.data.position || ''}
                      onChange={(e) => updateNode(selectedNode!, {
                        data: { ...selectedNodeData.data, position: e.target.value }
                      })}
                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                    />
                  </div>
                  
                  <div>
                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                      Platform
                    </label>
                    <select
                      value={selectedNodeData.data.platform || ''}
                      onChange={(e) => updateNode(selectedNode!, {
                        data: { ...selectedNodeData.data, platform: e.target.value }
                      })}
                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                    >
                      <option value=\"\">Select platform...</option>
                      {platformOptions.map(platform => (
                        <option key={platform.value} value={platform.value}>
                          {platform.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                      Job URL
                    </label>
                    <input
                      type=\"url\"
                      value={selectedNodeData.data.url || ''}
                      onChange={(e) => updateNode(selectedNode!, {
                        data: { ...selectedNodeData.data, url: e.target.value }
                      })}
                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                    />
                  </div>
                </>
              )}
              
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Priority (1-10)
                </label>
                <input
                  type=\"number\"
                  min=\"1\"
                  max=\"10\"
                  value={selectedNodeData.data.priority || 5}
                  onChange={(e) => updateNode(selectedNode!, {
                    data: { ...selectedNodeData.data, priority: parseInt(e.target.value) }
                  })}
                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                />
              </div>
              
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Retry Count
                </label>
                <input
                  type=\"number\"
                  min=\"0\"
                  max=\"10\"
                  value={selectedNodeData.data.retryCount || 3}
                  onChange={(e) => updateNode(selectedNode!, {
                    data: { ...selectedNodeData.data, retryCount: parseInt(e.target.value) }
                  })}
                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"
                />
              </div>
              
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Dependencies
                </label>
                <div className=\"text-sm text-gray-600\">
                  {selectedNodeData.dependencies.length === 0 ? (
                    <p>No dependencies</p>
                  ) : (
                    <ul className=\"space-y-1\">
                      {selectedNodeData.dependencies.map(depId => {
                        const depNode = workflow.nodes.find(n => n.id === depId);
                        return (
                          <li key={depId} className=\"flex items-center space-x-2\">
                            <ArrowRight className=\"h-3 w-3\" />
                            <span>{depNode?.data.title || 'Unknown'}</span>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Node Palette Modal */}
      {showNodePalette && (
        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">
          <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">
            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Node</h3>
            <div className=\"grid grid-cols-2 gap-4\">
              {nodePaletteItems.map((item) => (
                <button
                  key={item.type}
                  onClick={() => addNode(item.type, { x: 100, y: 100 })}
                  className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"
                >
                  <div className=\"text-2xl mb-2\">{item.icon}</div>
                  <div className=\"font-medium text-sm\">{item.title}</div>
                  <div className=\"text-xs text-gray-600\">{item.description}</div>
                </button>
              ))}
            </div>
            <div className=\"mt-4 flex justify-end\">
              <button
                onClick={() => setShowNodePalette(false)}
                className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Connection indicator */}
      {isConnecting && (
        <div className=\"fixed top-4 left-1/2 transform -translate-x-1/2 bg-blue-100 text-blue-800 px-4 py-2 rounded-lg z-50\">
          Click on a target node to create connection
        </div>
      )}
    </div>
  );
};

export default JobWorkflowBuilder;