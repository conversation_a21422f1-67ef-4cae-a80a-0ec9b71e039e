/**
 * Test for PWA Offline Sync Functionality
 * Tests the enhanced offline sync features
 */

import { OfflineStorage, ConflictResolution, SyncStatus } from '../hooks/usePWA';

// Mock localStorage
const localStorageMock = (() => {
  let store: { [key: string]: string } = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    key: (index: number) => Object.keys(store)[index] || null,
    get length() { return Object.keys(store).length; },
    clear: () => { store = {}; }
  };
})();

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;
global.localStorage = localStorageMock;

// Mock console to reduce test noise
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

describe('OfflineStorage Sync Functionality', () => {
  beforeEach(() => {
    localStorageMock.clear();
    mockFetch.mockClear();
    jest.clearAllMocks();
  });

  describe('Data Storage and Retrieval', () => {
    test('should store and retrieve data with metadata', async () => {
      const testData = { name: 'John Doe', title: 'Software Engineer' };
      const key = 'test-resume';

      await OfflineStorage.storeData(key, testData);
      const retrieved = await OfflineStorage.getData(key);

      expect(retrieved).toEqual(testData);
    });

    test('should handle stale data removal', async () => {
      const testData = { name: 'John Doe' };
      const key = 'stale-data';

      // Store data with old timestamp
      const oldData = {
        data: testData,
        timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago
        version: 1
      };
      localStorageMock.setItem(key, JSON.stringify(oldData));

      const retrieved = await OfflineStorage.getData(key);
      expect(retrieved).toBeNull();
      expect(localStorageMock.getItem(key)).toBeNull();
    });
  });

  describe('Pending Changes Detection', () => {
    test('should identify pending changes correctly', async () => {
      // Add some pending data
      await OfflineStorage.storeData('pending-resume-update-123', { title: 'Updated Resume' });
      await OfflineStorage.storeData('mobile-resume-draft', { sections: [] });
      await OfflineStorage.storeData('normal-data', { other: 'data' });

      const pendingKeys = Object.keys(localStorageMock)
        .filter(key => key.startsWith('pending-') || key.startsWith('mobile-resume-draft'));

      expect(pendingKeys).toHaveLength(2);
      expect(pendingKeys).toContain('pending-resume-update-123');
      expect(pendingKeys).toContain('mobile-resume-draft');
    });
  });

  describe('Sync Operations', () => {
    test('should sync resume updates successfully', async () => {
      const resumeData = { title: 'Test Resume', content: 'Updated content' };
      await OfflineStorage.storeData('pending-resume-update-123', resumeData);

      // Mock successful API response
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([{ id: '123', updatedAt: '2023-01-01' }])
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        });

      const syncStatus = await OfflineStorage.syncPendingChanges();

      expect(syncStatus.completedItems).toBe(1);
      expect(syncStatus.errors).toHaveLength(0);
      expect(localStorageMock.getItem('pending-resume-update-123')).toBeNull();
    });

    test('should handle sync errors gracefully', async () => {
      await OfflineStorage.storeData('pending-resume-update-456', { title: 'Test' });

      // Mock API failure
      mockFetch.mockRejectedValue(new Error('Network error'));

      const syncStatus = await OfflineStorage.syncPendingChanges();

      expect(syncStatus.errors).toHaveLength(1);
      expect(syncStatus.errors[0].key).toBe('pending-resume-update-456');
      expect(syncStatus.errors[0].error).toContain('Network error');
    });

    test('should track sync progress correctly', async () => {
      // Add multiple pending items
      await OfflineStorage.storeData('pending-resume-update-1', { title: 'Resume 1' });
      await OfflineStorage.storeData('pending-resume-update-2', { title: 'Resume 2' });
      await OfflineStorage.storeData('pending-application-1', { jobId: 'job1' });

      const statusUpdates: SyncStatus[] = [];
      const unsubscribe = OfflineStorage.onSyncStatusChange((status) => {
        statusUpdates.push({ ...status });
      });

      // Mock successful API responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });

      await OfflineStorage.syncPendingChanges();
      unsubscribe();

      // Should have status updates showing progress
      expect(statusUpdates.length).toBeGreaterThan(0);
      const finalStatus = statusUpdates[statusUpdates.length - 1];
      expect(finalStatus.isActive).toBe(false);
      expect(finalStatus.progress).toBe(100);
      expect(finalStatus.totalItems).toBe(3);
    });
  });

  describe('Conflict Resolution', () => {
    test('should handle server-wins conflict resolution', async () => {
      const localData = { title: 'Local Version', updatedAt: '2023-01-01' };
      const serverData = { title: 'Server Version', updatedAt: '2023-01-02' };

      // Access private method for testing
      const resolveConflict = (OfflineStorage as any).resolveConflict;
      const result = await resolveConflict(localData, serverData, ConflictResolution.SERVER_WINS);

      expect(result).toEqual(serverData);
    });

    test('should handle merge conflict resolution', async () => {
      const localData = { title: 'Local Version', description: 'Local desc' };
      const serverData = { title: 'Server Version', tags: ['tag1'] };

      const resolveConflict = (OfflineStorage as any).resolveConflict;
      const result = await resolveConflict(localData, serverData, ConflictResolution.MERGE);

      expect(result).toMatchObject({
        title: 'Local Version', // Local wins in merge
        description: 'Local desc',
        tags: ['tag1']
      });
    });
  });

  describe('Retry Logic', () => {
    test('should retry failed operations with exponential backoff', async () => {
      let attemptCount = 0;
      const failingOperation = jest.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Temporary failure');
        }
        return Promise.resolve('success');
      });

      const retryWithBackoff = (OfflineStorage as any).retryWithBackoff;
      const result = await retryWithBackoff(failingOperation, 3, 'test');

      expect(result).toBe('success');
      expect(failingOperation).toHaveBeenCalledTimes(3);
    });

    test('should fail after max retry attempts', async () => {
      const alwaysFailingOperation = jest.fn().mockRejectedValue(new Error('Persistent failure'));

      const retryWithBackoff = (OfflineStorage as any).retryWithBackoff;
      
      await expect(
        retryWithBackoff(alwaysFailingOperation, 2, 'test')
      ).rejects.toThrow('Persistent failure');

      expect(alwaysFailingOperation).toHaveBeenCalledTimes(2);
    });
  });

  describe('Background Sync Integration', () => {
    test('should handle background sync registration', async () => {
      const mockRegistration = {
        sync: {
          register: jest.fn().mockResolvedValue(undefined)
        }
      };

      // Mock service worker ready
      Object.defineProperty(navigator, 'serviceWorker', {
        value: {
          ready: Promise.resolve(mockRegistration)
        },
        configurable: true
      });

      await OfflineStorage.requestBackgroundSync('test-sync');

      expect(mockRegistration.sync.register).toHaveBeenCalledWith('test-sync');
    });

    test('should fallback to immediate sync when background sync fails', async () => {
      // Mock service worker not available
      Object.defineProperty(navigator, 'serviceWorker', {
        value: undefined,
        configurable: true
      });

      const syncSpy = jest.spyOn(OfflineStorage, 'syncPendingChanges').mockResolvedValue({
        isActive: false,
        progress: 100,
        currentItem: null,
        totalItems: 0,
        completedItems: 0,
        errors: []
      });

      await OfflineStorage.requestBackgroundSync('test-sync');

      expect(syncSpy).toHaveBeenCalled();
      syncSpy.mockRestore();
    });
  });

  describe('Data Type Handling', () => {
    test('should handle different data types correctly', async () => {
      // Store different types of pending data
      await OfflineStorage.storeData('pending-resume-create', { title: 'New Resume' });
      await OfflineStorage.storeData('pending-application-123', { jobId: 'job123' });
      await OfflineStorage.storeData('pending-profile-update', { name: 'John' });

      // Mock successful API responses for different endpoints
      mockFetch.mockImplementation((url) => {
        if (url.includes('/api/resumes') && !url.includes('/api/resumes/')) {
          return Promise.resolve({ ok: true, json: () => Promise.resolve({ id: 'new-id' }) });
        }
        if (url.includes('/api/applications/queue')) {
          return Promise.resolve({ ok: true, json: () => Promise.resolve({ queued: true }) });
        }
        return Promise.resolve({ ok: true, json: () => Promise.resolve({ success: true }) });
      });

      const syncStatus = await OfflineStorage.syncPendingChanges();

      expect(syncStatus.completedItems).toBe(3);
      expect(syncStatus.errors).toHaveLength(0);
    });
  });
});

// Run tests manually if this file is executed directly
if (typeof module !== 'undefined' && require.main === module) {
  console.log('Running PWA Sync Tests...');
  
  // Simple test runner for basic verification
  const runBasicTests = async () => {
    try {
      console.log('Testing data storage...');
      const testData = { name: 'Test User' };
      await OfflineStorage.storeData('test-key', testData);
      const retrieved = await OfflineStorage.getData('test-key');
      console.log('✓ Data storage and retrieval works:', retrieved);

      console.log('Testing sync status tracking...');
      const status = OfflineStorage.getSyncStatus();
      console.log('✓ Sync status:', status);

      console.log('All basic tests passed!');
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  runBasicTests();
}

export default {};