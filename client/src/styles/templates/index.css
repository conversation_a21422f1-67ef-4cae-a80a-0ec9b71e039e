/* Template Library Styles */

/* Base template styles */
.template-container {
  @apply bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden;
}

.template-preview {
  @apply relative bg-white;
  min-height: 297mm; /* A4 height */
  width: 210mm; /* A4 width */
}

/* Professional Template Styles */
.template-professional {
  @apply font-sans;
}

.template-professional .header {
  @apply border-b-2 border-gray-300 pb-4 mb-6;
}

.template-professional .section-title {
  @apply text-lg font-bold text-gray-900 mb-3 uppercase tracking-wide;
}

.template-professional .section-content {
  @apply space-y-3;
}

/* Creative Template Styles */
.template-creative {
  @apply font-sans;
}

.template-creative .header {
  @apply bg-gradient-to-r from-purple-500 to-pink-500 text-white p-6 rounded-t-lg;
}

.template-creative .section-title {
  @apply text-xl font-bold text-purple-700 mb-4 border-l-4 border-purple-500 pl-3;
}

.template-creative .section-content {
  @apply space-y-4;
}

/* Industry-Specific Template Styles */
.template-industry {
  @apply font-serif;
}

.template-industry.healthcare .header {
  @apply border-b-4 border-green-500;
}

.template-industry.education .header {
  @apply border-b-4 border-blue-500;
}

.template-industry.engineering .header {
  @apply border-b-4 border-gray-600;
}

.template-industry.legal .header {
  @apply border-b-4 border-indigo-600;
}

.template-industry .section-title {
  @apply text-lg font-semibold text-gray-800 mb-3 pb-1 border-b border-gray-300;
}

/* Experience Level Template Styles */
.template-entry-level {
  @apply font-sans;
}

.template-entry-level .header {
  @apply text-center pb-4 mb-6 border-b border-gray-200;
}

.template-entry-level .section-title {
  @apply text-lg font-semibold text-blue-600 mb-3;
}

.template-mid-career {
  @apply font-sans;
}

.template-mid-career .header {
  @apply grid grid-cols-2 gap-6 pb-4 mb-6 border-b-2 border-gray-400;
}

.template-mid-career .section-title {
  @apply text-lg font-bold text-gray-700 mb-3 uppercase;
}

.template-senior-executive {
  @apply font-serif;
}

.template-senior-executive .header {
  @apply text-center py-6 mb-8 border-t-4 border-b-4 border-gray-800;
}

.template-senior-executive .section-title {
  @apply text-xl font-bold text-gray-900 mb-4 text-center;
}

/* Layout Variations */
.layout-single-column {
  @apply max-w-full;
}

.layout-two-column {
  @apply grid grid-cols-3 gap-8;
}

.layout-two-column .main-content {
  @apply col-span-2;
}

.layout-two-column .sidebar {
  @apply col-span-1 bg-gray-50 p-4 rounded;
}

.layout-three-column {
  @apply grid grid-cols-4 gap-6;
}

.layout-three-column .main-content {
  @apply col-span-2;
}

.layout-three-column .left-sidebar {
  @apply col-span-1;
}

.layout-three-column .right-sidebar {
  @apply col-span-1;
}

.layout-sidebar {
  @apply grid grid-cols-4 gap-0;
}

.layout-sidebar .sidebar {
  @apply col-span-1 bg-gray-100 p-6;
}

.layout-sidebar .main-content {
  @apply col-span-3 p-6;
}

/* Color Scheme Utilities */
.color-scheme-classic-blue {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --accent-color: #10b981;
}

.color-scheme-corporate-navy {
  --primary-color: #1e293b;
  --secondary-color: #475569;
  --accent-color: #0ea5e9;
}

.color-scheme-executive-black {
  --primary-color: #000000;
  --secondary-color: #374151;
  --accent-color: #dc2626;
}

.color-scheme-vibrant-orange {
  --primary-color: #ea580c;
  --secondary-color: #78716c;
  --accent-color: #06b6d4;
}

.color-scheme-modern-pink {
  --primary-color: #ec4899;
  --secondary-color: #64748b;
  --accent-color: #10b981;
}

.color-scheme-creative-purple {
  --primary-color: #a855f7;
  --secondary-color: #6b7280;
  --accent-color: #f59e0b;
}

/* Spacing Variations */
.spacing-compact {
  @apply space-y-2;
}

.spacing-compact .section {
  @apply mb-4;
}

.spacing-normal {
  @apply space-y-4;
}

.spacing-normal .section {
  @apply mb-6;
}

.spacing-spacious {
  @apply space-y-6;
}

.spacing-spacious .section {
  @apply mb-8;
}

/* Typography Styles */
.font-arial {
  font-family: Arial, sans-serif;
}

.font-calibri {
  font-family: Calibri, sans-serif;
}

.font-times {
  font-family: 'Times New Roman', serif;
}

.font-georgia {
  font-family: Georgia, serif;
}

.font-helvetica {
  font-family: Helvetica, sans-serif;
}

.font-inter {
  font-family: Inter, sans-serif;
}

/* Section Styling Options */
.section-header {
  @apply flex items-center space-x-2 mb-3;
}

.section-divider {
  @apply border-b border-gray-300 pb-1 mb-3;
}

.bullet-dots li:before {
  content: '•';
  @apply text-gray-600 mr-2;
}

.bullet-lines li:before {
  content: '—';
  @apply text-gray-600 mr-2;
}

.bullet-arrows li:before {
  content: '→';
  @apply text-gray-600 mr-2;
}

/* Icon Styles */
.section-icon {
  @apply w-5 h-5 text-gray-600;
}

/* Date Format Styles */
.date-short {
  @apply text-sm text-gray-500 font-medium;
}

.date-long {
  @apply text-sm text-gray-600 italic;
}

/* ATS Optimization Styles */
.ats-optimized {
  @apply font-sans;
}

.ats-optimized h1,
.ats-optimized h2,
.ats-optimized h3 {
  @apply font-bold text-black;
}

.ats-optimized .section-title {
  @apply text-lg font-bold text-black mb-3 uppercase;
}

.ats-optimized ul {
  @apply list-disc list-inside;
}

.ats-optimized .contact-info {
  @apply text-center mb-6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .template-preview {
    width: 100%;
    min-height: auto;
  }
  
  .layout-two-column,
  .layout-three-column,
  .layout-sidebar {
    @apply grid-cols-1;
  }
  
  .layout-two-column .sidebar,
  .layout-sidebar .sidebar {
    @apply order-first;
  }
}

/* Print Styles */
@media print {
  .template-preview {
    @apply shadow-none border-none;
    width: 210mm;
    min-height: 297mm;
  }
  
  .template-container {
    @apply shadow-none border-none;
  }
  
  body {
    @apply bg-white;
  }
}

/* Animation Classes */
.template-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hover Effects */
.template-hover-lift:hover {
  @apply transform -translate-y-1 shadow-lg;
  transition: all 0.2s ease-in-out;
}

.template-hover-glow:hover {
  @apply shadow-lg ring-2 ring-blue-500 ring-opacity-50;
  transition: all 0.2s ease-in-out;
}

/* Grid Background for Preview */
.bg-grid {
  background-image: 
    linear-gradient(to right, #f3f4f6 1px, transparent 1px),
    linear-gradient(to bottom, #f3f4f6 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Loading Skeleton */
.template-skeleton {
  @apply animate-pulse;
}

.template-skeleton .skeleton-line {
  @apply bg-gray-200 rounded h-4 mb-2;
}

.template-skeleton .skeleton-title {
  @apply bg-gray-300 rounded h-6 mb-4;
}

.template-skeleton .skeleton-image {
  @apply bg-gray-200 rounded;
}