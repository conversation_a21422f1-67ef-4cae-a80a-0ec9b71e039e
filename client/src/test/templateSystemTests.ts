import { 
  RESUME_TEMPLATES, 
  getTemplateStats, 
  getTemplatesByCategory,
  getTemplateById,
  getAvailableCategories,
  getAvailableIndustries,
  getATSCompatibleTemplates
} from '../constants/resumeTemplates';

interface TestResult {
  test: string;
  passed: boolean;
  details?: string;
}

export function runTemplateSystemTests(): TestResult[] {
  const results: TestResult[] = [];

  // Test 1: Total template count meets requirement (100+)
  const stats = getTemplateStats();
  results.push({
    test: 'Total template count >= 100',
    passed: stats.total >= 100,
    details: `Found ${stats.total} templates`
  });

  // Test 2: All required categories are present
  const categories = getAvailableCategories();
  const requiredCategories = ['professional', 'creative', 'industry-specific', 'ats-optimized'];
  const hasCategoriesTest = requiredCategories.every(cat => categories.includes(cat));
  results.push({
    test: 'Required categories present',
    passed: hasCategoriesTest,
    details: `Categories: ${categories.join(', ')}`
  });

  // Test 3: Templates have color schemes
  const templatesWithColorSchemes = RESUME_TEMPLATES.filter(t => t.colorSchemes && t.colorSchemes.length > 0);
  results.push({
    test: 'Templates have color scheme variations',
    passed: templatesWithColorSchemes.length > 50,
    details: `${templatesWithColorSchemes.length}/${RESUME_TEMPLATES.length} templates have color schemes`
  });

  // Test 4: ATS compatibility is marked
  const atsTemplates = getATSCompatibleTemplates();
  results.push({
    test: 'ATS compatible templates exist',
    passed: atsTemplates.length > 50,
    details: `${atsTemplates.length} ATS-compatible templates`
  });

  // Test 5: Template ID lookup works
  const testTemplate = getTemplateById('professional-1');
  results.push({
    test: 'Template lookup by ID works',
    passed: testTemplate !== null,
    details: testTemplate ? `Found: ${testTemplate.name}` : 'Template not found'
  });

  // Test 6: Category filtering works
  const professionalTemplates = getTemplatesByCategory('professional');
  results.push({
    test: 'Category filtering works',
    passed: professionalTemplates.length > 0,
    details: `${professionalTemplates.length} professional templates`
  });

  // Test 7: Multiple industries supported
  const industries = getAvailableIndustries();
  results.push({
    test: 'Multiple industries supported',
    passed: industries.length >= 8,
    details: `${industries.length} industries: ${industries.slice(0, 5).join(', ')}...`
  });

  // Test 8: Template metadata completeness
  const completeTemplates = RESUME_TEMPLATES.filter(t => 
    t.id && t.name && t.description && t.category && 
    t.industry && t.styles && t.sections && t.features
  );
  results.push({
    test: 'Template metadata complete',
    passed: completeTemplates.length === RESUME_TEMPLATES.length,
    details: `${completeTemplates.length}/${RESUME_TEMPLATES.length} templates complete`
  });

  // Test 9: Premium vs Free templates
  results.push({
    test: 'Mix of premium and free templates',
    passed: stats.free > 0 && stats.premium > 0,
    details: `${stats.free} free, ${stats.premium} premium`
  });

  // Test 10: Template sections are properly configured
  const templatesWithSections = RESUME_TEMPLATES.filter(t => t.sections && t.sections.length > 0);
  results.push({
    test: 'Templates have section configurations',
    passed: templatesWithSections.length === RESUME_TEMPLATES.length,
    details: `${templatesWithSections.length} templates have sections`
  });

  return results;
}

export function displayTestResults(): void {
  const results = runTemplateSystemTests();
  
  console.log('🧪 Template System Test Results\n');
  
  let passed = 0;
  const total = results.length;
  
  results.forEach(result => {
    const icon = result.passed ? '✅' : '❌';
    console.log(`${icon} ${result.test}`);
    if (result.details) {
      console.log(`   ${result.details}`);
    }
    if (result.passed) passed++;
  });
  
  console.log(`\n📊 Summary: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Template system is fully functional.');
  } else {
    console.log('⚠️ Some tests failed. Review the issues above.');
  }
  
  return results;
}