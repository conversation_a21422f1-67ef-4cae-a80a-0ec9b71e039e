import type { ResumeTemplate, TemplateSectionConfig, ColorScheme } from './resumeTemplates';

// Color scheme definitions for variations
export const COLOR_SCHEMES: Record<string, ColorScheme[]> = {
  professional: [
    { name: 'Classic Blue', primaryColor: '#2563eb', secondaryColor: '#64748b', accentColor: '#10b981' },
    { name: 'Corporate Navy', primaryColor: '#1e293b', secondaryColor: '#475569', accentColor: '#0ea5e9' },
    { name: 'Executive Black', primaryColor: '#000000', secondaryColor: '#374151', accentColor: '#dc2626' },
    { name: 'Professional Teal', primaryColor: '#0d9488', secondaryColor: '#6b7280', accentColor: '#f59e0b' },
    { name: 'Business Purple', primaryColor: '#7c3aed', secondaryColor: '#64748b', accentColor: '#10b981' },
    { name: '<PERSON> <PERSON>', primaryColor: '#374151', secondaryColor: '#6b7280', accentColor: '#3b82f6' }
  ],
  creative: [
    { name: 'Vibrant Orange', primaryColor: '#ea580c', secondaryColor: '#78716c', accentColor: '#06b6d4' },
    { name: 'Modern Pink', primaryColor: '#ec4899', secondaryColor: '#64748b', accentColor: '#10b981' },
    { name: 'Creative Purple', primaryColor: '#a855f7', secondaryColor: '#6b7280', accentColor: '#f59e0b' },
    { name: 'Designer Green', primaryColor: '#059669', secondaryColor: '#78716c', accentColor: '#dc2626' },
    { name: 'Artistic Blue', primaryColor: '#3b82f6', secondaryColor: '#64748b', accentColor: '#ec4899' },
    { name: 'Bold Red', primaryColor: '#dc2626', secondaryColor: '#6b7280', accentColor: '#0d9488' }
  ],
  technical: [
    { name: 'Code Blue', primaryColor: '#1e40af', secondaryColor: '#64748b', accentColor: '#06b6d4' },
    { name: 'Terminal Green', primaryColor: '#065f46', secondaryColor: '#6b7280', accentColor: '#10b981' },
    { name: 'Matrix Black', primaryColor: '#0f172a', secondaryColor: '#475569', accentColor: '#00ff00' },
    { name: 'Tech Purple', primaryColor: '#581c87', secondaryColor: '#64748b', accentColor: '#a855f7' },
    { name: 'Data Orange', primaryColor: '#c2410c', secondaryColor: '#78716c', accentColor: '#06b6d4' },
    { name: 'System Gray', primaryColor: '#1f2937', secondaryColor: '#6b7280', accentColor: '#3b82f6' }
  ],
  classic: [
    { name: 'Traditional Black', primaryColor: '#000000', secondaryColor: '#333333', accentColor: '#000000' },
    { name: 'Formal Navy', primaryColor: '#1e3a8a', secondaryColor: '#374151', accentColor: '#1e40af' },
    { name: 'Conservative Brown', primaryColor: '#92400e', secondaryColor: '#78716c', accentColor: '#d97706' },
    { name: 'Academic Blue', primaryColor: '#1e40af', secondaryColor: '#475569', accentColor: '#0ea5e9' },
    { name: 'Legal Gray', primaryColor: '#374151', secondaryColor: '#6b7280', accentColor: '#475569' },
    { name: 'Medical Green', primaryColor: '#065f46', secondaryColor: '#6b7280', accentColor: '#059669' }
  ],
  ats: [
    { name: 'ATS Black', primaryColor: '#000000', secondaryColor: '#333333', accentColor: '#000000' },
    { name: 'ATS Navy', primaryColor: '#1e3a8a', secondaryColor: '#374151', accentColor: '#1e40af' },
    { name: 'ATS Dark Gray', primaryColor: '#374151', secondaryColor: '#6b7280', accentColor: '#475569' },
    { name: 'ATS Charcoal', primaryColor: '#1f2937', secondaryColor: '#4b5563', accentColor: '#374151' },
    { name: 'ATS Deep Blue', primaryColor: '#1e40af', secondaryColor: '#475569', accentColor: '#0ea5e9' },
    { name: 'ATS Brown', primaryColor: '#92400e', secondaryColor: '#78716c', accentColor: '#d97706' }
  ]
};

// Base section configurations for different template types
export const SECTION_CONFIGS: Record<string, TemplateSectionConfig[]> = {
  standard: [
    {
      id: 'header',
      type: 'header',
      title: 'Personal Information',
      required: true,
      order: 1,
      layout: 'grid',
      styling: {
        showIcons: true,
        showDividers: false,
        bulletStyle: 'none',
        dateFormat: 'short'
      }
    },
    {
      id: 'summary',
      type: 'summary',
      title: 'Professional Summary',
      required: true,
      order: 2,
      layout: 'list',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'none',
        dateFormat: 'short'
      }
    },
    {
      id: 'experience',
      type: 'experience',
      title: 'Work Experience',
      required: true,
      order: 3,
      layout: 'timeline',
      styling: {
        showIcons: true,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    },
    {
      id: 'education',
      type: 'education',
      title: 'Education',
      required: true,
      order: 4,
      layout: 'list',
      styling: {
        showIcons: true,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'long'
      }
    },
    {
      id: 'skills',
      type: 'skills',
      title: 'Skills',
      required: true,
      order: 5,
      layout: 'grid',
      styling: {
        showIcons: false,
        showDividers: false,
        bulletStyle: 'none',
        dateFormat: 'short'
      }
    }
  ],
  executive: [
    {
      id: 'header',
      type: 'header',
      title: 'Executive Profile',
      required: true,
      order: 1,
      layout: 'list',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'lines',
        dateFormat: 'long'
      }
    },
    {
      id: 'summary',
      type: 'summary',
      title: 'Executive Summary',
      required: true,
      order: 2,
      layout: 'list',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'none',
        dateFormat: 'long'
      }
    },
    {
      id: 'experience',
      type: 'experience',
      title: 'Leadership Experience',
      required: true,
      order: 3,
      layout: 'timeline',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'lines',
        dateFormat: 'long'
      }
    },
    {
      id: 'education',
      type: 'education',
      title: 'Education & Certifications',
      required: true,
      order: 4,
      layout: 'list',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'lines',
        dateFormat: 'long'
      }
    }
  ],
  technical: [
    {
      id: 'header',
      type: 'header',
      title: 'Developer Profile',
      required: true,
      order: 1,
      layout: 'list',
      styling: {
        showIcons: true,
        showDividers: false,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    },
    {
      id: 'skills',
      type: 'skills',
      title: 'Technical Stack',
      required: true,
      order: 2,
      layout: 'grid',
      styling: {
        showIcons: false,
        showDividers: false,
        bulletStyle: 'none',
        dateFormat: 'short'
      }
    },
    {
      id: 'experience',
      type: 'experience',
      title: 'Professional Experience',
      required: true,
      order: 3,
      layout: 'timeline',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    },
    {
      id: 'projects',
      type: 'projects',
      title: 'Key Projects',
      required: true,
      order: 4,
      layout: 'list',
      styling: {
        showIcons: true,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    },
    {
      id: 'education',
      type: 'education',
      title: 'Education & Certifications',
      required: true,
      order: 5,
      layout: 'list',
      styling: {
        showIcons: false,
        showDividers: true,
        bulletStyle: 'dots',
        dateFormat: 'short'
      }
    }
  ]
};

// Template generation function
export function generateTemplates(): ResumeTemplate[] {
  const templates: ResumeTemplate[] = [];
  let templateId = 1;

  // Professional Templates (25 templates)
  const professionalTemplates = [
    { name: 'Corporate Professional', layout: 'two-column', font: 'Inter, sans-serif', spacing: 'normal' },
    { name: 'Business Executive', layout: 'single-column', font: 'Georgia, serif', spacing: 'spacious' },
    { name: 'Management Leader', layout: 'three-column', font: 'Helvetica, sans-serif', spacing: 'normal' },
    { name: 'Corporate Analyst', layout: 'single-column', font: 'Arial, sans-serif', spacing: 'compact' },
    { name: 'Business Consultant', layout: 'two-column', font: 'Calibri, sans-serif', spacing: 'normal' },
    { name: 'Finance Professional', layout: 'single-column', font: 'Times New Roman, serif', spacing: 'normal' },
    { name: 'Banking Executive', layout: 'two-column', font: 'Georgia, serif', spacing: 'spacious' },
    { name: 'Investment Advisor', layout: 'single-column', font: 'Inter, sans-serif', spacing: 'normal' },
    { name: 'Corporate Strategist', layout: 'two-column', font: 'Helvetica, sans-serif', spacing: 'normal' },
    { name: 'Business Development', layout: 'single-column', font: 'Arial, sans-serif', spacing: 'compact' },
    { name: 'Operations Manager', layout: 'two-column', font: 'Calibri, sans-serif', spacing: 'normal' },
    { name: 'Project Manager', layout: 'single-column', font: 'Inter, sans-serif', spacing: 'normal' },
    { name: 'Account Manager', layout: 'two-column', font: 'Helvetica, sans-serif', spacing: 'normal' },
    { name: 'Sales Director', layout: 'single-column', font: 'Georgia, serif', spacing: 'spacious' },
    { name: 'Marketing Manager', layout: 'two-column', font: 'Arial, sans-serif', spacing: 'normal' },
    { name: 'HR Professional', layout: 'single-column', font: 'Calibri, sans-serif', spacing: 'normal' },
    { name: 'Legal Advisor', layout: 'two-column', font: 'Times New Roman, serif', spacing: 'normal' },
    { name: 'Compliance Officer', layout: 'single-column', font: 'Inter, sans-serif', spacing: 'compact' },
    { name: 'Risk Manager', layout: 'two-column', font: 'Helvetica, sans-serif', spacing: 'normal' },
    { name: 'Quality Assurance', layout: 'single-column', font: 'Arial, sans-serif', spacing: 'normal' },
    { name: 'Program Manager', layout: 'three-column', font: 'Georgia, serif', spacing: 'spacious' },
    { name: 'Strategy Director', layout: 'single-column', font: 'Calibri, sans-serif', spacing: 'normal' },
    { name: 'Business Analyst', layout: 'two-column', font: 'Inter, sans-serif', spacing: 'normal' },
    { name: 'Operations Director', layout: 'single-column', font: 'Times New Roman, serif', spacing: 'normal' },
    { name: 'Executive Assistant', layout: 'two-column', font: 'Helvetica, sans-serif', spacing: 'compact' }
  ];

  professionalTemplates.forEach((template, index) => {
    const colorScheme = COLOR_SCHEMES.professional[index % COLOR_SCHEMES.professional.length];
    templates.push({
      id: `professional-${templateId++}`,
      name: template.name,
      description: `Professional template designed for corporate and business environments`,
      category: 'professional',
      industry: ['Finance', 'Consulting', 'Technology', 'Healthcare', 'Legal'],
      isPremium: index >= 15,
      previewImage: `/templates/previews/professional-${templateId-1}.png`,
      styles: {
        layout: template.layout as 'single-column' | 'two-column',
        fontFamily: template.font,
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor,
        spacing: template.spacing as 'compact' | 'normal' | 'spacious'
      },
      colorSchemes: COLOR_SCHEMES.professional,
      sections: SECTION_CONFIGS.standard,
      features: ['Professional Design', 'Corporate Layout', 'ATS-Optimized', 'Clean Typography'],
      atsCompatible: true
    });
  });

  // Creative Templates (25 templates)
  const creativeTemplates = [
    { name: 'Creative Designer', layout: 'three-column', font: 'Montserrat, sans-serif', spacing: 'spacious' },
    { name: 'Graphic Artist', layout: 'sidebar', font: 'Roboto, sans-serif', spacing: 'normal' },
    { name: 'UX Designer', layout: 'two-column', font: 'Open Sans, sans-serif', spacing: 'normal' },
    { name: 'Marketing Creative', layout: 'single-column', font: 'Lato, sans-serif', spacing: 'spacious' },
    { name: 'Brand Designer', layout: 'two-column', font: 'Nunito, sans-serif', spacing: 'normal' },
    { name: 'Web Designer', layout: 'sidebar', font: 'Source Sans Pro, sans-serif', spacing: 'normal' },
    { name: 'Art Director', layout: 'single-column', font: 'Playfair Display, serif', spacing: 'spacious' },
    { name: 'Visual Designer', layout: 'two-column', font: 'Poppins, sans-serif', spacing: 'normal' },
    { name: 'Motion Designer', layout: 'sidebar', font: 'Raleway, sans-serif', spacing: 'normal' },
    { name: 'Product Designer', layout: 'two-column', font: 'Inter, sans-serif', spacing: 'compact' },
    { name: 'Creative Writer', layout: 'single-column', font: 'Crimson Text, serif', spacing: 'spacious' },
    { name: 'Content Creator', layout: 'two-column', font: 'Merriweather, serif', spacing: 'normal' },
    { name: 'Social Media', layout: 'sidebar', font: 'Rubik, sans-serif', spacing: 'normal' },
    { name: 'Photography', layout: 'single-column', font: 'Oswald, sans-serif', spacing: 'spacious' },
    { name: 'Video Producer', layout: 'two-column', font: 'Ubuntu, sans-serif', spacing: 'normal' },
    { name: 'Creative Director', layout: 'three-column', font: 'Libre Baskerville, serif', spacing: 'spacious' },
    { name: 'Illustrator', layout: 'single-column', font: 'Quicksand, sans-serif', spacing: 'normal' },
    { name: 'UI Designer', layout: 'two-column', font: 'Work Sans, sans-serif', spacing: 'compact' },
    { name: 'Creative Manager', layout: 'sidebar', font: 'PT Sans, sans-serif', spacing: 'normal' },
    { name: 'Animator', layout: 'single-column', font: 'Fira Sans, sans-serif', spacing: 'spacious' },
    { name: 'Creative Strategist', layout: 'two-column', font: 'Titillium Web, sans-serif', spacing: 'normal' },
    { name: 'Brand Manager', layout: 'sidebar', font: 'Oxygen, sans-serif', spacing: 'normal' },
    { name: 'Digital Artist', layout: 'single-column', font: 'Karla, sans-serif', spacing: 'spacious' },
    { name: 'Creative Lead', layout: 'two-column', font: 'Barlow, sans-serif', spacing: 'normal' },
    { name: 'Innovation Designer', layout: 'sidebar', font: 'Mukti, sans-serif', spacing: 'compact' }
  ];

  creativeTemplates.forEach((template, index) => {
    const colorScheme = COLOR_SCHEMES.creative[index % COLOR_SCHEMES.creative.length];
    templates.push({
      id: `creative-${templateId++}`,
      name: template.name,
      description: `Creative and modern template for design and marketing professionals`,
      category: 'creative',
      industry: ['Design', 'Marketing', 'Media', 'Advertising', 'Technology'],
      isPremium: index >= 15,
      previewImage: `/templates/previews/creative-${templateId-1}.png`,
      styles: {
        layout: template.layout as 'single-column' | 'two-column' | 'sidebar',
        fontFamily: template.font,
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor,
        spacing: template.spacing as 'compact' | 'normal' | 'spacious'
      },
      colorSchemes: COLOR_SCHEMES.creative,
      sections: SECTION_CONFIGS.standard,
      features: ['Creative Design', 'Modern Layout', 'Visual Appeal', 'Portfolio Focus'],
      atsCompatible: true
    });
  });

  // Industry-Specific Templates (30 templates)
  const industryTemplates = [
    { name: 'Healthcare Professional', industry: 'Healthcare', layout: 'single-column', font: 'Arial, sans-serif' },
    { name: 'Medical Doctor', industry: 'Healthcare', layout: 'two-column', font: 'Times New Roman, serif' },
    { name: 'Nurse Practitioner', industry: 'Healthcare', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'Healthcare Administrator', industry: 'Healthcare', layout: 'two-column', font: 'Georgia, serif' },
    { name: 'Physical Therapist', industry: 'Healthcare', layout: 'single-column', font: 'Inter, sans-serif' },
    { name: 'Education Professional', industry: 'Education', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'Teacher', industry: 'Education', layout: 'two-column', font: 'Georgia, serif' },
    { name: 'Principal', industry: 'Education', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'Academic Researcher', industry: 'Education', layout: 'two-column', font: 'Arial, sans-serif' },
    { name: 'University Professor', industry: 'Education', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'Engineering Professional', industry: 'Engineering', layout: 'two-column', font: 'Arial, sans-serif' },
    { name: 'Software Engineer', industry: 'Engineering', layout: 'single-column', font: 'JetBrains Mono, monospace' },
    { name: 'Mechanical Engineer', industry: 'Engineering', layout: 'two-column', font: 'Calibri, sans-serif' },
    { name: 'Civil Engineer', industry: 'Engineering', layout: 'single-column', font: 'Inter, sans-serif' },
    { name: 'Electrical Engineer', industry: 'Engineering', layout: 'two-column', font: 'Helvetica, sans-serif' },
    { name: 'Sales Professional', industry: 'Sales', layout: 'single-column', font: 'Georgia, serif' },
    { name: 'Sales Manager', industry: 'Sales', layout: 'two-column', font: 'Inter, sans-serif' },
    { name: 'Sales Director', industry: 'Sales', layout: 'single-column', font: 'Helvetica, sans-serif' },
    { name: 'Account Executive', industry: 'Sales', layout: 'two-column', font: 'Calibri, sans-serif' },
    { name: 'Business Development', industry: 'Sales', layout: 'single-column', font: 'Arial, sans-serif' },
    { name: 'Legal Professional', industry: 'Legal', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'Corporate Lawyer', industry: 'Legal', layout: 'two-column', font: 'Georgia, serif' },
    { name: 'Paralegal', industry: 'Legal', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'Legal Advisor', industry: 'Legal', layout: 'two-column', font: 'Times New Roman, serif' },
    { name: 'Contract Specialist', industry: 'Legal', layout: 'single-column', font: 'Arial, sans-serif' },
    // Additional industry-specific templates to reach 30+
    { name: 'Financial Analyst', industry: 'Finance', layout: 'two-column', font: 'Arial, sans-serif' },
    { name: 'Investment Banker', industry: 'Finance', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'Accountant', industry: 'Finance', layout: 'two-column', font: 'Calibri, sans-serif' },
    { name: 'Retail Manager', industry: 'Retail', layout: 'single-column', font: 'Inter, sans-serif' },
    { name: 'Customer Service Manager', industry: 'Customer Service', layout: 'two-column', font: 'Helvetica, sans-serif' }
  ];

  industryTemplates.forEach((template, index) => {
    const colorType = template.industry === 'Healthcare' ? 'classic' : 
                     template.industry === 'Education' ? 'classic' :
                     template.industry === 'Engineering' ? 'technical' :
                     template.industry === 'Sales' ? 'professional' : 
                     template.industry === 'Finance' ? 'professional' :
                     template.industry === 'Retail' ? 'creative' :
                     template.industry === 'Customer Service' ? 'creative' : 'classic';
    const colorScheme = COLOR_SCHEMES[colorType][index % COLOR_SCHEMES[colorType].length];
    
    templates.push({
      id: `industry-${templateId++}`,
      name: template.name,
      description: `Specialized template designed for ${template.industry} professionals`,
      category: 'industry-specific',
      industry: [template.industry],
      isPremium: index >= 15,
      previewImage: `/templates/previews/industry-${templateId-1}.png`,
      styles: {
        layout: template.layout as 'single-column' | 'two-column',
        fontFamily: template.font,
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor,
        spacing: 'normal'
      },
      colorSchemes: COLOR_SCHEMES[colorType],
      sections: template.industry === 'Engineering' ? SECTION_CONFIGS.technical : SECTION_CONFIGS.standard,
      features: [`${template.industry} Focused`, 'Industry Standards', 'Professional Layout', 'ATS-Optimized'],
      atsCompatible: true
    });
  });

  // Experience Level Templates (20 templates)
  const experienceLevelTemplates = [
    { name: 'Entry Level Graduate', level: 'entry-level', layout: 'single-column', font: 'Inter, sans-serif' },
    { name: 'Fresh Graduate', level: 'entry-level', layout: 'two-column', font: 'Arial, sans-serif' },
    { name: 'Junior Professional', level: 'entry-level', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'Career Starter', level: 'entry-level', layout: 'two-column', font: 'Helvetica, sans-serif' },
    { name: 'New Graduate', level: 'entry-level', layout: 'single-column', font: 'Georgia, serif' },
    { name: 'First Job Seeker', level: 'entry-level', layout: 'two-column', font: 'Times New Roman, serif' },
    { name: 'Recent Graduate', level: 'entry-level', layout: 'single-column', font: 'Source Sans Pro, sans-serif' },
    { name: 'Mid-Career Professional', level: 'mid-career', layout: 'two-column', font: 'Inter, sans-serif' },
    { name: 'Experienced Manager', level: 'mid-career', layout: 'single-column', font: 'Georgia, serif' },
    { name: 'Senior Specialist', level: 'mid-career', layout: 'two-column', font: 'Arial, sans-serif' },
    { name: 'Team Leader', level: 'mid-career', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'Department Manager', level: 'mid-career', layout: 'two-column', font: 'Helvetica, sans-serif' },
    { name: 'Operations Manager', level: 'mid-career', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'Project Manager', level: 'mid-career', layout: 'two-column', font: 'Open Sans, sans-serif' },
    { name: 'Senior Executive', level: 'senior-executive', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'C-Suite Executive', level: 'senior-executive', layout: 'two-column', font: 'Georgia, serif' },
    { name: 'Vice President', level: 'senior-executive', layout: 'single-column', font: 'Crimson Text, serif' },
    { name: 'Director Level', level: 'senior-executive', layout: 'two-column', font: 'Playfair Display, serif' },
    { name: 'Executive Leader', level: 'senior-executive', layout: 'single-column', font: 'Libre Baskerville, serif' },
    { name: 'Chief Officer', level: 'senior-executive', layout: 'two-column', font: 'Merriweather, serif' }
  ];

  experienceLevelTemplates.forEach((template, index) => {
    const colorType = template.level === 'senior-executive' ? 'professional' : 
                     template.level === 'mid-career' ? 'creative' : 'classic';
    const colorScheme = COLOR_SCHEMES[colorType][index % COLOR_SCHEMES[colorType].length];
    
    templates.push({
      id: `experience-${templateId++}`,
      name: template.name,
      description: `Template tailored for ${template.level.replace('-', ' ')} professionals`,
      category: template.level as 'entry-level' | 'mid-career' | 'senior-executive',
      experienceLevel: template.level as 'entry-level' | 'mid-career' | 'senior-executive',
      industry: ['Technology', 'Finance', 'Consulting', 'Healthcare', 'Legal'],
      isPremium: template.level === 'senior-executive',
      previewImage: `/templates/previews/experience-${templateId-1}.png`,
      styles: {
        layout: template.layout as 'single-column' | 'two-column',
        fontFamily: template.font,
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor,
        spacing: template.level === 'senior-executive' ? 'spacious' : 'normal'
      },
      colorSchemes: COLOR_SCHEMES[colorType],
      sections: template.level === 'senior-executive' ? SECTION_CONFIGS.executive : SECTION_CONFIGS.standard,
      features: [`${template.level.replace('-', ' ')} Focus`, 'Career Appropriate', 'Professional Design', 'ATS-Optimized'],
      atsCompatible: true
    });
  });

  // ATS-Optimized Templates (10 templates)
  const atsTemplates = [
    { name: 'ATS Classic', layout: 'single-column', font: 'Arial, sans-serif' },
    { name: 'ATS Professional', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'ATS Simple', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'ATS Clean', layout: 'single-column', font: 'Helvetica, sans-serif' },
    { name: 'ATS Modern', layout: 'single-column', font: 'Inter, sans-serif' },
    { name: 'ATS Basic', layout: 'single-column', font: 'Georgia, serif' },
    { name: 'ATS Standard', layout: 'single-column', font: 'Arial, sans-serif' },
    { name: 'ATS Corporate', layout: 'single-column', font: 'Calibri, sans-serif' },
    { name: 'ATS Traditional', layout: 'single-column', font: 'Times New Roman, serif' },
    { name: 'ATS Minimal', layout: 'single-column', font: 'Helvetica, sans-serif' }
  ];

  atsTemplates.forEach((template, index) => {
    const colorScheme = COLOR_SCHEMES.ats[index % COLOR_SCHEMES.ats.length];
    
    templates.push({
      id: `ats-${templateId++}`,
      name: template.name,
      description: 'Highly ATS-optimized template with clean, simple formatting',
      category: 'ats-optimized',
      industry: ['Technology', 'Finance', 'Consulting', 'Healthcare', 'Legal', 'Engineering', 'Sales'],
      isPremium: false,
      previewImage: `/templates/previews/ats-${templateId-1}.png`,
      styles: {
        layout: 'single-column',
        fontFamily: template.font,
        primaryColor: colorScheme.primaryColor,
        secondaryColor: colorScheme.secondaryColor,
        accentColor: colorScheme.accentColor,
        spacing: 'normal'
      },
      colorSchemes: COLOR_SCHEMES.ats,
      sections: SECTION_CONFIGS.standard,
      features: ['ATS-Optimized', 'Simple Layout', 'Clean Formatting', 'Universal Compatibility'],
      atsCompatible: true
    });
  });

  return templates;
}