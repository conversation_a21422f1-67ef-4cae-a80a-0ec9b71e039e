export interface ColorScheme {
  name: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
}

export interface ResumeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'professional' | 'creative' | 'executive' | 'ats-optimized' | 'industry-specific' | 'entry-level' | 'mid-career' | 'senior-executive' | 'modern' | 'classic' | 'minimalist' | 'technical';
  industry: string[];
  experienceLevel?: 'entry-level' | 'mid-career' | 'senior-executive';
  isPremium: boolean;
  previewImage: string;
  styles: {
    layout: 'single-column' | 'two-column' | 'three-column' | 'sidebar';
    fontFamily: string;
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    spacing: 'compact' | 'normal' | 'spacious';
  };
  colorSchemes?: ColorScheme[]; // Support for multiple color variations
  sections: TemplateSectionConfig[];
  features: string[];
  atsCompatible: boolean;
}

export interface TemplateSectionConfig {
  id: string;
  type: 'header' | 'summary' | 'experience' | 'education' | 'skills' | 'projects' | 'custom';
  title: string;
  required: boolean;
  order: number;
  layout: 'list' | 'grid' | 'timeline' | 'cards';
  styling: {
    showIcons: boolean;
    showDividers: boolean;
    bulletStyle: 'dots' | 'lines' | 'arrows' | 'none';
    dateFormat: 'short' | 'long' | 'custom';
  };
}

import { generateTemplates } from './templateGenerator';

// Legacy templates (keeping for backward compatibility)
const LEGACY_TEMPLATES: ResumeTemplate[] = [
  {
    id: 'modern-professional',
    name: 'Modern Professional',
    description: 'Clean, contemporary design perfect for most industries',
    category: 'professional', // Updated to match new categories
    industry: ['Technology', 'Finance', 'Consulting', 'Healthcare'],
    isPremium: false,
    previewImage: '/templates/previews/modern-professional.png',
    styles: {
      layout: 'two-column',
      fontFamily: 'Inter, sans-serif',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      accentColor: '#10b981',
      spacing: 'normal'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        title: 'Personal Information',
        required: true,
        order: 1,
        layout: 'grid',
        styling: {
          showIcons: true,
          showDividers: false,
          bulletStyle: 'none',
          dateFormat: 'short'
        }
      },
      {
        id: 'summary',
        type: 'summary',
        title: 'Professional Summary',
        required: true,
        order: 2,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'none',
          dateFormat: 'short'
        }
      },
      {
        id: 'experience',
        type: 'experience',
        title: 'Work Experience',
        required: true,
        order: 3,
        layout: 'timeline',
        styling: {
          showIcons: true,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'short'
        }
      },
      {
        id: 'skills',
        type: 'skills',
        title: 'Core Skills',
        required: true,
        order: 4,
        layout: 'grid',
        styling: {
          showIcons: false,
          showDividers: false,
          bulletStyle: 'none',
          dateFormat: 'short'
        }
      },
      {
        id: 'education',
        type: 'education',
        title: 'Education',
        required: true,
        order: 5,
        layout: 'list',
        styling: {
          showIcons: true,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'long'
        }
      }
    ],
    features: ['ATS-Optimized', 'Modern Design', 'Two-Column Layout', 'Icon Integration'],
    atsCompatible: true
  },
  {
    id: 'executive-elite',
    name: 'Executive Elite',
    description: 'Sophisticated design for senior leadership positions',
    category: 'executive',
    industry: ['Executive', 'Finance', 'Legal', 'Consulting'],
    isPremium: true,
    previewImage: '/templates/previews/executive-elite.png',
    styles: {
      layout: 'single-column',
      fontFamily: 'Crimson Text, serif',
      primaryColor: '#1e293b',
      secondaryColor: '#475569',
      accentColor: '#dc2626',
      spacing: 'spacious'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        title: 'Executive Profile',
        required: true,
        order: 1,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'lines',
          dateFormat: 'long'
        }
      },
      {
        id: 'summary',
        type: 'summary',
        title: 'Executive Summary',
        required: true,
        order: 2,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'none',
          dateFormat: 'long'
        }
      },
      {
        id: 'experience',
        type: 'experience',
        title: 'Leadership Experience',
        required: true,
        order: 3,
        layout: 'timeline',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'lines',
          dateFormat: 'long'
        }
      },
      {
        id: 'education',
        type: 'education',
        title: 'Education & Certifications',
        required: true,
        order: 4,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'lines',
          dateFormat: 'long'
        }
      }
    ],
    features: ['Executive Layout', 'Sophisticated Typography', 'Leadership Focus', 'Premium Design'],
    atsCompatible: true
  },
  {
    id: 'tech-minimalist',
    name: 'Tech Minimalist',
    description: 'Clean, code-friendly design for technical professionals',
    category: 'technical',
    industry: ['Technology', 'Engineering', 'Data Science', 'DevOps'],
    isPremium: false,
    previewImage: '/templates/previews/tech-minimalist.png',
    styles: {
      layout: 'two-column',
      fontFamily: 'JetBrains Mono, monospace',
      primaryColor: '#0f172a',
      secondaryColor: '#64748b',
      accentColor: '#06b6d4',
      spacing: 'compact'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        title: 'Developer Profile',
        required: true,
        order: 1,
        layout: 'list',
        styling: {
          showIcons: true,
          showDividers: false,
          bulletStyle: 'dots',
          dateFormat: 'short'
        }
      },
      {
        id: 'skills',
        type: 'skills',
        title: 'Technical Stack',
        required: true,
        order: 2,
        layout: 'grid',
        styling: {
          showIcons: false,
          showDividers: false,
          bulletStyle: 'none',
          dateFormat: 'short'
        }
      },
      {
        id: 'experience',
        type: 'experience',
        title: 'Professional Experience',
        required: true,
        order: 3,
        layout: 'timeline',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'short'
        }
      },
      {
        id: 'projects',
        type: 'projects',
        title: 'Key Projects',
        required: true,
        order: 4,
        layout: 'list',
        styling: {
          showIcons: true,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'short'
        }
      },
      {
        id: 'education',
        type: 'education',
        title: 'Education & Certifications',
        required: true,
        order: 5,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'short'
        }
      }
    ],
    features: ['Developer Focus', 'Monospace Font', 'Technical Layout', 'Project Emphasis'],
    atsCompatible: true
  },
  {
    id: 'classic-formal',
    name: 'Classic Formal',
    description: 'Traditional, professional layout for conservative industries',
    category: 'ats-optimized', // Updated to match new categories
    industry: ['Legal', 'Government', 'Academia', 'Healthcare'],
    isPremium: false,
    previewImage: '/templates/previews/classic-formal.png',
    styles: {
      layout: 'single-column',
      fontFamily: 'Times New Roman, serif',
      primaryColor: '#000000',
      secondaryColor: '#333333',
      accentColor: '#000000',
      spacing: 'normal'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        title: 'Personal Information',
        required: true,
        order: 1,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'none',
          dateFormat: 'long'
        }
      },
      {
        id: 'summary',
        type: 'summary',
        title: 'Objective',
        required: true,
        order: 2,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'none',
          dateFormat: 'long'
        }
      },
      {
        id: 'experience',
        type: 'experience',
        title: 'Professional Experience',
        required: true,
        order: 3,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'long'
        }
      },
      {
        id: 'education',
        type: 'education',
        title: 'Education',
        required: true,
        order: 4,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'long'
        }
      },
      {
        id: 'skills',
        type: 'skills',
        title: 'Skills',
        required: true,
        order: 5,
        layout: 'list',
        styling: {
          showIcons: false,
          showDividers: true,
          bulletStyle: 'dots',
          dateFormat: 'long'
        }
      }
    ],
    features: ['Traditional Layout', 'Conservative Design', 'Formal Typography', 'Classic Structure'],
    atsCompatible: true
  }
];

// Generate all templates (100+ professional templates)
const GENERATED_TEMPLATES = generateTemplates();

// Combine legacy and generated templates
export const RESUME_TEMPLATES: ResumeTemplate[] = [
  ...LEGACY_TEMPLATES,
  ...GENERATED_TEMPLATES
];

export const getTemplateById = (id: string): ResumeTemplate | null => {
  return RESUME_TEMPLATES.find(template => template.id === id) || null;
};

export const getTemplatesByCategory = (category: string): ResumeTemplate[] => {
  if (category === 'all') return RESUME_TEMPLATES;
  return RESUME_TEMPLATES.filter(template => template.category === category);
};

export const getTemplatesByIndustry = (industry: string): ResumeTemplate[] => {
  if (industry === 'all') return RESUME_TEMPLATES;
  return RESUME_TEMPLATES.filter(template => template.industry.includes(industry));
};

export const getTemplatesByExperienceLevel = (level: string): ResumeTemplate[] => {
  if (level === 'all') return RESUME_TEMPLATES;
  return RESUME_TEMPLATES.filter(template => template.experienceLevel === level);
};

export const getFreeTemplates = (): ResumeTemplate[] => {
  return RESUME_TEMPLATES.filter(template => !template.isPremium);
};

export const getPremiumTemplates = (): ResumeTemplate[] => {
  return RESUME_TEMPLATES.filter(template => template.isPremium);
};

export const getATSCompatibleTemplates = (): ResumeTemplate[] => {
  return RESUME_TEMPLATES.filter(template => template.atsCompatible);
};

export const getTemplateWithColorScheme = (templateId: string, colorSchemeName: string): ResumeTemplate | null => {
  const template = getTemplateById(templateId);
  if (!template || !template.colorSchemes) return template;
  
  const colorScheme = template.colorSchemes.find(scheme => scheme.name === colorSchemeName);
  if (!colorScheme) return template;
  
  return {
    ...template,
    styles: {
      ...template.styles,
      primaryColor: colorScheme.primaryColor,
      secondaryColor: colorScheme.secondaryColor,
      accentColor: colorScheme.accentColor
    }
  };
};

export const getAvailableCategories = (): string[] => {
  const categories = new Set(RESUME_TEMPLATES.map(template => template.category));
  return Array.from(categories).sort();
};

export const getAvailableIndustries = (): string[] => {
  const industries = new Set(RESUME_TEMPLATES.flatMap(template => template.industry));
  return Array.from(industries).sort();
};

export const getTemplateStats = () => {
  const total = RESUME_TEMPLATES.length;
  const free = getFreeTemplates().length;
  const premium = getPremiumTemplates().length;
  const atsOptimized = getATSCompatibleTemplates().length;
  
  const byCategory = getAvailableCategories().reduce((acc, category) => {
    acc[category] = getTemplatesByCategory(category).length;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    total,
    free,
    premium,
    atsOptimized,
    byCategory
  };
};