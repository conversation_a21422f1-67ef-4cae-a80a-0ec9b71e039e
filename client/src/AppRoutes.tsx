import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import type { RootState } from './store';
import { useAuth } from './contexts/AuthContext';
import { LoadingSpinner } from './components/ui';
import ErrorBoundary from './components/ErrorBoundary';

// Import layout components
import MainLayout from './components/layout/MainLayout';

// Lazy load components for better performance and code splitting
const AuthForm = lazy(() => import('./components/auth/AuthForm'));
const Dashboard = lazy(() => import('./components/dashboard/Dashboard'));
const SplitScreenResumeBuilder = lazy(() => import('./components/resume/SplitScreenResumeBuilder'));
const ResourcesPage = lazy(() => import('./pages/ResourcesPage'));
const AdvancedTemplatesPage = lazy(() => import('./pages/AdvancedTemplatesPage'));
const LoopsPage = lazy(() => import('./pages/LoopsPage'));
const TemplateAnalyticsDashboard = lazy(() => import('./pages/TemplateAnalyticsDashboard'));
const ModularResumePage = lazy(() => import('./pages/ModularResumePage'));

// Enhanced loading fallback component with skeleton
const PageLoadingFallback = () => (
  <div className="min-h-screen-mobile flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center space-y-4">
      <LoadingSpinner size="lg" />
      <div className="space-y-2">
        <p className="text-gray-600 dark:text-gray-400 font-medium">Loading page...</p>
        <div className="w-48 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div className="h-full bg-blue-500 rounded-full animate-pulse" style={{ width: '60%' }}></div>
        </div>
      </div>
    </div>
  </div>
);

// Enhanced Protected Route component with better UX
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);

  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);

  if (state.isLoading) {
    return <PageLoadingFallback />;
  }

  return isAuthenticated ? (
    <ErrorBoundary>
      <Suspense fallback={<PageLoadingFallback />}>
        {children}
      </Suspense>
    </ErrorBoundary>
  ) : (
    <Navigate to="/login" replace />
  );
};

export default function AppRoutes() {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);

  return (
    <ErrorBoundary>
      <Routes>
        {/* Public routes with lazy loading */}
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Suspense fallback={<PageLoadingFallback />}>
                <AuthForm />
              </Suspense>
            )
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Suspense fallback={<PageLoadingFallback />}>
                <AuthForm />
              </Suspense>
            )
          }
        />
        <Route
          path="/resources"
          element={
            <MainLayout>
              <Suspense fallback={<PageLoadingFallback />}>
                <ResourcesPage />
              </Suspense>
            </MainLayout>
          }
        />
      
      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <MainLayout>
              <Dashboard />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/resume-builder"
        element={
          <ProtectedRoute>
            <MainLayout>
              <SplitScreenResumeBuilder />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/templates"
        element={
          <ProtectedRoute>
            <MainLayout>
              <AdvancedTemplatesPage />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/loops"
        element={
          <ProtectedRoute>
            <MainLayout>
              <LoopsPage />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/template-analytics"
        element={
          <ProtectedRoute>
            <MainLayout>
              <TemplateAnalyticsDashboard />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/modular-resume"
        element={
          <ProtectedRoute>
            <MainLayout>
              <ModularResumePage />
            </MainLayout>
          </ProtectedRoute>
        }
      />
      
      {/* Default redirect */}
      <Route 
        path="/" 
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/resources"} replace />} 
      />
      
      {/* Catch all route */}
      <Route
        path="*"
        element={
          <div className="min-h-screen-mobile flex items-center justify-center container-bounded">
            <div className="text-center content-wrapper">
              <h1 className="text-4xl font-bold text-gray-900">404</h1>
              <p className="text-gray-600 mt-2">Page not found</p>
              <button
                onClick={() => window.history.back()}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 btn-touch"
              >
                Go Back
              </button>
            </div>
          </div>
        }
      />
    </Routes>
  );
}