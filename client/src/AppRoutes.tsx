import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import type { RootState } from './store';
import { useAuth } from './contexts/AuthContext';

// Import existing components
import AuthForm from './components/auth/AuthForm';
import Dashboard from './components/dashboard/Dashboard';
import SplitScreenResumeBuilder from './components/resume/SplitScreenResumeBuilder';
import ResourcesPage from './pages/ResourcesPage';
import AdvancedTemplatesPage from './pages/AdvancedTemplatesPage';
import LoopsPage from './pages/LoopsPage';
import TemplateAnalyticsDashboard from './pages/TemplateAnalyticsDashboard';
import ModularResumePage from './pages/ModularResumePage';

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);
  
  if (state.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

export default function AppRoutes() {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);

  return (
    <Routes>
      {/* Public routes */}
      <Route 
        path="/login" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthForm />} 
      />
      <Route 
        path="/register" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthForm />} 
      />
      <Route 
        path="/resources" 
        element={<ResourcesPage />} 
      />
      
      {/* Protected routes */}
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } 
      />

      <Route 
        path="/resume-builder" 
        element={
          <ProtectedRoute>
            <SplitScreenResumeBuilder />
          </ProtectedRoute>
        } 
      />

      <Route 
        path="/templates" 
        element={
          <ProtectedRoute>
            <AdvancedTemplatesPage />
          </ProtectedRoute>
        } 
      />

      <Route 
        path="/loops" 
        element={
          <ProtectedRoute>
            <LoopsPage />
          </ProtectedRoute>
        } 
      />

      <Route 
        path="/template-analytics" 
        element={
          <ProtectedRoute>
            <TemplateAnalyticsDashboard />
          </ProtectedRoute>
        } 
      />

      <Route 
        path="/modular-resume" 
        element={
          <ProtectedRoute>
            <ModularResumePage />
          </ProtectedRoute>
        } 
      />
      
      {/* Default redirect */}
      <Route 
        path="/" 
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/resources"} replace />} 
      />
      
      {/* Catch all route */}
      <Route 
        path="*" 
        element={
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900">404</h1>
              <p className="text-gray-600 mt-2">Page not found</p>
              <button 
                onClick={() => window.history.back()}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Go Back
              </button>
            </div>
          </div>
        } 
      />
    </Routes>
  );
}