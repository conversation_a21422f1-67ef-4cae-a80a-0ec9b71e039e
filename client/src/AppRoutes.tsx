import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import type { RootState } from './store';
import { useAuth } from './contexts/AuthContext';

// Import layout components
import MainLayout from './components/layout/MainLayout';

// Import existing components
import AuthForm from './components/auth/AuthForm';
import Dashboard from './components/dashboard/Dashboard';
import SplitScreenResumeBuilder from './components/resume/SplitScreenResumeBuilder';
import ResourcesPage from './pages/ResourcesPage';
import AdvancedTemplatesPage from './pages/AdvancedTemplatesPage';
import LoopsPage from './pages/LoopsPage';
import TemplateAnalyticsDashboard from './pages/TemplateAnalyticsDashboard';
import ModularResumePage from './pages/ModularResumePage';

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);
  
  if (state.isLoading) {
    return (
      <div className="min-h-screen-mobile flex items-center justify-center container-bounded">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

export default function AppRoutes() {
  const { state } = useAuth();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  // Check both new auth context and legacy Redux store
  const isAuthenticated = state.isAuthenticated || (user && token);

  return (
    <Routes>
      {/* Public routes */}
      <Route 
        path="/login" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthForm />} 
      />
      <Route 
        path="/register" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthForm />} 
      />
      <Route
        path="/resources"
        element={
          <MainLayout>
            <ResourcesPage />
          </MainLayout>
        }
      />
      
      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <MainLayout>
              <Dashboard />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/resume-builder"
        element={
          <ProtectedRoute>
            <MainLayout>
              <SplitScreenResumeBuilder />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/templates"
        element={
          <ProtectedRoute>
            <MainLayout>
              <AdvancedTemplatesPage />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/loops"
        element={
          <ProtectedRoute>
            <MainLayout>
              <LoopsPage />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/template-analytics"
        element={
          <ProtectedRoute>
            <MainLayout>
              <TemplateAnalyticsDashboard />
            </MainLayout>
          </ProtectedRoute>
        }
      />

      <Route
        path="/modular-resume"
        element={
          <ProtectedRoute>
            <MainLayout>
              <ModularResumePage />
            </MainLayout>
          </ProtectedRoute>
        }
      />
      
      {/* Default redirect */}
      <Route 
        path="/" 
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/resources"} replace />} 
      />
      
      {/* Catch all route */}
      <Route
        path="*"
        element={
          <div className="min-h-screen-mobile flex items-center justify-center container-bounded">
            <div className="text-center content-wrapper">
              <h1 className="text-4xl font-bold text-gray-900">404</h1>
              <p className="text-gray-600 mt-2">Page not found</p>
              <button
                onClick={() => window.history.back()}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 btn-touch"
              >
                Go Back
              </button>
            </div>
          </div>
        }
      />
    </Routes>
  );
}