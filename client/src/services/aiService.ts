import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// API client configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Enhanced AI Service Types
export interface ResumeData {
  name: string;
  title: string;
  summary: string;
  skills: string[];
  experience: Array<{
    role: string;
    company: string;
    start: string;
    end: string;
    description?: string;
  }>;
  education?: Array<{
    degree: string;
    institution: string;
    year?: string;
  }>;
}

export interface CompanyInfo {
  name: string;
  culture?: string;
  values?: string[];
  industry?: string;
  size?: string;
  description?: string;
}

export interface QuantifiedAchievement {
  original: string;
  quantified: string;
  metrics: string[];
  impact: string;
  actionVerb: string;
  suggestions?: string[];
}

export interface PersonalizedContent {
  summary: string;
  keywordAdjustments: string[];
  toneRecommendations: {
    current: string;
    recommended: string;
    reasoning: string;
  };
  culturalAlignments: Array<{
    companyValue: string;
    resumeAlignment: string;
    suggestedContent: string;
  }>;
}

export interface SectionRecommendation {
  name: string;
  priority: 'essential' | 'high' | 'medium' | 'low';
  reasoning: string;
  industrySpecific: boolean;
  order: number;
  tips: string[];
}

export interface KeywordAnalysis {
  extractedKeywords: Array<{
    keyword: string;
    importance: 'high' | 'medium' | 'low';
    currentCount: number;
    recommendedCount: string;
    density: number;
    status: 'optimal' | 'under-used' | 'over-used';
  }>;
  overallDensity: number;
  optimalRange: string;
  riskAssessment: 'low' | 'medium' | 'high';
}

export class AIService {
  // Core AI Enhancement Features
  static async enhanceResume(resumeData: ResumeData, targetJob?: string, preferredModel?: string) {
    try {
      const response = await apiClient.post('/api/ai/enhance-resume', {
        resumeData,
        targetJob,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Resume enhancement failed:', error);
      throw error;
    }
  }

  static async generateCoverLetter(
    resumeData: ResumeData,
    jobDescription: string,
    companyName?: string,
    preferredModel?: string
  ) {
    try {
      const response = await apiClient.post('/api/ai/generate-cover-letter', {
        resumeData,
        jobDescription,
        companyName,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Cover letter generation failed:', error);
      throw error;
    }
  }

  static async analyzeATS(resumeData: ResumeData, preferredModel?: string) {
    try {
      const response = await apiClient.post('/api/ai/analyze-ats', {
        resumeData,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('ATS analysis failed:', error);
      throw error;
    }
  }

  static async suggestSkills(
    currentSkills: string[],
    jobTitle: string,
    industry?: string,
    preferredModel?: string
  ) {
    try {
      const response = await apiClient.post('/api/ai/suggest-skills', {
        currentSkills,
        jobTitle,
        industry,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Skills suggestion failed:', error);
      throw error;
    }
  }

  // Phase 1 Enhancement Features
  static async quantifyAchievements(
    achievements: string[] | string,
    jobContext?: string,
    preferredModel?: string
  ): Promise<{
    message: string;
    result: {
      quantifiedAchievements: QuantifiedAchievement[];
      overallScore: number;
      recommendations: string[];
    };
  }> {
    try {
      const response = await apiClient.post('/api/ai/quantify-achievements', {
        achievements,
        jobContext,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Achievement quantification failed:', error);
      throw error;
    }
  }

  static async personalizeForCompanyCulture(
    resumeData: ResumeData,
    companyInfo: CompanyInfo,
    preferredModel?: string
  ): Promise<{
    message: string;
    result: {
      personalizedContent: PersonalizedContent;
      modifications: Array<{
        section: string;
        original: string;
        personalized: string;
        reasoning: string;
      }>;
      fitScore: number;
      recommendations: string[];
    };
  }> {
    try {
      const response = await apiClient.post('/api/ai/personalize-content', {
        resumeData,
        companyInfo,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Content personalization failed:', error);
      throw error;
    }
  }

  static async recommendResumeSections(
    industry: string,
    jobRole: string,
    experienceLevel?: string,
    preferredModel?: string
  ): Promise<{
    message: string;
    result: {
      recommendedSections: SectionRecommendation[];
      sectionsToAvoid: Array<{
        name: string;
        reasoning: string;
        alternative?: string;
      }>;
      industryInsights: {
        keyPriorities: string[];
        commonMistakes: string[];
        atsConsiderations: string[];
      };
      customization: {
        sectionOrder: number[];
        emphasisAreas: string[];
      };
    };
  }> {
    try {
      const response = await apiClient.post('/api/ai/recommend-sections', {
        industry,
        jobRole,
        experienceLevel,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Section recommendations failed:', error);
      throw error;
    }
  }

  static async analyzeKeywordDensity(
    resumeContent: string | ResumeData,
    jobDescription: string,
    preferredModel?: string
  ): Promise<{
    message: string;
    result: {
      keywordAnalysis: KeywordAnalysis;
      optimization: {
        addKeywords: Array<{
          keyword: string;
          suggestion: string;
          priority: 'high' | 'medium' | 'low';
          context: string;
        }>;
        reduceKeywords: Array<{
          keyword: string;
          currentCount: number;
          suggestion: string;
          priority: 'high' | 'medium' | 'low';
        }>;
        naturalIntegration: string[];
      };
      atsScore: number;
      recommendations: string[];
    };
  }> {
    try {
      const response = await apiClient.post('/api/ai/analyze-keyword-density', {
        resumeContent,
        jobDescription,
        preferredModel,
      });
      return response.data;
    } catch (error) {
      console.error('Keyword density analysis failed:', error);
      throw error;
    }
  }

  // Advanced AI Features (existing)
  static async getAdvancedScoring(resumeData: ResumeData, targetJob?: string, industry?: string) {
    try {
      const response = await apiClient.post('/api/ai/advanced-scoring', {
        resumeData,
        targetJob,
        industry,
      });
      return response.data;
    } catch (error) {
      console.error('Advanced scoring failed:', error);
      throw error;
    }
  }

  static async detectWeaknesses(resumeData: ResumeData, targetJob?: string) {
    try {
      const response = await apiClient.post('/api/ai/detect-weaknesses', {
        resumeData,
        targetJob,
      });
      return response.data;
    } catch (error) {
      console.error('Weakness detection failed:', error);
      throw error;
    }
  }

  static async getRealTimeSuggestions(
    currentText: string,
    sectionType: string,
    targetJob?: string
  ) {
    try {
      const response = await apiClient.post('/api/ai/real-time-suggestions', {
        currentText,
        sectionType,
        targetJob,
      });
      return response.data;
    } catch (error) {
      console.error('Real-time suggestions failed:', error);
      throw error;
    }
  }

  // Utility methods
  static async getModelStatus() {
    try {
      const response = await apiClient.get('/api/ai/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get model status:', error);
      throw error;
    }
  }

  static async getAvailableModels() {
    try {
      const response = await apiClient.get('/api/ai/models');
      return response.data;
    } catch (error) {
      console.error('Failed to get available models:', error);
      throw error;
    }
  }

  // Helper method for batch processing
  static async processInBatch<T>(
    items: T[],
    processor: (item: T) => Promise<any>,
    batchSize: number = 3
  ): Promise<any[]> {
    const results = [];
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(processor));
      results.push(...batchResults);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    return results;
  }
}

export default AIService;