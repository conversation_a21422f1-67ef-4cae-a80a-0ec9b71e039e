// Enhanced Template API service functions

import type {
  Template,
  APIResponse,
  TemplateUsageData,
  TemplateAnalytics
} from '../types/phase1';
import type { ResumeTemplate } from '../constants/resumeTemplates';

const API_BASE = '/api';

// Get authentication token
const getAuthToken = (): string | null => {
  return localStorage.getItem('token');
};

// Create authenticated headers
const createAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Enhanced Template API Functions
export const templateAPI = {
  // Get all templates with enhanced filtering
  async getTemplates(filters?: { 
    category?: string; 
    industry?: string;
    experienceLevel?: string;
    isPremium?: boolean;
    atsOptimized?: boolean;
    searchQuery?: string;
    sortBy?: 'popularity' | 'rating' | 'newest' | 'alphabetical';
  }): Promise<APIResponse<Template[]>> {
    const queryParams = new URLSearchParams();
    if (filters?.category) queryParams.append('category', filters.category);
    if (filters?.industry) queryParams.append('industry', filters.industry);
    if (filters?.experienceLevel) queryParams.append('experienceLevel', filters.experienceLevel);
    if (filters?.isPremium !== undefined) queryParams.append('premium', filters.isPremium.toString());
    if (filters?.atsOptimized !== undefined) queryParams.append('atsOptimized', filters.atsOptimized.toString());
    if (filters?.searchQuery) queryParams.append('search', filters.searchQuery);
    if (filters?.sortBy) queryParams.append('sort', filters.sortBy);

    const response = await fetch(`${API_BASE}/templates?${queryParams}`);
    const result: APIResponse<Template[]> = await response.json();
    
    return result;
  },

  // Get specific template by ID with analytics
  async getTemplate(id: string | number): Promise<APIResponse<Template>> {
    const response = await fetch(`${API_BASE}/templates/${id}`);
    const result: APIResponse<Template> = await response.json();
    
    return result;
  },

  // Get template recommendations
  async getTemplateRecommendations(criteria: {
    industry?: string;
    experienceLevel?: string;
    targetRole?: string;
    skills?: string[];
    limit?: number;
  }): Promise<APIResponse<Template[]>> {
    const response = await fetch(`${API_BASE}/templates/recommendations`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify(criteria),
    });
    
    const result: APIResponse<Template[]> = await response.json();
    return result;
  },

  // Create new template (requires authentication)
  async createTemplate(templateData: Partial<ResumeTemplate>): Promise<APIResponse<Template>> {
    const response = await fetch(`${API_BASE}/templates`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify(templateData),
    });
    
    const result: APIResponse<Template> = await response.json();
    return result;
  },

  // Update existing template (requires authentication)
  async updateTemplate(id: string | number, updateData: Partial<ResumeTemplate>): Promise<APIResponse<Template>> {
    const response = await fetch(`${API_BASE}/templates/${id}`, {
      method: 'PUT',
      headers: createAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    
    const result: APIResponse<Template> = await response.json();
    return result;
  },

  // Delete template (requires authentication)
  async deleteTemplate(id: string | number): Promise<APIResponse<any>> {
    const response = await fetch(`${API_BASE}/templates/${id}`, {
      method: 'DELETE',
      headers: createAuthHeaders(),
    });
    
    const result: APIResponse<any> = await response.json();
    return result;
  },

  // Duplicate template (requires authentication)
  async duplicateTemplate(id: string | number, customizations?: any): Promise<APIResponse<Template>> {
    const response = await fetch(`${API_BASE}/templates/${id}/duplicate`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify({ customizations }),
    });
    
    const result: APIResponse<Template> = await response.json();
    return result;
  },

  // Get template analytics with time range
  async getTemplateAnalytics(id: string | number, timeRange?: string): Promise<APIResponse<TemplateAnalytics>> {
    const queryParams = timeRange ? `?timeRange=${timeRange}` : '';
    const response = await fetch(`${API_BASE}/templates/${id}/analytics${queryParams}`, {
      headers: createAuthHeaders(),
    });
    const result: APIResponse<TemplateAnalytics> = await response.json();
    
    return result;
  },

  // Get template performance metrics
  async getTemplatePerformance(id: string | number): Promise<APIResponse<any>> {
    const response = await fetch(`${API_BASE}/templates/${id}/performance`, {
      headers: createAuthHeaders(),
    });
    const result: APIResponse<any> = await response.json();
    
    return result;
  },

  // Track template usage (requires authentication) - supports both /use and /usage endpoints
  async trackTemplateUsage(id: string | number, usageData: TemplateUsageData): Promise<APIResponse<any>> {
    const token = getAuthToken();
    if (!token) {
      return { success: false, data: null, error: 'No authentication token' };
    }

    // Try new endpoint first (/use), fallback to old endpoint (/usage)
    try {
      const response = await fetch(`${API_BASE}/templates/${id}/use`, {
        method: 'POST',
        headers: createAuthHeaders(),
        body: JSON.stringify({ usageData }),
      });
      
      const result: APIResponse<any> = await response.json();
      return result;
    } catch {
      // Fallback to old endpoint
      try {
        const response = await fetch(`${API_BASE}/templates/${id}/usage`, {
          method: 'POST',
          headers: createAuthHeaders(),
          body: JSON.stringify({ usageData }),
        });
        
        const result: APIResponse<any> = await response.json();
        return result;
      } catch {
        return { success: false, data: null, error: 'Failed to track template usage' };
      }
    }
  },

  // Export template in different formats
  async exportTemplate(id: string | number, format: 'pdf' | 'word' | 'google-docs', resumeData?: any): Promise<APIResponse<{ downloadUrl: string }>> {
    const response = await fetch(`${API_BASE}/templates/${id}/export`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify({ format, resumeData }),
    });
    
    const result: APIResponse<{ downloadUrl: string }> = await response.json();
    return result;
  },

  // A/B test template variants
  async createTemplateVariant(id: string | number, variantData: any): Promise<APIResponse<Template>> {
    const response = await fetch(`${API_BASE}/templates/${id}/variants`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify(variantData),
    });
    
    const result: APIResponse<Template> = await response.json();
    return result;
  },

  // Get template A/B test results
  async getTemplateTestResults(id: string | number): Promise<APIResponse<any>> {
    const response = await fetch(`${API_BASE}/templates/${id}/test-results`, {
      headers: createAuthHeaders(),
    });
    
    const result: APIResponse<any> = await response.json();
    return result;
  },

  // Submit template rating
  async rateTemplate(id: string | number, rating: number, review?: string): Promise<APIResponse<any>> {
    const response = await fetch(`${API_BASE}/templates/${id}/rate`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify({ rating, review }),
    });
    
    const result: APIResponse<any> = await response.json();
    return result;
  },

  // Get template categories and statistics
  async getTemplateStatistics(): Promise<APIResponse<any>> {
    const response = await fetch(`${API_BASE}/templates/statistics`);
    const result: APIResponse<any> = await response.json();
    
    return result;
  },

  // Validate template structure
  async validateTemplate(templateData: Partial<ResumeTemplate>): Promise<APIResponse<{ isValid: boolean; errors: string[] }>> {
    const response = await fetch(`${API_BASE}/templates/validate`, {
      method: 'POST',
      headers: createAuthHeaders(),
      body: JSON.stringify(templateData),
    });
    
    const result: APIResponse<{ isValid: boolean; errors: string[] }> = await response.json();
    return result;
  }
};