import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export interface ExportOptions {
  format?: 'pdf' | 'png' | 'jpg';
  quality?: number;
  filename?: string;
  paperSize?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

export interface ResumeExportData {
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    location: string;
    linkedin?: string;
    website?: string;
  };
  summary?: string;
  experience: Array<{
    company: string;
    position: string;
    duration: string;
    location: string;
    achievements: string[];
  }>;
  education: Array<{
    school: string;
    degree: string;
    duration: string;
    gpa?: string;
  }>;
  skills: {
    [category: string]: string[];
  };
  projects?: Array<{
    name: string;
    description: string;
    technologies: string[];
    url?: string;
  }>;
}

class ResumeExportService {
  /**
   * Export resume as PDF using html2canvas and jsPDF
   */
  static async exportToPDF(elementId: string, options: ExportOptions = {}): Promise<void> {
    try {
      const element = document.getElementById(elementId);
      if (!element) {
        throw new Error('Resume element not found');
      }

      // Show loading state
      const loadingOverlay = this.showLoadingOverlay('Generating PDF...');

      // Configure html2canvas options
      const canvas = await html2canvas(element, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        onclone: (clonedDoc) => {
          // Ensure fonts are loaded in cloned document
          const clonedElement = clonedDoc.getElementById(elementId);
          if (clonedElement) {
            clonedElement.style.fontFamily = 'Arial, sans-serif';
          }
        }
      });

      // Configure PDF options
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: options.orientation || 'portrait',
        unit: 'mm',
        format: options.paperSize || 'a4'
      });

      // Calculate dimensions
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      
      // Calculate scale to fit page
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const finalWidth = imgWidth * ratio;
      const finalHeight = imgHeight * ratio;
      
      // Center the image on the page
      const x = (pdfWidth - finalWidth) / 2;
      const y = (pdfHeight - finalHeight) / 2;

      pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

      // Generate filename
      const filename = options.filename || `resume_${new Date().toISOString().split('T')[0]}.pdf`;
      
      // Download the PDF
      pdf.save(filename);

      this.hideLoadingOverlay(loadingOverlay);
    } catch (error) {
      console.error('PDF export failed:', error);
      throw new Error('Failed to export PDF. Please try again.');
    }
  }

  /**
   * Export resume as image (PNG/JPG)
   */
  static async exportToImage(elementId: string, options: ExportOptions = {}): Promise<void> {
    try {
      const element = document.getElementById(elementId);
      if (!element) {
        throw new Error('Resume element not found');
      }

      const loadingOverlay = this.showLoadingOverlay('Generating image...');

      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false
      });

      // Convert to blob
      const quality = options.quality || 0.95;
      const mimeType = options.format === 'jpg' ? 'image/jpeg' : 'image/png';
      
      canvas.toBlob((blob) => {
        if (!blob) {
          throw new Error('Failed to generate image');
        }

        // Create download link
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = options.filename || `resume_${new Date().toISOString().split('T')[0]}.${options.format || 'png'}`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
        this.hideLoadingOverlay(loadingOverlay);
      }, mimeType, quality);

    } catch (error) {
      console.error('Image export failed:', error);
      throw new Error('Failed to export image. Please try again.');
    }
  }

  /**
   * Generate Word document content (simplified version)
   */
  static generateWordContent(data: ResumeExportData): string {
    let content = `
${data.personalInfo.fullName}
${data.personalInfo.email} | ${data.personalInfo.phone} | ${data.personalInfo.location}
${data.personalInfo.linkedin ? `LinkedIn: ${data.personalInfo.linkedin}` : ''}
${data.personalInfo.website ? `Website: ${data.personalInfo.website}` : ''}

PROFESSIONAL SUMMARY
${data.summary || ''}

WORK EXPERIENCE
`;

    data.experience.forEach(exp => {
      content += `
${exp.position} - ${exp.company}
${exp.duration} | ${exp.location}
${exp.achievements.map(achievement => `• ${achievement}`).join('\n')}
`;
    });

    content += `
EDUCATION
`;

    data.education.forEach(edu => {
      content += `
${edu.degree} - ${edu.school}
${edu.duration}${edu.gpa ? ` | GPA: ${edu.gpa}` : ''}
`;
    });

    content += `
SKILLS
`;

    Object.entries(data.skills).forEach(([category, skills]) => {
      content += `${category}: ${skills.join(', ')}\n`;
    });

    if (data.projects?.length) {
      content += `
PROJECTS
`;
      data.projects.forEach(project => {
        content += `
${project.name}
${project.description}
Technologies: ${project.technologies.join(', ')}${project.url ? `\nURL: ${project.url}` : ''}
`;
      });
    }

    return content;
  }

  /**
   * Export as Word document (simplified text format)
   */
  static exportToWord(data: ResumeExportData, filename?: string): void {
    const content = this.generateWordContent(data);
    const blob = new Blob([content], { type: 'text/plain' });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `resume_${new Date().toISOString().split('T')[0]}.txt`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Show loading overlay
   */
  private static showLoadingOverlay(message: string): HTMLElement {
    const overlay = document.createElement('div');
    overlay.id = 'export-loading-overlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    overlay.innerHTML = `
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span class="text-lg font-medium">${message}</span>
      </div>
    `;
    document.body.appendChild(overlay);
    return overlay;
  }

  /**
   * Hide loading overlay
   */
  private static hideLoadingOverlay(overlay: HTMLElement): void {
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay);
    }
  }

  /**
   * Export as Google Docs compatible format
   */
  static exportToGoogleDocs(data: ResumeExportData, filename?: string): void {
    const content = this.generateGoogleDocsContent(data);
    const blob = new Blob([content], { type: 'text/html' });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `resume_${new Date().toISOString().split('T')[0]}.html`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Generate Google Docs compatible HTML content
   */
  static generateGoogleDocsContent(data: ResumeExportData): string {
    let content = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>${data.personalInfo.fullName} - Resume</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
    .header { text-align: center; margin-bottom: 30px; }
    .header h1 { margin: 0; font-size: 24px; }
    .contact-info { margin: 10px 0; }
    .section { margin: 20px 0; }
    .section h2 { border-bottom: 2px solid #333; margin-bottom: 10px; }
    .experience-item, .education-item, .project-item { margin-bottom: 15px; }
    .company, .school, .project-name { font-weight: bold; }
    .position, .degree { font-style: italic; }
    .duration, .location { color: #666; }
    .skills-category { margin-bottom: 10px; }
    .skills-list { margin-left: 20px; }
  </style>
</head>
<body>`;

    // Header
    content += `
  <div class="header">
    <h1>${data.personalInfo.fullName}</h1>
    <div class="contact-info">
      ${data.personalInfo.email} | ${data.personalInfo.phone} | ${data.personalInfo.location}
      ${data.personalInfo.linkedin ? `<br>LinkedIn: ${data.personalInfo.linkedin}` : ''}
      ${data.personalInfo.website ? `<br>Website: ${data.personalInfo.website}` : ''}
    </div>
  </div>`;

    // Summary
    if (data.summary) {
      content += `
  <div class="section">
    <h2>Professional Summary</h2>
    <p>${data.summary}</p>
  </div>`;
    }

    // Experience
    content += `
  <div class="section">
    <h2>Work Experience</h2>`;
    data.experience.forEach(exp => {
      content += `
    <div class="experience-item">
      <div class="position">${exp.position}</div>
      <div class="company">${exp.company}</div>
      <div class="duration">${exp.duration} | ${exp.location}</div>
      <ul>
        ${exp.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
      </ul>
    </div>`;
    });
    content += `  </div>`;

    // Education
    content += `
  <div class="section">
    <h2>Education</h2>`;
    data.education.forEach(edu => {
      content += `
    <div class="education-item">
      <div class="degree">${edu.degree}</div>
      <div class="school">${edu.school}</div>
      <div class="duration">${edu.duration}${edu.gpa ? ` | GPA: ${edu.gpa}` : ''}</div>
    </div>`;
    });
    content += `  </div>`;

    // Skills
    content += `
  <div class="section">
    <h2>Skills</h2>`;
    Object.entries(data.skills).forEach(([category, skills]) => {
      content += `
    <div class="skills-category">
      <strong>${category}:</strong>
      <div class="skills-list">${skills.join(', ')}</div>
    </div>`;
    });
    content += `  </div>`;

    // Projects
    if (data.projects?.length) {
      content += `
  <div class="section">
    <h2>Projects</h2>`;
      data.projects.forEach(project => {
        content += `
    <div class="project-item">
      <div class="project-name">${project.name}</div>
      <p>${project.description}</p>
      <div><strong>Technologies:</strong> ${project.technologies.join(', ')}</div>
      ${project.url ? `<div><strong>URL:</strong> <a href="${project.url}">${project.url}</a></div>` : ''}
    </div>`;
      });
      content += `  </div>`;
    }

    content += `
</body>
</html>`;

    return content;
  }

  /**
   * Check if export capabilities are available
   */
  static checkExportSupport(): { pdf: boolean; image: boolean; word: boolean; googleDocs: boolean } {
    return {
      pdf: typeof html2canvas !== 'undefined' && typeof jsPDF !== 'undefined',
      image: typeof html2canvas !== 'undefined',
      word: true, // Simple text export always available
      googleDocs: true // HTML export always available
    };
  }
}

export default ResumeExportService;