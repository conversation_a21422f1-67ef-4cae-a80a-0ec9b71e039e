// Loop API service for frontend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

interface LoopData {
  name: string;
  description: string;
  configuration: {
    jobTitles: string[];
    locations: string[];
    industries: string[];
    experienceLevels: string[];
    salaryMin?: number;
    salaryMax?: number;
    remoteOptions: string;
    excludedCompanies: string[];
    keywords: string[];
    scheduleType: string;
    maxApplicationsPerDay: number;
  };
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class LoopApiService {
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('token'); // Adjust based on your auth implementation
      
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
          ...options.headers,
        },
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get all loops for the current user
  async getLoops() {
    return this.request('/loops');
  }

  // Create a new loop
  async createLoop(loopData: LoopData) {
    return this.request('/loops', {
      method: 'POST',
      body: JSON.stringify(loopData),
    });
  }

  // Get a specific loop
  async getLoop(loopId: string) {
    return this.request(`/loops/${loopId}`);
  }

  // Update a loop
  async updateLoop(loopId: string, updateData: Partial<LoopData> & { status?: string }) {
    return this.request(`/loops/${loopId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  // Delete a loop
  async deleteLoop(loopId: string) {
    return this.request(`/loops/${loopId}`, {
      method: 'DELETE',
    });
  }

  // Pause or resume a loop
  async toggleLoop(loopId: string, action: 'pause' | 'resume') {
    return this.request(`/loops/${loopId}/pause`, {
      method: 'POST',
      body: JSON.stringify({ action }),
    });
  }

  // Get loop performance analytics
  async getLoopPerformance(loopId: string) {
    return this.request(`/loops/${loopId}/performance`);
  }

  // Get discovered jobs for all loops
  async getDiscoveredJobs(limit = 50) {
    return this.request(`/loops/discovered-jobs?limit=${limit}`);
  }

  // Test job discovery for a loop
  async testDiscovery(loopId: string) {
    return this.request(`/loops/${loopId}/test-discovery`, {
      method: 'POST',
    });
  }

  // Get discovery statistics for a loop
  async getDiscoveryStats(loopId: string) {
    return this.request(`/loops/${loopId}/discovery-stats`);
  }

  // Mark a discovered job as applied
  async markJobAsApplied(jobId: string) {
    return this.request(`/loops/jobs/${jobId}/apply`, {
      method: 'POST',
    });
  }
}

export const loopApiService = new LoopApiService();
export default loopApiService;