const CACHE_NAME = 'cvleap-v1.1.0';
const API_CACHE_NAME = 'cvleap-api-v1.1.0';
const MOBILE_CACHE_NAME = 'cvleap-mobile-v1.1.0';

// Background sync queue name
const SYNC_QUEUE_NAME = 'cvleap-sync-queue';

// Resources to cache on install
const STATIC_RESOURCES = [
  '/',
  '/manifest.json',
  '/offline.html'
];

// Mobile-specific resources for offline functionality
const MOBILE_RESOURCES = [
  '/resume/builder',
  '/dashboard',
  '/applications',
  '/analytics'
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/resume',
  '/api/templates',
  '/api/auth/me',
  '/api/jobs',
  '/api/applications'
];

// Background sync queue for offline actions
let syncQueue = [];

// Install event - cache static resources
self.addEventListener('install', event => {
  console.log('Service worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static resources
      caches.open(CACHE_NAME)
        .then(cache => {
          console.log('Caching static resources');
          return cache.addAll(STATIC_RESOURCES);
        }),
      // Cache mobile-specific resources
      caches.open(MOBILE_CACHE_NAME)
        .then(cache => {
          console.log('Caching mobile resources');
          return cache.addAll(MOBILE_RESOURCES);
        })
    ])
    .then(() => {
      console.log('Service worker installed successfully');
      return self.skipWaiting();
    })
    .catch(error => {
      console.error('Service worker installation failed:', error);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        const deletePromises = cacheNames
          .filter(cacheName => 
            cacheName !== CACHE_NAME && 
            cacheName !== API_CACHE_NAME &&
            cacheName !== MOBILE_CACHE_NAME
          )
          .map(cacheName => {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          });
        
        return Promise.all(deletePromises);
      })
      .then(() => {
        console.log('Service worker activated successfully');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategy
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip cross-origin requests
  if (!url.origin.includes(self.location.origin)) {
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleAPIRequest(request));
    return;
  }

  // Handle static assets
  if (url.pathname.includes('.')) {
    event.respondWith(handleStaticAssets(request));
    return;
  }

  // Handle navigation requests
  event.respondWith(handleNavigation(request));
});

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
  const cache = await caches.open(API_CACHE_NAME);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone();
      
      // Only cache GET requests
      if (request.method === 'GET') {
        cache.put(request, responseClone);
      }
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network request failed, trying cache:', error);
    
    // Fall back to cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline response for critical API endpoints
    if (request.url.includes('/api/auth/me') || request.url.includes('/api/resume')) {
      return new Response(
        JSON.stringify({ 
          error: 'Offline', 
          message: 'You are currently offline. Some features may be limited.' 
        }),
        {
          status: 503,
          statusText: 'Service Unavailable',
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    throw error;
  }
}

// Handle static assets with cache-first strategy
async function handleStaticAssets(request) {
  const cache = await caches.open(CACHE_NAME);
  
  // Try cache first
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // Fall back to network
    const networkResponse = await fetch(request);
    
    // Cache the response
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Failed to fetch static asset:', request.url);
    throw error;
  }
}

// Handle navigation requests
async function handleNavigation(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // Try network first for navigation
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    console.log('Navigation request failed, serving cached index or offline page');
    
    // Try to serve cached index.html
    const cachedIndex = await cache.match('/');
    if (cachedIndex) {
      return cachedIndex;
    }
    
    // Fall back to offline page
    const offlinePage = await cache.match('/offline.html');
    if (offlinePage) {
      return offlinePage;
    }
    
    // Ultimate fallback
    return new Response(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>CVleap - Offline</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { 
              font-family: system-ui, sans-serif; 
              text-align: center; 
              padding: 2rem; 
              background: #f8fafc;
            }
            .container {
              max-width: 400px;
              margin: 0 auto;
              background: white;
              padding: 2rem;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .icon { font-size: 4rem; margin-bottom: 1rem; }
            h1 { color: #374151; margin-bottom: 1rem; }
            p { color: #6b7280; margin-bottom: 2rem; }
            button {
              background: #2563eb;
              color: white;
              border: none;
              padding: 0.75rem 1.5rem;
              border-radius: 6px;
              cursor: pointer;
              font-size: 1rem;
            }
            button:hover { background: #1d4ed8; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">📱</div>
            <h1>You're Offline</h1>
            <p>CVleap is currently unavailable. Please check your internet connection and try again.</p>
            <button onclick="window.location.reload()">Try Again</button>
          </div>
        </body>
      </html>
      `,
      {
        headers: { 'Content-Type': 'text/html' }
      }
    );
  }
}

// Handle background sync for offline actions
self.addEventListener('sync', event => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'resume-sync' || event.tag === 'cvleap-sync' || event.tag === 'online-sync') {
    event.waitUntil(syncOfflineData());
  }
  
  if (event.tag === 'application-sync') {
    event.waitUntil(syncApplicationData());
  }
});

// Enhanced sync functions
async function syncOfflineData() {
  try {
    console.log('Starting comprehensive offline data sync...');
    
    // Get all pending data from localStorage
    const pendingKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('pending-') || key.startsWith('mobile-resume-draft'))) {
        pendingKeys.push(key);
      }
    }
    
    if (pendingKeys.length === 0) {
      console.log('No pending data to sync');
      return;
    }

    console.log(`Found ${pendingKeys.length} items to sync`);
    
    // Sync each item with retry logic
    const syncPromises = pendingKeys.map(async (key) => {
      const maxRetries = 3;
      let attempt = 1;
      
      while (attempt <= maxRetries) {
        try {
          await syncSingleItem(key);
          console.log(`Successfully synced: ${key}`);
          return { key, success: true };
        } catch (error) {
          console.warn(`Sync attempt ${attempt} failed for ${key}:`, error);
          
          if (attempt < maxRetries) {
            // Exponential backoff with jitter
            const delay = Math.min(1000 * Math.pow(2, attempt - 1) + Math.random() * 1000, 30000);
            await new Promise(resolve => setTimeout(resolve, delay));
            attempt++;
          } else {
            console.error(`Failed to sync ${key} after ${maxRetries} attempts`);
            return { key, success: false, error: error.message };
          }
        }
      }
    });
    
    const results = await Promise.allSettled(syncPromises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;
    
    console.log(`Sync completed: ${successful} successful, ${failed} failed`);
    
    // Notify main thread about sync completion
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'sync-complete',
        successful,
        failed,
        details: results
      });
    });
    
  } catch (error) {
    console.error('Comprehensive sync failed:', error);
  }
}

async function syncSingleItem(key) {
  const stored = localStorage.getItem(key);
  if (!stored) {
    throw new Error(`No data found for key: ${key}`);
  }
  
  let data;
  try {
    const parsed = JSON.parse(stored);
    data = parsed.data || parsed; // Handle both wrapped and direct data
  } catch (error) {
    throw new Error(`Failed to parse data for key: ${key}`);
  }
  
  // Determine sync strategy based on key pattern
  if (key.startsWith('pending-resume-update') || key.startsWith('mobile-resume-draft')) {
    await syncResume(key, data);
  } else if (key.startsWith('pending-resume-create')) {
    await createResume(data);
  } else if (key.startsWith('pending-application')) {
    await syncApplication(data);
  } else {
    console.warn(`Unknown data type for key: ${key}, skipping`);
    return;
  }
  
  // Remove successfully synced data
  localStorage.removeItem(key);
}

async function syncResume(key, data) {
  const resumeId = extractIdFromKey(key);
  if (!resumeId) {
    throw new Error(`Cannot extract resume ID from key: ${key}`);
  }
  
  const response = await fetch(`/api/resumes/${resumeId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Resume sync failed: ${response.status} ${response.statusText}`);
  }
}

async function createResume(data) {
  const response = await fetch('/api/resumes', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Resume creation failed: ${response.status} ${response.statusText}`);
  }
}

async function syncApplication(data) {
  const response = await fetch('/api/applications/queue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Application sync failed: ${response.status} ${response.statusText}`);
  }
}

function extractIdFromKey(key) {
  const matches = key.match(/-([\w-]+)$/);
  return matches ? matches[1] : null;
}

// Handle push notifications
self.addEventListener('push', event => {
  console.log('Push notification received:', event);
  
  const options = {
    body: event.data ? event.data.text() : 'You have a new notification from CVleap',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [200, 100, 200],
    data: event.data ? JSON.parse(event.data.text()) : {},
    actions: [
      {
        action: 'open',
        title: 'Open CVleap',
        icon: '/icons/icon-96x96.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/icon-96x96.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('CVleap', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  if (event.action === 'open' || !event.action) {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Listen for messages from main thread
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'TRIGGER_SYNC') {
    // Manually trigger sync from main thread
    syncOfflineData().catch(error => {
      console.error('Manual sync failed:', error);
    });
  }
});

async function syncApplicationData() {
  try {
    console.log('Syncing application data...');
    // Enhanced implementation for syncing job application data when back online
    
    const pendingKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('pending-application')) {
        pendingKeys.push(key);
      }
    }
    
    for (const key of pendingKeys) {
      try {
        await syncSingleItem(key);
      } catch (error) {
        console.error(`Failed to sync application data for ${key}:`, error);
      }
    }
  } catch (error) {
    console.error('Application sync failed:', error);
  }
}

// Listen for messages from main thread
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'TRIGGER_SYNC') {
    // Manually trigger sync from main thread
    syncOfflineData().catch(error => {
      console.error('Manual sync failed:', error);
    });
  }
});