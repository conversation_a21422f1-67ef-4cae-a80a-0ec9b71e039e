<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVleap - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .features h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .features ul {
            list-style: none;
            text-align: left;
        }
        
        .features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 120px;
            font-weight: 500;
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .primary {
            background: rgba(37, 99, 235, 0.8);
            border-color: rgba(37, 99, 235, 1);
        }
        
        .primary:hover {
            background: rgba(37, 99, 235, 1);
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(239, 68, 68, 0.9);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .status.online {
            background: rgba(34, 197, 94, 0.9);
            animation: none;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }
        
        @media (max-width: 640px) {
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            button {
                width: 100%;
                max-width: 250px;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            .icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        <div class="indicator"></div>
        <span>Offline</span>
    </div>
    
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>No worries! CVleap works offline too. You can continue building your resume and we'll sync your changes when you're back online.</p>
        
        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>Edit existing resumes</li>
                <li>Create new resume sections</li>
                <li>Use pre-loaded templates</li>
                <li>Preview your resume</li>
                <li>Save changes locally</li>
            </ul>
        </div>
        
        <div class="buttons">
            <button class="primary" onclick="goOffline()">Continue Offline</button>
            <button onclick="retry()">Try Again</button>
        </div>
    </div>

    <script>
        // Check network status
        function updateStatus() {
            const status = document.getElementById('status');
            const indicator = status.querySelector('.indicator');
            const text = status.querySelector('span');
            
            if (navigator.onLine) {
                status.classList.add('online');
                text.textContent = 'Online';
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                status.classList.remove('online');
                text.textContent = 'Offline';
            }
        }
        
        // Listen for network changes
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);
        
        // Initial status check
        updateStatus();
        
        function retry() {
            window.location.reload();
        }
        
        function goOffline() {
            // Try to navigate to cached app
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then(registration => {
                    if (registration.active) {
                        window.location.href = '/';
                    }
                });
            } else {
                window.location.href = '/';
            }
        }
        
        // Auto-retry connection every 30 seconds
        setInterval(() => {
            if (!navigator.onLine) {
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        updateStatus();
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 30000);
        
        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered from offline page:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed from offline page:', error);
                });
        }
    </script>
</body>
</html>