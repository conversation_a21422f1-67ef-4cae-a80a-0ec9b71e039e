#!/usr/bin/env node

const WebSocket = require('ws');

// Test the collaboration WebSocket functionality
async function testCollaboration() {
  console.log('🧪 Testing Real-Time Collaboration Features...\n');
  
  const ws = new WebSocket('ws://localhost:3000/ws');
  
  ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Test authentication
    ws.send(JSON.stringify({
      type: 'auth',
      token: 'test-token'  // Would be a real JWT in production
    }));
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('📨 Received:', message.type, message);
      
      if (message.type === 'welcome') {
        console.log('✅ Welcome message received');
      }
      
      if (message.type === 'auth_success') {
        console.log('✅ Authentication successful');
        
        // Test joining editing session
        ws.send(JSON.stringify({
          type: 'join_editing',
          resumeId: 'test-resume-123',
          sessionId: 'test-session-456'
        }));
      }
      
      if (message.type === 'session_joined') {
        console.log('✅ Joined editing session');
        
        // Test sending an operation
        ws.send(JSON.stringify({
          type: 'editing_operation',
          sessionId: 'test-session-456',
          operation: {
            type: 'insert',
            position: 0,
            newContent: 'Hello World',
            timestamp: Date.now()
          }
        }));
        
        // Test cursor update
        ws.send(JSON.stringify({
          type: 'cursor_update',
          sessionId: 'test-session-456',
          cursor: { line: 0, column: 11 },
          selection: null
        }));
      }
    } catch (error) {
      console.error('❌ Error parsing message:', error);
    }
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
  });
  
  ws.on('close', () => {
    console.log('🔌 WebSocket disconnected');
  });
  
  // Close after 5 seconds
  setTimeout(() => {
    ws.close();
    process.exit(0);
  }, 5000);
}

// Test API endpoints
async function testAPIEndpoints() {
  console.log('🧪 Testing Collaboration API Endpoints...\n');
  
  const baseURL = 'http://localhost:3000';
  
  try {
    // Test health endpoint
    const healthResponse = await fetch(`${baseURL}/api/health`);
    const health = await healthResponse.json();
    console.log('✅ Health check:', health.status);
    console.log('   Collaboration features:', health.collaboration ? 'Ready' : 'Not available');
    
    // Test detailed health
    const detailedHealthResponse = await fetch(`${baseURL}/api/health/detailed`, {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    });
    
    if (detailedHealthResponse.ok) {
      const detailedHealth = await detailedHealthResponse.json();
      console.log('✅ Detailed health check successful');
      console.log('   WebSocket connections:', detailedHealth.websocket?.connectedClients || 0);
      console.log('   Active collaborations:', detailedHealth.collaboration?.activeSessions || 0);
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 CVleap Real-Time Collaboration Test Suite\n');
  console.log('='.repeat(50));
  
  try {
    await testAPIEndpoints();
    console.log('\n' + '='.repeat(50));
    testCollaboration();
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { testAPIEndpoints, testCollaboration };