#!/bin/bash

# CVleap Production Deployment Script
# This script deploys the CVleap application using Docker Compose

set -e

echo "🚀 Starting CVleap deployment..."

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# Check if environment file exists
check_environment() {
    log_info "Checking environment configuration..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file not found. Creating from template..."
        cp .env.docker .env
        log_warning "Please edit .env file with your configuration before continuing."
        read -p "Press enter when ready to continue..."
    fi
    
    # Check for required environment variables
    source $ENV_FILE
    
    if [ -z "$JWT_SECRET" ] || [ "$JWT_SECRET" = "your_super_secure_jwt_secret_key_here" ]; then
        log_error "JWT_SECRET is not set or using default value. Please update .env file."
        exit 1
    fi
    
    if [ -z "$POSTGRES_PASSWORD" ] || [ "$POSTGRES_PASSWORD" = "your_secure_postgres_password" ]; then
        log_error "POSTGRES_PASSWORD is not set or using default value. Please update .env file."
        exit 1
    fi
    
    log_success "Environment configuration check passed"
}

# Create backup of existing data
create_backup() {
    log_info "Creating backup of existing data..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
    fi
    
    # Backup database if containers are running
    if docker-compose ps | grep -q "postgres"; then
        log_info "Backing up database..."
        docker-compose exec -T postgres pg_dump -U cvleap_user cvleap > "$BACKUP_DIR/database_backup_$TIMESTAMP.sql" || log_warning "Database backup failed"
    fi
    
    # Backup uploaded files
    if [ -d "./uploads" ]; then
        log_info "Backing up uploaded files..."
        tar -czf "$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz" ./uploads || log_warning "Uploads backup failed"
    fi
    
    log_success "Backup completed"
}

# Pull latest images
pull_images() {
    log_info "Pulling latest Docker images..."
    docker-compose pull
    log_success "Images pulled successfully"
}

# Build and deploy services
deploy_services() {
    log_info "Building and deploying services..."
    
    # Build images
    docker-compose build --no-cache
    
    # Stop existing services
    docker-compose down
    
    # Start services
    docker-compose up -d
    
    log_success "Services deployed successfully"
}

# Wait for services to be healthy
wait_for_health() {
    log_info "Waiting for services to be healthy..."
    
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker-compose ps | grep -q "healthy"; then
            log_success "Services are healthy"
            return 0
        fi
        
        attempt=$((attempt + 1))
        log_info "Attempt $attempt/$max_attempts - waiting for services..."
        sleep 10
    done
    
    log_error "Services failed to become healthy within timeout"
    return 1
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations
    docker-compose exec -T server npm run migrate || log_warning "Migration failed - database might already be up to date"
    
    log_success "Database migrations completed"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if all services are running
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Some services are not running"
        docker-compose logs
        return 1
    fi
    
    # Check health endpoint
    local health_check_url="http://localhost:3000/health"
    local max_attempts=10
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f "$health_check_url" &> /dev/null; then
            log_success "Health check passed"
            break
        fi
        
        attempt=$((attempt + 1))
        log_info "Health check attempt $attempt/$max_attempts..."
        sleep 5
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_error "Health check failed"
        return 1
    fi
    
    log_success "Deployment verification completed"
}

# Cleanup old images and containers
cleanup() {
    log_info "Cleaning up old images and containers..."
    
    # Remove old images
    docker image prune -f
    
    # Remove old containers
    docker container prune -f
    
    log_success "Cleanup completed"
}

# Show deployment status
show_status() {
    echo ""
    echo "🎉 Deployment Summary"
    echo "===================="
    echo ""
    
    log_info "Services Status:"
    docker-compose ps
    
    echo ""
    log_info "Application URLs:"
    echo "  - Frontend: http://localhost:3001"
    echo "  - Backend API: http://localhost:3000"
    echo "  - Health Check: http://localhost:3000/health"
    
    echo ""
    log_info "Useful Commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo "  - View status: docker-compose ps"
    
    echo ""
    log_success "CVleap deployment completed successfully! 🚀"
}

# Main deployment process
main() {
    log_info "CVleap Production Deployment"
    echo "==============================="
    echo ""
    
    check_dependencies
    check_environment
    create_backup
    pull_images
    deploy_services
    wait_for_health
    run_migrations
    verify_deployment
    cleanup
    show_status
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping CVleap services..."
        docker-compose down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting CVleap services..."
        docker-compose restart
        log_success "Services restarted"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "backup")
        create_backup
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|backup|cleanup}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show service logs"
        echo "  status  - Show service status"
        echo "  backup  - Create backup only"
        echo "  cleanup - Clean up old images/containers"
        exit 1
        ;;
esac