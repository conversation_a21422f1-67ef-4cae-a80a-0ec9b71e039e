#!/bin/bash

# Enhanced Production Backup Script for CVleap
# Supports automated backups, encryption, and multiple storage backends

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="cvleap_backup_$TIMESTAMP"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-cvleap}"
DB_USER="${DB_USER:-cvleap_user}"
DB_PASSWORD="${DB_PASSWORD:-}"

# Backup retention
KEEP_DAYS="${BACKUP_RETENTION_DAYS:-30}"
KEEP_WEEKLY="${BACKUP_RETENTION_WEEKS:-12}"
KEEP_MONTHLY="${BACKUP_RETENTION_MONTHS:-12}"

# Encryption
ENABLE_ENCRYPTION="${BACKUP_ENCRYPTION:-true}"
ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY:-}"

# Storage backends
ENABLE_S3="${BACKUP_S3_ENABLED:-false}"
S3_BUCKET="${BACKUP_S3_BUCKET:-}"
S3_PREFIX="${BACKUP_S3_PREFIX:-backups}"

ENABLE_AZURE="${BACKUP_AZURE_ENABLED:-false}"
AZURE_ACCOUNT="${BACKUP_AZURE_ACCOUNT:-}"
AZURE_CONTAINER="${BACKUP_AZURE_CONTAINER:-backups}"

# Notification
WEBHOOK_URL="${BACKUP_WEBHOOK_URL:-}"
SLACK_WEBHOOK="${SLACK_WEBHOOK_URL:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Backup failed with exit code $exit_code"
        send_notification "❌ CVleap backup failed" "Backup process failed at $(date)"
    fi
    
    # Clean up temporary files
    if [ -n "${TEMP_DIR:-}" ] && [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    exit $exit_code
}

trap cleanup EXIT

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    # Check for required tools
    command -v pg_dump >/dev/null 2>&1 || missing_deps+=("postgresql-client")
    command -v tar >/dev/null 2>&1 || missing_deps+=("tar")
    command -v gzip >/dev/null 2>&1 || missing_deps+=("gzip")
    
    if [ "$ENABLE_ENCRYPTION" = "true" ]; then
        command -v openssl >/dev/null 2>&1 || missing_deps+=("openssl")
    fi
    
    if [ "$ENABLE_S3" = "true" ]; then
        command -v aws >/dev/null 2>&1 || missing_deps+=("awscli")
    fi
    
    if [ "$ENABLE_AZURE" = "true" ]; then
        command -v az >/dev/null 2>&1 || missing_deps+=("azure-cli")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Install missing dependencies and retry"
        exit 1
    fi
}

# Create backup directory
setup_backup_environment() {
    mkdir -p "$BACKUP_DIR"
    
    # Create temporary directory for this backup
    TEMP_DIR=$(mktemp -d)
    log_info "Using temporary directory: $TEMP_DIR"
}

# Database backup
backup_database() {
    log_info "Starting database backup..."
    
    local db_backup_file="$TEMP_DIR/${BACKUP_NAME}_database.sql"
    
    if [ -n "$DB_PASSWORD" ]; then
        export PGPASSWORD="$DB_PASSWORD"
    fi
    
    # Create database dump
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
       --verbose --clean --if-exists --create > "$db_backup_file"; then
        log_success "Database backup completed: $(basename "$db_backup_file")"
        
        # Get backup size
        local size=$(du -h "$db_backup_file" | cut -f1)
        log_info "Database backup size: $size"
    else
        log_error "Database backup failed"
        return 1
    fi
    
    unset PGPASSWORD
}

# Application files backup
backup_application_files() {
    log_info "Starting application files backup..."
    
    local files_backup="$TEMP_DIR/${BACKUP_NAME}_files.tar.gz"
    
    # Files to include in backup
    local include_patterns=(
        "uploads/"
        "logs/"
        "server/.env"
        "client/.env"
        ".env"
        ".env.production"
        "package.json"
        "docker-compose.yml"
        "docker-compose.prod.yml"
    )
    
    # Files to exclude
    local exclude_patterns=(
        "node_modules"
        "*.log"
        "*.tmp"
        ".git"
        "dist"
        "build"
        ".cache"
        "coverage"
    )
    
    # Build tar command
    local tar_cmd="tar -czf $files_backup -C $PROJECT_ROOT"
    
    # Add exclude patterns
    for pattern in "${exclude_patterns[@]}"; do
        tar_cmd="$tar_cmd --exclude=$pattern"
    done
    
    # Add include patterns
    for pattern in "${include_patterns[@]}"; do
        if [ -e "$PROJECT_ROOT/$pattern" ]; then
            tar_cmd="$tar_cmd $pattern"
        fi
    done
    
    # Execute backup
    if eval "$tar_cmd" 2>/dev/null; then
        log_success "Application files backup completed: $(basename "$files_backup")"
        
        # Get backup size
        local size=$(du -h "$files_backup" | cut -f1)
        log_info "Files backup size: $size"
    else
        log_warning "Some application files could not be backed up"
    fi
}

# Configuration backup
backup_configuration() {
    log_info "Starting configuration backup..."
    
    local config_backup="$TEMP_DIR/${BACKUP_NAME}_config.tar.gz"
    
    # Create configuration archive
    tar -czf "$config_backup" -C "$PROJECT_ROOT" \
        --exclude="*.key" \
        --exclude="*.pem" \
        --exclude="*secret*" \
        k8s/ \
        scripts/ \
        nginx/ \
        .github/ \
        *.md \
        *.yml \
        *.yaml \
        *.json \
        Dockerfile* \
        2>/dev/null || true
    
    if [ -f "$config_backup" ]; then
        log_success "Configuration backup completed: $(basename "$config_backup")"
    else
        log_warning "Configuration backup failed or no files found"
    fi
}

# Create backup metadata
create_backup_metadata() {
    local metadata_file="$TEMP_DIR/${BACKUP_NAME}_metadata.json"
    
    cat > "$metadata_file" << EOF
{
  "backup_name": "$BACKUP_NAME",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "version": "$(cd "$PROJECT_ROOT" && git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "environment": "${NODE_ENV:-production}",
  "database": {
    "host": "$DB_HOST",
    "port": $DB_PORT,
    "name": "$DB_NAME",
    "user": "$DB_USER"
  },
  "files": {
    "database": "${BACKUP_NAME}_database.sql",
    "application": "${BACKUP_NAME}_files.tar.gz",
    "configuration": "${BACKUP_NAME}_config.tar.gz"
  },
  "encryption": $ENABLE_ENCRYPTION,
  "retention": {
    "daily": $KEEP_DAYS,
    "weekly": $KEEP_WEEKLY,
    "monthly": $KEEP_MONTHLY
  }
}
EOF
    
    log_info "Backup metadata created: $(basename "$metadata_file")"
}

# Encrypt backup files
encrypt_backup() {
    if [ "$ENABLE_ENCRYPTION" != "true" ]; then
        return 0
    fi
    
    log_info "Encrypting backup files..."
    
    if [ -z "$ENCRYPTION_KEY" ]; then
        log_error "Encryption enabled but no encryption key provided"
        return 1
    fi
    
    # Encrypt each backup file
    for file in "$TEMP_DIR"/*.sql "$TEMP_DIR"/*.tar.gz "$TEMP_DIR"/*.json; do
        if [ -f "$file" ]; then
            local encrypted_file="${file}.enc"
            
            if openssl enc -aes-256-cbc -salt -pbkdf2 -in "$file" -out "$encrypted_file" -k "$ENCRYPTION_KEY"; then
                rm "$file"
                log_info "Encrypted: $(basename "$encrypted_file")"
            else
                log_error "Failed to encrypt: $(basename "$file")"
                return 1
            fi
        fi
    done
    
    log_success "Backup encryption completed"
}

# Create final backup archive
create_final_archive() {
    log_info "Creating final backup archive..."
    
    local final_archive="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    
    # Create compressed archive
    if tar -czf "$final_archive" -C "$TEMP_DIR" .; then
        log_success "Final backup archive created: $(basename "$final_archive")"
        
        # Get final size
        local size=$(du -h "$final_archive" | cut -f1)
        log_info "Final backup size: $size"
        
        # Create checksum
        cd "$BACKUP_DIR"
        sha256sum "$(basename "$final_archive")" > "${BACKUP_NAME}.sha256"
        log_info "Checksum created: ${BACKUP_NAME}.sha256"
    else
        log_error "Failed to create final backup archive"
        return 1
    fi
}

# Upload to S3
upload_to_s3() {
    if [ "$ENABLE_S3" != "true" ] || [ -z "$S3_BUCKET" ]; then
        return 0
    fi
    
    log_info "Uploading backup to S3..."
    
    local final_archive="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    local checksum_file="$BACKUP_DIR/${BACKUP_NAME}.sha256"
    
    # Upload backup file
    if aws s3 cp "$final_archive" "s3://$S3_BUCKET/$S3_PREFIX/"; then
        log_success "Backup uploaded to S3: s3://$S3_BUCKET/$S3_PREFIX/$(basename "$final_archive")"
    else
        log_error "Failed to upload backup to S3"
        return 1
    fi
    
    # Upload checksum
    if aws s3 cp "$checksum_file" "s3://$S3_BUCKET/$S3_PREFIX/"; then
        log_info "Checksum uploaded to S3"
    else
        log_warning "Failed to upload checksum to S3"
    fi
}

# Upload to Azure
upload_to_azure() {
    if [ "$ENABLE_AZURE" != "true" ] || [ -z "$AZURE_ACCOUNT" ]; then
        return 0
    fi
    
    log_info "Uploading backup to Azure Blob Storage..."
    
    local final_archive="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    local checksum_file="$BACKUP_DIR/${BACKUP_NAME}.sha256"
    
    # Upload backup file
    if az storage blob upload \
       --account-name "$AZURE_ACCOUNT" \
       --container-name "$AZURE_CONTAINER" \
       --file "$final_archive" \
       --name "$(basename "$final_archive")"; then
        log_success "Backup uploaded to Azure: $AZURE_ACCOUNT/$AZURE_CONTAINER/$(basename "$final_archive")"
    else
        log_error "Failed to upload backup to Azure"
        return 1
    fi
    
    # Upload checksum
    if az storage blob upload \
       --account-name "$AZURE_ACCOUNT" \
       --container-name "$AZURE_CONTAINER" \
       --file "$checksum_file" \
       --name "$(basename "$checksum_file")"; then
        log_info "Checksum uploaded to Azure"
    else
        log_warning "Failed to upload checksum to Azure"
    fi
}

# Clean up old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Remove backups older than specified days
    find "$BACKUP_DIR" -name "cvleap_backup_*.tar.gz" -mtime +$KEEP_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "cvleap_backup_*.sha256" -mtime +$KEEP_DAYS -delete 2>/dev/null || true
    
    log_info "Cleanup completed - keeping backups for $KEEP_DAYS days"
}

# Send notification
send_notification() {
    local title="$1"
    local message="$2"
    local status="${3:-info}"
    
    # Webhook notification
    if [ -n "$WEBHOOK_URL" ]; then
        curl -s -X POST "$WEBHOOK_URL" \
             -H "Content-Type: application/json" \
             -d "{\"title\":\"$title\",\"message\":\"$message\",\"status\":\"$status\"}" \
             >/dev/null 2>&1 || true
    fi
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="good"
        if [ "$status" = "error" ]; then
            color="danger"
        elif [ "$status" = "warning" ]; then
            color="warning"
        fi
        
        curl -s -X POST "$SLACK_WEBHOOK" \
             -H "Content-Type: application/json" \
             -d "{\"attachments\":[{\"color\":\"$color\",\"title\":\"$title\",\"text\":\"$message\"}]}" \
             >/dev/null 2>&1 || true
    fi
}

# Main backup process
main() {
    log_info "Starting CVleap backup process..."
    log_info "Backup name: $BACKUP_NAME"
    
    # Pre-flight checks
    check_dependencies
    setup_backup_environment
    
    # Create backups
    backup_database
    backup_application_files
    backup_configuration
    create_backup_metadata
    
    # Encrypt if enabled
    encrypt_backup
    
    # Create final archive
    create_final_archive
    
    # Upload to cloud storage
    upload_to_s3
    upload_to_azure
    
    # Cleanup
    cleanup_old_backups
    
    # Success notification
    local backup_size=$(du -h "$BACKUP_DIR/${BACKUP_NAME}.tar.gz" | cut -f1)
    send_notification "✅ CVleap backup completed" "Backup $BACKUP_NAME completed successfully. Size: $backup_size" "success"
    
    log_success "Backup process completed successfully!"
    log_info "Backup location: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
}

# Handle script arguments
case "${1:-backup}" in
    "backup")
        main
        ;;
    "restore")
        log_info "Restore functionality - please use the restore script"
        ;;
    "test")
        log_info "Testing backup configuration..."
        check_dependencies
        log_success "Backup configuration test passed"
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        echo "Usage: $0 {backup|restore|test|cleanup}"
        echo "  backup  - Create a full backup (default)"
        echo "  restore - Restore from backup"
        echo "  test    - Test backup configuration"
        echo "  cleanup - Clean up old backups"
        exit 1
        ;;
esac