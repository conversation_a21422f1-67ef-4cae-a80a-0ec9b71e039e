#!/usr/bin/env node

/**
 * Container Density Calculator for CVLeap
 * Calculates optimal container density based on VM resources and container type
 */

const containerTypes = {
  standard: {
    name: 'Standard Docker',
    memoryOverhead: 0, // MB
    cpuOverhead: 0, // percentage
    description: 'Standard Docker containers with security hardening'
  },
  gvisor: {
    name: 'gVisor',
    memoryOverhead: 75, // MB
    cpuOverhead: 12.5, // percentage
    description: 'gVisor containers with enhanced security'
  },
  kata: {
    name: 'Kata Containers',
    memoryOverhead: 150, // MB
    cpuOverhead: 7.5, // percentage
    description: 'Kata containers with VM-level isolation'
  },
  firecracker: {
    name: 'Firecracker',
    memoryOverhead: 100, // MB
    cpuOverhead: 4, // percentage
    description: 'Firecracker microVMs'
  }
};

const vmConfigurations = {
  small: { cpu: 2, memory: 4096, name: 'Small (2 vCPU, 4GB)' },
  medium: { cpu: 4, memory: 8192, name: 'Medium (4 vCPU, 8GB)' },
  large: { cpu: 8, memory: 16384, name: 'Large (8 vCPU, 16GB)' },
  xlarge: { cpu: 16, memory: 32768, name: 'XL (16 vCPU, 32GB)' },
  xxlarge: { cpu: 32, memory: 65536, name: 'XXL (32 vCPU, 64GB)' }
};

const applicationProfiles = {
  cvleap_server: {
    name: 'CVLeap Server',
    baseCpu: 0.1, // vCPU
    baseMemory: 100, // MB
    description: 'CVLeap Node.js server container'
  },
  cvleap_client: {
    name: 'CVLeap Client',
    baseCpu: 0.05, // vCPU
    baseMemory: 50, // MB
    description: 'CVLeap Nginx client container'
  },
  cvleap_job: {
    name: 'CVLeap Job Runner',
    baseCpu: 0.2, // vCPU
    baseMemory: 150, // MB
    description: 'CVLeap job execution container'
  },
  microservice: {
    name: 'Generic Microservice',
    baseCpu: 0.05, // vCPU
    baseMemory: 64, // MB
    description: 'Generic microservice container'
  }
};

function calculateDensity(vmConfig, containerType, appProfile, options = {}) {
  const {
    osOverheadCpu = 1, // vCPU reserved for OS
    osOverheadMemory = 2048, // MB reserved for OS
    safetyMargin = 0.1, // 10% safety margin
    enableBurstable = false
  } = options;

  // Available resources after OS overhead
  const availableCpu = vmConfig.cpu - osOverheadCpu;
  const availableMemory = vmConfig.memory - osOverheadMemory;

  // Container resource requirements
  const containerCpu = appProfile.baseCpu * (1 + containerType.cpuOverhead / 100);
  const containerMemory = appProfile.baseMemory + containerType.memoryOverhead;

  // Calculate theoretical maximums
  const maxByCpu = Math.floor(availableCpu / containerCpu);
  const maxByMemory = Math.floor(availableMemory / containerMemory);

  // Practical maximum (limited by most constrained resource)
  const theoreticalMax = Math.min(maxByCpu, maxByMemory);
  
  // Apply safety margin
  const practicalMax = Math.floor(theoreticalMax * (1 - safetyMargin));

  // Burstable calculation (allows CPU overcommit)
  const burstableMax = enableBurstable ? 
    Math.floor(maxByMemory * (1 - safetyMargin)) : practicalMax;

  return {
    vmConfig,
    containerType,
    appProfile,
    resources: {
      availableCpu,
      availableMemory,
      containerCpu,
      containerMemory
    },
    density: {
      maxByCpu,
      maxByMemory,
      theoreticalMax,
      practicalMax,
      burstableMax,
      limitingFactor: maxByCpu < maxByMemory ? 'CPU' : 'Memory'
    },
    utilization: {
      cpu: (practicalMax * containerCpu / availableCpu * 100).toFixed(1),
      memory: (practicalMax * containerMemory / availableMemory * 100).toFixed(1)
    }
  };
}

function generateDensityReport() {
  console.log('🚀 CVLeap Container Density Analysis Report\n');
  console.log('=' .repeat(80));
  
  // Test different VM configurations
  Object.entries(vmConfigurations).forEach(([vmKey, vmConfig]) => {
    console.log(`\n📊 ${vmConfig.name}`);
    console.log('-'.repeat(50));
    
    // Test CVLeap server containers with different runtimes
    Object.entries(containerTypes).forEach(([typeKey, containerType]) => {
      const result = calculateDensity(
        vmConfig, 
        containerType, 
        applicationProfiles.cvleap_server,
        { enableBurstable: true }
      );
      
      console.log(`\n${containerType.name}:`);
      console.log(`  Practical Max: ${result.density.practicalMax} containers`);
      console.log(`  Burstable Max: ${result.density.burstableMax} containers`);
      console.log(`  Limiting Factor: ${result.density.limitingFactor}`);
      console.log(`  CPU Utilization: ${result.utilization.cpu}%`);
      console.log(`  Memory Utilization: ${result.utilization.memory}%`);
      console.log(`  Per Container: ${result.resources.containerCpu.toFixed(2)} vCPU, ${result.resources.containerMemory}MB`);
    });
  });
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 Optimization Recommendations\n');
  
  // Generate recommendations
  const recommendations = generateRecommendations();
  recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });
}

function generateRecommendations() {
  return [
    '**Right-size containers**: Use resource limits and requests to prevent resource waste',
    '**Enable CPU bursting**: Allow CPU overcommit for better density in non-CPU intensive workloads',
    '**Use horizontal pod autoscaling**: Scale containers based on actual resource usage',
    '**Implement resource quotas**: Prevent resource exhaustion and ensure fair sharing',
    '**Monitor resource utilization**: Use Prometheus/Grafana to track actual vs allocated resources',
    '**Consider mixed workloads**: Combine CPU-intensive and memory-intensive containers',
    '**Use node affinity**: Place containers optimally based on resource requirements',
    '**Implement quality of service**: Use QoS classes to prioritize critical workloads'
  ];
}

function calculateCostOptimization(vmConfig, containerType, appProfile) {
  const result = calculateDensity(vmConfig, containerType, appProfile);
  
  // Assume VM cost (example: AWS pricing)
  const vmCostPerHour = {
    small: 0.096,   // t3.medium
    medium: 0.192,  // t3.large
    large: 0.384,   // t3.xlarge
    xlarge: 0.768,  // t3.2xlarge
    xxlarge: 1.536  // t3.4xlarge
  };
  
  const vmSize = Object.keys(vmConfigurations).find(
    key => vmConfigurations[key] === vmConfig
  );
  
  const costPerHour = vmCostPerHour[vmSize] || 0.384;
  const costPerContainerPerHour = costPerHour / result.density.practicalMax;
  const costPerContainerPerMonth = costPerContainerPerHour * 24 * 30;
  
  return {
    vmCostPerHour: costPerHour,
    costPerContainerPerHour: costPerContainerPerHour.toFixed(4),
    costPerContainerPerMonth: costPerContainerPerMonth.toFixed(2),
    containersPerVM: result.density.practicalMax
  };
}

function generateCostReport() {
  console.log('\n💰 Cost Optimization Analysis\n');
  console.log('=' .repeat(60));
  
  Object.entries(vmConfigurations).forEach(([vmKey, vmConfig]) => {
    const standardCost = calculateCostOptimization(
      vmConfig, 
      containerTypes.standard, 
      applicationProfiles.cvleap_server
    );
    
    const gvisorCost = calculateCostOptimization(
      vmConfig, 
      containerTypes.gvisor, 
      applicationProfiles.cvleap_server
    );
    
    console.log(`\n${vmConfig.name}:`);
    console.log(`  Standard Docker: ${standardCost.containersPerVM} containers @ $${standardCost.costPerContainerPerMonth}/month`);
    console.log(`  gVisor: ${gvisorCost.containersPerVM} containers @ $${gvisorCost.costPerContainerPerMonth}/month`);
    
    const costIncrease = ((parseFloat(gvisorCost.costPerContainerPerMonth) / parseFloat(standardCost.costPerContainerPerMonth) - 1) * 100).toFixed(1);
    console.log(`  gVisor cost increase: ${costIncrease}%`);
  });
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node container-density-calculator.js [options]

Options:
  --report, -r     Generate full density report
  --cost, -c       Generate cost optimization report
  --vm <size>      Calculate for specific VM size (small|medium|large|xlarge|xxlarge)
  --type <type>    Container type (standard|gvisor|kata|firecracker)
  --app <app>      Application profile (cvleap_server|cvleap_client|cvleap_job|microservice)
  --help, -h       Show this help message

Examples:
  node container-density-calculator.js --report
  node container-density-calculator.js --cost
  node container-density-calculator.js --vm large --type gvisor --app cvleap_server
    `);
    process.exit(0);
  }
  
  if (args.includes('--report') || args.includes('-r')) {
    generateDensityReport();
  }
  
  if (args.includes('--cost') || args.includes('-c')) {
    generateCostReport();
  }
  
  // Custom calculation
  const vmIndex = args.indexOf('--vm');
  const typeIndex = args.indexOf('--type');
  const appIndex = args.indexOf('--app');
  
  if (vmIndex !== -1 && typeIndex !== -1 && appIndex !== -1) {
    const vmSize = args[vmIndex + 1];
    const containerType = args[typeIndex + 1];
    const appProfile = args[appIndex + 1];
    
    if (vmConfigurations[vmSize] && containerTypes[containerType] && applicationProfiles[appProfile]) {
      const result = calculateDensity(
        vmConfigurations[vmSize],
        containerTypes[containerType],
        applicationProfiles[appProfile],
        { enableBurstable: true }
      );
      
      console.log(`\n🎯 Custom Calculation Results:`);
      console.log(`VM: ${result.vmConfig.name}`);
      console.log(`Container Type: ${result.containerType.name}`);
      console.log(`Application: ${result.appProfile.name}`);
      console.log(`Practical Maximum: ${result.density.practicalMax} containers`);
      console.log(`Burstable Maximum: ${result.density.burstableMax} containers`);
      console.log(`Limiting Factor: ${result.density.limitingFactor}`);
      console.log(`Resource Utilization: ${result.utilization.cpu}% CPU, ${result.utilization.memory}% Memory`);
    } else {
      console.error('Invalid parameters. Use --help for usage information.');
    }
  }
  
  // Default behavior
  if (args.length === 0) {
    generateDensityReport();
    generateCostReport();
  }
}

module.exports = {
  calculateDensity,
  containerTypes,
  vmConfigurations,
  applicationProfiles,
  generateDensityReport,
  generateCostReport
};
