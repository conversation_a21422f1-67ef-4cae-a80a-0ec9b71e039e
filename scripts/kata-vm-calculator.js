#!/usr/bin/env node

/**
 * Kata Containers VM Requirements Calculator for CVLeap
 * Calculates VM requirements for different deployment scales using Kata Containers
 */

const kataContainerProfile = {
  name: 'Kata Containers',
  memoryOverhead: 150, // MB per container
  cpuOverhead: 7.5, // percentage
  startupTime: 6, // seconds
  securityLevel: 99, // out of 100
  isolationLevel: 'VM-level'
};

const cvleapApplicationProfile = {
  server: {
    baseCpu: 0.1, // vCPU
    baseMemory: 100, // MB
    description: 'CVLeap Node.js server'
  },
  client: {
    baseCpu: 0.05, // vCPU
    baseMemory: 50, // MB
    description: 'CVLeap React client'
  },
  jobRunner: {
    baseCpu: 0.2, // vCPU
    baseMemory: 150, // MB
    description: 'CVLeap job execution'
  },
  database: {
    baseCpu: 0.3, // vCPU
    baseMemory: 512, // MB
    description: 'PostgreSQL database'
  },
  redis: {
    baseCpu: 0.1, // vCPU
    baseMemory: 128, // MB
    description: 'Redis cache'
  }
};

const vmConfigurations = {
  small: { 
    cpu: 2, 
    memory: 4096, 
    costPerMonth: 96,
    name: 'Small (2 vCPU, 4GB)',
    awsInstance: 't3.medium'
  },
  medium: { 
    cpu: 4, 
    memory: 8192, 
    costPerMonth: 192,
    name: 'Medium (4 vCPU, 8GB)',
    awsInstance: 't3.large'
  },
  large: { 
    cpu: 8, 
    memory: 16384, 
    costPerMonth: 384,
    name: 'Large (8 vCPU, 16GB)',
    awsInstance: 't3.xlarge'
  },
  xlarge: { 
    cpu: 16, 
    memory: 32768, 
    costPerMonth: 768,
    name: 'XL (16 vCPU, 32GB)',
    awsInstance: 't3.2xlarge'
  },
  xxlarge: { 
    cpu: 32, 
    memory: 65536, 
    costPerMonth: 1536,
    name: 'XXL (32 vCPU, 64GB)',
    awsInstance: 't3.4xlarge'
  }
};

const deploymentScenarios = {
  startup: {
    users: 1000,
    description: 'Startup deployment',
    containerRatio: 0.1, // containers per user
    redundancy: 1.2 // 20% redundancy
  },
  growth: {
    users: 10000,
    description: 'Growth stage',
    containerRatio: 0.1,
    redundancy: 1.3 // 30% redundancy
  },
  scale: {
    users: 100000,
    description: 'Scale stage',
    containerRatio: 0.1,
    redundancy: 1.4 // 40% redundancy
  },
  enterprise: {
    users: 1000000,
    description: 'Enterprise deployment',
    containerRatio: 0.1,
    redundancy: 1.5 // 50% redundancy
  }
};

function calculateKataContainerDensity(vmConfig, appProfile, options = {}) {
  const {
    osOverheadCpu = 1, // vCPU reserved for OS
    osOverheadMemory = 2048, // MB reserved for OS
    safetyMargin = 0.1 // 10% safety margin
  } = options;

  // Available resources after OS overhead
  const availableCpu = vmConfig.cpu - osOverheadCpu;
  const availableMemory = vmConfig.memory - osOverheadMemory;

  // Kata container resource requirements
  const containerCpu = appProfile.baseCpu * (1 + kataContainerProfile.cpuOverhead / 100);
  const containerMemory = appProfile.baseMemory + kataContainerProfile.memoryOverhead;

  // Calculate theoretical maximums
  const maxByCpu = Math.floor(availableCpu / containerCpu);
  const maxByMemory = Math.floor(availableMemory / containerMemory);

  // Practical maximum (limited by most constrained resource)
  const theoreticalMax = Math.min(maxByCpu, maxByMemory);
  const practicalMax = Math.floor(theoreticalMax * (1 - safetyMargin));

  return {
    vmConfig,
    appProfile,
    resources: {
      availableCpu,
      availableMemory,
      containerCpu: containerCpu.toFixed(3),
      containerMemory
    },
    density: {
      maxByCpu,
      maxByMemory,
      theoreticalMax,
      practicalMax,
      limitingFactor: maxByCpu < maxByMemory ? 'CPU' : 'Memory'
    },
    utilization: {
      cpu: (practicalMax * containerCpu / availableCpu * 100).toFixed(1),
      memory: (practicalMax * containerMemory / availableMemory * 100).toFixed(1)
    }
  };
}

function calculateVMRequirements(scenario, vmSize = 'large', appType = 'server') {
  const vmConfig = vmConfigurations[vmSize];
  const appProfile = cvleapApplicationProfile[appType];
  
  // Calculate containers needed
  const containersNeeded = Math.ceil(
    scenario.users * scenario.containerRatio * scenario.redundancy
  );
  
  // Calculate density per VM
  const densityResult = calculateKataContainerDensity(vmConfig, appProfile);
  const containersPerVM = densityResult.density.practicalMax;
  
  // Calculate VMs required
  const vmsRequired = Math.ceil(containersNeeded / containersPerVM);
  
  // Calculate costs
  const totalMonthlyCost = vmsRequired * vmConfig.costPerMonth;
  const costPerUser = totalMonthlyCost / scenario.users;
  const costPerContainer = totalMonthlyCost / containersNeeded;
  
  return {
    scenario,
    vmConfig,
    appProfile,
    requirements: {
      users: scenario.users,
      containersNeeded,
      containersPerVM,
      vmsRequired,
      totalContainerCapacity: vmsRequired * containersPerVM,
      utilizationRate: (containersNeeded / (vmsRequired * containersPerVM) * 100).toFixed(1)
    },
    costs: {
      totalMonthlyCost,
      costPerUser: costPerUser.toFixed(2),
      costPerContainer: costPerContainer.toFixed(2),
      vmCostPerMonth: vmConfig.costPerMonth
    },
    resources: {
      totalCpu: vmsRequired * vmConfig.cpu,
      totalMemory: vmsRequired * vmConfig.memory,
      cpuUtilization: densityResult.utilization.cpu,
      memoryUtilization: densityResult.utilization.memory
    }
  };
}

function generateVMRequirementsReport() {
  console.log('🏗️  Kata Containers VM Requirements Analysis for CVLeap\n');
  console.log('=' .repeat(80));
  
  // Test different scenarios with large VMs
  Object.entries(deploymentScenarios).forEach(([scenarioKey, scenario]) => {
    console.log(`\n📊 ${scenario.description.toUpperCase()} (${scenario.users.toLocaleString()} users)`);
    console.log('-'.repeat(60));
    
    const result = calculateVMRequirements(scenario, 'large', 'server');
    
    console.log(`Containers needed: ${result.requirements.containersNeeded.toLocaleString()}`);
    console.log(`Containers per VM: ${result.requirements.containersPerVM}`);
    console.log(`VMs required: ${result.requirements.vmsRequired.toLocaleString()}`);
    console.log(`VM utilization: ${result.requirements.utilizationRate}%`);
    console.log(`Total monthly cost: $${result.costs.totalMonthlyCost.toLocaleString()}`);
    console.log(`Cost per user: $${result.costs.costPerUser}/month`);
    console.log(`Cost per container: $${result.costs.costPerContainer}/month`);
    console.log(`Total resources: ${result.resources.totalCpu} vCPU, ${(result.resources.totalMemory/1024).toFixed(0)}GB RAM`);
  });
}

function compareVMSizes(scenario = deploymentScenarios.growth) {
  console.log(`\n🔍 VM Size Comparison for ${scenario.description}`);
  console.log('=' .repeat(70));
  
  Object.entries(vmConfigurations).forEach(([vmKey, vmConfig]) => {
    const result = calculateVMRequirements(scenario, vmKey, 'server');
    
    console.log(`\n${vmConfig.name} (${vmConfig.awsInstance}):`);
    console.log(`  VMs required: ${result.requirements.vmsRequired}`);
    console.log(`  Monthly cost: $${result.costs.totalMonthlyCost.toLocaleString()}`);
    console.log(`  Cost per user: $${result.costs.costPerUser}`);
    console.log(`  Utilization: ${result.requirements.utilizationRate}%`);
  });
}

function generateCostComparison() {
  console.log('\n💰 Cost Comparison: Kata vs Standard Docker vs gVisor\n');
  console.log('=' .repeat(80));
  
  const scenario = deploymentScenarios.growth; // 10,000 users
  
  // Kata Containers
  const kataResult = calculateVMRequirements(scenario, 'large', 'server');
  
  // Standard Docker (estimated)
  const dockerContainersPerVM = 140; // from previous analysis
  const dockerVMsRequired = Math.ceil(scenario.users * scenario.containerRatio * scenario.redundancy / dockerContainersPerVM);
  const dockerCost = dockerVMsRequired * vmConfigurations.large.costPerMonth;
  
  // gVisor (estimated)
  const gvisorContainersPerVM = 105; // from previous analysis
  const gvisorVMsRequired = Math.ceil(scenario.users * scenario.containerRatio * scenario.redundancy / gvisorContainersPerVM);
  const gvisorCost = gvisorVMsRequired * vmConfigurations.large.costPerMonth;
  
  console.log(`Deployment: ${scenario.description} (${scenario.users.toLocaleString()} users)`);
  console.log(`Containers needed: ${Math.ceil(scenario.users * scenario.containerRatio * scenario.redundancy).toLocaleString()}\n`);
  
  console.log('Technology Comparison:');
  console.log(`Standard Docker: ${dockerVMsRequired} VMs, $${dockerCost.toLocaleString()}/month`);
  console.log(`gVisor:          ${gvisorVMsRequired} VMs, $${gvisorCost.toLocaleString()}/month (+${((gvisorCost/dockerCost-1)*100).toFixed(0)}%)`);
  console.log(`Kata Containers: ${kataResult.requirements.vmsRequired} VMs, $${kataResult.costs.totalMonthlyCost.toLocaleString()}/month (+${((kataResult.costs.totalMonthlyCost/dockerCost-1)*100).toFixed(0)}%)`);
  
  console.log('\nSecurity vs Cost Trade-off:');
  console.log('Standard Docker: 95/100 security, lowest cost');
  console.log('gVisor:          98/100 security, +33% cost');
  console.log('Kata Containers: 99/100 security, +67% cost');
}

function generateOptimizationRecommendations() {
  console.log('\n🎯 Kata Containers Optimization Recommendations\n');
  console.log('=' .repeat(60));
  
  const recommendations = [
    '1. **Use larger VMs**: Better resource utilization with fewer VMs to manage',
    '2. **Mixed workload strategy**: Combine CPU and memory-intensive containers',
    '3. **Resource right-sizing**: Optimize container resource requests/limits',
    '4. **Horizontal scaling**: Use auto-scaling to handle traffic spikes',
    '5. **Reserved instances**: Use AWS Reserved Instances for 30-60% cost savings',
    '6. **Spot instances**: Use spot instances for non-critical workloads (70% savings)',
    '7. **Multi-region deployment**: Distribute load across regions for better performance',
    '8. **Container optimization**: Minimize container image size and startup time'
  ];
  
  recommendations.forEach(rec => console.log(rec));
  
  console.log('\n📈 Expected Optimizations:');
  console.log('- Resource optimization: +20% density improvement');
  console.log('- Reserved instances: -30% to -60% cost reduction');
  console.log('- Spot instances: -70% cost reduction (non-critical workloads)');
  console.log('- Auto-scaling: -15% to -25% average cost reduction');
}

function calculateBreakEvenAnalysis() {
  console.log('\n📊 Break-Even Analysis: When Kata Containers Make Sense\n');
  console.log('=' .repeat(70));
  
  const securityIncidentCost = 500000; // $500k average cost
  const complianceBenefit = 50000; // $50k annual compliance savings
  
  Object.entries(deploymentScenarios).forEach(([key, scenario]) => {
    const kataResult = calculateVMRequirements(scenario, 'large', 'server');
    const dockerCost = Math.ceil(scenario.users * scenario.containerRatio * scenario.redundancy / 140) * vmConfigurations.large.costPerMonth;
    
    const annualCostIncrease = (kataResult.costs.totalMonthlyCost - dockerCost) * 12;
    const breakEvenMonths = annualCostIncrease / (complianceBenefit / 12);
    
    console.log(`${scenario.description}:`);
    console.log(`  Annual cost increase: $${annualCostIncrease.toLocaleString()}`);
    console.log(`  Break-even time: ${breakEvenMonths.toFixed(1)} months`);
    console.log(`  ROI after security incident prevention: ${((securityIncidentCost / annualCostIncrease) * 100).toFixed(0)}%`);
    console.log('');
  });
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node kata-vm-calculator.js [options]

Options:
  --report, -r         Generate full VM requirements report
  --compare, -c        Compare VM sizes for optimal configuration
  --cost              Generate cost comparison analysis
  --optimize, -o      Show optimization recommendations
  --breakeven, -b     Calculate break-even analysis
  --scenario <name>   Calculate for specific scenario (startup|growth|scale|enterprise)
  --vm <size>         Use specific VM size (small|medium|large|xlarge|xxlarge)
  --help, -h          Show this help message

Examples:
  node kata-vm-calculator.js --report
  node kata-vm-calculator.js --scenario enterprise --vm xlarge
  node kata-vm-calculator.js --cost --optimize
    `);
    process.exit(0);
  }
  
  if (args.includes('--report') || args.includes('-r')) {
    generateVMRequirementsReport();
  }
  
  if (args.includes('--compare') || args.includes('-c')) {
    compareVMSizes();
  }
  
  if (args.includes('--cost')) {
    generateCostComparison();
  }
  
  if (args.includes('--optimize') || args.includes('-o')) {
    generateOptimizationRecommendations();
  }
  
  if (args.includes('--breakeven') || args.includes('-b')) {
    calculateBreakEvenAnalysis();
  }
  
  // Custom scenario calculation
  const scenarioIndex = args.indexOf('--scenario');
  const vmIndex = args.indexOf('--vm');
  
  if (scenarioIndex !== -1) {
    const scenarioName = args[scenarioIndex + 1];
    const vmSize = vmIndex !== -1 ? args[vmIndex + 1] : 'large';
    
    if (deploymentScenarios[scenarioName] && vmConfigurations[vmSize]) {
      const result = calculateVMRequirements(deploymentScenarios[scenarioName], vmSize, 'server');
      
      console.log(`\n🎯 Custom Calculation: ${result.scenario.description}`);
      console.log(`VM Configuration: ${result.vmConfig.name}`);
      console.log(`Users: ${result.requirements.users.toLocaleString()}`);
      console.log(`Containers needed: ${result.requirements.containersNeeded.toLocaleString()}`);
      console.log(`VMs required: ${result.requirements.vmsRequired.toLocaleString()}`);
      console.log(`Monthly cost: $${result.costs.totalMonthlyCost.toLocaleString()}`);
      console.log(`Cost per user: $${result.costs.costPerUser}/month`);
    } else {
      console.error('Invalid scenario or VM size. Use --help for valid options.');
    }
  }
  
  // Default behavior
  if (args.length === 0) {
    generateVMRequirementsReport();
    generateCostComparison();
  }
}

module.exports = {
  calculateVMRequirements,
  calculateKataContainerDensity,
  deploymentScenarios,
  vmConfigurations,
  kataContainerProfile
};
