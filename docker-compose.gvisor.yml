version: '3.8'

services:
  # PostgreSQL Database with gVisor
  postgres:
    image: postgres:15-alpine
    container_name: cvleap-postgres-gvisor
    runtime: runsc  # Use gVisor runtime
    environment:
      POSTGRES_DB: cvleap
      POSTGRES_USER: cvleap_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cvleap_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cvleap_user -d cvleap"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis Cache with gVisor
  redis:
    image: redis:7-alpine
    container_name: cvleap-redis-gvisor
    runtime: runsc  # Use gVisor runtime
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Backend Server with gVisor
  server:
    build:
      context: ./server
      dockerfile: Dockerfile.gvisor
    container_name: cvleap-server-gvisor
    runtime: runsc  # Use gVisor runtime
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: postgresql://cvleap_user:${POSTGRES_PASSWORD:-cvleap_password}@postgres:5432/cvleap
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-secure-jwt-secret}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      NOVITA_API_KEY: ${NOVITA_API_KEY}
      AZURE_STORAGE_ACCOUNT_NAME: ${AZURE_STORAGE_ACCOUNT_NAME}
      AZURE_STORAGE_ACCOUNT_KEY: ${AZURE_STORAGE_ACCOUNT_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      CLIENT_URL: ${CLIENT_URL:-http://localhost:3001}
      # gVisor optimizations
      NODE_OPTIONS: "--max-old-space-size=1024 --optimize-for-size"
      GVISOR_RUNTIME: "true"
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - uploads_data:/app/uploads
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Frontend Client with gVisor
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.gvisor
    container_name: cvleap-client-gvisor
    runtime: runsc  # Use gVisor runtime
    ports:
      - "3001:80"
    depends_on:
      - server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Job Execution Container with Enhanced gVisor Security
  job-runner:
    build:
      context: ./docker
      dockerfile: Dockerfile.job-runner.gvisor
    container_name: cvleap-job-runner-gvisor
    runtime: runsc  # Use gVisor runtime
    environment:
      NODE_ENV: production
      GVISOR_RUNTIME: "true"
      SECURITY_PROFILE: "strict"
    networks:
      - cvleap-job-network  # Isolated network for job execution
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
          pids: 100
        reservations:
          cpus: '0.25'
          memory: 256M
    profiles:
      - job-execution  # Only start when needed

  # Nginx Reverse Proxy with gVisor
  nginx:
    image: nginx:alpine
    container_name: cvleap-nginx-gvisor
    runtime: runsc  # Use gVisor runtime
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.gvisor.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - client
      - server
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"  # gVisor handles syscall filtering
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # gVisor Monitoring and Metrics
  gvisor-monitor:
    image: prom/node-exporter:latest
    container_name: cvleap-gvisor-monitor
    runtime: runsc  # Use gVisor runtime
    command:
      - '--path.rootfs=/host'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
      - '--collector.gvisor'
    volumes:
      - '/:/host:ro,rslave'
    ports:
      - "9100:9100"
    networks:
      - cvleap-network
    security_opt:
      - "seccomp=unconfined"
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 128M

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/cvleap/postgres
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/cvleap/redis
  uploads_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/cvleap/uploads

networks:
  cvleap-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: cvleap-br0
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
  
  cvleap-job-network:
    driver: bridge
    internal: true  # No external access for job containers
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: cvleap-job-br0
      com.docker.network.bridge.enable_icc: "false"
